<?php
/**
 * 站群插件修复测试脚本
 * 用于验证修复是否有效
 */

defined('ROOT_PATH') || exit;

echo "<h2>🔧 站群插件修复测试</h2>";
echo "<p>正在测试修复后的功能...</p>";

// 测试1: 检查F_APP_NAME常量
echo "<h3>测试1: F_APP_NAME常量检查</h3>";
if (defined('F_APP_NAME')) {
    echo "✅ F_APP_NAME常量已定义: " . F_APP_NAME . "<br>";
} else {
    echo "❌ F_APP_NAME常量未定义<br>";
}

// 测试2: 检查文件路径
echo "<h3>测试2: 文件路径检查</h3>";
$upgrade_file = PLUGIN_PATH . 'le_website_group/upgrade.php';
$verify_file = PLUGIN_PATH . 'le_website_group/verify_installation.php';

if (file_exists($upgrade_file)) {
    echo "✅ 升级文件存在: {$upgrade_file}<br>";
} else {
    echo "❌ 升级文件不存在: {$upgrade_file}<br>";
}

if (file_exists($verify_file)) {
    echo "✅ 验证文件存在: {$verify_file}<br>";
} else {
    echo "❌ 验证文件不存在: {$verify_file}<br>";
}

// 测试3: 检查数据库连接
echo "<h3>测试3: 数据库连接检查</h3>";
try {
    $tableprefix = $_ENV['_config']['db']['master']['tablepre'];
    echo "✅ 数据库表前缀: {$tableprefix}<br>";
    
    // 检查表是否存在
    $check_table_sql = "SHOW TABLES LIKE '".$tableprefix."website_group'";
    $table_exists = $this->db->fetch_first($check_table_sql);
    
    if ($table_exists) {
        echo "✅ 站群表存在<br>";
        
        // 检查表结构
        $check_columns_sql = "SHOW COLUMNS FROM ".$tableprefix."website_group";
        $columns = $this->db->fetch_all($check_columns_sql);
        
        $column_names = array();
        foreach ($columns as $column) {
            $column_names[] = $column['Field'];
        }
        
        $required_columns = array('id', 'webdomain', 'content', 'dateline', 'domain_type', 'parent_domain', 'subdomain_prefix', 'is_active', 'sort_order');
        $missing_columns = array();
        
        foreach ($required_columns as $column) {
            if (in_array($column, $column_names)) {
                echo "✅ 字段存在: {$column}<br>";
            } else {
                echo "❌ 字段缺失: {$column}<br>";
                $missing_columns[] = $column;
            }
        }
        
        if (empty($missing_columns)) {
            echo "<p style='color: green;'><strong>✅ 所有必需字段都存在，插件已完全升级</strong></p>";
        } else {
            echo "<p style='color: orange;'><strong>⚠️ 缺少字段，需要升级</strong></p>";
        }
        
    } else {
        echo "❌ 站群表不存在，需要安装插件<br>";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "<br>";
}

// 测试4: 检查常量定义
echo "<h3>测试4: 重要常量检查</h3>";
$constants = array('ROOT_PATH', 'APP_PATH', 'PLUGIN_PATH', 'FRAMEWORK_PATH');
foreach ($constants as $constant) {
    if (defined($constant)) {
        echo "✅ {$constant}: " . constant($constant) . "<br>";
    } else {
        echo "❌ {$constant}: 未定义<br>";
    }
}

// 测试5: 检查类是否可以实例化
echo "<h3>测试5: 类实例化检查</h3>";
try {
    if (class_exists('website_group_model')) {
        echo "✅ website_group_model 类存在<br>";
    } else {
        echo "❌ website_group_model 类不存在<br>";
    }
    
    if (class_exists('le_website_group_control')) {
        echo "✅ le_website_group_control 类存在<br>";
    } else {
        echo "❌ le_website_group_control 类不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ 类检查失败: " . $e->getMessage() . "<br>";
}

echo "<br><h3>🎯 测试总结</h3>";
echo "<p>如果上述测试都显示 ✅，说明修复成功。如果有 ❌ 或 ⚠️，请检查相应的问题。</p>";

echo "<br><div style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>";
echo "<h4>📋 下一步操作建议：</h4>";
echo "<ol>";
echo "<li>如果所有测试通过，可以正常使用插件功能</li>";
echo "<li>如果缺少字段，请访问 <a href='index.php?le_website_group-upgrade'>升级页面</a></li>";
echo "<li>如果需要验证安装，请访问 <a href='index.php?le_website_group-verify'>验证页面</a></li>";
echo "<li>返回 <a href='index.php?le_website_group-index'>站群管理</a></li>";
echo "</ol>";
echo "</div>";
?>
