<?php
defined('ROOT_PATH') || exit;

/**
 * 站群插件升级脚本
 * 用于从旧版本升级到支持二级域名的新版本
 */

// 获取数据库配置
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];

echo "<h2>🔄 站群插件升级</h2>";
echo "<p>正在检查数据库结构并进行必要的升级...</p>";

// 检查表是否存在
$check_table_sql = "SHOW TABLES LIKE '".$tableprefix."website_group'";
$table_exists = $this->db->fetch_first($check_table_sql);

if (!$table_exists) {
    echo "❌ 站群表不存在，请先安装插件<br>";
    echo "<a href='index.php?le_website_group-index' class='layui-btn'>返回管理</a>";
    exit;
}

// 检查新字段是否存在
$check_columns_sql = "SHOW COLUMNS FROM ".$tableprefix."website_group";
$columns = $this->db->fetch_all($check_columns_sql);

$existing_columns = array();
foreach ($columns as $column) {
    $existing_columns[] = $column['Field'];
}

$upgrade_needed = false;
$new_columns = array(
    'domain_type' => "enum('main','subdomain') NOT NULL DEFAULT 'main' COMMENT '域名类型：main=主域名，subdomain=二级域名'",
    'parent_domain' => "varchar(80) NOT NULL DEFAULT '' COMMENT '父域名（用于二级域名）'",
    'subdomain_prefix' => "varchar(50) NOT NULL DEFAULT '' COMMENT '二级域名前缀'",
    'is_active' => "tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用'",
    'sort_order' => "int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序'"
);

// 添加缺失的字段
foreach ($new_columns as $column_name => $column_definition) {
    if (!in_array($column_name, $existing_columns)) {
        $alter_sql = "ALTER TABLE ".$tableprefix."website_group ADD COLUMN ".$column_name." ".$column_definition;
        $result = $this->db->query($alter_sql);
        
        if ($result) {
            echo "✅ 添加字段 {$column_name} 成功<br>";
            $upgrade_needed = true;
        } else {
            echo "❌ 添加字段 {$column_name} 失败<br>";
        }
    } else {
        echo "ℹ️ 字段 {$column_name} 已存在<br>";
    }
}

// 添加新的索引
$new_indexes = array(
    'domain_type' => 'domain_type',
    'parent_domain' => 'parent_domain', 
    'is_active' => 'is_active',
    'sort_order' => 'sort_order'
);

foreach ($new_indexes as $index_name => $index_column) {
    // 检查索引是否存在
    $check_index_sql = "SHOW INDEX FROM ".$tableprefix."website_group WHERE Key_name = '".$index_name."'";
    $index_exists = $this->db->fetch_first($check_index_sql);
    
    if (!$index_exists) {
        $create_index_sql = "ALTER TABLE ".$tableprefix."website_group ADD KEY ".$index_name." (".$index_column.")";
        $result = $this->db->query($create_index_sql);
        
        if ($result) {
            echo "✅ 添加索引 {$index_name} 成功<br>";
            $upgrade_needed = true;
        } else {
            echo "❌ 添加索引 {$index_name} 失败<br>";
        }
    } else {
        echo "ℹ️ 索引 {$index_name} 已存在<br>";
    }
}

// 更新现有记录
if ($upgrade_needed) {
    // 将现有记录标记为主域名
    $update_sql = "UPDATE ".$tableprefix."website_group SET domain_type = 'main', is_active = 1, sort_order = 0 WHERE domain_type = '' OR domain_type IS NULL";
    $this->db->query($update_sql);
    echo "✅ 更新现有记录为主域名类型<br>";
    
    // 更新现有记录的配置，添加二级域名支持
    $sites = $this->db->fetch_all("SELECT id, webdomain, content FROM ".$tableprefix."website_group WHERE domain_type = 'main'");
    foreach ($sites as $site) {
        $content = json_decode($site['content'], true);
        if (!isset($content['allow_subdomain'])) {
            $content['allow_subdomain'] = '1';
            $content['subdomain_template'] = 'default';
            $content['subdomain_auto_create'] = '0';
            $content['subdomain_pattern'] = '{prefix}.' . $site['webdomain'];
            
            $update_content_sql = "UPDATE ".$tableprefix."website_group SET content = '".json_encode($content)."' WHERE id = ".$site['id'];
            $this->db->query($update_content_sql);
        }
    }
    echo "✅ 更新站点配置，添加二级域名支持<br>";
}

// 升级完成
if ($upgrade_needed) {
    echo "<br><div class='layui-card'>";
    echo "<div class='layui-card-header'><h3>🎉 升级完成！</h3></div>";
    echo "<div class='layui-card-body'>";
    echo "<p>站群插件已成功升级到支持二级域名的版本。</p>";
    echo "<p><strong>新功能包括：</strong></p>";
    echo "<ul>";
    echo "<li>✅ 支持二级域名绑定和管理</li>";
    echo "<li>✅ 域名类型管理（主域名/二级域名）</li>";
    echo "<li>✅ 二级域名自动创建功能</li>";
    echo "<li>✅ 域名状态管理和排序</li>";
    echo "<li>✅ 完善的域名解析逻辑</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<br><div class='layui-card'>";
    echo "<div class='layui-card-header'><h3>ℹ️ 无需升级</h3></div>";
    echo "<div class='layui-card-body'>";
    echo "<p>您的站群插件已经是最新版本，支持所有功能。</p>";
    echo "</div>";
    echo "</div>";
}

echo "<br><a href='index.php?le_website_group-index' class='layui-btn layui-btn-normal'>返回站群管理</a>";
?>
