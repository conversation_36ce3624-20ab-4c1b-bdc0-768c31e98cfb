{inc:header.htm}

<div class="layui-card">
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?le_website_group-{$_GET['action']}-ajax-1" method="post">
			<input name="id" type="hidden" value="{$data[id]}" />
			<div class="layui-form-item">
				<label class="layui-form-label required">网站域名</label>
				<div class="layui-input-inline">
					<input type="text" name="webdomain" value="{$data[webdomain]}" autocomplete="off" placeholder="网站域名" class="layui-input" lay-verify="required" />
				</div>
				<div class="layui-form-mid layui-word-aux">xx.com 不需要前面的www</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label required">网站名称</label>
				<div class="layui-input-inline">
					<input type="text" name="info[webname]" value="{$data[webname]}" autocomplete="off" placeholder="网站名称" class="layui-input" lay-verify="required" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">SEO标题</label>
				<div class="layui-input-block">
					<input type="text" name="info[seo_title]" value="{$data[seo_title]}" autocomplete="off" placeholder="SEO标题" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">SEO关键词</label>
				<div class="layui-input-block">
					<input type="text" name="info[seo_keywords]" value="{$data[seo_keywords]}" autocomplete="off" placeholder="SEO关键词" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">SEO描述</label>
				<div class="layui-input-block">
					<input type="text" name="info[seo_description]" value="{$data[seo_description]}" autocomplete="off" placeholder="SEO描述" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">PC模板</label>
				<div class="layui-input-inline">
					<input type="text" name="info[theme]" value="{$data[theme]}" autocomplete="off" placeholder="网站电脑端模板文件夹名" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">不同域名，可以使用不同的模板，为空表示用后台启用的</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">WAP模板</label>
				<div class="layui-input-inline">
					<input type="text" name="info[theme_mobile]" value="{$data[theme_mobile]}" autocomplete="off" placeholder="网站移动端模板文件夹名" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">不同域名，可以使用不同的模板，为空表示用后台启用的</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">伪原创</label>
				<div class="layui-input-inline">
					<input type="text" name="info[theme_bj]" value="{$data[theme_bj]}" autocomplete="off" placeholder="模板伪原创前缀标识" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">模板伪原创前缀标识，需要安装启用模板伪原创插件，需要设置自己对应的PC和WAP模板，不可共用</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">内容URL</label>
				<div class="layui-input-inline">
					{$link_show_type}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">备案号</label>
				<div class="layui-input-inline">
					<input type="text" name="info[beian]" value="{$data[beian]}" autocomplete="off" placeholder="网站备案号" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">Logo名</label>
				<div class="layui-input-inline">
					<input type="text" name="info[logoname]" value="{$data[logoname]}" autocomplete="off" placeholder="Logo名" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">示例：logo.png，需要模板支持</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">版权信息</label>
				<div class="layui-input-block">
					<textarea name="info[copyright]" placeholder="版权信息" class="layui-textarea">{$data[copyright]}</textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">统计代码</label>
				<div class="layui-input-block">
					<textarea name="info[tongji]" class="layui-textarea" placeholder="统计代码">{$data[tongji]}</textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">允许二级域名</label>
				<div class="layui-input-block">
					<input type="radio" name="info[allow_subdomain]" value="1" title="是" {if:$data[allow_subdomain]==1}checked{/if}>
					<input type="radio" name="info[allow_subdomain]" value="0" title="否" {if:$data[allow_subdomain]==0}checked{/if}>
				</div>
				<div class="layui-form-mid layui-word-aux">是否允许此域名创建二级域名</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">二级域名模板</label>
				<div class="layui-input-inline">
					<input type="text" name="info[subdomain_template]" value="{$data[subdomain_template]}" autocomplete="off" placeholder="二级域名默认模板" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">二级域名使用的默认模板文件夹名</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">二级域名模式</label>
				<div class="layui-input-inline">
					<input type="text" name="info[subdomain_pattern]" value="{$data[subdomain_pattern]}" autocomplete="off" placeholder="{prefix}.example.com" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">二级域名格式，{prefix}为前缀占位符</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">自动创建二级域名</label>
				<div class="layui-input-block">
					<input type="radio" name="info[subdomain_auto_create]" value="1" title="是" {if:$data[subdomain_auto_create]==1}checked{/if}>
					<input type="radio" name="info[subdomain_auto_create]" value="0" title="否" {if:$data[subdomain_auto_create]==0}checked{/if}>
				</div>
				<div class="layui-form-mid layui-word-aux">是否自动创建访问的二级域名</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">关闭网站</label>
				<div class="layui-input-block">
					<input type="radio" name="info[close_website]" value="1" title="是" {if:$data[close_website]==1}checked{/if}>
					<input type="radio" name="info[close_website]" value="0" title="否" {if:$data[close_website]==0}checked{/if}>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
	layui.use(['form','layer', 'miniTab'], function () {
		var layer = layui.layer, form = layui.form, miniTab = layui.miniTab;

		//监听提交
		form.on('submit(form)', function () {
			adminAjax.postform('#form',function (data) {
				var json = toJson(data);
				if( json.err == 0 ){
					var icon = 1;
				}else{
					var icon = 5;
				}
				layer.msg(json.msg, {icon: icon});
				if(json.err==0) {
					setTimeout(function(){
						miniTab.reloadIframe('index.php?le_website_group-index');
						miniTab.deleteCurrentByIframe();
					}, 1500);
				}
			});
			return false;
		});
	});
</script>
</body>
</html>
