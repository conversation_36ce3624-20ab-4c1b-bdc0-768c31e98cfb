<?php
defined('ROOT_PATH') || exit;

// 获取数据库配置
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];

// 创建站群表（支持二级域名）
$sql = "CREATE TABLE IF NOT EXISTS ".$tableprefix."website_group (
  id int(10) unsigned NOT NULL AUTO_INCREMENT,
  webdomain varchar(80) NOT NULL DEFAULT '' COMMENT '域名',
  content mediumtext NOT NULL COMMENT '站点设置信息',
  dateline int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  domain_type enum('main','subdomain') NOT NULL DEFAULT 'main' COMMENT '域名类型：main=主域名，subdomain=二级域名',
  parent_domain varchar(80) NOT NULL DEFAULT '' COMMENT '父域名（用于二级域名）',
  subdomain_prefix varchar(50) NOT NULL DEFAULT '' COMMENT '二级域名前缀',
  is_active tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  sort_order int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (id),
  UNIQUE KEY webdomain (webdomain),
  KEY domain_type (domain_type),
  KEY parent_domain (parent_domain),
  KEY is_active (is_active),
  KEY sort_order (sort_order)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='站群表';";

$result = $this->db->query($sql);

if ($result) {
    // 插入默认的主域名配置
    $main_domain = $_SERVER['HTTP_HOST'];
    $default_config = array(
        'webname' => '主站',
        'weburl' => 'http://' . $main_domain . '/',
        'webdesc' => '主站描述',
        'webkey' => '主站,关键词',
        'allow_subdomain' => '1', // 允许二级域名
        'subdomain_template' => 'default', // 二级域名默认模板
        'subdomain_auto_create' => '0', // 是否自动创建二级域名
        'subdomain_pattern' => '{prefix}.' . $main_domain // 二级域名模式
    );

    // 检查是否已存在主域名配置
    $check_sql = "SELECT id FROM ".$tableprefix."website_group WHERE webdomain = '".$main_domain."'";
    $exists = $this->db->fetch_first($check_sql);

    if (!$exists) {
        $insert_sql = "INSERT INTO ".$tableprefix."website_group
                      (webdomain, content, dateline, domain_type, is_active, sort_order)
                      VALUES ('".$main_domain."', '".json_encode($default_config)."', ".time().", 'main', 1, 0)";
        $this->db->query($insert_sql);
    }
}
