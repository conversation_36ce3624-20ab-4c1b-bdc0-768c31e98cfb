<?php

defined('ROOT_PATH') or exit;

class le_website_group_control extends admin_control {
    public $url_arr = array(
                0=>'跟随系统',
                1=>'数字型',
                2=>'推荐型',
                3=>'别名型',
                4=>'加密型',
                5=>'ID型',
                6=>'别名组合型',
                7=>'灵活型',
                8=>'HashID型',
            );

    public function __construct() {
        // 确保F_APP_NAME常量存在
        if (!defined('F_APP_NAME')) {
            define('F_APP_NAME', 'lecms');
        }
        parent::__construct();
        $this->website_group = new website_group_model();
    }

	// 管理
	public function index() {
		$this->display();
	}

    //获取列表
    public function get_list(){
        //分页
        $page = isset( $_REQUEST['page'] ) ? intval($_REQUEST['page']) : 1;
        $pagenum = isset( $_REQUEST['limit'] ) ? intval($_REQUEST['limit']) : 15;

        //获取查询条件
        $keyword = isset( $_REQUEST['keyword'] ) ? trim($_REQUEST['keyword']) : '';
        if($keyword) {
            $keyword = urldecode($keyword);
            $keyword = safe_str($keyword);
        }

        //组合查询条件
        $where= array();
        if( $keyword ){
            $where['webdomain'] = array('LIKE'=>$keyword);
        }

        //数据量
        if( $where ){
            $total = $this->website_group->find_count($where);
        }else{
            $total = $this->website_group->count();
        }

        //页数
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, $page));

        // 获取列表
        $data_arr = array();
        $cms_arr = $this->website_group->list_arr($where, 'id', -1, ($page-1)*$pagenum, $pagenum, $total);
        foreach($cms_arr as &$v) {
            $v['date'] = date('Y-m-d H:i:s', $v['dateline']);
            $data_arr[] = $v;   //排序需要索引从0开始
        }
        unset($cms_arr);
        //组合数据 输出到页面
        $arr = array(
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data_arr,
        );
        exit( json_encode($arr) );
    }


    // 发布
    public function add() {
        if(empty($_POST)) {

            $data = array('link_show_type'=>0, 'close_website'=>0);
            $this->assign('data', $data);

            $link_show_type = form::layui_loop('select', 'info[link_show_type]', $this->url_arr, $data['link_show_type']);
            $this->assign('link_show_type', $link_show_type);

            $this->display('le_website_group_set.htm');
        }else{
            $webdomain = trim(strip_tags(R('webdomain', 'P')));
            $info = R('info', 'P');

            empty($webdomain) && E(1, '网站域名不能为空');

            if($this->website_group->find_fetch_key(array('webdomain'=> $webdomain))) {
                E(1, '网站域名已经存在啦');
            }

            // 写入内容表
            $data = array(
                'webdomain' => $webdomain,
                'dateline' => $_ENV['_time'],
                'content' => _json_encode($info),
            );
            $id = $this->website_group->create($data);
            if(!$id) {
                E(1, lang('add_failed'));
            }
            E(0, lang('add_successfully'));
        }
    }

    //批量添加
    public function batch_add(){
        if(empty($_POST)) {
            $this->display('le_website_group_batch_add.htm');
        }else{
            $content = trim(strip_tags(R('content', 'P')));
            empty($content) && E(1, '不能为空');

            $content_arr = explode(PHP_EOL, $content);
            $succ = 0;
            foreach ($content_arr as $v){
                $arr = explode('$', $v);
                if(count($arr) < 2){
                    continue;
                }

                $webdomain = isset($arr[0]) ? $arr[0] : '';
                if($this->website_group->find_fetch_key(array('webdomain'=> $webdomain))) {
                    continue;
                }

                $info = array(
                    'webname'=> isset($arr[1]) ? $arr[1] : '',
                    'seo_title'=> isset($arr[2]) ? $arr[2] : '',
                    'seo_keywords'=> isset($arr[3]) ? $arr[3] : '',
                    'seo_description'=> isset($arr[4]) ? $arr[4] : '',
                    'theme'=> isset($arr[5]) ? $arr[5] : '',
                    'theme_bj'=> isset($arr[6]) ? $arr[6] : '',
                    'link_show_type'=> isset($arr[7]) ? (int)$arr[7] : 0,
                    'beian'=> isset($arr[8]) ? $arr[8] : '',
                );

                $data = array(
                    'webdomain'=>$webdomain,
                    'dateline' => $_ENV['_time'],
                    'content'=>_json_encode($info),
                );
                $id = $this->website_group->create($data);
                if($id){
                    $succ++;
                }
            }

            E(0, lang('add_successfully'));
        }
    }

    // 编辑
    public function edit() {
        if(empty($_POST)) {
            $id = intval(R('id'));
            $data = $this->website_group->get($id);
            if(empty($data)) $this->message(0, lang('data_no_exists'), -1);

            $arr = _json_decode($data['content']);
            $data = array_merge($data, $arr);

            $this->assign('data', $data);

            $link_show_type = form::layui_loop('select', 'info[link_show_type]', $this->url_arr, $data['link_show_type']);
            $this->assign('link_show_type', $link_show_type);

            $this->display('le_website_group_set.htm');
        }else{
            $id = intval(R('id', 'P'));
            $webdomain = trim(strip_tags(R('webdomain', 'P')));
            $info = R('info', 'P');

            empty($id) && E(1, lang('data_error'));
            empty($webdomain) && E(1, '网站域名不能为空');

            $olddata = $this->website_group->get($id);
            if(empty($olddata)) E(1, lang('data_no_exists'));

            if($olddata['webdomain'] != $webdomain && $this->website_group->find_fetch_key(array('webdomain'=> $webdomain))) {
                E(1, '网站域名已经存在啦');
            }

            // 写入内容表
            $data = array(
                'id' => $id,
                'webdomain' => $webdomain,
                'content' => _json_encode($info),
            );
            if(!$this->website_group->update($data)) {
                E(1, lang('edit_failed'));
            }

            $cache_key = 'website_group_'.substr($webdomain, 0, strpos($webdomain, '.'));
            $this->runtime->delete($cache_key);

            E(0, lang('edit_successfully'));
        }
    }

    // 删除
    public function del() {
        $id = (int) R('id', 'P');
        empty($id) && E(1, lang('data_error'));

        $olddata = $this->website_group->get($id);
        empty($olddata) && E(1, lang('data_error'));

        $res = $this->website_group->delete($id);
        if(!$res) {
            E(1, lang('delete_failed'));
        }else{
            //删除缓存里面的
            $cache_key = 'website_group_'.substr($olddata['webdomain'], 0, strpos($olddata['webdomain'], '.'));
            $this->runtime->xdelete($cache_key);

            E(0, lang('delete_successfully'));
        }
    }

    // 批量删除
    public function batch_del() {
        $id_arr = R('id_arr', 'P');
        if(!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach($id_arr as $id) {
                $res = $this->website_group->delete($id);
                if(!$res) {
                    $err_num++;
                }else{
                    //删除缓存里面的
                    $olddata = $this->website_group->get($id);
                    if($olddata){
                        $cache_key = 'website_group_'.substr($olddata['webdomain'], 0, strpos($olddata['webdomain'], '.'));
                        $this->runtime->xdelete($cache_key);
                    }
                }
            }

            if($err_num) {
                E(1, $err_num.lang('num_del_failed'));
            }else{
                E(0, lang('delete_successfully'));
            }
        }else{
            E(1, lang('data_error'));
        }
    }

    // 升级插件
    public function upgrade() {
        $upgrade_file = PLUGIN_PATH . 'le_website_group/upgrade.php';
        if (file_exists($upgrade_file)) {
            include $upgrade_file;
        } else {
            echo "<h2>❌ 升级失败</h2>";
            echo "<p>升级文件不存在：{$upgrade_file}</p>";
            echo "<a href='index.php?le_website_group-index' class='layui-btn'>返回管理</a>";
        }
    }

    // 检查是否需要升级
    public function check_upgrade() {
        $tableprefix = $_ENV['_config']['db']['master']['tablepre'];

        // 检查新字段是否存在
        $check_columns_sql = "SHOW COLUMNS FROM ".$tableprefix."website_group";
        $columns = $this->db->fetch_all($check_columns_sql);

        $existing_columns = array();
        foreach ($columns as $column) {
            $existing_columns[] = $column['Field'];
        }

        $required_columns = array('domain_type', 'parent_domain', 'subdomain_prefix', 'is_active', 'sort_order');
        $needs_upgrade = false;

        foreach ($required_columns as $column) {
            if (!in_array($column, $existing_columns)) {
                $needs_upgrade = true;
                break;
            }
        }

        header('Content-Type: application/json');
        echo json_encode(array('needs_upgrade' => $needs_upgrade));
        exit;
    }

    // 验证安装
    public function verify() {
        $verify_file = PLUGIN_PATH . 'le_website_group/verify_installation.php';
        if (file_exists($verify_file)) {
            include $verify_file;
        } else {
            echo "<h2>❌ 验证失败</h2>";
            echo "<p>验证文件不存在：{$verify_file}</p>";
            echo "<a href='index.php?le_website_group-index' class='layui-btn'>返回管理</a>";
        }
    }

    // 测试修复
    public function test_fixes() {
        $test_file = PLUGIN_PATH . 'le_website_group/test_fixes.php';
        if (file_exists($test_file)) {
            include $test_file;
        } else {
            echo "<h2>❌ 测试失败</h2>";
            echo "<p>测试文件不存在：{$test_file}</p>";
            echo "<a href='index.php?le_website_group-index' class='layui-btn'>返回管理</a>";
        }
    }
}
