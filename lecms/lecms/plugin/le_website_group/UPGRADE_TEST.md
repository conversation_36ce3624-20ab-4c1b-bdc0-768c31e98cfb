# LECMS站群插件升级测试文档

## 测试目标
验证站群插件的新安装和升级功能是否正常工作，确保：
1. 新站安装插件时直接支持二级域名
2. 已有数据的站点能够安全升级
3. 升级过程不会导致数据丢失

## 测试环境要求
- LECMS 3.0.3 或更高版本
- MySQL/MariaDB 数据库
- PHP 7.0 或更高版本

## 测试场景

### 场景1：全新安装测试
**目标**: 验证新站安装插件时直接支持二级域名功能

**测试步骤**:
1. 确保系统中没有安装过站群插件
2. 进入后台 -> 插件管理
3. 找到站群插件，点击"安装"
4. 检查数据库表结构是否包含新字段
5. 检查是否自动创建了主域名配置

**预期结果**:
- 数据库表包含所有字段：id, webdomain, content, dateline, domain_type, parent_domain, subdomain_prefix, is_active, sort_order
- 自动插入主域名记录，domain_type='main'
- 主域名配置包含二级域名支持选项

**验证SQL**:
```sql
-- 检查表结构
SHOW COLUMNS FROM pre_website_group;

-- 检查主域名记录
SELECT * FROM pre_website_group WHERE domain_type = 'main';
```

### 场景2：旧版本升级测试
**目标**: 验证已有数据的站点能够安全升级

**测试步骤**:
1. 模拟旧版本数据（只有基础字段的记录）
2. 进入站群管理页面
3. 检查是否显示升级按钮
4. 点击升级按钮，执行升级
5. 验证升级后的数据完整性

**预期结果**:
- 升级按钮正确显示
- 新字段成功添加到数据库
- 现有记录被标记为主域名类型
- 现有记录的配置被更新，添加二级域名支持
- 原有数据保持完整

**验证SQL**:
```sql
-- 升级前检查（应该缺少新字段）
SHOW COLUMNS FROM pre_website_group;

-- 升级后检查（应该包含所有字段）
SHOW COLUMNS FROM pre_website_group;

-- 检查现有记录是否正确更新
SELECT id, webdomain, domain_type, is_active, content FROM pre_website_group;
```

### 场景3：升级幂等性测试
**目标**: 验证重复执行升级不会造成问题

**测试步骤**:
1. 在已升级的系统上再次执行升级
2. 检查是否显示"无需升级"信息
3. 验证数据没有被重复修改

**预期结果**:
- 显示"无需升级"信息
- 数据库结构和数据保持不变
- 不会产生错误或异常

## 安全检查清单

### 数据完整性检查
- [ ] 升级前后记录数量一致
- [ ] 升级前后webdomain字段值不变
- [ ] 升级前后content字段的原有内容保持完整
- [ ] 新增字段有正确的默认值

### 功能完整性检查
- [ ] 站群管理页面正常显示
- [ ] 添加/编辑站点功能正常
- [ ] 域名解析功能正常
- [ ] 缓存清理功能正常

### 错误处理检查
- [ ] 数据库连接失败时的错误处理
- [ ] 权限不足时的错误处理
- [ ] 重复升级时的处理

## 回滚方案

如果升级出现问题，可以使用以下SQL回滚：

```sql
-- 备份当前数据（升级前执行）
CREATE TABLE pre_website_group_backup AS SELECT * FROM pre_website_group;

-- 回滚到升级前状态（如果需要）
DROP TABLE pre_website_group;
CREATE TABLE pre_website_group (
  id int(10) unsigned NOT NULL AUTO_INCREMENT,
  webdomain varchar(80) NOT NULL DEFAULT '' COMMENT '域名',
  content mediumtext NOT NULL COMMENT '站点设置信息',
  dateline int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (id),
  UNIQUE KEY (webdomain)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='站群表';

INSERT INTO pre_website_group (id, webdomain, content, dateline)
SELECT id, webdomain, content, dateline FROM pre_website_group_backup;
```

## 测试报告模板

### 测试执行记录
- 测试时间：
- 测试环境：
- LECMS版本：
- PHP版本：
- 数据库版本：

### 测试结果
- [ ] 场景1：全新安装测试 - 通过/失败
- [ ] 场景2：旧版本升级测试 - 通过/失败  
- [ ] 场景3：升级幂等性测试 - 通过/失败

### 发现的问题
（记录测试过程中发现的任何问题）

### 建议
（对插件改进的建议）
