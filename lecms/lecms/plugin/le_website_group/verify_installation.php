<?php
/**
 * 站群插件安装验证脚本
 * 用于验证插件是否正确安装和升级
 */

defined('ROOT_PATH') || exit;

// 获取数据库配置
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];

echo "<h2>🔍 站群插件安装验证</h2>";
echo "<p>正在检查插件安装状态...</p>";

// 检查表是否存在
$check_table_sql = "SHOW TABLES LIKE '".$tableprefix."website_group'";
$table_exists = $this->db->fetch_first($check_table_sql);

if (!$table_exists) {
    echo "<div class='layui-card'>";
    echo "<div class='layui-card-header' style='background-color: #ff5722; color: white;'>";
    echo "<h3>❌ 验证失败</h3>";
    echo "</div>";
    echo "<div class='layui-card-body'>";
    echo "<p>站群表不存在，请先安装插件。</p>";
    echo "</div>";
    echo "</div>";
    echo "<br><a href='index.php?plugin-index' class='layui-btn'>返回插件管理</a>";
    exit;
}

// 检查表结构
$check_columns_sql = "SHOW COLUMNS FROM ".$tableprefix."website_group";
$columns = $this->db->fetch_all($check_columns_sql);

$existing_columns = array();
foreach ($columns as $column) {
    $existing_columns[] = $column['Field'];
}

// 必需的字段
$required_columns = array(
    'id' => '主键ID',
    'webdomain' => '域名',
    'content' => '站点设置信息',
    'dateline' => '添加时间',
    'domain_type' => '域名类型',
    'parent_domain' => '父域名',
    'subdomain_prefix' => '二级域名前缀',
    'is_active' => '是否启用',
    'sort_order' => '排序'
);

$missing_columns = array();
$existing_required_columns = array();

foreach ($required_columns as $column => $description) {
    if (in_array($column, $existing_columns)) {
        $existing_required_columns[] = $column;
    } else {
        $missing_columns[] = $column;
    }
}

// 检查索引
$check_indexes_sql = "SHOW INDEX FROM ".$tableprefix."website_group";
$indexes = $this->db->fetch_all($check_indexes_sql);

$existing_indexes = array();
foreach ($indexes as $index) {
    $existing_indexes[] = $index['Key_name'];
}

$required_indexes = array('PRIMARY', 'webdomain', 'domain_type', 'parent_domain', 'is_active', 'sort_order');
$missing_indexes = array();

foreach ($required_indexes as $index) {
    if (!in_array($index, $existing_indexes)) {
        $missing_indexes[] = $index;
    }
}

// 检查数据
$data_count = $this->db->fetch_first("SELECT COUNT(*) as count FROM ".$tableprefix."website_group");
$main_domain_count = $this->db->fetch_first("SELECT COUNT(*) as count FROM ".$tableprefix."website_group WHERE domain_type = 'main'");

// 显示验证结果
if (empty($missing_columns) && empty($missing_indexes)) {
    echo "<div class='layui-card'>";
    echo "<div class='layui-card-header' style='background-color: #5fb878; color: white;'>";
    echo "<h3>✅ 验证通过</h3>";
    echo "</div>";
    echo "<div class='layui-card-body'>";
    echo "<p>站群插件已正确安装，支持所有功能。</p>";
    echo "<p><strong>安装详情：</strong></p>";
    echo "<ul>";
    echo "<li>✅ 数据库表结构完整</li>";
    echo "<li>✅ 所有必需字段存在</li>";
    echo "<li>✅ 索引配置正确</li>";
    echo "<li>✅ 支持二级域名功能</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='layui-card'>";
    echo "<div class='layui-card-header' style='background-color: #ff9800; color: white;'>";
    echo "<h3>⚠️ 需要升级</h3>";
    echo "</div>";
    echo "<div class='layui-card-body'>";
    echo "<p>检测到插件需要升级以支持完整功能。</p>";
    
    if (!empty($missing_columns)) {
        echo "<p><strong>缺少字段：</strong></p>";
        echo "<ul>";
        foreach ($missing_columns as $column) {
            echo "<li>❌ {$column} - {$required_columns[$column]}</li>";
        }
        echo "</ul>";
    }
    
    if (!empty($missing_indexes)) {
        echo "<p><strong>缺少索引：</strong></p>";
        echo "<ul>";
        foreach ($missing_indexes as $index) {
            echo "<li>❌ {$index}</li>";
        }
        echo "</ul>";
    }
    
    echo "<p><a href='index.php?le_website_group-upgrade' class='layui-btn layui-btn-warm'>立即升级</a></p>";
    echo "</div>";
    echo "</div>";
}

// 显示详细信息
echo "<br><div class='layui-card'>";
echo "<div class='layui-card-header'>";
echo "<h3>📊 详细信息</h3>";
echo "</div>";
echo "<div class='layui-card-body'>";

echo "<p><strong>表结构信息：</strong></p>";
echo "<table class='layui-table'>";
echo "<thead><tr><th>字段名</th><th>类型</th><th>状态</th></tr></thead>";
echo "<tbody>";
foreach ($columns as $column) {
    $status = in_array($column['Field'], array_keys($required_columns)) ? 
              "<span style='color: #5fb878;'>✅ 必需</span>" : 
              "<span style='color: #999;'>ℹ️ 可选</span>";
    echo "<tr><td>{$column['Field']}</td><td>{$column['Type']}</td><td>{$status}</td></tr>";
}
echo "</tbody>";
echo "</table>";

echo "<p><strong>数据统计：</strong></p>";
echo "<ul>";
echo "<li>总站点数：{$data_count['count']}</li>";
echo "<li>主域名数：{$main_domain_count['count']}</li>";
echo "</ul>";

echo "</div>";
echo "</div>";

echo "<br><a href='index.php?le_website_group-index' class='layui-btn layui-btn-normal'>返回站群管理</a>";
echo " <a href='index.php?plugin-index' class='layui-btn'>返回插件管理</a>";
?>
