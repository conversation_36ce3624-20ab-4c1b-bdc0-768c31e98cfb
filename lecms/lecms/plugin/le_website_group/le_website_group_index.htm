{inc:header.htm}
<div class="layuimini-container">
	<div class="layuimini-main">
		<fieldset class="table-search-fieldset">
			<legend>{lang:search}</legend>
			<div>
				<form class="layui-form layui-form-pane">
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">域名</label>
							<div class="layui-input-inline">
								<input placeholder="域名" autocomplete="off" type="text" class="layui-input" name="keyword" />
							</div>
						</div>
						<div class="layui-inline">
							<button type="submit" class="layui-btn layui-btn-primary"  lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> {lang:search}</button>
						</div>
					</div>
				</form>
			</div>
		</fieldset>
		<script type="text/html" id="toolbar">
			<div class="layui-btn-container">
				<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="add">{lang:add}</button>
<!--				<button class="layui-btn layui-btn-sm" lay-event="batch_add">{lang:batch_add}</button>-->
				<button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete">{lang:batch_delete}</button>
				<button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="upgrade" id="upgrade-btn" style="display:none;">
					<i class="layui-icon layui-icon-upload"></i> 升级插件
				</button>
			</div>
		</script>
		<table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table>

		<script type="text/html" id="currentTableBar">
			<div class="layui-btn-group">
				<a class="layui-btn layui-btn-xs" lay-event="edit">{lang:edit}</a>
				<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">{lang:delete}</a>
			</div>
		</script>
	</div>
</div>

<script type="text/javascript">
	layui.use(['form','layer', 'table', 'miniTab'], function () {
		var layer = layui.layer, form = layui.form, table = layui.table, miniTab = layui.miniTab;

		table.render({
			elem: '#data-table',
			url: 'index.php?le_website_group-get_list-',
			height: 'full-145',
			toolbar: '#toolbar',
			defaultToolbar: ['filter', 'exports', 'print'],
			cellMinWidth: 50,
			escape: false,
			cols: [[
				{type: "checkbox", width: 50, fixed: 'left'},
				{field: 'webdomain', minwidth: 120, title: '域名'},
				{field: 'date', width: 175, title: '添加时间'},
				{title: '{lang:opt}', width: 105, toolbar: '#currentTableBar', align: "center"}
			]],
			limits: [10, 15, 20, 25, 50, 100],
			limit: 15,
			page: true
		});
		// 监听搜索操作
		form.on('submit(data-search-btn)', function (data) {
			//执行搜索重载
			table.reload('data-table', {
				page: {
					curr: 1
				}
				, where: {
					keyword: data.field.keyword
				}
			}, 'data');

			return false;
		});
		/**
		 * toolbar监听事件 table列表 头部的操作
		 */
		table.on('toolbar(data-table-filter)', function (obj) {
			if (obj.event === 'delete') {  // 监听删除操作
				var checkStatus = table.checkStatus('data-table')
						, data = checkStatus.data;
				var len = data.length;
				if(len == 0){
					layer.msg('{lang:select_data}',{icon:5});
					return false;
				}else{
					layer.confirm('{lang:delete_confirm}', function () {
						var id_arr = [];
						for (var i in data) {
							id_arr[i] = data[i]['id'];
						}
						adminAjax.postd("index.php?le_website_group-batch_del-ajax-1", {"id_arr": id_arr});
					});
				}
			}else if (obj.event === 'add') {
				miniTab.openNewTabByIframe({
					href:"index.php?le_website_group-add",
					title:"添加站点",
				});
			}else if (obj.event === 'upgrade') {
				layer.confirm('确定要升级站群插件吗？升级后将支持二级域名功能。', {
					btn: ['确定升级','取消']
				}, function(){
					miniTab.openNewTabByIframe({
						href:"index.php?le_website_group-upgrade",
						title:"升级站群插件",
					});
				});
			}else if (obj.event === 'batch_add') {
				miniTab.openNewTabByIframe({
					href:"index.php?le_website_group-batch_add",
					title:"批量添加站点",
				});
			}
		});

		//监听每一行的操作
		table.on('tool(data-table-filter)', function (obj) {
			var data = obj.data;

			if (obj.event === 'delete') {
				layer.confirm('{lang:delete_confirm}', function () {
					adminAjax.postd("index.php?le_website_group-del-ajax-1", {"id":data.id});
				});
			}else if (obj.event === 'edit') {
				miniTab.openNewTabByIframe({
					href:"index.php?le_website_group-edit-id-"+data.id,
					title:"编辑站点",
				});
			}
		});
	});

	// 检查是否需要升级
	$.get('index.php?le_website_group-check_upgrade', function(res){
		if(res.needs_upgrade) {
			$('#upgrade-btn').show();
			layer.msg('检测到站群插件有新版本，建议升级以支持二级域名功能', {
				icon: 6,
				time: 5000
			});
		}
	}, 'json');
</script>
</body>
</html>
