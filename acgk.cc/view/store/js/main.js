;var niceConfig = {
    "laycenter": 0,
    "infinite_load": "加载更多",
    "infinite_loading": "<i class=\"fa fa-spinner fa-spin\"><\/i> 加载中...",
    "color": "#4a86e8",
    "auto_open_tag_tab": 2,
    "notice": {
        "status": 1,
        "title": "支持赞助本站的理由",
        "content": "<h3 style=\"color: #fff;font-size:20px;text-align: left;\">一、十年仿站经验，极速仿站！！<\/h3><br>专业仿站，专注前端，多快好省，想您所想。<br><br> <h3 style=\"color: #fff;font-size:20px;text-align: left;\">二、专业定制，满足个性需求！！<\/h3><br> 在实现源站功效的基础上支持再开发和优化。<br><br> <h3 style=\"color: #fff;font-size:20px;text-align: left;\">三、免登录无人值守购买主题！！<\/h3><br> 本站支持支付宝当面付，无需注册会员扫码付款即可下载心仪主题。<br><br> <h3 style=\"color: #fff;font-size:20px;text-align: left;\">四、永久VIP\/SVIP福利多多！！<\/h3><br> 开通SVIP会员，全站精品主题资源第一时间更新任由下载。<br><br> <h3 style=\"color: #fff;font-size:20px;\/* text-align: left; *\/\">感谢各位对LECMS之家的信任！本站提供的资源均来自公共网络渠道，只提供研究学习用。 严禁用于非法网站搭建，否则后果自负。<\/h3>",
        "background": "#00bbff",
        "crc": 2200998624
    },
    "tcaptcha": 0
};
!function(){
    var body = jQuery('body');
    var navText = ['<i class="mdi mdi-chevron-left"></i>', '<i class="mdi mdi-chevron-right"></i>'];
    var iconspin = '<i class="fa fa-spinner fa-spin"></i> ';
    var iconcheck = '<i class="fa fa-check"></i> ';
    var iconwarning = '<i class="fa fa-warning "></i> ';
    window.lazySizesConfig = window.lazySizesConfig || {};
    window.lazySizesConfig.loadHidden = false;
    jQuery(function() {
        'use strict';
        offCanvas();
        sidebar();
        categoryBoxes();
        navlight();
        megaMenu();
		notify();//弹窗
		header();
    });
    jQuery(window).scroll(function() {
        'use strict';
        if (body.hasClass('navbar-sticky') || body.hasClass('navbar-sticky_transparent')) {
            window.requestAnimationFrame(navbar);
        }
    });
    document.addEventListener('lazyloaded', function(e) {
        var options = {
            disableParallax: /iPad|iPhone|iPod|Android/,
            disableVideo: /iPad|iPhone|iPod|Android/,
            speed: 0.1,
        };
        if (
            jQuery(e.target).parents('.hero').length ||
            jQuery(e.target).hasClass('hero')) {
            jQuery(e.target).jarallax(options);
        }
        if (
        (jQuery(e.target).parent().hasClass('module') && jQuery(e.target).parent().hasClass('parallax')) ||
            jQuery(e.target).parent().hasClass('entry-navigation')) {
            jQuery(e.target).parent().jarallax(options);
        }
    });
    // widget
    function navlight(){
        $('.nav-list li').each(function(k,v){
            if ($(v).find('a').attr('href') == location.href){
                $(v).addClass('current-menu-item');
            }
        })
    }
    function offCanvas() {
        'use strict';
        var burger = jQuery('.burger');
        var canvasClose = jQuery('.canvas-close');
        jQuery('.main-menu .nav-list').slicknav({
            label: '',
            prependTo: '.mobile-menu',
        });
        burger.on('click', function() {
            body.toggleClass('canvas-opened');
            body.addClass('canvas-visible');
            dimmer('open', 'medium');
        });
        canvasClose.on('click', function() {
            if (body.hasClass('canvas-opened')) {
                body.removeClass('canvas-opened');
                dimmer('close', 'medium');
            }
        });
        jQuery('.dimmer').on('click', function() {
            if (body.hasClass('canvas-opened')) {
                body.removeClass('canvas-opened');
                dimmer('close', 'medium');
            }
        });
        jQuery(document).keyup(function(e) {
            if (e.keyCode == 27 && body.hasClass('canvas-opened')) {
                body.removeClass('canvas-opened');
                dimmer('close', 'medium');
            }
        });
    }
    function sidebar() {
        'use strict';
        // 移动端自动将下载信息放文章末尾
        var this_max_width = $(window).width();
        if (this_max_width < 768) {
            $("aside .widget.widget-pay").insertAfter($("#pay-single-box"));
        } else {
            jQuery('.container .sidebar-column').theiaStickySidebar({
                additionalMarginTop: 20
            });
            jQuery('.user-profile .user-1sider').theiaStickySidebar({
                additionalMarginTop: 30
            });
        }
    }
    function categoryBoxes() {
      'use strict';
      jQuery('.category-boxes').owlCarousel({
        dots: false,
        margin: 30,
        nav: true,
        navSpeed: 500,
        navText: navText,
        responsive: {
          0: {
            items: 1,
          },
          768: {
            items: 2,
          },
          992: {
            items: 3,
          },
          1230: {
            items: 4,
          },
        },
      });
    }
    function dimmer(action, speed) {
        'use strict';
        var dimmer = jQuery('.dimmer');
        switch (action) {
            case 'open':
                dimmer.fadeIn(speed);
                break;
            case 'close':
                dimmer.fadeOut(speed);
                break;
        }
    }
    function megaMenu() {
        'use strict';
        var options = {
            items: 5,
            margin: 15,
        };
        var scroller = $('.rollbar')
        $(window).scroll(function() {
            var h = document.documentElement.scrollTop + document.body.scrollTop
            h > 200 ? scroller.fadeIn() : scroller.fadeOut();
        })
        $('[etap="to_top"]').on('click', function(){
        $('html,body').animate({
                scrollTop: 0
            }, 300)
        })
    }
    // 设置cookie + 公告弹窗
    function setCookie(cname,cvalue,exdays){
        var d = new Date();
        d.setTime(d.getTime()+(exdays*24*60*60*1000));
        var expires = "expires="+d.toGMTString();
        document.cookie = cname+"="+cvalue+"; "+expires;
    }
    // 获取cookie
    function getCookie(cname){
        var name = cname + "=";
        var ca = document.cookie.split(';');
        for(var i=0; i<ca.length; i++) {
            var c = ca[i].trim();
            if (c.indexOf(name)==0) { return c.substring(name.length,c.length); }
        }
        return "";
    }
    // 检查是否有该cookie
    function checkCookie(){
        var use=getCookie("notice_read");
        if (use != ""){ // 不为空处理
            document.getElementById('popup').style.display = 'none'
        }
        else {// 为空处理
            document.getElementById('popup').style.display = 'block'
        }
    }	
    //公告弹窗
    function notify() {
        'use strict';
        //点击按钮弹窗
        $('.note-open').on('click', function() {
            Swal.fire({
                html: '<div class="notify-content" ><h3>' + niceConfig.notice.title + '</h3><div>' + niceConfig.notice.content + '</div></div>',
                background: niceConfig.notice.background,
                showConfirmButton: false,
                width: 560,
                padding: '0',
                allowOutsideClick:true,
                showCloseButton: true,
            }).then((result) => {
                setCookie('notice_read', niceConfig.notice.crc,1)
            })
        })
		/*
		//进入弹窗
        $(function() {
            if (niceConfig.notice.status == 1 && setCookie("notice_read") != niceConfig.notice.crc) {
                $('.note-open').click();
            }
        });
		*/
    }	
    function header(){
        // if ($(window).width() < 990){
        //     return;
        // }
        var header = $('.site-header');
        header.addClass('site-header-headroom');
        $(window).scroll(function () {
            if($(window).scrollTop() == 0){
                header.css('position','absolute')
            }
        })
        var options = {
            offset : 80,
            onPin : function() {
                header.css('background','#4a86e8').css('position','fixed').css('opacity','.97');
            },
            onTop : function() {
                header.css('opacity','1')
            },
        };
        var myElement = document.querySelector(".site-header");
        var headroom  = new Headroom(myElement,options);
        headroom.init();
    }	
}();