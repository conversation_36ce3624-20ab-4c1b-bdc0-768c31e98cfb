{inc:header.htm} 
<section class="container"> 
<div class="content-wrap"> 
<style>
.panel{margin-bottom:17px;background-color:#fff;border:1px solid transparent;border-radius:3px;-webkit-box-shadow:0 1px 1px rgb(0 0 0 / 5%);box-shadow:0 1px 1px rgb(0 0 0 / 5%)}
.panel-default{border:none;padding:0 15px;-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none;-webkit-border-radius:2px;-webkit-background-clip:padding-box;-moz-border-radius:2px;-moz-background-clip:padding;border-radius:2px;background-clip:padding-box}
.panel-default > .panel-heading{position:relative;padding:15px 0;background:#fff;border-bottom:1px solid #f5f5f5}
.panel-default > .panel-heading .panel-title{font-size:16px}
.panel-default > .panel-heading .panel-title > i{display:none}
.panel-default > .panel-heading small{font-weight:normal;color:#999;font-size:13px}
.panel-default > .panel-heading .more{position:absolute;top:13px;right:0;display:block;color:#919191;-webkit-transition:all 0.3s ease;-moz-transition:all 0.3s ease;-o-transition:all 0.3s ease;transition:all 0.3s ease;font-weight:400;font-size:13px}
.panel-default > .panel-heading .more:hover{color:#616161;-webkit-transition:all 0.3s ease;-moz-transition:all 0.3s ease;-o-transition:all 0.3s ease;transition:all 0.3s ease}
.panel-default > .panel-heading div.more{top:17px}
.panel-default > .panel-heading .panel-bar{position:absolute;top:7px;right:0;display:block}
.panel-default > .panel-footer{padding:15px 0;background:none}
.panel-default > .panel-body{position:relative;padding:15px 0}
.panel-primary > .panel-heading{background-color:#46c37b;color:#fff}
.panel-primary > .panel-body{background:#fafafa;border-bottom-left-radius:2px;border-bottom-right-radius:2px}
.panel-gray{-webkit-box-shadow:0 2px 4px rgba(0,0,0,0.08);-moz-box-shadow:0 2px 4px rgba(0,0,0,0.08);box-shadow:0 2px 4px rgba(0,0,0,0.08)}
.panel-gray > .panel-heading{background-color:#f5f5f5;color:#919191}
.panel-gray > .panel-body{color:#919191;background:#fff;border-bottom-left-radius:4px;border-bottom-right-radius:4px}
.panel-page{padding:45px 50px 50px;min-height:500px}
.panel-page .panel-heading{background:transparent;border-bottom:none;margin:0 0 30px 0;padding:0}
.panel-page .panel-heading h2{font-size:25px;margin-top:0}
.special-headline{margin-top:15px}
.special-headline blockquote{border-radius:3px;padding:15px 17px;border:none;background:#f5f9ff;font-family:'Open Sans',sans-serif !important}
.special-headline blockquote:before{content:' '}
.special-headline blockquote h3{line-height:35px !important;margin-bottom:0px;margin-top:0 !important;font-size:16px !important;font-weight:400;color:#777}
.special-headline blockquote h3::before{content:"“";font-size:100px;display:block;float:left;line-height:1;height:35px;opacity:.4;padding-right:10px}
.special-banner{background-size:cover;background-attachment:scroll;background-position:center;height:300px;position:relative}
.special-title{position:absolute;bottom:0;left:0;width:100%;padding-top:10px;background:linear-gradient(-180deg,rgba(0,0,0,0.0),rgba(0,0,0,0.50))}
.special-title .label{margin-left:20px}
.special-title h1{font-size:22px;color:#fff;line-height:30px;padding-left:20px;margin-bottom:20px}
@media (max-width:767px){.special-banner{height:180px}
.special-headline blockquote h3{font-size:14px !important}
.special-headline blockquote h3::before{font-size:80px}
.article-list .media .media-body .article-title{font-size:1em}
.article-list .media .media-body .article-tag{display:none}
}
.article-list{padding:0;background:#fff}
.article-list .article-item{padding:20px 0;border-bottom:1px solid #efefef}
.article-list .article-item .content{margin-top:15px;color:#919191}
.article-list .gallery-article{margin-top:0}
.article-list .gallery-article .row{margin:0 -10px}
.article-list .gallery-article .article-title{margin-bottom:10px}
.article-list .gallery-article .article-title span.highlight{color:red}
.article-list .gallery-article .media .media-body{padding-left:0}
.article-list .gallery-article .media .media-body .article-tag{position:relative;margin-top:10px}
.article-list .article-title{margin:0;font-size:1.25em;line-height:1.45;color:#000}
.article-list .article-title a{color:#444;-webkit-transition:all 0.3s ease;-moz-transition:all 0.3s ease;-o-transition:all 0.3s ease;transition:all 0.3s ease}
.article-list .article-title a span.highlight{color:red}
.article-list .article-title a:hover{color:#007bff;-webkit-transition:all 0.3s ease;-moz-transition:all 0.3s ease;-o-transition:all 0.3s ease;transition:all 0.3s ease}
.article-list .article-title a .img-new{margin-left:2px;margin-bottom:2px;height:16px}
.article-list .article-intro{height:44px;line-height:22px;color:#828a92;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:2}
.article-list .media{color:#919191}
.article-list .media .media-body{padding-left:20px;line-height:25px}
.article-list .media .media-left{overflow:hidden;padding:5px 10px}
.article-list .media .media-left a{display:block;width:160px}
.article-list .media-body{position:relative}
.article-list .media-body .article-tag{display:block;clear:both;position:absolute;bottom:0;color:#aaa;font-size:13px}
.article-list .media-body .article-tag span{margin:0 8px}
.article-list .media-body .article-tag span a{color:#aaa}
.article-list .media-body .article-tag .pull-left{height:34px;line-height:34px;color:#919191}
.article-list .media-body .article-tag .pull-left a{color:#919191}
.article-list .pager{margin:40px 0 20px 0}
.media-left,.media-right,.media-body{display:table-cell;vertical-align:top}
.media-body{width:10000px}
.media,.media-body{zoom:1;overflow:hidden}
.img-zoom{overflow:hidden;display:inline-block}
.img-zoom img{-webkit-transition:all 0.3s;-moz-transition:all 0.3s;-o-transition:all 0.3s;transition:all 0.3s}
.img-zoom:hover img{-webkit-transform:scale(1.1);-moz-transform:scale(1.1);-o-transform:scale(1.1);-ms-transform:scale(1.1);transform:scale(1.1)}
.embed-responsive{position:relative;display:block;height:0;padding:0;overflow:hidden}
.embed-responsive img{position:absolute;object-fit:cover;width:100%;height:100%;border:0}
.embed-responsive-16by9{padding-bottom:56.25%}
.embed-responsive-4by3{padding-bottom:75%}
.embed-responsive-square{padding-bottom:100%}
</style>
{block:global_special_show showviews="1" showcate="1" dateformat="human_date"}
{if:isset($gdata[article]) && !empty($gdata[article])}	
<div class="panel panel-default"> 
      <div class="panel-body"> 
       <div class="special-banner" style="background-image: url('{$gdata[pic]}');"> 
        <div class="special-title"> 
         <div class="label label-primary">
           {$gdata[title]}
         </div> 
         <h1>{$gdata[content]}</h1> 
        </div> 
       </div> 
       <div class="special-headline"> 
        <blockquote> 
         <h3>{$gdata[intro]}</h3> 
        </blockquote> 
       </div> 
       <div class="special-body"> 
        <div class="article-list"> 
         <!-- S 专题文章列表 --> 		 
		 {loop:$gdata[article] $v}
         <article class="article-item"> 
          <div class="media"> 
           <div class="media-left"> 
            <a href="{$v[url]}" title="{$v[title]}" target="_blank"> 
             <div class="embed-responsive embed-responsive-4by3 img-zoom"> 
              <img src="{$v[pic]}" alt="{$v[subject]}" /> 
             </div> </a> 
           </div> 
           <div class="media-body"> 
            <h3 class="article-title"> <a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[subject]}</a> </h3> 
            <div class="article-intro">
              {$v[intro]}
            </div> 
            <div class="article-tag"> 
               <span itemprop="date">{$v[date]}</span> 
               <span class="hidden-xs" itemprop="likes" title="作者"> {$v[author]}</span> 
               <span class="hidden-xs" itemprop="comments"><a href="{$v[url]}#ctf_form" target="_blank" title="评论数"> {$v[comments]}</a> 评论</span> 
            </div> 
           </div> 
          </div> 
         </article> 
		 {/loop} 
         <!-- E 专题文章列表 --> 
        </div> 
       </div> 
       <div class="clearfix"></div> 
      </div> 
</div> 
{/if}
{/block}
</div>
</section> 
{inc:footer.htm} 