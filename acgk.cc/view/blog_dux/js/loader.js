var tbquirejs,tbquire,tbfine;!function(ba){function G(b){return"[object Function]"===K.call(b)}function H(b){return"[object Array]"===K.call(b)}function v(b,c){var d;if(b)for(d=0;d<b.length&&(!b[d]||!c(b[d],d,b));d+=1);}function T(b,c){var d;if(b)for(d=b.length-1;-1<d&&(!b[d]||!c(b[d],d,b));d-=1);}function t(b,c){return fa.call(b,c)}function m(b,c){return t(b,c)&&b[c]}function B(b,c){for(var d in b)if(t(b,d)&&c(b[d],d))break}function U(b,c,d,e){return c&&B(c,(function(c,g){!d&&t(b,g)||(!e||"object"!=typeof c||!c||H(c)||G(c)||c instanceof RegExp?b[g]=c:(b[g]||(b[g]={}),U(b[g],c,d,e)))})),b}function u(b,c){return function(){return c.apply(b,arguments)}}function ca(b){throw b}function da(b){if(!b)return b;var c=ba;return v(b.split("."),(function(b){c=c[b]})),c}function C(b,c,d,e){return(c=Error(c+b)).tbquireType=b,c.tbquireModules=e,d&&(c.originalError=d),c}function ga(b){function c(a,k,b){var f,l,c,d,e,g,i,p,k=k&&k.split("/"),h=j.map,n=h&&h["*"];if(a){for(l=(a=a.split("/")).length-1,j.nodeIdCompat&&Q.test(a[l])&&(a[l]=a[l].replace(Q,"")),"."===a[0].charAt(0)&&k&&(a=(l=k.slice(0,k.length-1)).concat(a)),l=a,c=0;c<l.length;c++)"."===(d=l[c])?(l.splice(c,1),c-=1):".."===d&&0!==c&&(1!=c||".."!==l[2])&&".."!==l[c-1]&&0<c&&(l.splice(c-1,2),c-=2);a=a.join("/")}if(b&&h&&(k||n)){c=(l=a.split("/")).length;a:for(;0<c;c-=1){if(e=l.slice(0,c).join("/"),k)for(d=k.length;0<d;d-=1)if((b=m(h,k.slice(0,d).join("/")))&&(b=m(b,e))){f=b,g=c;break a}!i&&n&&m(n,e)&&(i=m(n,e),p=c)}!f&&i&&(f=i,g=p),f&&(l.splice(0,g,f),a=l.join("/"))}return(f=m(j.pkgs,a))?f:a}function d(a){z&&v(document.getElementsByTagName("script"),(function(k){if(k.getAttribute("data-tbquiremodule")===a&&k.getAttribute("data-tbquirecontext")===i.contextName)return k.parentNode.removeChild(k),!0}))}function e(a){var k=m(j.paths,a);if(k&&H(k)&&1<k.length)return k.shift(),i.tbquire.undef(a),i.makeRequire(null,{skipMap:!0})([a]),!0}function n(a){var k,c=a?a.indexOf("!"):-1;return-1<c&&(k=a.substring(0,c),a=a.substring(c+1,a.length)),[k,a]}function p(a,k,b,f){var l,d,e=null,g=k?k.name:null,j=a,p=!0,h="";return a||(p=!1,a="_@r"+(K+=1)),e=(a=n(a))[0],a=a[1],e&&(e=c(e,g,f),d=m(r,e)),a&&(e?h=d&&d.normalize?d.normalize(a,(function(a){return c(a,g,f)})):-1===a.indexOf("!")?c(a,g,f):a:(e=(a=n(h=c(a,g,f)))[0],h=a[1],b=!0,l=i.nameToUrl(h))),{prefix:e,name:h,parentMap:k,unnormalized:!!(b=!e||d||b?"":"_unnormalized"+(O+=1)),url:l,originalName:j,isDefine:p,id:(e?e+"!"+h:h)+b}}function s(a){var k=a.id,b=m(h,k);return b||(b=h[k]=new i.Module(a)),b}function q(a,k,b){var f=a.id,c=m(h,f);!t(r,f)||c&&!c.defineEmitComplete?(c=s(a)).error&&"error"===k?b(c.error):c.on(k,b):"defined"===k&&b(r[f])}function w(a,b){var c=a.tbquireModules,f=!1;b?b(a):(v(c,(function(b){(b=m(h,b))&&(b.error=a,b.events.error&&(f=!0,b.emit("error",a)))})),f||g.onError(a))}function x(){R.length&&(ha.apply(A,[A.length,0].concat(R)),R=[])}function y(a){delete h[a],delete V[a]}function F(a,b,c){var f=a.map.id;a.error?a.emit("error",a.error):(b[f]=!0,v(a.depMaps,(function(f,d){var e=f.id,g=m(h,e);g&&!a.depMatched[d]&&!c[e]&&(m(b,e)?(a.defineDep(d,r[e]),a.check()):F(g,b,c))})),c[f]=!0)}function D(){var a,b,c=(a=1e3*j.waitSeconds)&&i.startTime+a<(new Date).getTime(),f=[],l=[],g=!1,h=!0;if(!W){if(W=!0,B(V,(function(a){var i=a.map,j=i.id;if(a.enabled&&(i.isDefine||l.push(a),!a.error))if(!a.inited&&c)e(j)?g=b=!0:(f.push(j),d(j));else if(!a.inited&&a.fetched&&i.isDefine&&(g=!0,!i.prefix))return h=!1})),c&&f.length)return(a=C("timeout","Load timeout for modules: "+f,null,f)).contextName=i.contextName,w(a);h&&v(l,(function(a){F(a,{},{})})),c&&!b||!g||!z&&!ea||X||(X=setTimeout((function(){X=0,D()}),50)),W=!1}}function E(a){t(r,a[0])||s(p(a[0],null,!0)).init(a[1],a[2])}function I(a){var a=a.currentTarget||a.srcElement,b=i.onScriptLoad;return a.detachEvent&&!Y?a.detachEvent("onreadystatechange",b):a.removeEventListener("load",b,!1),b=i.onScriptError,(!a.detachEvent||Y)&&a.removeEventListener("error",b,!1),{node:a,id:a&&a.getAttribute("data-tbquiremodule")}}function J(){var a;for(x();A.length;){if(null===(a=A.shift())[0])return w(C("mismatch","Mismatched anonymous define() module: "+a[a.length-1]));E(a)}}var W,Z,i,L,X,j={waitSeconds:7,baseUrl:"./",paths:{},bundles:{},pkgs:{},shim:{},config:{}},h={},V={},$={},A=[],r={},S={},aa={},K=1,O=1;return L={tbquire:function(a){return a.tbquire?a.tbquire:a.tbquire=i.makeRequire(a.map)},exports:function(a){if(a.usingExports=!0,a.map.isDefine)return a.exports?r[a.map.id]=a.exports:a.exports=r[a.map.id]={}},module:function(a){return a.module?a.module:a.module={id:a.map.id,uri:a.map.url,config:function(){return m(j.config,a.map.id)||{}},exports:a.exports||(a.exports={})}}},(Z=function(a){this.events=m($,a.id)||{},this.map=a,this.shim=m(j.shim,a.id),this.depExports=[],this.depMaps=[],this.depMatched=[],this.pluginMaps={},this.depCount=0}).prototype={init:function(a,b,c,f){f=f||{},this.inited||(this.factory=b,c?this.on("error",c):this.events.error&&(c=u(this,(function(a){this.emit("error",a)}))),this.depMaps=a&&a.slice(0),this.errback=c,this.inited=!0,this.ignore=f.ignore,f.enabled||this.enabled?this.enable():this.check())},defineDep:function(a,b){this.depMatched[a]||(this.depMatched[a]=!0,this.depCount-=1,this.depExports[a]=b)},fetch:function(){if(!this.fetched){this.fetched=!0,i.startTime=(new Date).getTime();var a=this.map;if(!this.shim)return a.prefix?this.callPlugin():this.load();i.makeRequire(this.map,{enableBuildCallback:!0})(this.shim.deps||[],u(this,(function(){return a.prefix?this.callPlugin():this.load()})))}},load:function(){var a=this.map.url;S[a]||(S[a]=!0,i.load(this.map.id,a))},check:function(){if(this.enabled&&!this.enabling){var a,b,c=this.map.id;b=this.depExports;var f=this.exports,l=this.factory;if(this.inited){if(this.error)this.emit("error",this.error);else if(!this.defining){if(this.defining=!0,1>this.depCount&&!this.defined){if(G(l)){if(this.events.error&&this.map.isDefine||g.onError!==ca)try{f=i.execCb(c,l,b,f)}catch(d){a=d}else f=i.execCb(c,l,b,f);if(this.map.isDefine&&void 0===f&&((b=this.module)?f=b.exports:this.usingExports&&(f=this.exports)),a)return a.tbquireMap=this.map,a.tbquireModules=this.map.isDefine?[this.map.id]:null,a.tbquireType=this.map.isDefine?"tbfine":"tbquire",w(this.error=a)}else f=l;this.exports=f,this.map.isDefine&&!this.ignore&&(r[c]=f,g.onResourceLoad)&&g.onResourceLoad(i,this.map,this.depMaps),y(c),this.defined=!0}this.defining=!1,this.defined&&!this.defineEmitted&&(this.defineEmitted=!0,this.emit("defined",this.exports),this.defineEmitComplete=!0)}}else this.fetch()}},callPlugin:function(){var a=this.map,b=a.id,d=p(a.prefix);this.depMaps.push(d),q(d,"defined",u(this,(function(f){var l,d;d=m(aa,this.map.id);var e=this.map.name,P=this.map.parentMap?this.map.parentMap.name:null,n=i.makeRequire(a.parentMap,{enableBuildCallback:!0});this.map.unnormalized?(f.normalize&&(e=f.normalize(e,(function(a){return c(a,P,!0)}))||""),q(f=p(a.prefix+"!"+e,this.map.parentMap),"defined",u(this,(function(a){this.init([],(function(){return a}),null,{enabled:!0,ignore:!0})}))),(d=m(h,f.id))&&(this.depMaps.push(f),this.events.error&&d.on("error",u(this,(function(a){this.emit("error",a)}))),d.enable())):d?(this.map.url=i.nameToUrl(d),this.load()):((l=u(this,(function(a){this.init([],(function(){return a}),null,{enabled:!0})}))).error=u(this,(function(a){this.inited=!0,this.error=a,a.tbquireModules=[b],B(h,(function(a){0===a.map.id.indexOf(b+"_unnormalized")&&y(a.map.id)})),w(a)})),l.fromText=u(this,(function(f,c){var d=a.name,e=p(d),P=M;c&&(f=c),P&&(M=!1),s(e),t(j.config,b)&&(j.config[d]=j.config[b]);try{g.exec(f)}catch(h){return w(C("fromtexteval","fromText eval for "+b+" failed: "+h,h,[b]))}P&&(M=!0),this.depMaps.push(e),i.completeLoad(d),n([d],l)})),f.load(a.name,n,l,j))}))),i.enable(d,this),this.pluginMaps[d.id]=d},enable:function(){V[this.map.id]=this,this.enabling=this.enabled=!0,v(this.depMaps,u(this,(function(a,b){var c,f;if("string"==typeof a){if(a=p(a,this.map.isDefine?this.map:this.map.parentMap,!1,!this.skipMap),this.depMaps[b]=a,c=m(L,a.id))return void(this.depExports[b]=c(this));this.depCount+=1,q(a,"defined",u(this,(function(a){this.defineDep(b,a),this.check()}))),this.errback&&q(a,"error",u(this,this.errback))}c=a.id,f=h[c],!t(L,c)&&f&&!f.enabled&&i.enable(a,this)}))),B(this.pluginMaps,u(this,(function(a){var b=m(h,a.id);b&&!b.enabled&&i.enable(a,this)}))),this.enabling=!1,this.check()},on:function(a,b){var c=this.events[a];c||(c=this.events[a]=[]),c.push(b)},emit:function(a,b){v(this.events[a],(function(a){a(b)})),"error"===a&&delete this.events[a]}},(i={config:j,contextName:b,registry:h,defined:r,urlFetched:S,defQueue:A,Module:Z,makeModuleMap:p,nextTick:g.nextTick,onError:w,configure:function(a){a.baseUrl&&"/"!==a.baseUrl.charAt(a.baseUrl.length-1)&&(a.baseUrl+="/");var b=j.shim,c={paths:!0,bundles:!0,config:!0,map:!0};B(a,(function(a,b){c[b]?(j[b]||(j[b]={}),U(j[b],a,!0,!0)):j[b]=a})),a.bundles&&B(a.bundles,(function(a,b){v(a,(function(a){a!==b&&(aa[a]=b)}))})),a.shim&&(B(a.shim,(function(a,c){H(a)&&(a={deps:a}),!a.exports&&!a.init||a.exportsFn||(a.exportsFn=i.makeShimExports(a)),b[c]=a})),j.shim=b),a.packages&&v(a.packages,(function(a){var b,a;b=(a="string"==typeof a?{name:a}:a).name,a.location&&(j.paths[b]=a.location),j.pkgs[b]=a.name+"/"+(a.main||"main").replace(ia,"").replace(Q,"")})),B(h,(function(a,b){!a.inited&&!a.map.unnormalized&&(a.map=p(b))})),(a.deps||a.callback)&&i.tbquire(a.deps||[],a.callback)},makeShimExports:function(a){return function(){var b;return a.init&&(b=a.init.apply(ba,arguments)),b||a.exports&&da(a.exports)}},makeRequire:function(a,e){function j(c,d,m){var n,q;return e.enableBuildCallback&&d&&G(d)&&(d.__tbquireJsBuild=!0),"string"==typeof c?G(d)?w(C("tbquireargs","Invalid tbquire call"),m):a&&t(L,c)?L[c](h[a.id]):g.get?g.get(i,c,a,j):(n=(n=p(c,a,!1,!0)).id,t(r,n)?r[n]:w(C("notloaded",'Module name "'+n+'" has not been loaded yet for context: '+b+(a?"":". Use tbquire([])")))):(J(),i.nextTick((function(){J(),(q=s(p(null,a))).skipMap=e.skipMap,q.init(c,d,m,{enabled:!0}),D()})),j)}return e=e||{},U(j,{isBrowser:z,toUrl:function(b){var d,e=b.lastIndexOf("."),k=b.split("/")[0];return-1!==e&&("."!==k&&".."!==k||1<e)&&(d=b.substring(e,b.length),b=b.substring(0,e)),i.nameToUrl(c(b,a&&a.id,!0),d,!0)},defined:function(b){return t(r,p(b,a,!1,!0).id)},specified:function(b){return b=p(b,a,!1,!0).id,t(r,b)||t(h,b)}}),a||(j.undef=function(b){x();var c=p(b,a,!0),e=m(h,b);d(b),delete r[b],delete S[c.url],delete $[b],T(A,(function(a,c){a[0]===b&&A.splice(c,1)})),e&&(e.events.defined&&($[b]=e.events),y(b))}),j},enable:function(a){m(h,a.id)&&s(a).enable()},completeLoad:function(a){var b,c,d=m(j.shim,a)||{},g=d.exports;for(x();A.length;){if(null===(c=A.shift())[0]){if(c[0]=a,b)break;b=!0}else c[0]===a&&(b=!0);E(c)}if(c=m(h,a),!b&&!t(r,a)&&c&&!c.inited){if(j.enforceDefine&&(!g||!da(g)))return e(a)?void 0:w(C("nodefine","No define call for "+a,null,[a]));E([a,d.deps||[],d.exportsFn])}D()},nameToUrl:function(a,b,c){var d,e,h;if((d=m(j.pkgs,a))&&(a=d),d=m(aa,a))return i.nameToUrl(d,b,c);if(g.jsExtRegExp.test(a))d=a+(b||"");else{for(d=j.paths,e=(a=a.split("/")).length;0<e;e-=1)if(h=m(d,h=a.slice(0,e).join("/"))){H(h)&&(h=h[0]),a.splice(0,e,h);break}d=a.join("/"),d=("/"===(d+=b||(/^data\:|\?/.test(d)||c?"":".js")).charAt(0)||d.match(/^[\w\+\.\-]+:/)?"":j.baseUrl)+d}return j.urlArgs?d+(-1===d.indexOf("?")?"?":"&")+j.urlArgs:d},load:function(a,b){g.load(i,a,b)},execCb:function(a,b,c,d){return b.apply(d,c)},onScriptLoad:function(a){("load"===a.type||ja.test((a.currentTarget||a.srcElement).readyState))&&(N=null,a=I(a),i.completeLoad(a.id))},onScriptError:function(a){var b=I(a);if(!e(b.id))return w(C("scripterror","Script error for: "+b.id,a,[b.id]))}}).tbquire=i.makeRequire(),i}var g,x,y,D,I,E,N,J,s,O,ka=/(\/\*([\s\S]*?)\*\/|([^:]|^)\/\/(.*)$)/gm,la=/[^.]\s*tbquire\s*\(\s*["']([^'"\s]+)["']\s*\)/g,Q=/\.js$/,ia=/^\.\//;x=Object.prototype;var K=x.toString,fa=x.hasOwnProperty,ha=Array.prototype.splice,z=!("undefined"==typeof window||"undefined"==typeof navigator||!window.document),ea=!z&&"undefined"!=typeof importScripts,ja=z&&"PLAYSTATION 3"===navigator.platform?/^complete$/:/^(complete|loaded)$/,Y="undefined"!=typeof opera&&"[object Opera]"===opera.toString(),F={},q={},R=[],M=!1;if("undefined"==typeof define){if(void 0!==tbquirejs){if(G(tbquirejs))return;q=tbquirejs,tbquirejs=void 0}void 0!==tbquire&&!G(tbquire)&&(q=tbquire,tbquire=void 0),g=tbquirejs=function(b,c,d,e){var n,p="_";return!H(b)&&"string"!=typeof b&&(n=b,H(c)?(b=c,c=d,d=e):b=[]),n&&n.context&&(p=n.context),(e=m(F,p))||(e=F[p]=g.s.newContext(p)),n&&e.configure(n),e.tbquire(b,c,d)},g.config=function(b){return g(b)},g.nextTick="undefined"!=typeof setTimeout?function(b){setTimeout(b,4)}:function(b){b()},tbquire||(tbquire=g),g.version="2.1.14",g.jsExtRegExp=/^\/|:|\?|\.js$/,g.isBrowser=z,x=g.s={contexts:F,newContext:ga},g({}),v(["toUrl","undef","defined","specified"],(function(b){g[b]=function(){var c=F._;return c.tbquire[b].apply(c,arguments)}})),z&&(y=x.head=document.getElementsByTagName("head")[0],D=document.getElementsByTagName("base")[0])&&(y=x.head=D.parentNode),g.onError=ca,g.createNode=function(b){var c=b.xhtml?document.createElementNS("http://www.w3.org/1999/xhtml","html:script"):document.createElement("script");return c.async=!0,c},g.load=function(b,c,d){var e=b&&b.config||{};if(z)return(e=g.createNode(e,c,d)).setAttribute("data-tbquirecontext",b.contextName),e.setAttribute("data-tbquiremodule",c),!e.attachEvent||e.attachEvent.toString&&0>e.attachEvent.toString().indexOf("[native code")||Y?(e.addEventListener("load",b.onScriptLoad,!1),e.addEventListener("error",b.onScriptError,!1)):(M=!0,e.attachEvent("onreadystatechange",b.onScriptLoad)),e.src=d,J=e,D?y.insertBefore(e,D):y.appendChild(e),J=null,e;if(ea)try{importScripts(d),b.completeLoad(c)}catch(m){b.onError(C("importscripts","importScripts failed for "+c+" at "+d,m,[c]))}},z&&!q.skipDataMain&&T(document.getElementsByTagName("script"),(function(b){if(y||(y=b.parentNode),I=b.getAttribute("data-main"))return s=I,q.baseUrl||(E=s.split("/"),s=E.pop(),O=E.length?E.join("/")+"/":"./",q.baseUrl=O),s=s.replace(Q,""),g.jsExtRegExp.test(s)&&(s=I),q.deps=q.deps?q.deps.concat(s):[s],!0})),tbfine=function(b,c,d){var e,g;"string"!=typeof b&&(d=c,c=b,b=null),H(c)||(d=c,c=null),!c&&G(d)&&(c=[],d.length&&(d.toString().replace(ka,"").replace(la,(function(b,d){c.push(d)})),c=(1===d.length?["tbquire"]:["tbquire","exports","module"]).concat(c))),M&&((e=J)||(N&&"interactive"===N.readyState||T(document.getElementsByTagName("script"),(function(b){if("interactive"===b.readyState)return N=b})),e=N),e&&(b||(b=e.getAttribute("data-tbquiremodule")),g=F[e.getAttribute("data-tbquirecontext")])),(g?g.defQueue:R).push([b,c,d])},tbfine.amd={jQuery:!0},g.exec=function(b){return eval(b)},g(q)}}(this),tbquire.config({baseUrl:TBUI.uri+"/js",urlArgs:"ver="+TBUI.ver,paths:{swiper:"libs/swiper.min","jquery.qrcode":"libs/jquery.qrcode.min",lazyload:"libs/lazyload.min",ias:"libs/ias.min",main:"main"}}),tbquire(["main"]);