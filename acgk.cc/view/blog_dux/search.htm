{inc:header.htm} 
  <section class="container"> 
   <div class="content-wrap"> 
    <div class="content"> 
     <div class="orbui orbui-cat orbui-cat">
      <a class="tbas" href="" target="_blank">广告位，电脑和手机可分别设置，可放任何广告代码</a>
     </div> 
	 {block:global_search pagenum="10" maxcount="20000" intronum="170" dateformat="m-d" showviews="1" showcate="1"} 
     <div class="catleader">
      <h1>搜索 “{$keyword}” 文章列表 </h1>
     </div>
	 {loop:$gdata[list] $v}
     <article class="excerpt excerpt excerpt-sticky">
      <a class="focus" href="{$v[url]}" target="_blank" title="{$v[title]}"><img data-src="{$v[pic]}" alt="{$v[title]}" src="{$cfg[tpl]}img/thumbnail.png" class="thumb" /></a>
      <header>
       <h2><a href="{$v[url]}" target="_blank" title="{$v[title]}">{$v[subject]}</a></h2>
      </header>
      <p class="note"></p>
      <div class="meta">
       <span class="author"><img class="avatar" data-src="{$v[avatar]}" src="{$v[avatar]}" alt="{$v[author]}" /><a href="{$v[user_url]}" target="_blank" title="{$v[author]}">{$v[author]}</a></span>
       <time>{$v[date]}</time>
       <a class="cat" href="{$cfg_var[url]}" title="更多{$cfg_var[name]}的文章"><i class="tbfa">&#xe60e;</i>{$cfg_var[name]}</a> 
       <span class="pv">阅读({$v[views]})</span>
       <a href="{$v[url]}#divCommentPost" etap="like" class="post-like"><i class="tbfa">&#xe64c;</i>评论(<span>{$v[comments]}</span>)</a>
      </div>
     </article>
     {/loop} 
	 <div class="pages">{$gdata[pages]}</div> 
     {/block} 	 
    </div> 
   </div> 
   {inc:side.htm}
  </section> 
{inc:footer.htm} 
