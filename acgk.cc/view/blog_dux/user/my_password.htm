{inc:user/header.htm}
  <main class="content">
    <div id="content-container" class="container">
      <div class="row">
        {inc:user/menu.htm}
        <!--右侧主体部分 start-->
        <div class="col-md-9">
          <div class="panel panel-default">
            <div class="panel-body">
              <h2 class="page-header">{lang:my_password}</h2>
              <div class="row">
                <form id="password-form" class="form-horizontal layui-form" action="index.php?my-password-ajax-1.html" method="post">
                  <div class="form-group">
                    <label for="oldpwd" class="col-sm-2 control-label">{lang:old_password}</label>
                    <div class="col-sm-10">
                      <input class="form-control" id="oldpwd" type="password" name="oldpwd" maxlength="32" value="" placeholder="{lang:input_old_password}" autocomplete="off">
                    </div>
                  </div>
                  <div class="form-group">
                    <label for="newpwd" class="col-sm-2 control-label">{lang:new_password}</label>
                    <div class="col-sm-10">
                      <input class="form-control" id="newpwd" type="password" name="newpwd" maxlength="32" value="" placeholder="{lang:input_new_password}" autocomplete="off">
                    </div>
                  </div>
                  <div class="form-group">
                    <label for="renewpwd" class="col-sm-2 control-label">{lang:confirm_new_password}</label>
                    <div class="col-sm-10">
                      <input class="form-control" id="renewpwd" type="password" name="renewpwd" maxlength="32" value="" placeholder="{lang:input_confirm_new_password}" autocomplete="off">
                    </div>
                  </div>
                  {hook:user_my_password_after.htm}
                  <div class="form-group normal-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                      <button type="submit" class="btn btn-primary btn-embossed" lay-submit lay-filter="form">{lang:submit}</button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <!--右侧主体部分 end-->
      </div>
    </div>
  </main>
{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;

    $("#my-password").addClass("active");

    form.on('submit(form)', function (data) {
      data = data.field;
      $.post("index.php?my-password-ajax-1",data,function(res){
        if(res.status){
          var icon = 1;
        }else{
          var icon = 5;
        }
        layer.msg(res.message, {icon: icon});
        if(res.status) setTimeout(function(){ window.location="{$login_url}"; }, 1000);
        return false;
      },'json');
      return false;
    });
  });
</script>
</body>
</html>