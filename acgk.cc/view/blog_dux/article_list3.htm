{inc:header.htm} 
 <section class="container"> 
   <div class="product-container"> 
    <div class="product-side"> 
	 {block:navigate mobile="1"}	
     <div class="product-filters"> 
      <h3><span class="product-filters-more"><i class="tbfa">&#xeac2;</i></span><i class="tbfa">&#xe60e;</i>产品中心</h3> 
      <ul> 
	   {loop:$data $v}
       <li class="cat-item cat-item {if:$cfg_var['topcid']==$v['cid']} current-cat{/if}"><a href="{$v[url]}" title="{$v[name]}">{$v[name]}</a> 
	    {if:isset($v[son])}
        <ul class="children"> 
         {loop:$v[son] $v2}<li class="cat-item cat-item-53"><a href="{$v2[url]}" title="{$v2[name]}">{$v2[name]}</a> </li>{/loop} 
        </ul>
		{/if}	 
	   </li> 
       {/loop}
      </ul> 
     </div> 
	 {/block} 	
     <div class="product-qrcode"> 
      <h4>手机扫码分享</h4> 
      <div class="tbqrcode" data-url="{$cfg[webroot]}{$cfg_var[url]}">
      </div> 
     </div> 
     <div class="orbui orbui-pcat orbui-pcat">
      <a class="tbas" href="" target="_blank">广告位，电脑和手机可分别设置，可放任何广告代码</a>
     </div> 
    </div> 
	{block:global_cate pagenum="16" dateformat="Y-m-d" pageoffset="3" showcate="1" showviews="1"} 
    <div class="product-content">
	{loop:$gdata[list] $v}
     <article class="-item">
      <a class="thumbnail" href="{$v[url]}" target="_blank" title="{$v[title]}"><img data-src="{$v[pic]}" alt="{$v[title]}" src="{$v[pic]}" class="thumb" style="height:150px;" /></a>
      <h2><a href="{$v[url]}" target="_blank" title="{$v[title]}">{$v[subject]}</a></h2>
      <footer>
       <span class="price">&yen; {$v[date]}</span>
      </footer>
     </article>
	 {/loop}
    </div>
    <div class="pages"><span>共 <font color="red">{$gdata[total]}</font> 篇</span>{$gdata[pages]}</div>
	{/block} 	
   </div> 
  </section>  
{inc:footer.htm} 