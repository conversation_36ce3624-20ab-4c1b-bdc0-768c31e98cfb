<!DOCTYPE html>
{php}
date_default_timezone_set('PRC'); //设定时区，PRC就是中国
$hour = date('H');
{/php}
<!--如果不需要到时间暗黑切换注释下一行-->
<!--html lang="zh-CN" {if:$hour >= 18 || $hour < 6}class="darking"{/if}-->
<html lang="zh-CN">
 <head> 
  <meta charset="UTF-8" /> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0" /> 
  {hook:hookheader.htm}
   
  
  <link rel="stylesheet" id="wp-block-library-css" href="{$cfg[tpl]}css/style.min.css" type="text/css" media="all" /> 
  <link rel="stylesheet" id="style-css" href="{$cfg[tpl]}css/style.css" type="text/css" media="all" /> 
  <link rel='stylesheet' id='fonts-css' href='{$cfg[tpl]}fonts/iconfont.css' type='text/css' media='all' />
  <script type="text/javascript" src="{$cfg[tpl]}js/jquery.min.js" id="jquery-js"></script>  
  <style>
  /*哀悼灰色 html, body {filter:grayscale(1);}*/
  :root{--tb--main: #007bff}a.tbas{border:2px dashed #aaa;padding:40px 15px;font-size:14px;background-color:#fff;display:block;text-decoration:none;color:#888;font-weight:bold;text-align:center;}
  a.tbas:hover{border-color:#666;color:#444;}
  @media (max-width:640px){
  a.tbas{font-size:12px;padding:25px 15px;}}
  </style> 
 </head> 
 {if:$control=='show' && $action == 'index'}  
 <body class="post-template-default single single-post  single-format-standard home nav_fixed m-excerpt-cat p_indent comment-open site-layout-2 text-justify-on m-sidebar m-user-on dark-on">
 {elseif:$control=='cate' && $action == 'index'}  
 <body class="archive category category-tech  home nav_fixed m-excerpt-cat site-layout-2 text-justify-on m-sidebar m-user-on dark-on">
 {elseif:$control=='cate' && $cfg_var[mid]==1 && $action == 'index'}  
 <body class="page-template page-template-pages page-template-no-sidebar page-template-pagesno-sidebar-php page home nav_fixed m-excerpt-cat p_indent comment-open site-layout-2 text-justify-on m-sidebar m-user-on dark-on"> 
 {else}
 <body class="home blog nav_fixed m-excerpt-cat site-layout-2 text-justify-on m-sidebar m-user-on dark-on"> 
 {/if}  
  <header class="header"> 
   <div class="container"> 
    <h1 class="logo"><a href="{$cfg[weburl]}" title="{$cfg[webname]}"><img src="{$cfg[tpl]}img/logo.png" alt="{$cfg[webname]}" /><img class="-dark" src="{$cfg[tpl]}img/logo-dark.png" alt="{$cfg[webname]}" />{$cfg[webname]}</a></h1> 
    <div class="brand">
     欢迎光临<br />我们一直在努力
    </div> 
	{block:navigate}	
    <ul class="site-nav site-navbar"> 
     <li {if:empty($cfg_var['topcid'])} class="current-menu-item" {/if}><a href="{$cfg[weburl]}" title="{$cfg[webname]}" aria-current="page">首页</a></li>
	 {loop:$data $v}
	 <li class="current-category-ancestor current-menu-ancestor {if:$cfg_var['topcid']==$v['cid']} current-menu-item{/if} {if:isset($v[son])} menu-item-has-children{/if}">
      <a href="{$v[url]}" title="{$v[name]}">{$v[name]}</a> 
	  {if:isset($v[son])}
      <ul class="sub-menu"> 
        {loop:$v[son] $v2}<li><a href="{$v2[url]}" title="{$v2[name]}">{$v2[name]}</a></li>{/loop} 
      </ul>
      {/if}	  
	 </li> 
	 {/loop}
     <li class="navto-search"><a href="javascript:;" class="search-show"><i class="tbfa">&#xe611;</i></a></li> 
     {if:!is_mobile()}<li class="sitedark" etap="darking" style="display:none;"><i class="tbfa">&#xe6a0;</i><i class="tbfa">&#xe635;</i></li> {/if} 
    </ul> 
	{/block}  
    <div class="topbar"> 
     <ul class="site-nav topmenu"> 
     <li><a href="/special/1.html">专题</a></li>
      <li><a href="/tags">标签云</a></li> 
      <li><a href="/archives">页面存档</a></li> 
      <li><a href="/links">友情链接</a></li> 
      <li class="menusns menu-item-has-children"> <a href="javascript:;">关注我们</a> 
       <ul class="sub-menu"> 
        <li><a class="sns-wechat" href="javascript:;" title="关注微信" data-src="{$cfg[tpl]}img/wechat.jpg">关注微信</a></li> 
        <li><a target="_blank" rel="external nofollow" href="">分类一</a></li>
        <li><a target="_blank" rel="external nofollow" href="">分类二</a></li> 
       </ul> </li> 
     </ul> 
     <a rel="nofollow" target="_blank" href="{$login_url}" class="signin-loader">Hi, 请登录</a> &nbsp; &nbsp; 
     <a rel="nofollow" target="_blank" href="{$register_url}" class="signup-loader">我要注册</a> &nbsp; &nbsp; 
     <a rel="nofollow" target="_blank" href="/user-forget.html">找回密码</a> 
    </div> 
    {if:!is_mobile()}<a rel="nofollow" href="javascript:;" class="signin-loader m-icon-user"><i class="tbfa">&#xe641;</i></a>{/if} 
   </div> 
  </header>
  <i class="tbfa m-icon-nav">&#xe612;</i>
  <div class="site-search"> 
   <div class="container"> 
    <form id="search_form" method="get" target="_blank" class="site-search-form"> 
	 <input type="hidden" name="u" value="search-index" />
     <input class="search-input" name="keyword" type="text" placeholder="输入关键字" value="" required="required" /> 
     <button class="search-btn" type="submit"><i class="tbfa">&#xe611;</i></button> 
    </form> 
   </div> 
  </div>   