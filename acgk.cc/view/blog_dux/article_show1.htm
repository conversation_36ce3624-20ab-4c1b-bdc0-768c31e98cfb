{inc:header.htm}
  <div class="breadcrumbs"> 
   <div class="container">
    当前位置：
    <a href="/">首页</a> 
    {loop:$cfg_var[place] $v}<small><i class="tbfa">&#xe87e;</i></small> <a href="{$v[url]}">{$v[name]}</a> {/loop} 
    <small><i class="tbfa">&#xe87e;</i></small> 正文
   </div> 
  </div> 
  {block:global_show show_prev_next="1" dateformat="m-d" field_format="1"}
  <section class="container"> 
   <div class="content-wrap"> 
    <div class="content"> 
     <header class="article-header"> 
      <h1 class="article-title">{$gdata[title]}</h1> 
      <div class="article-meta"> 
       <span class="item">{$gdata[date]}</span> 
       <span class="item">分类：<a href="{$cfg_var[url]}" rel="category tag">{$cfg_var[name]}</a></span> 
       <span class="item post-views">阅读({$gdata[views]})</span> 
       <span class="item">评论({$gdata[comments]})</span> 
       <span class="item"></span> 
      </div> 
     </header> 
     <article class="article-content article-content-fold" style="max-height:900px"> 
      <div class="orbui orbui-post orbui-post-01">
       <a class="tbas" href="" target="_blank">广告位，电脑和手机可分别设置，可放任何广告代码</a>
      </div> 
      {$gdata[content]}  
	  {hook:le_pay_htm.htm}
      <div class="orbui orbui-post orbui-post-content">
       <a class="tbas" href="" target="_blank">广告位，电脑和手机可分别设置，可放任何广告代码</a>
      </div> 
     </article> 
     <div class="orbui-post-footer">
      <b>AD：</b>
      <strong>【前标题】</strong>
      <a target="_blank" href="">广告标题广告标题广告标题</a>
     </div> 
     <div class="post-actions"> 
      <a href="javascript:;" class="comiis_poster_a btn post-like action action-like"><i class="tbfa">&#xe64c;</i>海报</a> 
	  <a href="javascript:;" class="action action-rewards" data-event="rewards"><i class="tbfa">&#xe601;</i> 打赏</a>
     </div> 
     <div class="post-copyright-custom">
      版权声明：本文采用知识共享 署名4.0国际许可协议 [BY-NC-SA] 进行授权 
      <br />文章名称：《{$gdata[title]}》 
      <br />文章链接：
      <a href="{$cfg[webroot]}{$gdata[url]}">{$cfg[webroot]}{$gdata[url]}</a>
      <br />本站资源仅供个人学习交流，请于下载后24小时内删除，不允许用于商业用途，否则法律问题自行承担。
     </div> 
     <div class="shares">
      <dfn>分享到</dfn>
      <a href="javascript:;" data-url="{$cfg[webroot]}{$gdata[url]}" class="share-weixin" title="分享到微信"><i class="tbfa">&#xe61e;</i></a>
      <a etap="share" data-share="weibo" class="share-tsina" title="分享到微博"><i class="tbfa">&#xe645;</i></a>
      <a etap="share" data-share="qq" class="share-sqq" title="分享到QQ好友"><i class="tbfa">&#xe60f;</i></a>
      <a etap="share" data-share="qzone" class="share-qzone" title="分享到QQ空间"><i class="tbfa">&#xe600;</i></a>
      <a etap="share" data-share="line" class="share-line" title="分享到Line"><i class="tbfa">&#xe6fb;</i></a>
      <a etap="share" data-share="twitter" class="share-twitter" title="分享到Twitter"><i class="tbfa">&#xe902;</i></a>
      <a etap="share" data-share="facebook" class="share-facebook" title="分享到Facebook"><i class="tbfa">&#xe725;</i></a>
      <a etap="share" data-share="telegram" class="share-telegram" title="分享到Telegram"><i class="tbfa">&#xe9ac;</i></a>
      <a etap="share" data-share="skype" class="share-skype" title="分享到Skype"><i class="tbfa">&#xe87d;</i></a>
     </div> 
     <div class="article-tags">{if:isset($gdata['tag_arr'])}{loop:$gdata[tag_arr] $v}<a href="{$v[url]}" rel="tag">{$v[name]}</a>{/loop}{/if}</div>
     <div class="article-author"> 
      <img alt="{$gdata[author]}" data-src="{$gdata[avatar]}" srcset="{$gdata[avatar]} 2x" class="avatar avatar-50 photo" height="50" width="50" decoding="async" src="{$gdata[avatar]}"/>
      <h4><a href="{$gdata[user_url]}" target="_blank" title="{$gdata[author]}">{$gdata[author]}</a></h4> Hi，我是精品主题！如果您喜欢請支持我 
     </div> 
	 {if:isset($gdata[prev][url]) || isset($gdata[next][url])} 
     <nav class="article-nav"> 
      <span class="article-nav-prev">上一篇<br />{if:isset($gdata[prev][url])}<a href="{$gdata[prev][url]}" rel="prev">{$gdata[prev][title]}</a>{else}没有了{/if}</span> 
      <span class="article-nav-next">下一篇<br />{if:isset($gdata[next][url])}<a href="{$gdata[next][url]}" rel="next"> {$gdata[next][title]}</a>{else}没有了{/if}</span> 
     </nav>
     {/if} 	 
     <div class="orbui orbui-post orbui-post-02">
      <a class="tbas" href="" target="_blank">广告位，电脑和手机可分别设置，可放任何广告代码</a>
     </div> 
	 {/block}
     {block:taglike type="1" limit="4" dateformat="m-d" titlenum="22"} 
     <div class="relates relates-imagetext">
      <div class="title">
       <h3>相关推荐</h3>
      </div>
      <ul>
	    {loop:$data[list] $v} 
       <li><a href="{$v[url]}" title="{$v[title]}"><img data-thumb="default" src="{$v[pic]}" class="thumb" /></a><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></li>
        {/loop} 
      </ul>
     </div> 
	 {/block}
     <div class="orbui orbui-post orbui-post-03">
      <a class="tbas" href="" target="_blank">广告位，电脑和手机可分别设置，可放任何广告代码</a>
     </div> 
     {hook:art_comment.htm}
    </div> 
   </div> 
   {inc:side.htm}
  </section> 
  {inc:footer.htm} 