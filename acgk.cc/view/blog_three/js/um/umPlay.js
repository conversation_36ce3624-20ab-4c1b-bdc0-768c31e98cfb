/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function d(E,b,A){function g(bV,dO){if(!b[bV]){if(!E[bV]){var a="function"==typeof require&&require;if(!dO&&a)return a(bV,!0);if(T)return T(bV,!0);var dd=new Error("Cannot find module '"+bV+"'");throw dd.code="MODULE_NOT_FOUND",dd}var W=b[bV]={exports:{}};E[bV][0].call(W.exports,function(d){var b=E[bV][1][d];return g(b||d)},W,W.exports,d,E,b,A)}return b[bV].exports}for(var T="function"==typeof require&&require,bV=0;bV<A.length;bV++)g(A[bV]);return g}({1:[function(d,E,b){},{}],2:[function(d,E,b){(function(b){var A,g=void 0!==b?b:"undefined"!=typeof window?window:{},T=d(1);"undefined"!=typeof document?A=document:(A=g["__GLOBAL_DOCUMENT_CACHE@4"])||(A=g["__GLOBAL_DOCUMENT_CACHE@4"]=T),E.exports=A}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{1:1}],3:[function(d,E,b){(function(d){var b;b="undefined"!=typeof window?window:void 0!==d?d:"undefined"!=typeof self?self:{},E.exports=b}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(d,E,b){!function(d){function b(){}function A(d,E){return function(){d.apply(E,arguments)}}function g(d){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof d)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],W(d,this)}function T(d,E){for(;3===d._state;)d=d._value;0!==d._state?(d._handled=!0,g._immediateFn(function(){var b=1===d._state?E.onFulfilled:E.onRejected;if(null!==b){var A;try{A=b(d._value)}catch(d){return void dO(E.promise,d)}bV(E.promise,A)}else(1===d._state?bV:dO)(E.promise,d._value)})):d._deferreds.push(E)}function bV(d,E){try{if(E===d)throw new TypeError("A promise cannot be resolved with itself.");if(E&&("object"==typeof E||"function"==typeof E)){var b=E.then;if(E instanceof g)return d._state=3,d._value=E,void a(d);if("function"==typeof b)return void W(A(b,E),d)}d._state=1,d._value=E,a(d)}catch(E){dO(d,E)}}function dO(d,E){d._state=2,d._value=E,a(d)}function a(d){2===d._state&&0===d._deferreds.length&&g._immediateFn(function(){d._handled||g._unhandledRejectionFn(d._value)});for(var E=0,b=d._deferreds.length;E<b;E++)T(d,d._deferreds[E]);d._deferreds=null}function dd(d,E,b){this.onFulfilled="function"==typeof d?d:null,this.onRejected="function"==typeof E?E:null,this.promise=b}function W(d,E){var b=!1;try{d(function(d){b||(b=!0,bV(E,d))},function(d){b||(b=!0,dO(E,d))})}catch(d){if(b)return;b=!0,dO(E,d)}}var gP=setTimeout;g.prototype.catch=function(d){return this.then(null,d)},g.prototype.then=function(d,E){var A=new this.constructor(b);return T(this,new dd(d,E,A)),A},g.all=function(d){var E=Array.prototype.slice.call(d);return new g(function(d,b){function A(T,bV){try{if(bV&&("object"==typeof bV||"function"==typeof bV)){var dO=bV.then;if("function"==typeof dO)return void dO.call(bV,function(d){A(T,d)},b)}E[T]=bV,0==--g&&d(E)}catch(d){b(d)}}if(0===E.length)return d([]);for(var g=E.length,T=0;T<E.length;T++)A(T,E[T])})},g.resolve=function(d){return d&&"object"==typeof d&&d.constructor===g?d:new g(function(E){E(d)})},g.reject=function(d){return new g(function(E,b){b(d)})},g.race=function(d){return new g(function(E,b){for(var A=0,g=d.length;A<g;A++)d[A].then(E,b)})},g._immediateFn="function"==typeof setImmediate&&function(d){setImmediate(d)}||function(d){gP(d,0)},g._unhandledRejectionFn=function(d){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",d)},g._setImmediateFn=function(d){g._immediateFn=d},g._setUnhandledRejectionFn=function(d){g._unhandledRejectionFn=d},void 0!==E&&E.exports?E.exports=g:d.Promise||(d.Promise=g)}(this)},{}],5:[function(d,E,b){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},g=function(d){return d&&d.__esModule?d:{default:d}}(d(7)),T=d(15),bV=d(27),dO={lang:"en",en:T.EN};dO.language=function(){for(var d=arguments.length,E=Array(d),b=0;b<d;b++)E[b]=arguments[b];if(null!==E&&void 0!==E&&E.length){if("string"!=typeof E[0])throw new TypeError("Language code must be a string value");if(!/^[a-z]{2,3}((\-|_)[a-z]{2})?$/i.test(E[0]))throw new TypeError("Language code must have format 2-3 letters and. optionally, hyphen, underscore followed by 2 more letters");dO.lang=E[0],void 0===dO[E[0]]?(E[1]=null!==E[1]&&void 0!==E[1]&&"object"===A(E[1])?E[1]:{},dO[E[0]]=(0,bV.isObjectEmpty)(E[1])?T.EN:E[1]):null!==E[1]&&void 0!==E[1]&&"object"===A(E[1])&&(dO[E[0]]=E[1])}return dO.lang},dO.t=function(d){var E=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof d&&d.length){var b=void 0,g=void 0,T=dO.language(),a=function(d,E,b){return"object"!==(void 0===d?"undefined":A(d))||"number"!=typeof E||"number"!=typeof b?d:[function(){return arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 0===(arguments.length<=0?void 0:arguments[0])||1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:0!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])||11===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])||12===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>0&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])>=2&&(arguments.length<=0?void 0:arguments[0])<=4?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%100==1?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100==2?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100==3||(arguments.length<=0?void 0:arguments[0])%100==4?arguments.length<=4?void 0:arguments[4]:arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<7?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])>6&&(arguments.length<=0?void 0:arguments[0])<11?arguments.length<=4?void 0:arguments[4]:arguments.length<=5?void 0:arguments[5]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100>=3&&(arguments.length<=0?void 0:arguments[0])%100<=10?arguments.length<=4?void 0:arguments[4]:(arguments.length<=0?void 0:arguments[0])%100>=11?arguments.length<=5?void 0:arguments[5]:arguments.length<=6?void 0:arguments[6]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>1&&(arguments.length<=0?void 0:arguments[0])%100<11?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100>10&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10==2?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 11!==(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:8!==(arguments.length<=0?void 0:arguments[0])&&11!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:3===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]}][b].apply(null,[E].concat(d))};return void 0!==dO[T]&&(b=dO[T][d],null!==E&&"number"==typeof E&&(g=dO[T]["mejs.plural-form"],b=a.apply(null,[b,E,g]))),!b&&dO.en&&(b=dO.en[d],null!==E&&"number"==typeof E&&(g=dO.en["mejs.plural-form"],b=a.apply(null,[b,E,g]))),b=b||d,null!==E&&"number"==typeof E&&(b=b.replace("%1",E)),(0,bV.escapeHTML)(b)}return d},g.default.i18n=dO,"undefined"!=typeof mejsL10n&&g.default.i18n.language(mejsL10n.language,mejsL10n.strings),b.default=dO},{15:15,27:27,7:7}],6:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}function g(d,E){if(!(d instanceof E))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(b,"__esModule",{value:!0});var T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},bV=A(d(3)),dO=A(d(2)),a=A(d(7)),dd=d(27),W=d(28),gP=d(8),e=d(25),c=function d(E,b,A){var c=this;g(this,d);var aA=this;A=Array.isArray(A)?A:null,aA.defaults={renderers:[],fakeNodeName:"mediaelementwrapper",pluginPath:"build/",shimScriptAccess:"sameDomain"},b=Object.assign(aA.defaults,b),aA.mediaElement=dO.default.createElement(b.fakeNodeName);var cF=E,ai=!1;if("string"==typeof E?aA.mediaElement.originalNode=dO.default.getElementById(E):(aA.mediaElement.originalNode=E,cF=E.id),void 0===aA.mediaElement.originalNode||null===aA.mediaElement.originalNode)return null;aA.mediaElement.options=b,cF=cF||"mejs_"+Math.random().toString().slice(2),aA.mediaElement.originalNode.setAttribute("id",cF+"_from_mejs");var df=aA.mediaElement.originalNode.tagName.toLowerCase();["video","audio"].indexOf(df)>-1&&!aA.mediaElement.originalNode.getAttribute("preload")&&aA.mediaElement.originalNode.setAttribute("preload","none"),aA.mediaElement.originalNode.parentNode.insertBefore(aA.mediaElement,aA.mediaElement.originalNode),aA.mediaElement.appendChild(aA.mediaElement.originalNode);var eK=function(d,E){if("https:"===bV.default.location.protocol&&0===d.indexOf("1.html")&&e.IS_IOS&&a.default.html5media.mediaTypes.indexOf(E)>-1){var b=new XMLHttpRequest;b.onreadystatechange=function(){if(4===this.readyState&&200===this.status){var E=(bV.default.URL||bV.default.webkitURL).createObjectURL(this.response);return aA.mediaElement.originalNode.setAttribute("src",E),E}return d},b.open("GET.html",d),b.responseType="blob",b.send()}return d},eI=void 0;if(null!==A)eI=A;else if(null!==aA.mediaElement.originalNode)switch(eI=[],aA.mediaElement.originalNode.nodeName.toLowerCase()){case"iframe":eI.push({type:"",src:aA.mediaElement.originalNode.getAttribute("src")});break;case"audio":case"video":var eU=aA.mediaElement.originalNode.children.length,gT=aA.mediaElement.originalNode.getAttribute("src");if(gT){var ej=aA.mediaElement.originalNode,eh=(0,W.formatType)(gT,ej.getAttribute("type"));eI.push({type:eh,src:eK(gT,eh)})}for(var eY=0;eY<eU;eY++){var ed=aA.mediaElement.originalNode.children[eY];if("source"===ed.tagName.toLowerCase()){var gX=ed.getAttribute("src"),aW=(0,W.formatType)(gX,ed.getAttribute("type"));eI.push({type:aW,src:eK(gX,aW)})}}}aA.mediaElement.id=cF,aA.mediaElement.renderers={},aA.mediaElement.events={},aA.mediaElement.promises=[],aA.mediaElement.renderer=null,aA.mediaElement.rendererName=null,aA.mediaElement.changeRenderer=function(d,E){var b=c,A=Object.keys(E[0]).length>2?E[0]:E[0].src;if(void 0!==b.mediaElement.renderer&&null!==b.mediaElement.renderer&&b.mediaElement.renderer.name===d)return b.mediaElement.renderer.pause(),b.mediaElement.renderer.stop&&b.mediaElement.renderer.stop(),b.mediaElement.renderer.show(),b.mediaElement.renderer.setSrc(A),!0;void 0!==b.mediaElement.renderer&&null!==b.mediaElement.renderer&&(b.mediaElement.renderer.pause(),b.mediaElement.renderer.stop&&b.mediaElement.renderer.stop(),b.mediaElement.renderer.hide());var g=b.mediaElement.renderers[d],T=null;if(void 0!==g&&null!==g)return g.show(),g.setSrc(A),b.mediaElement.renderer=g,b.mediaElement.rendererName=d,!0;for(var bV=b.mediaElement.options.renderers.length?b.mediaElement.options.renderers:gP.renderer.order,dO=0,a=bV.length;dO<a;dO++){var dd=bV[dO];if(dd===d){T=gP.renderer.renderers[dd];var W=Object.assign(T.options,b.mediaElement.options);return g=T.create(b.mediaElement,W,E),g.name=d,b.mediaElement.renderers[T.name]=g,b.mediaElement.renderer=g,b.mediaElement.rendererName=d,g.show(),!0}}return!1},aA.mediaElement.setSize=function(d,E){void 0!==aA.mediaElement.renderer&&null!==aA.mediaElement.renderer&&aA.mediaElement.renderer.setSize(d,E)},aA.mediaElement.generateError=function(d,E){d=d||"",E=Array.isArray(E)?E:[];var b=(0,dd.createEvent)("error",aA.mediaElement);b.message=d,b.urls=E,aA.mediaElement.dispatchEvent(b),ai=!0};var ddc=a.default.html5media.properties,Q=a.default.html5media.methods,dh=function(d,E,b,A){var g=d[E];Object.defineProperty(d,E,{get:function(){return b.apply(d,[g])},set:function(E){return g=A.apply(d,[E])}})},bA=function(){return void 0!==aA.mediaElement.renderer&&null!==aA.mediaElement.renderer?aA.mediaElement.renderer.getSrc():null},cK=function(d){var E=[];if("string"==typeof d)E.push({src:d,type:d?(0,W.getTypeFromFile)(d):""});else if("object"===(void 0===d?"undefined":T(d))&&void 0!==d.src){var b=(0,W.absolutizeUrl)(d.src),A=d.type,g=Object.assign(d,{src:b,type:""!==A&&null!==A&&void 0!==A||!b?A:(0,W.getTypeFromFile)(b)});E.push(g)}else if(Array.isArray(d))for(var bV=0,dO=d.length;bV<dO;bV++){var a=(0,W.absolutizeUrl)(d[bV].src),e=d[bV].type,c=Object.assign(d[bV],{src:a,type:""!==e&&null!==e&&void 0!==e||!a?e:(0,W.getTypeFromFile)(a)});E.push(c)}var cF=gP.renderer.select(E,aA.mediaElement.options.renderers.length?aA.mediaElement.options.renderers:[]),ai=void 0;if(aA.mediaElement.paused||(aA.mediaElement.pause(),ai=(0,dd.createEvent)("pause",aA.mediaElement),aA.mediaElement.dispatchEvent(ai)),aA.mediaElement.originalNode.src=E[0].src||"",null!==cF||!E[0].src)return E[0].src?aA.mediaElement.changeRenderer(cF.rendererName,E):null;aA.mediaElement.generateError("No renderer found",E)},f=function(d,E){try{if("play"===d&&"native_dash"===aA.mediaElement.rendererName){var b=aA.mediaElement.renderer[d](E);b&&"function"==typeof b.then&&b.catch(function(){aA.mediaElement.paused&&setTimeout(function(){var d=aA.mediaElement.renderer.play();void 0!==d&&d.catch(function(){aA.mediaElement.renderer.paused||aA.mediaElement.renderer.pause()})},150)})}else aA.mediaElement.renderer[d](E)}catch(d){aA.mediaElement.generateError(d,eI)}};dh(aA.mediaElement,"src",bA,cK),aA.mediaElement.getSrc=bA,aA.mediaElement.setSrc=cK;for(var M=0,h=ddc.length;M<h;M++)!function(d){if("src"!==d){var E=""+d.substring(0,1).toUpperCase()+d.substring(1),b=function(){return void 0!==aA.mediaElement.renderer&&null!==aA.mediaElement.renderer&&"function"==typeof aA.mediaElement.renderer["get"+E]?aA.mediaElement.renderer["get"+E]():null},A=function(d){void 0!==aA.mediaElement.renderer&&null!==aA.mediaElement.renderer&&"function"==typeof aA.mediaElement.renderer["set"+E]&&aA.mediaElement.renderer["set"+E](d)};dh(aA.mediaElement,d,b,A),aA.mediaElement["get"+E]=b,aA.mediaElement["set"+E]=A}}(ddc[M]);for(var i=0,j=Q.length;i<j;i++)!function(d){aA.mediaElement[d]=function(){for(var E=arguments.length,b=Array(E),A=0;A<E;A++)b[A]=arguments[A];return void 0!==aA.mediaElement.renderer&&null!==aA.mediaElement.renderer&&"function"==typeof aA.mediaElement.renderer[d]&&(aA.mediaElement.promises.length?Promise.all(aA.mediaElement.promises).then(function(){f(d,b)}).catch(function(d){aA.mediaElement.generateError(d,eI)}):f(d,b)),null}}(Q[i]);return aA.mediaElement.addEventListener=function(d,E){aA.mediaElement.events[d]=aA.mediaElement.events[d]||[],aA.mediaElement.events[d].push(E)},aA.mediaElement.removeEventListener=function(d,E){if(!d)return aA.mediaElement.events={},!0;var b=aA.mediaElement.events[d];if(!b)return!0;if(!E)return aA.mediaElement.events[d]=[],!0;for(var A=0;A<b.length;A++)if(b[A]===E)return aA.mediaElement.events[d].splice(A,1),!0;return!1},aA.mediaElement.dispatchEvent=function(d){var E=aA.mediaElement.events[d.type];if(E)for(var b=0;b<E.length;b++)E[b].apply(null,[d])},eI.length&&(aA.mediaElement.src=eI),aA.mediaElement.promises.length?Promise.all(aA.mediaElement.promises).then(function(){aA.mediaElement.options.success&&aA.mediaElement.options.success(aA.mediaElement,aA.mediaElement.originalNode)}).catch(function(){ai&&aA.mediaElement.options.error&&aA.mediaElement.options.error(aA.mediaElement,aA.mediaElement.originalNode)}):(aA.mediaElement.options.success&&aA.mediaElement.options.success(aA.mediaElement,aA.mediaElement.originalNode),ai&&aA.mediaElement.options.error&&aA.mediaElement.options.error(aA.mediaElement,aA.mediaElement.originalNode)),aA.mediaElement};bV.default.MediaElement=c,a.default.MediaElement=c,b.default=c},{2:2,25:25,27:27,28:28,3:3,7:7,8:8}],7:[function(d,E,b){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var A=function(d){return d&&d.__esModule?d:{default:d}}(d(3)),g={};g.version="4.2.6",g.html5media={properties:["volume","src","currentTime","muted","duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable","currentSrc","preload","bufferedBytes","bufferedTime","initialTime","startOffsetTime","defaultPlaybackRate","playbackRate","played","autoplay","loop","controls"],readOnlyProperties:["duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable"],methods:["load","play","pause","canPlayType"],events:["loadstart","durationchange","loadedmetadata","loadeddata","progress","canplay","canplaythrough","suspend","abort","error","emptied","stalled","play","playing","pause","waiting","seeking","seeked","timeupdate","ended","ratechange","volumechange"],mediaTypes:["audio/mp3","audio/ogg","audio/oga","audio/wav","audio/x-wav","audio/wave","audio/x-pn-wav","audio/mpeg","audio/mp4","video/mp4","video/webm","video/ogg","video/ogv"]},A.default.mejs=g,b.default=g},{3:3}],8:[function(d,E,b){"use strict";function A(d,E){if(!(d instanceof E))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(b,"__esModule",{value:!0}),b.renderer=void 0;var g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},T=function(){function d(d,E){for(var b=0;b<E.length;b++){var A=E[b];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(d,A.key,A)}}return function(E,b,A){return b&&d(E.prototype,b),A&&d(E,A),E}}(),bV=function(d){return d&&d.__esModule?d:{default:d}}(d(7)),dO=function(){function d(){A(this,d),this.renderers={},this.order=[]}return T(d,[{key:"add",value:function(d){if(void 0===d.name)throw new TypeError("renderer must contain at least `name` property");this.renderers[d.name]=d,this.order.push(d.name)}},{key:"select",value:function(d){var E=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],b=E.length;if(E=E.length?E:this.order,!b){var A=[/^(html5|native)/i,/^flash/i,/iframe$/i],g=function(d){for(var E=0,b=A.length;E<b;E++)if(A[E].test(d))return E;return A.length};E.sort(function(d,E){return g(d)-g(E)})}for(var T=0,bV=E.length;T<bV;T++){var dO=E[T],a=this.renderers[dO];if(null!==a&&void 0!==a)for(var dd=0,W=d.length;dd<W;dd++)if("function"==typeof a.canPlayType&&"string"==typeof d[dd].type&&a.canPlayType(d[dd].type))return{rendererName:a.name,src:d[dd].src}}return null}},{key:"order",set:function(d){if(!Array.isArray(d))throw new TypeError("order must be an array of strings.");this._order=d},get:function(){return this._order}},{key:"renderers",set:function(d){if(null!==d&&"object"!==(void 0===d?"undefined":g(d)))throw new TypeError("renderers must be an array of objects.");this._renderers=d},get:function(){return this._renderers}}]),d}(),a=b.renderer=new dO;bV.default.Renderers=a},{7:7}],9:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(3)),T=A(d(2)),bV=A(d(5)),dO=d(16),a=A(dO),dd=function(d){if(d&&d.__esModule)return d;var E={};if(null!=d)for(var b in d)Object.prototype.hasOwnProperty.call(d,b)&&(E[b]=d[b]);return E.default=d,E}(d(25)),W=d(27),gP=d(26),e=d(28);Object.assign(dO.config,{usePluginFullScreen:!0,fullscreenText:null,useFakeFullscreen:!1}),Object.assign(a.default.prototype,{isFullScreen:!1,isNativeFullScreen:!1,isInIframe:!1,isPluginClickThroughCreated:!1,fullscreenMode:"",containerSizeTimeout:null,buildfullscreen:function(d){if(d.isVideo){d.isInIframe=g.default.location!==g.default.parent.location,d.detectFullscreenMode();var E=this,b=(0,W.isString)(E.options.fullscreenText)?E.options.fullscreenText:bV.default.t("mejs.fullscreen"),A=T.default.createElement("div");if(A.className=E.options.classPrefix+"button "+E.options.classPrefix+"fullscreen-button",A.innerHTML='<button type="button" aria-controls="'+E.id+'" title="'+b+'" aria-label="'+b+'" tabindex="0"></button>',E.addControlElement(A,"fullscreen"),A.addEventListener("click",function(){dd.HAS_TRUE_NATIVE_FULLSCREEN&&dd.IS_FULLSCREEN||d.isFullScreen?d.exitFullScreen():d.enterFullScreen()}),d.fullscreenBtn=A,E.options.keyActions.push({keys:[70],action:function(d,E,b,A){A.ctrlKey||void 0!==d.enterFullScreen&&(d.isFullScreen?d.exitFullScreen():d.enterFullScreen())}}),E.exitFullscreenCallback=function(b){27===(b.which||b.keyCode||0)&&(dd.HAS_TRUE_NATIVE_FULLSCREEN&&dd.IS_FULLSCREEN||E.isFullScreen)&&d.exitFullScreen()},E.globalBind("keydown",E.exitFullscreenCallback),E.normalHeight=0,E.normalWidth=0,dd.HAS_TRUE_NATIVE_FULLSCREEN){d.globalBind(dd.FULLSCREEN_EVENT_NAME,function(){d.isFullScreen&&(dd.isFullScreen()?(d.isNativeFullScreen=!0,d.setControlsSize()):(d.isNativeFullScreen=!1,d.exitFullScreen()))})}}},cleanfullscreen:function(d){d.exitFullScreen(),d.globalUnbind("keydown",d.exitFullscreenCallback)},detectFullscreenMode:function(){var d=this,E=null!==d.media.rendererName&&/(native|html5)/i.test(d.media.rendererName),b="";return dd.HAS_TRUE_NATIVE_FULLSCREEN&&E?b="native-native":dd.HAS_TRUE_NATIVE_FULLSCREEN&&!E?b="plugin-native":d.usePluginFullScreen&&dd.SUPPORT_POINTER_EVENTS&&(b="plugin-click"),d.fullscreenMode=b,b},enterFullScreen:function(){var d=this,E=null!==d.media.rendererName&&/(html5|native)/i.test(d.media.rendererName),b=getComputedStyle(d.getElement(d.container));if(!1===d.options.useFakeFullscreen&&dd.IS_IOS&&dd.HAS_IOS_FULLSCREEN&&"function"==typeof d.media.originalNode.webkitEnterFullscreen&&d.media.originalNode.canPlayType((0,e.getTypeFromFile)(d.media.getSrc())))d.media.originalNode.webkitEnterFullscreen();else{if((0,gP.addClass)(T.default.documentElement,d.options.classPrefix+"fullscreen"),(0,gP.addClass)(d.getElement(d.container),d.options.classPrefix+"container-fullscreen"),d.normalHeight=parseFloat(b.height),d.normalWidth=parseFloat(b.width),"native-native"!==d.fullscreenMode&&"plugin-native"!==d.fullscreenMode||(dd.requestFullScreen(d.getElement(d.container)),d.isInIframe&&setTimeout(function E(){if(d.isNativeFullScreen){var b=g.default.innerWidth||T.default.documentElement.clientWidth||T.default.body.clientWidth,A=screen.width;Math.abs(A-b)>.002*A?d.exitFullScreen():setTimeout(E,500)}},1e3)),d.getElement(d.container).style.width="100%",d.getElement(d.container).style.height="100%",d.containerSizeTimeout=setTimeout(function(){d.getElement(d.container).style.width="100%",d.getElement(d.container).style.height="100%",d.setControlsSize()},500),E)d.node.style.width="100%",d.node.style.height="100%";else for(var A=d.getElement(d.container).querySelectorAll("embed, object, video"),bV=A.length,dO=0;dO<bV;dO++)A[dO].style.width="100%",A[dO].style.height="100%";d.options.setDimensions&&"function"==typeof d.media.setSize&&d.media.setSize(screen.width,screen.height);for(var a=d.getElement(d.layers).children,c=a.length,aA=0;aA<c;aA++)a[aA].style.width="100%",a[aA].style.height="100%";d.fullscreenBtn&&((0,gP.removeClass)(d.fullscreenBtn,d.options.classPrefix+"fullscreen"),(0,gP.addClass)(d.fullscreenBtn,d.options.classPrefix+"unfullscreen")),d.setControlsSize(),d.isFullScreen=!0;var cF=Math.min(screen.width/d.width,screen.height/d.height),ai=d.getElement(d.container).querySelector("."+d.options.classPrefix+"captions-text");ai&&(ai.style.fontSize=100*cF+"%",ai.style.lineHeight="normal",d.getElement(d.container).querySelector("."+d.options.classPrefix+"captions-position").style.bottom="45px");var df=(0,W.createEvent)("enteredfullscreen",d.getElement(d.container));d.getElement(d.container).dispatchEvent(df)}},exitFullScreen:function(){var d=this,E=null!==d.media.rendererName&&/(native|html5)/i.test(d.media.rendererName);if(clearTimeout(d.containerSizeTimeout),dd.HAS_TRUE_NATIVE_FULLSCREEN&&(dd.IS_FULLSCREEN||d.isFullScreen)&&dd.cancelFullScreen(),(0,gP.removeClass)(T.default.documentElement,d.options.classPrefix+"fullscreen"),(0,gP.removeClass)(d.getElement(d.container),d.options.classPrefix+"container-fullscreen"),d.options.setDimensions){if(d.getElement(d.container).style.width=d.normalWidth+"px",d.getElement(d.container).style.height=d.normalHeight+"px",E)d.node.style.width=d.normalWidth+"px",d.node.style.height=d.normalHeight+"px";else for(var b=d.getElement(d.container).querySelectorAll("embed, object, video"),A=b.length,g=0;g<A;g++)b[g].style.width=d.normalWidth+"px",b[g].style.height=d.normalHeight+"px";"function"==typeof d.media.setSize&&d.media.setSize(d.normalWidth,d.normalHeight);for(var bV=d.getElement(d.layers).children,dO=bV.length,a=0;a<dO;a++)bV[a].style.width=d.normalWidth+"px",bV[a].style.height=d.normalHeight+"px"}d.fullscreenBtn&&((0,gP.removeClass)(d.fullscreenBtn,d.options.classPrefix+"unfullscreen"),(0,gP.addClass)(d.fullscreenBtn,d.options.classPrefix+"fullscreen")),d.setControlsSize(),d.isFullScreen=!1;var e=d.getElement(d.container).querySelector("."+d.options.classPrefix+"captions-text");e&&(e.style.fontSize="",e.style.lineHeight="",d.getElement(d.container).querySelector("."+d.options.classPrefix+"captions-position").style.bottom="");var c=(0,W.createEvent)("exitedfullscreen",d.getElement(d.container));d.getElement(d.container).dispatchEvent(c)}})},{16:16,2:2,25:25,26:26,27:27,28:28,3:3,5:5}],10:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(2)),T=d(16),bV=A(T),dO=A(d(5)),a=d(27),dd=d(26);Object.assign(T.config,{playText:null,pauseText:null}),Object.assign(bV.default.prototype,{buildplaypause:function(d,E,b,A){function T(d){"play"===d?((0,dd.removeClass)(c,bV.options.classPrefix+"play"),(0,dd.removeClass)(c,bV.options.classPrefix+"replay"),(0,dd.addClass)(c,bV.options.classPrefix+"pause"),aA.setAttribute("title",e),aA.setAttribute("aria-label",e)):((0,dd.removeClass)(c,bV.options.classPrefix+"pause"),(0,dd.removeClass)(c,bV.options.classPrefix+"replay"),(0,dd.addClass)(c,bV.options.classPrefix+"play"),aA.setAttribute("title",gP),aA.setAttribute("aria-label",gP))}var bV=this,W=bV.options,gP=(0,a.isString)(W.playText)?W.playText:dO.default.t("mejs.play"),e=(0,a.isString)(W.pauseText)?W.pauseText:dO.default.t("mejs.pause"),c=g.default.createElement("div");c.className=bV.options.classPrefix+"button "+bV.options.classPrefix+"playpause-button "+bV.options.classPrefix+"play",c.innerHTML='<button type="button" aria-controls="'+bV.id+'" title="'+gP+'" aria-label="'+e+'" tabindex="0"></button>',c.addEventListener("click",function(){bV.paused?bV.play():bV.pause()});var aA=c.querySelector("button");bV.addControlElement(c,"playpause"),T("pse"),A.addEventListener("loadedmetadata",function(){-1===A.rendererName.indexOf("flash")&&T("pse")}),A.addEventListener("play",function(){T("play")}),A.addEventListener("playing",function(){T("play")}),A.addEventListener("pause",function(){T("pse")}),A.addEventListener("ended",function(){d.options.loop||((0,dd.removeClass)(c,bV.options.classPrefix+"pause"),(0,dd.removeClass)(c,bV.options.classPrefix+"play"),(0,dd.addClass)(c,bV.options.classPrefix+"replay"),aA.setAttribute("title",gP),aA.setAttribute("aria-label",gP))})}})},{16:16,2:2,26:26,27:27,5:5}],11:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(2)),T=d(16),bV=A(T),dO=A(d(5)),a=d(25),dd=d(30),W=d(26);Object.assign(T.config,{enableProgressTooltip:!0,useSmoothHover:!0,forceLive:!1}),Object.assign(bV.default.prototype,{buildprogress:function(d,E,b,A){var bV=0,gP=!1,e=!1,c=this,aA=d.options.autoRewind,cF=d.options.enableProgressTooltip?'<span class="'+c.options.classPrefix+'time-float"><span class="'+c.options.classPrefix+'time-float-current">00:00</span><span class="'+c.options.classPrefix+'time-float-corner"></span></span>':"",ai=g.default.createElement("div");ai.className=c.options.classPrefix+"time-rail",ai.innerHTML='<span class="'+c.options.classPrefix+"time-total "+c.options.classPrefix+'time-slider"><span class="'+c.options.classPrefix+'time-buffering"></span><span class="'+c.options.classPrefix+'time-loaded"></span><span class="'+c.options.classPrefix+'time-current"></span><span class="'+c.options.classPrefix+'time-hovered no-hover"></span><span class="'+c.options.classPrefix+'time-handle"><span class="'+c.options.classPrefix+'time-handle-content"></span></span>'+cF+"</span>",c.addControlElement(ai,"progress"),c.options.keyActions.push({keys:[37,227],action:function(d){if(!isNaN(d.duration)&&d.duration>0){d.isVideo&&(d.showControls(),d.startControlsTimer()),d.getElement(d.container).querySelector("."+T.config.classPrefix+"time-total").focus();var E=Math.max(d.currentTime-d.options.defaultSeekBackwardInterval(d),0);d.setCurrentTime(E)}}},{keys:[39,228],action:function(d){if(!isNaN(d.duration)&&d.duration>0){d.isVideo&&(d.showControls(),d.startControlsTimer()),d.getElement(d.container).querySelector("."+T.config.classPrefix+"time-total").focus();var E=Math.min(d.currentTime+d.options.defaultSeekForwardInterval(d),d.duration);d.setCurrentTime(E)}}}),c.rail=E.querySelector("."+c.options.classPrefix+"time-rail"),c.total=E.querySelector("."+c.options.classPrefix+"time-total"),c.loaded=E.querySelector("."+c.options.classPrefix+"time-loaded"),c.current=E.querySelector("."+c.options.classPrefix+"time-current"),c.handle=E.querySelector("."+c.options.classPrefix+"time-handle"),c.timefloat=E.querySelector("."+c.options.classPrefix+"time-float"),c.timefloatcurrent=E.querySelector("."+c.options.classPrefix+"time-float-current"),c.slider=E.querySelector("."+c.options.classPrefix+"time-slider"),c.hovered=E.querySelector("."+c.options.classPrefix+"time-hovered"),c.buffer=E.querySelector("."+c.options.classPrefix+"time-buffering"),c.newTime=0,c.forcedHandlePause=!1,c.setTransformStyle=function(d,E){d.style.transform=E,d.style.webkitTransform=E,d.style.MozTransform=E,d.style.msTransform=E,d.style.OTransform=E},c.buffer.style.display="none";var df=function(E){var b=getComputedStyle(c.total),A=(0,W.offset)(c.total),g=c.total.offsetWidth,T=void 0!==b.webkitTransform?"webkitTransform":void 0!==b.mozTransform?"mozTransform ":void 0!==b.oTransform?"oTransform":void 0!==b.msTransform?"msTransform":"transform",bV="WebKitCSSMatrix"in window?"WebKitCSSMatrix":"MSCSSMatrix"in window?"MSCSSMatrix":"CSSMatrix"in window?"CSSMatrix":void 0,dO=0,e=0,aA=0,cF=void 0;if(cF=E.originalEvent&&E.originalEvent.changedTouches?E.originalEvent.changedTouches[0].pageX:E.changedTouches?E.changedTouches[0].pageX:E.pageX,c.getDuration()){if(cF<A.left?cF=A.left:cF>g+A.left&&(cF=g+A.left),aA=cF-A.left,dO=aA/g,c.newTime=dO<=.02?0:dO*c.getDuration(),gP&&null!==c.getCurrentTime()&&c.newTime.toFixed(4)!==c.getCurrentTime().toFixed(4)&&(c.setCurrentRailHandle(c.newTime),c.updateCurrent(c.newTime)),!a.IS_IOS&&!a.IS_ANDROID){if(aA<0&&(aA=0),c.options.useSmoothHover&&null!==bV&&void 0!==window[bV]){var ai=new window[bV](getComputedStyle(c.handle)[T]).m41,df=aA/parseFloat(getComputedStyle(c.total).width)-ai/parseFloat(getComputedStyle(c.total).width);c.hovered.style.left=ai+"px",c.setTransformStyle(c.hovered,"scaleX("+df+")"),c.hovered.setAttribute("pos",aA),df>=0?(0,W.removeClass)(c.hovered,"negative"):(0,W.addClass)(c.hovered,"negative")}if(c.timefloat){var eK=c.timefloat.offsetWidth/2,eI=mejs.Utils.offset(c.getElement(c.container)),eU=getComputedStyle(c.timefloat);e=cF-eI.left<c.timefloat.offsetWidth?eK:cF-eI.left>=c.getElement(c.container).offsetWidth-eK?c.total.offsetWidth-eK:aA,(0,W.hasClass)(c.getElement(c.container),c.options.classPrefix+"long-video")&&(e+=parseFloat(eU.marginLeft)/2+c.timefloat.offsetWidth/2),c.timefloat.style.left=e+"px",c.timefloatcurrent.innerHTML=(0,dd.secondsToTimeCode)(c.newTime,d.options.alwaysShowHours,d.options.showTimecodeFrameCount,d.options.framesPerSecond,d.options.secondsDecimalLength,d.options.timeFormat),c.timefloat.style.display="block"}}}else a.IS_IOS||a.IS_ANDROID||!c.timefloat||(e=c.timefloat.offsetWidth+g>=c.getElement(c.container).offsetWidth?c.timefloat.offsetWidth/2:0,c.timefloat.style.left=e+"px",c.timefloat.style.left=e+"px",c.timefloat.style.display="block")},eK=function(){var E=c.getCurrentTime(),b=dO.default.t("mejs.time-slider"),g=(0,dd.secondsToTimeCode)(E,d.options.alwaysShowHours,d.options.showTimecodeFrameCount,d.options.framesPerSecond,d.options.secondsDecimalLength,d.options.timeFormat),T=c.getDuration();c.slider.setAttribute("role","slider"),c.slider.tabIndex=0,A.paused?(c.slider.setAttribute("aria-label",b),c.slider.setAttribute("aria-valuemin",0),c.slider.setAttribute("aria-valuemax",T),c.slider.setAttribute("aria-valuenow",E),c.slider.setAttribute("aria-valuetext",g)):(c.slider.removeAttribute("aria-label"),c.slider.removeAttribute("aria-valuemin"),c.slider.removeAttribute("aria-valuemax"),c.slider.removeAttribute("aria-valuenow"),c.slider.removeAttribute("aria-valuetext"))},eI=function(){new Date-bV>=1e3&&c.play()},eU=function(){gP&&null!==c.getCurrentTime()&&c.newTime.toFixed(4)!==c.getCurrentTime().toFixed(4)&&(c.setCurrentTime(c.newTime),c.setCurrentRail(),c.updateCurrent(c.newTime)),c.forcedHandlePause&&(c.slider.focus(),c.play()),c.forcedHandlePause=!1};c.slider.addEventListener("focus",function(){d.options.autoRewind=!1}),c.slider.addEventListener("blur",function(){d.options.autoRewind=aA}),c.slider.addEventListener("keydown",function(E){if(new Date-bV>=1e3&&(e=c.paused),c.options.keyActions.length){var b=E.which||E.keyCode||0,g=c.getDuration(),T=d.options.defaultSeekForwardInterval(A),dO=d.options.defaultSeekBackwardInterval(A),dd=c.getCurrentTime(),W=c.getElement(c.container).querySelector("."+c.options.classPrefix+"volume-slider");if(38===b||40===b){W&&(W.style.display="block"),c.isVideo&&(c.showControls(),c.startControlsTimer());var gP=38===b?Math.min(c.volume+.1,1):Math.max(c.volume-.1,0),aA=gP<=0;return c.setVolume(gP),void c.setMuted(aA)}switch(W&&(W.style.display="none"),b){case 37:c.getDuration()!==1/0&&(dd-=dO);break;case 39:c.getDuration()!==1/0&&(dd+=T);break;case 36:dd=0;break;case 35:dd=g;break;case 13:case 32:return void(a.IS_FIREFOX&&(c.paused?c.play():c.pause()));default:return}dd=dd<0?0:dd>=g?g:Math.floor(dd),bV=new Date,e||d.pause(),dd<c.getDuration()&&!e&&setTimeout(eI,1100),c.setCurrentTime(dd),d.showControls(),E.preventDefault(),E.stopPropagation()}});var gT=["mousedown","touchstart"];c.slider.addEventListener("dragstart",function(){return!1});for(var ej=0,eh=gT.length;ej<eh;ej++)c.slider.addEventListener(gT[ej],function(d){if(c.forcedHandlePause=!1,c.getDuration()!==1/0&&(1===d.which||0===d.which)){c.paused||(c.pause(),c.forcedHandlePause=!0),gP=!0,df(d);for(var E=["mouseup","touchend"],b=0,A=E.length;b<A;b++)c.getElement(c.container).addEventListener(E[b],function(d){var E=d.target;(E===c.slider||E.closest("."+c.options.classPrefix+"time-slider"))&&df(d)});c.globalBind("mouseup.dur touchend.dur",function(){eU(),gP=!1,c.timefloat&&(c.timefloat.style.display="none")})}},!(!a.SUPPORT_PASSIVE_EVENT||"touchstart"!==gT[ej])&&{passive:!0});c.slider.addEventListener("mouseenter",function(d){d.target===c.slider&&c.getDuration()!==1/0&&(c.getElement(c.container).addEventListener("mousemove",function(d){var E=d.target;(E===c.slider||E.closest("."+c.options.classPrefix+"time-slider"))&&df(d)}),!c.timefloat||a.IS_IOS||a.IS_ANDROID||(c.timefloat.style.display="block"),c.hovered&&!a.IS_IOS&&!a.IS_ANDROID&&c.options.useSmoothHover&&(0,W.removeClass)(c.hovered,"no-hover"))}),c.slider.addEventListener("mouseleave",function(){c.getDuration()!==1/0&&(gP||(c.timefloat&&(c.timefloat.style.display="none"),c.hovered&&c.options.useSmoothHover&&(0,W.addClass)(c.hovered,"no-hover")))}),c.broadcastCallback=function(b){var A=E.querySelector("."+c.options.classPrefix+"broadcast");if(c.options.forceLive||c.getDuration()===1/0){if(!A||c.options.forceLive){var T=g.default.createElement("span");T.className=c.options.classPrefix+"broadcast",T.innerText=dO.default.t("mejs.live-broadcast"),c.slider.style.display="none",c.rail.appendChild(T)}}else A&&(c.slider.style.display="",A.remove()),d.setProgressRail(b),c.forcedHandlePause||d.setCurrentRail(b),eK()},A.addEventListener("progress",c.broadcastCallback),A.addEventListener("timeupdate",c.broadcastCallback),A.addEventListener("play",function(){c.buffer.style.display="none"}),A.addEventListener("playing",function(){c.buffer.style.display="none"}),A.addEventListener("seeking",function(){c.buffer.style.display=""}),A.addEventListener("seeked",function(){c.buffer.style.display="none"}),A.addEventListener("pause",function(){c.buffer.style.display="none"}),A.addEventListener("waiting",function(){c.buffer.style.display=""}),A.addEventListener("loadeddata",function(){c.buffer.style.display=""}),A.addEventListener("canplay",function(){c.buffer.style.display="none"}),A.addEventListener("error",function(){c.buffer.style.display="none"}),c.getElement(c.container).addEventListener("controlsresize",function(E){c.getDuration()!==1/0&&(d.setProgressRail(E),c.forcedHandlePause||d.setCurrentRail(E))})},cleanprogress:function(d,E,b,A){A.removeEventListener("progress",d.broadcastCallback),A.removeEventListener("timeupdate",d.broadcastCallback),d.rail&&d.rail.remove()},setProgressRail:function(d){var E=this,b=void 0!==d?d.detail.target||d.target:E.media,A=null;b&&b.buffered&&b.buffered.length>0&&b.buffered.end&&E.getDuration()?A=b.buffered.end(b.buffered.length-1)/E.getDuration():b&&void 0!==b.bytesTotal&&b.bytesTotal>0&&void 0!==b.bufferedBytes?A=b.bufferedBytes/b.bytesTotal:d&&d.lengthComputable&&0!==d.total&&(A=d.loaded/d.total),null!==A&&(A=Math.min(1,Math.max(0,A)),E.loaded&&E.setTransformStyle(E.loaded,"scaleX("+A+")"))},setCurrentRailHandle:function(d){var E=this;E.setCurrentRailMain(E,d)},setCurrentRail:function(){var d=this;d.setCurrentRailMain(d)},setCurrentRailMain:function(d,E){if(void 0!==d.getCurrentTime()&&d.getDuration()){var b=void 0===E?d.getCurrentTime():E;if(d.total&&d.handle){var A=parseFloat(getComputedStyle(d.total).width),g=Math.round(A*b/d.getDuration()),T=g-Math.round(d.handle.offsetWidth/2);if(T=T<0?0:T,d.setTransformStyle(d.current,"scaleX("+g/A+")"),d.setTransformStyle(d.handle,"translateX("+T+"px)"),d.options.useSmoothHover&&!(0,W.hasClass)(d.hovered,"no-hover")){var bV=parseInt(d.hovered.getAttribute("pos"),10),dO=(bV=isNaN(bV)?0:bV)/A-T/A;d.hovered.style.left=T+"px",d.setTransformStyle(d.hovered,"scaleX("+dO+")"),dO>=0?(0,W.removeClass)(d.hovered,"negative"):(0,W.addClass)(d.hovered,"negative")}}}}})},{16:16,2:2,25:25,26:26,30:30,5:5}],12:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(2)),T=d(16),bV=A(T),dO=d(30),a=d(26);Object.assign(T.config,{duration:0,timeAndDurationSeparator:"<span> | </span>"}),Object.assign(bV.default.prototype,{buildcurrent:function(d,E,b,A){var T=this,bV=g.default.createElement("div");bV.className=T.options.classPrefix+"time",bV.setAttribute("role","timer"),bV.setAttribute("aria-live","off"),bV.innerHTML='<span class="'+T.options.classPrefix+'currenttime">'+(0,dO.secondsToTimeCode)(0,d.options.alwaysShowHours,d.options.showTimecodeFrameCount,d.options.framesPerSecond,d.options.secondsDecimalLength,d.options.timeFormat)+"</span>",T.addControlElement(bV,"current"),d.updateCurrent(),T.updateTimeCallback=function(){T.controlsAreVisible&&d.updateCurrent()},A.addEventListener("timeupdate",T.updateTimeCallback)},cleancurrent:function(d,E,b,A){A.removeEventListener("timeupdate",d.updateTimeCallback)},buildduration:function(d,E,b,A){var T=this;if(E.lastChild.querySelector("."+T.options.classPrefix+"currenttime"))E.querySelector("."+T.options.classPrefix+"time").innerHTML+=T.options.timeAndDurationSeparator+'<span class="'+T.options.classPrefix+'duration">'+(0,dO.secondsToTimeCode)(T.options.duration,T.options.alwaysShowHours,T.options.showTimecodeFrameCount,T.options.framesPerSecond,T.options.secondsDecimalLength,T.options.timeFormat)+"</span>";else{E.querySelector("."+T.options.classPrefix+"currenttime")&&(0,a.addClass)(E.querySelector("."+T.options.classPrefix+"currenttime").parentNode,T.options.classPrefix+"currenttime-container");var bV=g.default.createElement("div");bV.className=T.options.classPrefix+"time "+T.options.classPrefix+"duration-container",bV.innerHTML='<span class="'+T.options.classPrefix+'duration">'+(0,dO.secondsToTimeCode)(T.options.duration,T.options.alwaysShowHours,T.options.showTimecodeFrameCount,T.options.framesPerSecond,T.options.secondsDecimalLength,T.options.timeFormat)+"</span>",T.addControlElement(bV,"duration")}T.updateDurationCallback=function(){T.controlsAreVisible&&d.updateDuration()},A.addEventListener("timeupdate",T.updateDurationCallback)},cleanduration:function(d,E,b,A){A.removeEventListener("timeupdate",d.updateDurationCallback)},updateCurrent:function(){var d=this,E=d.getCurrentTime();isNaN(E)&&(E=0);var b=(0,dO.secondsToTimeCode)(E,d.options.alwaysShowHours,d.options.showTimecodeFrameCount,d.options.framesPerSecond,d.options.secondsDecimalLength,d.options.timeFormat);b.length>5?(0,a.addClass)(d.getElement(d.container),d.options.classPrefix+"long-video"):(0,a.removeClass)(d.getElement(d.container),d.options.classPrefix+"long-video"),d.getElement(d.controls).querySelector("."+d.options.classPrefix+"currenttime")&&(d.getElement(d.controls).querySelector("."+d.options.classPrefix+"currenttime").innerText=b)},updateDuration:function(){var d=this,E=d.getDuration();(isNaN(E)||E===1/0||E<0)&&(d.media.duration=d.options.duration=E=0),d.options.duration>0&&(E=d.options.duration);var b=(0,dO.secondsToTimeCode)(E,d.options.alwaysShowHours,d.options.showTimecodeFrameCount,d.options.framesPerSecond,d.options.secondsDecimalLength,d.options.timeFormat);b.length>5?(0,a.addClass)(d.getElement(d.container),d.options.classPrefix+"long-video"):(0,a.removeClass)(d.getElement(d.container),d.options.classPrefix+"long-video"),d.getElement(d.controls).querySelector("."+d.options.classPrefix+"duration")&&E>0&&(d.getElement(d.controls).querySelector("."+d.options.classPrefix+"duration").innerHTML=b)}})},{16:16,2:2,26:26,30:30}],13:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(2)),T=A(d(7)),bV=A(d(5)),dO=d(16),a=A(dO),dd=d(30),W=d(27),gP=d(26);Object.assign(dO.config,{startLanguage:"",tracksText:null,chaptersText:null,tracksAriaLive:!1,hideCaptionsButtonWhenEmpty:!0,toggleCaptionsButtonWhenOnlyOne:!1,slidesSelector:""}),Object.assign(a.default.prototype,{hasChapters:!1,buildtracks:function(d,E,b,A){if(this.findTracks(),d.tracks.length||d.trackFiles&&0!==!d.trackFiles.length){var T=this,dO=T.options.tracksAriaLive?' role="log" aria-live="assertive" aria-atomic="false"':"",a=(0,W.isString)(T.options.tracksText)?T.options.tracksText:bV.default.t("mejs.captions-subtitles"),dd=(0,W.isString)(T.options.chaptersText)?T.options.chaptersText:bV.default.t("mejs.captions-chapters"),e=null===d.trackFiles?d.tracks.length:d.trackFiles.length;if(T.domNode.textTracks)for(var c=T.domNode.textTracks.length-1;c>=0;c--)T.domNode.textTracks[c].mode="hidden";T.cleartracks(d),d.captions=g.default.createElement("div"),d.captions.className=T.options.classPrefix+"captions-layer "+T.options.classPrefix+"layer",d.captions.innerHTML='<div class="'+T.options.classPrefix+"captions-position "+T.options.classPrefix+'captions-position-hover"'+dO+'><span class="'+T.options.classPrefix+'captions-text"></span></div>',d.captions.style.display="none",b.insertBefore(d.captions,b.firstChild),d.captionsText=d.captions.querySelector("."+T.options.classPrefix+"captions-text"),d.captionsButton=g.default.createElement("div"),d.captionsButton.className=T.options.classPrefix+"button "+T.options.classPrefix+"captions-button",d.captionsButton.innerHTML='<button type="button" aria-controls="'+T.id+'" title="'+a+'" aria-label="'+a+'" tabindex="0"></button><div class="'+T.options.classPrefix+"captions-selector "+T.options.classPrefix+'offscreen"><ul class="'+T.options.classPrefix+'captions-selector-list"><li class="'+T.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+T.options.classPrefix+'captions-selector-input" name="'+d.id+'_captions" id="'+d.id+'_captions_none" value="none" checked disabled><label class="'+T.options.classPrefix+"captions-selector-label "+T.options.classPrefix+'captions-selected" for="'+d.id+'_captions_none">'+bV.default.t("mejs.none")+"</label></li></ul></div>",T.addControlElement(d.captionsButton,"tracks"),d.captionsButton.querySelector("."+T.options.classPrefix+"captions-selector-input").disabled=!1,d.chaptersButton=g.default.createElement("div"),d.chaptersButton.className=T.options.classPrefix+"button "+T.options.classPrefix+"chapters-button",d.chaptersButton.innerHTML='<button type="button" aria-controls="'+T.id+'" title="'+dd+'" aria-label="'+dd+'" tabindex="0"></button><div class="'+T.options.classPrefix+"chapters-selector "+T.options.classPrefix+'offscreen"><ul class="'+T.options.classPrefix+'chapters-selector-list"></ul></div>';for(var aA=0,cF=0;cF<e;cF++){var ai=d.tracks[cF].kind;d.tracks[cF].src.trim()&&("subtitles"===ai||"captions"===ai?aA++:"chapters"!==ai||E.querySelector("."+T.options.classPrefix+"chapter-selector")||d.captionsButton.parentNode.insertBefore(d.chaptersButton,d.captionsButton))}d.trackToLoad=-1,d.selectedTrack=null,d.isLoadingTrack=!1;for(var df=0;df<e;df++){var eK=d.tracks[df].kind;!d.tracks[df].src.trim()||"subtitles"!==eK&&"captions"!==eK||d.addTrackButton(d.tracks[df].trackId,d.tracks[df].srclang,d.tracks[df].label)}d.loadNextTrack();var eI=["mouseenter","focusin"],eU=["mouseleave","focusout"];if(T.options.toggleCaptionsButtonWhenOnlyOne&&1===aA)d.captionsButton.addEventListener("click",function(E){var b="none";null===d.selectedTrack&&(b=d.tracks[0].trackId);var A=E.keyCode||E.which;d.setTrack(b,void 0!==A)});else{for(var gT=d.captionsButton.querySelectorAll("."+T.options.classPrefix+"captions-selector-label"),ej=d.captionsButton.querySelectorAll("input[type=radio]"),eh=0,eY=eI.length;eh<eY;eh++)d.captionsButton.addEventListener(eI[eh],function(){(0,gP.removeClass)(this.querySelector("."+T.options.classPrefix+"captions-selector"),T.options.classPrefix+"offscreen")});for(var ed=0,gX=eU.length;ed<gX;ed++)d.captionsButton.addEventListener(eU[ed],function(){(0,gP.addClass)(this.querySelector("."+T.options.classPrefix+"captions-selector"),T.options.classPrefix+"offscreen")});for(var aW=0,ddc=ej.length;aW<ddc;aW++)ej[aW].addEventListener("click",function(E){var b=E.keyCode||E.which;d.setTrack(this.value,void 0!==b)});for(var Q=0,dh=gT.length;Q<dh;Q++)gT[Q].addEventListener("click",function(d){var E=(0,gP.siblings)(this,function(d){return"INPUT"===d.tagName})[0],b=(0,W.createEvent)("click",E);E.dispatchEvent(b),d.preventDefault()});d.captionsButton.addEventListener("keydown",function(d){d.stopPropagation()})}for(var bA=0,cK=eI.length;bA<cK;bA++)d.chaptersButton.addEventListener(eI[bA],function(){this.querySelector("."+T.options.classPrefix+"chapters-selector-list").children.length&&(0,gP.removeClass)(this.querySelector("."+T.options.classPrefix+"chapters-selector"),T.options.classPrefix+"offscreen")});for(var f=0,M=eU.length;f<M;f++)d.chaptersButton.addEventListener(eU[f],function(){(0,gP.addClass)(this.querySelector("."+T.options.classPrefix+"chapters-selector"),T.options.classPrefix+"offscreen")});d.chaptersButton.addEventListener("keydown",function(d){d.stopPropagation()}),d.options.alwaysShowControls?(0,gP.addClass)(d.getElement(d.container).querySelector("."+T.options.classPrefix+"captions-position"),T.options.classPrefix+"captions-position-hover"):(d.getElement(d.container).addEventListener("controlsshown",function(){(0,gP.addClass)(d.getElement(d.container).querySelector("."+T.options.classPrefix+"captions-position"),T.options.classPrefix+"captions-position-hover")}),d.getElement(d.container).addEventListener("controlshidden",function(){A.paused||(0,gP.removeClass)(d.getElement(d.container).querySelector("."+T.options.classPrefix+"captions-position"),T.options.classPrefix+"captions-position-hover")})),A.addEventListener("timeupdate",function(){d.displayCaptions()}),""!==d.options.slidesSelector&&(d.slidesContainer=g.default.querySelectorAll(d.options.slidesSelector),A.addEventListener("timeupdate",function(){d.displaySlides()}))}},cleartracks:function(d){d&&(d.captions&&d.captions.remove(),d.chapters&&d.chapters.remove(),d.captionsText&&d.captionsText.remove(),d.captionsButton&&d.captionsButton.remove(),d.chaptersButton&&d.chaptersButton.remove())},rebuildtracks:function(){var d=this;d.findTracks(),d.buildtracks(d,d.getElement(d.controls),d.getElement(d.layers),d.media)},findTracks:function(){var d=this,E=null===d.trackFiles?d.node.querySelectorAll("track"):d.trackFiles,b=E.length;d.tracks=[];for(var A=0;A<b;A++){var g=E[A],T=g.getAttribute("srclang").toLowerCase()||"",bV=d.id+"_track_"+A+"_"+g.getAttribute("kind")+"_"+T;d.tracks.push({trackId:bV,srclang:T,src:g.getAttribute("src"),kind:g.getAttribute("kind"),label:g.getAttribute("label")||"",entries:[],isLoaded:!1})}},setTrack:function(d,E){for(var b=this,A=b.captionsButton.querySelectorAll('input[type="radio"]'),g=b.captionsButton.querySelectorAll("."+b.options.classPrefix+"captions-selected"),T=b.captionsButton.querySelector('input[value="'+d+'"]'),bV=0,dO=A.length;bV<dO;bV++)A[bV].checked=!1;for(var a=0,dd=g.length;a<dd;a++)(0,gP.removeClass)(g[a],b.options.classPrefix+"captions-selected");T.checked=!0;for(var e=(0,gP.siblings)(T,function(d){return(0,gP.hasClass)(d,b.options.classPrefix+"captions-selector-label")}),c=0,aA=e.length;c<aA;c++)(0,gP.addClass)(e[c],b.options.classPrefix+"captions-selected");if("none"===d)b.selectedTrack=null,(0,gP.removeClass)(b.captionsButton,b.options.classPrefix+"captions-enabled");else for(var cF=0,ai=b.tracks.length;cF<ai;cF++){var df=b.tracks[cF];if(df.trackId===d){null===b.selectedTrack&&(0,gP.addClass)(b.captionsButton,b.options.classPrefix+"captions-enabled"),b.selectedTrack=df,b.captions.setAttribute("lang",b.selectedTrack.srclang),b.displayCaptions();break}}var eK=(0,W.createEvent)("captionschange",b.media);eK.detail.caption=b.selectedTrack,b.media.dispatchEvent(eK),E||setTimeout(function(){b.getElement(b.container).focus()},500)},loadNextTrack:function(){var d=this;d.trackToLoad++,d.trackToLoad<d.tracks.length?(d.isLoadingTrack=!0,d.loadTrack(d.trackToLoad)):(d.isLoadingTrack=!1,d.checkForTracks())},loadTrack:function(d){var E=this,b=E.tracks[d];void 0===b||void 0===b.src&&""===b.src||(0,gP.ajax)(b.src,"text",function(d){b.entries="string"==typeof d&&/<tt\s+xml/gi.exec(d)?T.default.TrackFormatParser.dfxp.parse(d):T.default.TrackFormatParser.webvtt.parse(d),b.isLoaded=!0,E.enableTrackButton(b),E.loadNextTrack(),"slides"===b.kind?E.setupSlides(b):"chapters"!==b.kind||E.hasChapters||(E.drawChapters(b),E.hasChapters=!0)},function(){E.removeTrackButton(b.trackId),E.loadNextTrack()})},enableTrackButton:function(d){var E=this,b=d.srclang,A=g.default.getElementById(""+d.trackId);if(A){var dO=d.label;""===dO&&(dO=bV.default.t(T.default.language.codes[b])||b),A.disabled=!1;for(var a=(0,gP.siblings)(A,function(d){return(0,gP.hasClass)(d,E.options.classPrefix+"captions-selector-label")}),dd=0,e=a.length;dd<e;dd++)a[dd].innerHTML=dO;if(E.options.startLanguage===b){A.checked=!0;var c=(0,W.createEvent)("click",A);A.dispatchEvent(c)}}},removeTrackButton:function(d){var E=g.default.getElementById(""+d);if(E){var b=E.closest("li");b&&b.remove()}},addTrackButton:function(d,E,b){var A=this;""===b&&(b=bV.default.t(T.default.language.codes[E])||E),A.captionsButton.querySelector("ul").innerHTML+='<li class="'+A.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+A.options.classPrefix+'captions-selector-input" name="'+A.id+'_captions" id="'+d+'" value="'+d+'" disabled><label class="'+A.options.classPrefix+'captions-selector-label"for="'+d+'">'+b+" (loading)</label></li>"},checkForTracks:function(){var d=this,E=!1;if(d.options.hideCaptionsButtonWhenEmpty){for(var b=0,A=d.tracks.length;b<A;b++){var g=d.tracks[b].kind;if(("subtitles"===g||"captions"===g)&&d.tracks[b].isLoaded){E=!0;break}}d.captionsButton.style.display=E?"":"none",d.setControlsSize()}},displayCaptions:function(){if(void 0!==this.tracks){var d=this,E=d.selectedTrack;if(null!==E&&E.isLoaded){var b=d.searchTrackPosition(E.entries,d.media.currentTime);if(b>-1)return d.captionsText.innerHTML=function(d){var E=g.default.createElement("div");E.innerHTML=d;for(var b=E.getElementsByTagName("script"),A=b.length;A--;)b[A].remove();for(var T=E.getElementsByTagName("*"),bV=0,dO=T.length;bV<dO;bV++)for(var a=T[bV].attributes,dd=Array.prototype.slice.call(a),W=0,gP=dd.length;W<gP;W++)dd[W].name.startsWith("on")||dd[W].value.startsWith("javascript")?T[bV].remove():"style"===dd[W].name&&T[bV].removeAttribute(dd[W].name);return E.innerHTML}(E.entries[b].text),d.captionsText.className=d.options.classPrefix+"captions-text "+(E.entries[b].identifier||""),d.captions.style.display="",void(d.captions.style.height="0px");d.captions.style.display="none"}else d.captions.style.display="none"}},setupSlides:function(d){var E=this;E.slides=d,E.slides.entries.imgs=[E.slides.entries.length],E.showSlide(0)},showSlide:function(d){var E=this,b=this;if(void 0!==b.tracks&&void 0!==b.slidesContainer){var A=b.slides.entries[d].text,T=b.slides.entries[d].imgs;if(void 0===T||void 0===T.fadeIn){var bV=g.default.createElement("img");bV.src=A,bV.addEventListener("load",function(){var d=E,A=(0,gP.siblings)(d,function(d){return A(d)});d.style.display="none",b.slidesContainer.innerHTML+=d.innerHTML,(0,gP.fadeIn)(b.slidesContainer.querySelector(bV));for(var g=0,T=A.length;g<T;g++)(0,gP.fadeOut)(A[g],400)}),b.slides.entries[d].imgs=T=bV}else if(!(0,gP.visible)(T)){var dO=(0,gP.siblings)(self,function(d){return dO(d)});(0,gP.fadeIn)(b.slidesContainer.querySelector(T));for(var a=0,dd=dO.length;a<dd;a++)(0,gP.fadeOut)(dO[a])}}},displaySlides:function(){var d=this;if(void 0!==this.slides){var E=d.slides,b=d.searchTrackPosition(E.entries,d.media.currentTime);b>-1&&d.showSlide(b)}},drawChapters:function(d){var E=this,b=d.entries.length;if(b){E.chaptersButton.querySelector("ul").innerHTML="";for(var A=0;A<b;A++)E.chaptersButton.querySelector("ul").innerHTML+='<li class="'+E.options.classPrefix+'chapters-selector-list-item" role="menuitemcheckbox" aria-live="polite" aria-disabled="false" aria-checked="false"><input type="radio" class="'+E.options.classPrefix+'captions-selector-input" name="'+E.id+'_chapters" id="'+E.id+"_chapters_"+A+'" value="'+d.entries[A].start+'" disabled><label class="'+E.options.classPrefix+'chapters-selector-label"for="'+E.id+"_chapters_"+A+'">'+d.entries[A].text+"</label></li>";for(var g=E.chaptersButton.querySelectorAll('input[type="radio"]'),T=E.chaptersButton.querySelectorAll("."+E.options.classPrefix+"chapters-selector-label"),bV=0,dO=g.length;bV<dO;bV++)g[bV].disabled=!1,g[bV].checked=!1,g[bV].addEventListener("click",function(d){var b=this,A=E.chaptersButton.querySelectorAll("li"),g=(0,gP.siblings)(b,function(d){return(0,gP.hasClass)(d,E.options.classPrefix+"chapters-selector-label")})[0];b.checked=!0,b.parentNode.setAttribute("aria-checked",!0),(0,gP.addClass)(g,E.options.classPrefix+"chapters-selected"),(0,gP.removeClass)(E.chaptersButton.querySelector("."+E.options.classPrefix+"chapters-selected"),E.options.classPrefix+"chapters-selected");for(var T=0,bV=A.length;T<bV;T++)A[T].setAttribute("aria-checked",!1);void 0===(d.keyCode||d.which)&&setTimeout(function(){E.getElement(E.container).focus()},500),E.media.setCurrentTime(parseFloat(b.value)),E.media.paused&&E.media.play()});for(var a=0,dd=T.length;a<dd;a++)T[a].addEventListener("click",function(d){var E=(0,gP.siblings)(this,function(d){return"INPUT"===d.tagName})[0],b=(0,W.createEvent)("click",E);E.dispatchEvent(b),d.preventDefault()})}},searchTrackPosition:function(d,E){for(var b=0,A=d.length-1,g=void 0,T=void 0,bV=void 0;b<=A;){if(g=b+A>>1,T=d[g].start,bV=d[g].stop,E>=T&&E<bV)return g;T<E?b=g+1:T>E&&(A=g-1)}return-1}}),T.default.language={codes:{af:"mejs.afrikaans",sq:"mejs.albanian",ar:"mejs.arabic",be:"mejs.belarusian",bg:"mejs.bulgarian",ca:"mejs.catalan",zh:"mejs.chinese","zh-cn":"mejs.chinese-simplified","zh-tw":"mejs.chines-traditional",hr:"mejs.croatian",cs:"mejs.czech",da:"mejs.danish",nl:"mejs.dutch",en:"mejs.english",et:"mejs.estonian",fl:"mejs.filipino",fi:"mejs.finnish",fr:"mejs.french",gl:"mejs.galician",de:"mejs.german",el:"mejs.greek",ht:"mejs.haitian-creole",iw:"mejs.hebrew",hi:"mejs.hindi",hu:"mejs.hungarian",is:"mejs.icelandic",id:"mejs.indonesian",ga:"mejs.irish",it:"mejs.italian",ja:"mejs.japanese",ko:"mejs.korean",lv:"mejs.latvian",lt:"mejs.lithuanian",mk:"mejs.macedonian",ms:"mejs.malay",mt:"mejs.maltese",no:"mejs.norwegian",fa:"mejs.persian",pl:"mejs.polish",pt:"mejs.portuguese",ro:"mejs.romanian",ru:"mejs.russian",sr:"mejs.serbian",sk:"mejs.slovak",sl:"mejs.slovenian",es:"mejs.spanish",sw:"mejs.swahili",sv:"mejs.swedish",tl:"mejs.tagalog",th:"mejs.thai",tr:"mejs.turkish",uk:"mejs.ukrainian",vi:"mejs.vietnamese",cy:"mejs.welsh",yi:"mejs.yiddish"}},T.default.TrackFormatParser={webvtt:{pattern:/^((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{1,3})?) --\> ((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{3})?)(.*)$/,parse:function(d){for(var E=d.split(/\r?\n/),b=[],A=void 0,g=void 0,T=void 0,bV=0,dO=E.length;bV<dO;bV++){if((A=this.pattern.exec(E[bV]))&&bV<E.length){for(bV-1>=0&&""!==E[bV-1]&&(T=E[bV-1]),g=E[++bV],bV++;""!==E[bV]&&bV<E.length;)g=g+"\n"+E[bV],bV++;g=g.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),b.push({identifier:T,start:0===(0,dd.convertSMPTEtoSeconds)(A[1])?.2:(0,dd.convertSMPTEtoSeconds)(A[1]),stop:(0,dd.convertSMPTEtoSeconds)(A[3]),text:g,settings:A[5]})}T=""}return b}},dfxp:{parse:function(d){var E=(d=$(d).filter("tt")).firstChild,b=E.querySelectorAll("p"),A=d.getElementById(""+E.attr("style")),g=[],T=void 0;if(A.length){A.removeAttribute("id");var bV=A.attributes;if(bV.length){T={};for(var dO=0,a=bV.length;dO<a;dO++)T[bV[dO].name.split(":")[1]]=bV[dO].value}}for(var W=0,gP=b.length;W<gP;W++){var e=void 0,c={start:null,stop:null,style:null,text:null};if(b.eq(W).attr("begin")&&(c.start=(0,dd.convertSMPTEtoSeconds)(b.eq(W).attr("begin"))),!c.start&&b.eq(W-1).attr("end")&&(c.start=(0,dd.convertSMPTEtoSeconds)(b.eq(W-1).attr("end"))),b.eq(W).attr("end")&&(c.stop=(0,dd.convertSMPTEtoSeconds)(b.eq(W).attr("end"))),!c.stop&&b.eq(W+1).attr("begin")&&(c.stop=(0,dd.convertSMPTEtoSeconds)(b.eq(W+1).attr("begin"))),T){e="";for(var aA in T)e+=aA+":"+T[aA]+";"}e&&(c.style=e),0===c.start&&(c.start=.2),c.text=b.eq(W).innerHTML.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),g.push(c)}return g}}}},{16:16,2:2,26:26,27:27,30:30,5:5,7:7}],14:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(2)),T=d(16),bV=A(T),dO=A(d(5)),a=d(25),dd=d(27),W=d(26);Object.assign(T.config,{muteText:null,unmuteText:null,allyVolumeControlText:null,hideVolumeOnTouchDevices:!0,audioVolume:"horizontal",videoVolume:"vertical",startVolume:.8}),Object.assign(bV.default.prototype,{buildvolume:function(d,E,b,A){if(!a.IS_ANDROID&&!a.IS_IOS||!this.options.hideVolumeOnTouchDevices){var bV=this,gP=bV.isVideo?bV.options.videoVolume:bV.options.audioVolume,e=(0,dd.isString)(bV.options.muteText)?bV.options.muteText:dO.default.t("mejs.mute"),c=(0,dd.isString)(bV.options.unmuteText)?bV.options.unmuteText:dO.default.t("mejs.unmute"),aA=(0,dd.isString)(bV.options.allyVolumeControlText)?bV.options.allyVolumeControlText:dO.default.t("mejs.volume-help-text"),cF=g.default.createElement("div");if(cF.className=bV.options.classPrefix+"button "+bV.options.classPrefix+"volume-button "+bV.options.classPrefix+"mute",cF.innerHTML="horizontal"===gP?'<button type="button" aria-controls="'+bV.id+'" title="'+e+'" aria-label="'+e+'" tabindex="0"></button>':'<button type="button" aria-controls="'+bV.id+'" title="'+e+'" aria-label="'+e+'" tabindex="0"></button><a href="javascript:void(0);" class="'+bV.options.classPrefix+'volume-slider" aria-label="'+dO.default.t("mejs.volume-slider")+'" aria-valuemin="0" aria-valuemax="100" role="slider" aria-orientation="vertical"><span class="'+bV.options.classPrefix+'offscreen">'+aA+'</span><div class="'+bV.options.classPrefix+'volume-total"><div class="'+bV.options.classPrefix+'volume-current"></div><div class="'+bV.options.classPrefix+'volume-handle"></div></div></a>',bV.addControlElement(cF,"volume"),bV.options.keyActions.push({keys:[38],action:function(d){var E=d.getElement(d.container).querySelector("."+T.config.classPrefix+"volume-slider");(E||d.getElement(d.container).querySelector("."+T.config.classPrefix+"volume-slider").matches(":focus"))&&(E.style.display="block"),d.isVideo&&(d.showControls(),d.startControlsTimer());var b=Math.min(d.volume+.1,1);d.setVolume(b),b>0&&d.setMuted(!1)}},{keys:[40],action:function(d){var E=d.getElement(d.container).querySelector("."+T.config.classPrefix+"volume-slider");E&&(E.style.display="block"),d.isVideo&&(d.showControls(),d.startControlsTimer());var b=Math.max(d.volume-.1,0);d.setVolume(b),b<=.1&&d.setMuted(!0)}},{keys:[77],action:function(d){d.getElement(d.container).querySelector("."+T.config.classPrefix+"volume-slider").style.display="block",d.isVideo&&(d.showControls(),d.startControlsTimer()),d.media.muted?d.setMuted(!1):d.setMuted(!0)}}),"horizontal"===gP){var ai=g.default.createElement("a");ai.className=bV.options.classPrefix+"horizontal-volume-slider",ai.href="javascript:void(0);",ai.setAttribute("aria-label",dO.default.t("mejs.volume-slider")),ai.setAttribute("aria-valuemin",0),ai.setAttribute("aria-valuemax",100),ai.setAttribute("role","slider"),ai.innerHTML+='<span class="'+bV.options.classPrefix+'offscreen">'+aA+'</span><div class="'+bV.options.classPrefix+'horizontal-volume-total"><div class="'+bV.options.classPrefix+'horizontal-volume-current"></div><div class="'+bV.options.classPrefix+'horizontal-volume-handle"></div></div>',cF.parentNode.insertBefore(ai,cF.nextSibling)}var df=!1,eK=!1,eI=!1,eU=function(){var d=Math.floor(100*A.volume);gT.setAttribute("aria-valuenow",d),gT.setAttribute("aria-valuetext",d+"%")},gT="vertical"===gP?bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"volume-slider"):bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"horizontal-volume-slider"),ej="vertical"===gP?bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"volume-total"):bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"horizontal-volume-total"),eh="vertical"===gP?bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"volume-current"):bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"horizontal-volume-current"),eY="vertical"===gP?bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"volume-handle"):bV.getElement(bV.container).querySelector("."+bV.options.classPrefix+"horizontal-volume-handle"),ed=function(d){if(null!==d&&!isNaN(d)&&void 0!==d){if(d=Math.max(0,d),0===(d=Math.min(d,1))){(0,W.removeClass)(cF,bV.options.classPrefix+"mute"),(0,W.addClass)(cF,bV.options.classPrefix+"unmute");var E=cF.firstElementChild;E.setAttribute("title",c),E.setAttribute("aria-label",c)}else{(0,W.removeClass)(cF,bV.options.classPrefix+"unmute"),(0,W.addClass)(cF,bV.options.classPrefix+"mute");var b=cF.firstElementChild;b.setAttribute("title",e),b.setAttribute("aria-label",e)}var A=100*d+"%",g=getComputedStyle(eY);"vertical"===gP?(eh.style.bottom=0,eh.style.height=A,eY.style.bottom=A,eY.style.marginBottom=-parseFloat(g.height)/2+"px"):(eh.style.left=0,eh.style.width=A,eY.style.left=A,eY.style.marginLeft=-parseFloat(g.width)/2+"px")}},gX=function(d){var E=(0,W.offset)(ej),b=getComputedStyle(ej);eI=!0;var A=null;if("vertical"===gP){var g=parseFloat(b.height);if(A=(g-(d.pageY-E.top))/g,0===E.top||0===E.left)return}else{var T=parseFloat(b.width);A=(d.pageX-E.left)/T}A=Math.max(0,A),A=Math.min(A,1),ed(A),bV.setMuted(0===A),bV.setVolume(A),d.preventDefault(),d.stopPropagation()},aW=function(){bV.muted?(ed(0),(0,W.removeClass)(cF,bV.options.classPrefix+"mute"),(0,W.addClass)(cF,bV.options.classPrefix+"unmute")):(ed(A.volume),(0,W.removeClass)(cF,bV.options.classPrefix+"unmute"),(0,W.addClass)(cF,bV.options.classPrefix+"mute"))};d.getElement(d.container).addEventListener("keydown",function(d){!!d.target.closest("."+bV.options.classPrefix+"container")||"vertical"!==gP||(gT.style.display="none")}),cF.addEventListener("mouseenter",function(d){d.target===cF&&(gT.style.display="block",eK=!0,d.preventDefault(),d.stopPropagation())}),cF.addEventListener("focusin",function(){gT.style.display="block",eK=!0}),cF.addEventListener("focusout",function(d){d.relatedTarget&&(!d.relatedTarget||d.relatedTarget.matches("."+bV.options.classPrefix+"volume-slider"))||"vertical"!==gP||(gT.style.display="none")}),cF.addEventListener("mouseleave",function(){eK=!1,df||"vertical"!==gP||(gT.style.display="none")}),cF.addEventListener("focusout",function(){eK=!1}),cF.addEventListener("keydown",function(d){if(bV.options.keyActions.length){var E=d.which||d.keyCode||0,b=A.volume;switch(E){case 38:b=Math.min(b+.1,1);break;case 40:b=Math.max(0,b-.1);break;default:return!0}df=!1,ed(b),A.setVolume(b),d.preventDefault(),d.stopPropagation()}}),cF.querySelector("button").addEventListener("click",function(){A.setMuted(!A.muted);var d=(0,dd.createEvent)("volumechange",A);A.dispatchEvent(d)}),gT.addEventListener("dragstart",function(){return!1}),gT.addEventListener("mouseover",function(){eK=!0}),gT.addEventListener("focusin",function(){gT.style.display="block",eK=!0}),gT.addEventListener("focusout",function(){eK=!1,df||"vertical"!==gP||(gT.style.display="none")}),gT.addEventListener("mousedown",function(d){gX(d),bV.globalBind("mousemove.vol",function(d){var E=d.target;df&&(E===gT||E.closest("vertical"===gP?"."+bV.options.classPrefix+"volume-slider":"."+bV.options.classPrefix+"horizontal-volume-slider"))&&gX(d)}),bV.globalBind("mouseup.vol",function(){df=!1,eK||"vertical"!==gP||(gT.style.display="none")}),df=!0,d.preventDefault(),d.stopPropagation()}),A.addEventListener("volumechange",function(d){df||aW(),eU()});var ddc=!1;A.addEventListener("rendererready",function(){eI||setTimeout(function(){ddc=!0,(0===d.options.startVolume||A.originalNode.muted)&&(A.setMuted(!0),d.options.startVolume=0),A.setVolume(d.options.startVolume),bV.setControlsSize()},250)}),A.addEventListener("loadedmetadata",function(){setTimeout(function(){eI||ddc||((0===d.options.startVolume||A.originalNode.muted)&&(A.setMuted(!0),d.options.startVolume=0),A.setVolume(d.options.startVolume),bV.setControlsSize()),ddc=!1},250)}),(0===d.options.startVolume||A.originalNode.muted)&&(A.setMuted(!0),d.options.startVolume=0,aW()),bV.getElement(bV.container).addEventListener("controlsresize",function(){aW()})}}})},{16:16,2:2,25:25,26:26,27:27,5:5}],15:[function(d,E,b){"use strict";Object.defineProperty(b,"__esModule",{value:!0});b.EN={"mejs.plural-form":1,"mejs.download-file":"Download File","mejs.install-flash":"You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/","mejs.fullscreen":"Fullscreen","mejs.play":"Play","mejs.pause":"Pause","mejs.time-slider":"Time Slider","mejs.time-help-text":"Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds.","mejs.live-broadcast":"Live Broadcast","mejs.volume-help-text":"Use Up/Down Arrow keys to increase or decrease volume.","mejs.unmute":"Unmute","mejs.mute":"Mute","mejs.volume-slider":"Volume Slider","mejs.video-player":"Video Player","mejs.audio-player":"Audio Player","mejs.captions-subtitles":"Captions/Subtitles","mejs.captions-chapters":"Chapters","mejs.none":"None","mejs.afrikaans":"Afrikaans","mejs.albanian":"Albanian","mejs.arabic":"Arabic","mejs.belarusian":"Belarusian","mejs.bulgarian":"Bulgarian","mejs.catalan":"Catalan","mejs.chinese":"Chinese","mejs.chinese-simplified":"Chinese (Simplified)","mejs.chinese-traditional":"Chinese (Traditional)","mejs.croatian":"Croatian","mejs.czech":"Czech","mejs.danish":"Danish","mejs.dutch":"Dutch","mejs.english":"English","mejs.estonian":"Estonian","mejs.filipino":"Filipino","mejs.finnish":"Finnish","mejs.french":"French","mejs.galician":"Galician","mejs.german":"German","mejs.greek":"Greek","mejs.haitian-creole":"Haitian Creole","mejs.hebrew":"Hebrew","mejs.hindi":"Hindi","mejs.hungarian":"Hungarian","mejs.icelandic":"Icelandic","mejs.indonesian":"Indonesian","mejs.irish":"Irish","mejs.italian":"Italian","mejs.japanese":"Japanese","mejs.korean":"Korean","mejs.latvian":"Latvian","mejs.lithuanian":"Lithuanian","mejs.macedonian":"Macedonian","mejs.malay":"Malay","mejs.maltese":"Maltese","mejs.norwegian":"Norwegian","mejs.persian":"Persian","mejs.polish":"Polish","mejs.portuguese":"Portuguese","mejs.romanian":"Romanian","mejs.russian":"Russian","mejs.serbian":"Serbian","mejs.slovak":"Slovak","mejs.slovenian":"Slovenian","mejs.spanish":"Spanish","mejs.swahili":"Swahili","mejs.swedish":"Swedish","mejs.tagalog":"Tagalog","mejs.thai":"Thai","mejs.turkish":"Turkish","mejs.ukrainian":"Ukrainian","mejs.vietnamese":"Vietnamese","mejs.welsh":"Welsh","mejs.yiddish":"Yiddish"}},{}],16:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}function g(d,E){if(!(d instanceof E))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(b,"__esModule",{value:!0}),b.config=void 0;var T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},bV=function(){function d(d,E){for(var b=0;b<E.length;b++){var A=E[b];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(d,A.key,A)}}return function(E,b,A){return b&&d(E.prototype,b),A&&d(E,A),E}}(),dO=A(d(3)),a=A(d(2)),dd=A(d(7)),W=A(d(6)),gP=A(d(17)),e=A(d(5)),c=d(25),aA=d(27),cF=d(30),ai=d(28),df=function(d){if(d&&d.__esModule)return d;var E={};if(null!=d)for(var b in d)Object.prototype.hasOwnProperty.call(d,b)&&(E[b]=d[b]);return E.default=d,E}(d(26));dd.default.mepIndex=0,dd.default.players={};var eK=b.config={poster:"",showPosterWhenEnded:!1,showPosterWhenPaused:!1,defaultVideoWidth:480,defaultVideoHeight:270,videoWidth:-1,videoHeight:-1,defaultAudioWidth:400,defaultAudioHeight:40,defaultSeekBackwardInterval:function(d){return.05*d.getDuration()},defaultSeekForwardInterval:function(d){return.05*d.getDuration()},setDimensions:!0,audioWidth:-1,audioHeight:-1,loop:!1,autoRewind:!0,enableAutosize:!0,timeFormat:"",alwaysShowHours:!1,showTimecodeFrameCount:!1,framesPerSecond:25,alwaysShowControls:!1,hideVideoControlsOnLoad:!1,hideVideoControlsOnPause:!1,clickToPlayPause:!0,controlsTimeoutDefault:1500,controlsTimeoutMouseEnter:2500,controlsTimeoutMouseLeave:1e3,iPadUseNativeControls:!1,iPhoneUseNativeControls:!1,AndroidUseNativeControls:!1,features:["playpause","current","progress","duration","tracks","volume","fullscreen"],useDefaultControls:!1,isVideo:!0,stretching:"auto",classPrefix:"mejs__",enableKeyboard:!0,pauseOtherPlayers:!0,secondsDecimalLength:0,customError:null,keyActions:[{keys:[32,179],action:function(d){c.IS_FIREFOX||(d.paused||d.ended?d.play():d.pause())}}]};dd.default.MepDefaults=eK;var eI=function(){function d(E,b){g(this,d);var A=this,T="string"==typeof E?a.default.getElementById(E):E;if(!(A instanceof d))return new d(T,b);if(A.node=A.media=T,A.node){if(A.media.player)return A.media.player;if(A.hasFocus=!1,A.controlsAreVisible=!0,A.controlsEnabled=!0,A.controlsTimer=null,A.currentMediaTime=0,A.proxy=null,void 0===b){var bV=A.node.getAttribute("data-mejsoptions");b=bV?JSON.parse(bV):{}}return A.options=Object.assign({},eK,b),A.options.loop&&!A.media.getAttribute("loop")?(A.media.loop=!0,A.node.loop=!0):A.media.loop&&(A.options.loop=!0),A.options.timeFormat||(A.options.timeFormat="mm:ss",A.options.alwaysShowHours&&(A.options.timeFormat="hh:mm:ss"),A.options.showTimecodeFrameCount&&(A.options.timeFormat+=":ff")),(0,cF.calculateTimeFormat)(0,A.options,A.options.framesPerSecond||25),A.id="mep_"+dd.default.mepIndex++,dd.default.players[A.id]=A,A.init(),A}}return bV(d,[{key:"getElement",value:function(d){return d}},{key:"init",value:function(){var d=this,E=Object.assign({},d.options,{success:function(E,b){d._meReady(E,b)},error:function(E){d._handleError(E)}}),b=d.node.tagName.toLowerCase();if(d.isDynamic="audio"!==b&&"video"!==b&&"iframe"!==b,d.isVideo=d.isDynamic?d.options.isVideo:"audio"!==b&&d.options.isVideo,d.mediaFiles=null,d.trackFiles=null,c.IS_IPAD&&d.options.iPadUseNativeControls||c.IS_IPHONE&&d.options.iPhoneUseNativeControls)d.node.setAttribute("controls",!0),c.IS_IPAD&&d.node.getAttribute("autoplay")&&d.play();else if(!d.isVideo&&(d.isVideo||!d.options.features.length&&!d.options.useDefaultControls)||c.IS_ANDROID&&d.options.AndroidUseNativeControls)d.isVideo||d.options.features.length||d.options.useDefaultControls||(d.node.style.display="none");else{d.node.removeAttribute("controls");var A=d.isVideo?e.default.t("mejs.video-player"):e.default.t("mejs.audio-player"),g=a.default.createElement("span");if(g.className=d.options.classPrefix+"offscreen",g.innerText=A,d.media.parentNode.insertBefore(g,d.media),d.container=a.default.createElement("div"),d.getElement(d.container).id=d.id,d.getElement(d.container).className=d.options.classPrefix+"container "+d.options.classPrefix+"container-keyboard-inactive "+d.media.className,d.getElement(d.container).tabIndex=0,d.getElement(d.container).setAttribute("role","application"),d.getElement(d.container).setAttribute("aria-label",A),d.getElement(d.container).innerHTML='<div class="'+d.options.classPrefix+'inner"><div class="'+d.options.classPrefix+'mediaelement"></div><div class="'+d.options.classPrefix+'layers"></div><div class="'+d.options.classPrefix+'controls"></div></div>',d.getElement(d.container).addEventListener("focus",function(E){if(!d.controlsAreVisible&&!d.hasFocus&&d.controlsEnabled){d.showControls(!0);var b=(0,aA.isNodeAfter)(E.relatedTarget,d.getElement(d.container))?"."+d.options.classPrefix+"controls ."+d.options.classPrefix+"button:last-child > button":"."+d.options.classPrefix+"playpause-button > button";d.getElement(d.container).querySelector(b).focus()}}),d.node.parentNode.insertBefore(d.getElement(d.container),d.node),d.options.features.length||d.options.useDefaultControls||(d.getElement(d.container).style.background="transparent",d.getElement(d.container).querySelector("."+d.options.classPrefix+"controls").style.display="none"),d.isVideo&&"fill"===d.options.stretching&&!df.hasClass(d.getElement(d.container).parentNode,d.options.classPrefix+"fill-container")){d.outerContainer=d.media.parentNode;var T=a.default.createElement("div");T.className=d.options.classPrefix+"fill-container",d.getElement(d.container).parentNode.insertBefore(T,d.getElement(d.container)),T.appendChild(d.getElement(d.container))}if(c.IS_ANDROID&&df.addClass(d.getElement(d.container),d.options.classPrefix+"android"),c.IS_IOS&&df.addClass(d.getElement(d.container),d.options.classPrefix+"ios"),c.IS_IPAD&&df.addClass(d.getElement(d.container),d.options.classPrefix+"ipad"),c.IS_IPHONE&&df.addClass(d.getElement(d.container),d.options.classPrefix+"iphone"),df.addClass(d.getElement(d.container),d.isVideo?d.options.classPrefix+"video":d.options.classPrefix+"audio"),c.IS_SAFARI&&!c.IS_IOS){df.addClass(d.getElement(d.container),d.options.classPrefix+"hide-cues");for(var bV=d.node.cloneNode(),dO=d.node.children,gP=[],cF=[],eK=0,eI=dO.length;eK<eI;eK++){var eU=dO[eK];!function(){switch(eU.tagName.toLowerCase()){case"source":var d={};Array.prototype.slice.call(eU.attributes).forEach(function(E){d[E.name]=E.value}),d.type=(0,ai.formatType)(d.src,d.type),gP.push(d);break;case"track":eU.mode="hidden",cF.push(eU);break;default:bV.appendChild(eU)}}()}d.node.remove(),d.node=d.media=bV,gP.length&&(d.mediaFiles=gP),cF.length&&(d.trackFiles=cF)}d.getElement(d.container).querySelector("."+d.options.classPrefix+"mediaelement").appendChild(d.node),d.media.player=d,d.controls=d.getElement(d.container).querySelector("."+d.options.classPrefix+"controls"),d.layers=d.getElement(d.container).querySelector("."+d.options.classPrefix+"layers");var gT=d.isVideo?"video":"audio",ej=gT.substring(0,1).toUpperCase()+gT.substring(1);d.options[gT+"Width"]>0||d.options[gT+"Width"].toString().indexOf("%")>-1?d.width=d.options[gT+"Width"]:""!==d.node.style.width&&null!==d.node.style.width?d.width=d.node.style.width:d.node.getAttribute("width")?d.width=d.node.getAttribute("width"):d.width=d.options["default"+ej+"Width"],d.options[gT+"Height"]>0||d.options[gT+"Height"].toString().indexOf("%")>-1?d.height=d.options[gT+"Height"]:""!==d.node.style.height&&null!==d.node.style.height?d.height=d.node.style.height:d.node.getAttribute("height")?d.height=d.node.getAttribute("height"):d.height=d.options["default"+ej+"Height"],d.initialAspectRatio=d.height>=d.width?d.width/d.height:d.height/d.width,d.setPlayerSize(d.width,d.height),E.pluginWidth=d.width,E.pluginHeight=d.height}if(dd.default.MepDefaults=E,new W.default(d.media,E,d.mediaFiles),void 0!==d.getElement(d.container)&&d.options.features.length&&d.controlsAreVisible&&!d.options.hideVideoControlsOnLoad){var eh=(0,aA.createEvent)("controlsshown",d.getElement(d.container));d.getElement(d.container).dispatchEvent(eh)}}},{key:"showControls",value:function(d){var E=this;if(d=void 0===d||d,!E.controlsAreVisible&&E.isVideo){if(d)!function(){df.fadeIn(E.getElement(E.controls),200,function(){df.removeClass(E.getElement(E.controls),E.options.classPrefix+"offscreen");var d=(0,aA.createEvent)("controlsshown",E.getElement(E.container));E.getElement(E.container).dispatchEvent(d)});for(var d=E.getElement(E.container).querySelectorAll("."+E.options.classPrefix+"control"),b=0,A=d.length;b<A;b++)!function(b,A){df.fadeIn(d[b],200,function(){df.removeClass(d[b],E.options.classPrefix+"offscreen")})}(b)}();else{df.removeClass(E.getElement(E.controls),E.options.classPrefix+"offscreen"),E.getElement(E.controls).style.display="",E.getElement(E.controls).style.opacity=1;for(var b=E.getElement(E.container).querySelectorAll("."+E.options.classPrefix+"control"),A=0,g=b.length;A<g;A++)df.removeClass(b[A],E.options.classPrefix+"offscreen"),b[A].style.display="";var T=(0,aA.createEvent)("controlsshown",E.getElement(E.container));E.getElement(E.container).dispatchEvent(T)}E.controlsAreVisible=!0,E.setControlsSize()}}},{key:"hideControls",value:function(d,E){var b=this;if(d=void 0===d||d,!0===E||!(!b.controlsAreVisible||b.options.alwaysShowControls||b.paused&&4===b.readyState&&(!b.options.hideVideoControlsOnLoad&&b.currentTime<=0||!b.options.hideVideoControlsOnPause&&b.currentTime>0)||b.isVideo&&!b.options.hideVideoControlsOnLoad&&!b.readyState||b.ended)){if(d)!function(){df.fadeOut(b.getElement(b.controls),200,function(){df.addClass(b.getElement(b.controls),b.options.classPrefix+"offscreen"),b.getElement(b.controls).style.display="";var d=(0,aA.createEvent)("controlshidden",b.getElement(b.container));b.getElement(b.container).dispatchEvent(d)});for(var d=b.getElement(b.container).querySelectorAll("."+b.options.classPrefix+"control"),E=0,A=d.length;E<A;E++)!function(E,A){df.fadeOut(d[E],200,function(){df.addClass(d[E],b.options.classPrefix+"offscreen"),d[E].style.display=""})}(E)}();else{df.addClass(b.getElement(b.controls),b.options.classPrefix+"offscreen"),b.getElement(b.controls).style.display="",b.getElement(b.controls).style.opacity=0;for(var A=b.getElement(b.container).querySelectorAll("."+b.options.classPrefix+"control"),g=0,T=A.length;g<T;g++)df.addClass(A[g],b.options.classPrefix+"offscreen"),A[g].style.display="";var bV=(0,aA.createEvent)("controlshidden",b.getElement(b.container));b.getElement(b.container).dispatchEvent(bV)}b.controlsAreVisible=!1}}},{key:"startControlsTimer",value:function(d){var E=this;d=void 0!==d?d:E.options.controlsTimeoutDefault,E.killControlsTimer("start"),E.controlsTimer=setTimeout(function(){E.hideControls(),E.killControlsTimer("hide")},d)}},{key:"killControlsTimer",value:function(){var d=this;null!==d.controlsTimer&&(clearTimeout(d.controlsTimer),delete d.controlsTimer,d.controlsTimer=null)}},{key:"disableControls",value:function(){var d=this;d.killControlsTimer(),d.controlsEnabled=!1,d.hideControls(!1,!0)}},{key:"enableControls",value:function(){var d=this;d.controlsEnabled=!0,d.showControls(!1)}},{key:"_setDefaultPlayer",value:function(){var d=this;d.proxy&&d.proxy.pause(),d.proxy=new gP.default(d),d.media.addEventListener("loadedmetadata",function(){d.getCurrentTime()>0&&d.currentMediaTime>0&&(d.setCurrentTime(d.currentMediaTime),c.IS_IOS||c.IS_ANDROID||d.play())})}},{key:"_meReady",value:function(d,E){var b=this,A=E.getAttribute("autoplay"),g=!(void 0===A||null===A||"false"===A),T=null!==d.rendererName&&/(native|html5)/i.test(b.media.rendererName);if(b.getElement(b.controls)&&b.enableControls(),b.getElement(b.container)&&b.getElement(b.container).querySelector("."+b.options.classPrefix+"overlay-play")&&(b.getElement(b.container).querySelector("."+b.options.classPrefix+"overlay-play").style.display=""),!b.created){if(b.created=!0,b.media=d,b.domNode=E,!(c.IS_ANDROID&&b.options.AndroidUseNativeControls||c.IS_IPAD&&b.options.iPadUseNativeControls||c.IS_IPHONE&&b.options.iPhoneUseNativeControls)){if(!b.isVideo&&!b.options.features.length&&!b.options.useDefaultControls)return g&&T&&b.play(),void(b.options.success&&("string"==typeof b.options.success?dO.default[b.options.success](b.media,b.domNode,b):b.options.success(b.media,b.domNode,b)));if(b.featurePosition={},b._setDefaultPlayer(),b.buildposter(b,b.getElement(b.controls),b.getElement(b.layers),b.media),b.buildkeyboard(b,b.getElement(b.controls),b.getElement(b.layers),b.media),b.buildoverlays(b,b.getElement(b.controls),b.getElement(b.layers),b.media),b.options.useDefaultControls){var bV=["playpause","current","progress","duration","tracks","volume","fullscreen"];b.options.features=bV.concat(b.options.features.filter(function(d){return-1===bV.indexOf(d)}))}b.buildfeatures(b,b.getElement(b.controls),b.getElement(b.layers),b.media);var W=(0,aA.createEvent)("controlsready",b.getElement(b.container));b.getElement(b.container).dispatchEvent(W),b.setPlayerSize(b.width,b.height),b.setControlsSize(),b.isVideo&&(b.clickToPlayPauseCallback=function(){if(b.options.clickToPlayPause){var d=b.getElement(b.container).querySelector("."+b.options.classPrefix+"overlay-button"),E=d.getAttribute("aria-pressed");b.paused&&E?b.pause():b.paused?b.play():b.pause(),d.setAttribute("aria-pressed",!E),b.getElement(b.container).focus()}},b.createIframeLayer(),b.media.addEventListener("click",b.clickToPlayPauseCallback),!c.IS_ANDROID&&!c.IS_IOS||b.options.alwaysShowControls?(b.getElement(b.container).addEventListener("mouseenter",function(){b.controlsEnabled&&(b.options.alwaysShowControls||(b.killControlsTimer("enter"),b.showControls(),b.startControlsTimer(b.options.controlsTimeoutMouseEnter)))}),b.getElement(b.container).addEventListener("mousemove",function(){b.controlsEnabled&&(b.controlsAreVisible||b.showControls(),b.options.alwaysShowControls||b.startControlsTimer(b.options.controlsTimeoutMouseEnter))}),b.getElement(b.container).addEventListener("mouseleave",function(){b.controlsEnabled&&(b.paused||b.options.alwaysShowControls||b.startControlsTimer(b.options.controlsTimeoutMouseLeave))})):b.node.addEventListener("touchstart",function(){b.controlsAreVisible?b.hideControls(!1):b.controlsEnabled&&b.showControls(!1)},!!c.SUPPORT_PASSIVE_EVENT&&{passive:!0}),b.options.hideVideoControlsOnLoad&&b.hideControls(!1),b.options.enableAutosize&&b.media.addEventListener("loadedmetadata",function(d){var E=void 0!==d?d.detail.target||d.target:b.media;b.options.videoHeight<=0&&!b.domNode.getAttribute("height")&&!b.domNode.style.height&&null!==E&&!isNaN(E.videoHeight)&&(b.setPlayerSize(E.videoWidth,E.videoHeight),b.setControlsSize(),b.media.setSize(E.videoWidth,E.videoHeight))})),b.media.addEventListener("play",function(){b.hasFocus=!0;for(var d in dd.default.players)if(dd.default.players.hasOwnProperty(d)){var E=dd.default.players[d];E.id===b.id||!b.options.pauseOtherPlayers||E.paused||E.ended||(E.pause(),E.hasFocus=!1)}c.IS_ANDROID||c.IS_IOS||b.options.alwaysShowControls||!b.isVideo||b.hideControls()}),b.media.addEventListener("ended",function(){if(b.options.autoRewind)try{b.setCurrentTime(0),setTimeout(function(){var d=b.getElement(b.container).querySelector("."+b.options.classPrefix+"overlay-loading");d&&d.parentNode&&(d.parentNode.style.display="none")},20)}catch(d){}"function"==typeof b.media.renderer.stop?b.media.renderer.stop():b.pause(),b.setProgressRail&&b.setProgressRail(),b.setCurrentRail&&b.setCurrentRail(),b.options.loop?b.play():!b.options.alwaysShowControls&&b.controlsEnabled&&b.showControls()}),b.media.addEventListener("loadedmetadata",function(){(0,cF.calculateTimeFormat)(b.getDuration(),b.options,b.options.framesPerSecond||25),b.updateDuration&&b.updateDuration(),b.updateCurrent&&b.updateCurrent(),b.isFullScreen||(b.setPlayerSize(b.width,b.height),b.setControlsSize())});var gP=null;b.media.addEventListener("timeupdate",function(){isNaN(b.getDuration())||gP===b.getDuration()||(gP=b.getDuration(),(0,cF.calculateTimeFormat)(gP,b.options,b.options.framesPerSecond||25),b.updateDuration&&b.updateDuration(),b.updateCurrent&&b.updateCurrent(),b.setControlsSize())}),b.getElement(b.container).addEventListener("click",function(d){df.addClass(d.currentTarget,b.options.classPrefix+"container-keyboard-inactive")}),b.getElement(b.container).addEventListener("focusin",function(d){df.removeClass(d.currentTarget,b.options.classPrefix+"container-keyboard-inactive"),!b.isVideo||c.IS_ANDROID||c.IS_IOS||!b.controlsEnabled||b.options.alwaysShowControls||(b.killControlsTimer("enter"),b.showControls(),b.startControlsTimer(b.options.controlsTimeoutMouseEnter))}),b.getElement(b.container).addEventListener("focusout",function(d){setTimeout(function(){d.relatedTarget&&b.keyboardAction&&!d.relatedTarget.closest("."+b.options.classPrefix+"container")&&(b.keyboardAction=!1,!b.isVideo||b.options.alwaysShowControls||b.paused||b.startControlsTimer(b.options.controlsTimeoutMouseLeave))},0)}),setTimeout(function(){b.setPlayerSize(b.width,b.height),b.setControlsSize()},0),b.globalResizeCallback=function(){b.isFullScreen||c.HAS_TRUE_NATIVE_FULLSCREEN&&a.default.webkitIsFullScreen||b.setPlayerSize(b.width,b.height),b.setControlsSize()},b.globalBind("resize",b.globalResizeCallback)}g&&T&&b.play(),b.options.success&&("string"==typeof b.options.success?dO.default[b.options.success](b.media,b.domNode,b):b.options.success(b.media,b.domNode,b))}}},{key:"_handleError",value:function(d,E,b){var A=this,g=A.getElement(A.layers).querySelector("."+A.options.classPrefix+"overlay-play");g&&(g.style.display="none"),A.options.error&&A.options.error(d,E,b),A.getElement(A.container).querySelector("."+A.options.classPrefix+"cannotplay")&&A.getElement(A.container).querySelector("."+A.options.classPrefix+"cannotplay").remove();var T=a.default.createElement("div");T.className=A.options.classPrefix+"cannotplay",T.style.width="100%",T.style.height="100%";var bV="function"==typeof A.options.customError?A.options.customError(A.media,A.media.originalNode):A.options.customError,dO="";if(!bV){var W=A.media.originalNode.getAttribute("poster");if(W&&(dO='<img src="'+W+'" alt="'+dd.default.i18n.t("mejs.download-file")+'">'),d.message&&(bV="<p>"+d.message+"</p>"),d.urls)for(var gP=0,e=d.urls.length;gP<e;gP++){var c=d.urls[gP];bV+='<a href="'+c.src+'" data-type="'+c.type+'"><span>'+dd.default.i18n.t("mejs.download-file")+": "+c.src+"</span></a>"}}bV&&A.getElement(A.layers).querySelector("."+A.options.classPrefix+"overlay-error")&&(T.innerHTML=bV,A.getElement(A.layers).querySelector("."+A.options.classPrefix+"overlay-error").innerHTML=""+dO+T.outerHTML,A.getElement(A.layers).querySelector("."+A.options.classPrefix+"overlay-error").parentNode.style.display="block"),A.controlsEnabled&&A.disableControls()}},{key:"setPlayerSize",value:function(d,E){var b=this;if(!b.options.setDimensions)return!1;switch(void 0!==d&&(b.width=d),void 0!==E&&(b.height=E),b.options.stretching){case"fill":b.isVideo?b.setFillMode():b.setDimensions(b.width,b.height);break;case"responsive":b.setResponsiveMode();break;case"none":b.setDimensions(b.width,b.height);break;default:!0===b.hasFluidMode()?b.setResponsiveMode():b.setDimensions(b.width,b.height)}}},{key:"hasFluidMode",value:function(){var d=this;return-1!==d.height.toString().indexOf("%")||d.node&&d.node.style.maxWidth&&"none"!==d.node.style.maxWidth&&d.node.style.maxWidth!==d.width||d.node&&d.node.currentStyle&&"100%"===d.node.currentStyle.maxWidth}},{key:"setResponsiveMode",value:function(){var d=this,E=function(){for(var E=void 0,b=d.getElement(d.container);b;){try{if(c.IS_FIREFOX&&"html"===b.tagName.toLowerCase()&&dO.default.self!==dO.default.top&&null!==dO.default.frameElement)return dO.default.frameElement;E=b.parentElement}catch(d){E=b.parentElement}if(E&&df.visible(E))return E;b=E}return null}(),b=E?getComputedStyle(E,null):getComputedStyle(a.default.body,null),A=d.isVideo?d.media.videoWidth&&d.media.videoWidth>0?d.media.videoWidth:d.node.getAttribute("width")?d.node.getAttribute("width"):d.options.defaultVideoWidth:d.options.defaultAudioWidth,g=d.isVideo?d.media.videoHeight&&d.media.videoHeight>0?d.media.videoHeight:d.node.getAttribute("height")?d.node.getAttribute("height"):d.options.defaultVideoHeight:d.options.defaultAudioHeight,T=function(){var E=1;return d.isVideo?(E=d.media.videoWidth&&d.media.videoWidth>0&&d.media.videoHeight&&d.media.videoHeight>0?d.height>=d.width?d.media.videoWidth/d.media.videoHeight:d.media.videoHeight/d.media.videoWidth:d.initialAspectRatio,(isNaN(E)||E<.01||E>100)&&(E=1),E):E}(),bV=parseFloat(b.height),dd=void 0,W=parseFloat(b.width);if(dd=d.isVideo?"100%"===d.height?parseFloat(W*g/A,10):d.height>=d.width?parseFloat(W/T,10):parseFloat(W*T,10):g,isNaN(dd)&&(dd=bV),d.getElement(d.container).parentNode.length>0&&"body"===d.getElement(d.container).parentNode.tagName.toLowerCase()&&(W=dO.default.innerWidth||a.default.documentElement.clientWidth||a.default.body.clientWidth,dd=dO.default.innerHeight||a.default.documentElement.clientHeight||a.default.body.clientHeight),dd&&W){d.getElement(d.container).style.width=W+"px",d.getElement(d.container).style.height=dd+"px",d.node.style.width="100%",d.node.style.height="100%",d.isVideo&&d.media.setSize&&d.media.setSize(W,dd);for(var gP=d.getElement(d.layers).children,e=0,aA=gP.length;e<aA;e++)gP[e].style.width="100%",gP[e].style.height="100%"}}},{key:"setFillMode",value:function(){var d=this,E=dO.default.self!==dO.default.top&&null!==dO.default.frameElement,b=function(){for(var E=void 0,b=d.getElement(d.container);b;){try{if(c.IS_FIREFOX&&"html"===b.tagName.toLowerCase()&&dO.default.self!==dO.default.top&&null!==dO.default.frameElement)return dO.default.frameElement;E=b.parentElement}catch(d){E=b.parentElement}if(E&&df.visible(E))return E;b=E}return null}(),A=b?getComputedStyle(b,null):getComputedStyle(a.default.body,null);"none"!==d.node.style.height&&d.node.style.height!==d.height&&(d.node.style.height="auto"),"none"!==d.node.style.maxWidth&&d.node.style.maxWidth!==d.width&&(d.node.style.maxWidth="none"),"none"!==d.node.style.maxHeight&&d.node.style.maxHeight!==d.height&&(d.node.style.maxHeight="none"),d.node.currentStyle&&("100%"===d.node.currentStyle.height&&(d.node.currentStyle.height="auto"),"100%"===d.node.currentStyle.maxWidth&&(d.node.currentStyle.maxWidth="none"),"100%"===d.node.currentStyle.maxHeight&&(d.node.currentStyle.maxHeight="none")),E||parseFloat(A.width)||(b.style.width=d.media.offsetWidth+"px"),E||parseFloat(A.height)||(b.style.height=d.media.offsetHeight+"px"),A=getComputedStyle(b);var g=parseFloat(A.width),T=parseFloat(A.height);d.setDimensions("100%","100%");var bV=d.getElement(d.container).querySelector("."+d.options.classPrefix+"poster>img");bV&&(bV.style.display="");for(var dd=d.getElement(d.container).querySelectorAll("object, embed, iframe, video"),W=d.height,gP=d.width,e=g,aA=W*g/gP,cF=gP*T/W,ai=T,eK=cF>g==!1,eI=eK?Math.floor(e):Math.floor(cF),eU=eK?Math.floor(aA):Math.floor(ai),gT=eK?g+"px":eI+"px",ej=eK?eU+"px":T+"px",eh=0,eY=dd.length;eh<eY;eh++)dd[eh].style.height=ej,dd[eh].style.width=gT,d.media.setSize&&d.media.setSize(gT,ej),dd[eh].style.marginLeft=Math.floor((g-eI)/2)+"px",dd[eh].style.marginTop=0}},{key:"setDimensions",value:function(d,E){var b=this;d=(0,aA.isString)(d)&&d.indexOf("%")>-1?d:parseFloat(d)+"px",E=(0,aA.isString)(E)&&E.indexOf("%")>-1?E:parseFloat(E)+"px",b.getElement(b.container).style.width=d,b.getElement(b.container).style.height=E;for(var A=b.getElement(b.layers).children,g=0,T=A.length;g<T;g++)A[g].style.width=d,A[g].style.height=E}},{key:"setControlsSize",value:function(){var d=this;if(df.visible(d.getElement(d.container)))if(d.rail&&df.visible(d.rail)){for(var E=d.total?getComputedStyle(d.total,null):null,b=E?parseFloat(E.marginLeft)+parseFloat(E.marginRight):0,A=getComputedStyle(d.rail),g=parseFloat(A.marginLeft)+parseFloat(A.marginRight),T=0,bV=df.siblings(d.rail,function(E){return E!==d.rail}),dO=bV.length,a=0;a<dO;a++)T+=bV[a].offsetWidth;T+=b+(0===b?2*g:g)+1,d.getElement(d.container).style.minWidth=T+"px";var dd=(0,aA.createEvent)("controlsresize",d.getElement(d.container));d.getElement(d.container).dispatchEvent(dd)}else{for(var W=d.getElement(d.controls).children,gP=0,e=0,c=W.length;e<c;e++)gP+=W[e].offsetWidth;d.getElement(d.container).style.minWidth=gP+"px"}}},{key:"addControlElement",value:function(d,E){var b=this;if(void 0!==b.featurePosition[E]){var A=b.getElement(b.controls).children[b.featurePosition[E]-1];A.parentNode.insertBefore(d,A.nextSibling)}else{b.getElement(b.controls).appendChild(d);for(var g=b.getElement(b.controls).children,T=0,bV=g.length;T<bV;T++)if(d===g[T]){b.featurePosition[E]=T;break}}}},{key:"createIframeLayer",value:function(){var d=this;if(d.isVideo&&null!==d.media.rendererName&&d.media.rendererName.indexOf("iframe")>-1&&!a.default.getElementById(d.media.id+"-iframe-overlay")){var E=a.default.createElement("div"),b=a.default.getElementById(d.media.id+"_"+d.media.rendererName);E.id=d.media.id+"-iframe-overlay",E.className=d.options.classPrefix+"iframe-overlay",E.addEventListener("click",function(E){d.options.clickToPlayPause&&(d.paused?d.play():d.pause(),E.preventDefault(),E.stopPropagation())}),b.parentNode.insertBefore(E,b)}}},{key:"resetSize",value:function(){var d=this;setTimeout(function(){d.setPlayerSize(d.width,d.height),d.setControlsSize()},50)}},{key:"setPoster",value:function(d){var E=this;if(E.getElement(E.container)){var b=E.getElement(E.container).querySelector("."+E.options.classPrefix+"poster");b||((b=a.default.createElement("div")).className=E.options.classPrefix+"poster "+E.options.classPrefix+"layer",E.getElement(E.layers).appendChild(b));var A=b.querySelector("img");!A&&d&&((A=a.default.createElement("img")).className=E.options.classPrefix+"poster-img",A.width="100%",A.height="100%",b.style.display="",b.appendChild(A)),d?(A.setAttribute("src",d),b.style.backgroundImage='url("'+d+'")',b.style.display=""):A?(b.style.backgroundImage="none",b.style.display="none",A.remove()):b.style.display="none"}else(c.IS_IPAD&&E.options.iPadUseNativeControls||c.IS_IPHONE&&E.options.iPhoneUseNativeControls||c.IS_ANDROID&&E.options.AndroidUseNativeControls)&&(E.media.originalNode.poster=d)}},{key:"changeSkin",value:function(d){var E=this;E.getElement(E.container).className=E.options.classPrefix+"container "+d,E.setPlayerSize(E.width,E.height),E.setControlsSize()}},{key:"globalBind",value:function(d,E){var b=this,A=b.node?b.node.ownerDocument:a.default;if((d=(0,aA.splitEvents)(d,b.id)).d)for(var g=d.d.split(" "),T=0,bV=g.length;T<bV;T++)g[T].split(".").reduce(function(d,b){return A.addEventListener(b,E,!1),b},"");if(d.w)for(var dd=d.w.split(" "),W=0,gP=dd.length;W<gP;W++)dd[W].split(".").reduce(function(d,b){return dO.default.addEventListener(b,E,!1),b},"")}},{key:"globalUnbind",value:function(d,E){var b=this,A=b.node?b.node.ownerDocument:a.default;if((d=(0,aA.splitEvents)(d,b.id)).d)for(var g=d.d.split(" "),T=0,bV=g.length;T<bV;T++)g[T].split(".").reduce(function(d,b){return A.removeEventListener(b,E,!1),b},"");if(d.w)for(var dd=d.w.split(" "),W=0,gP=dd.length;W<gP;W++)dd[W].split(".").reduce(function(d,b){return dO.default.removeEventListener(b,E,!1),b},"")}},{key:"buildfeatures",value:function(d,E,b,A){for(var g=this,T=0,bV=g.options.features.length;T<bV;T++){var dO=g.options.features[T];if(g["build"+dO])try{g["build"+dO](d,E,b,A)}catch(d){console.error("error building "+dO,d)}}}},{key:"buildposter",value:function(d,E,b,A){var g=this,T=a.default.createElement("div");T.className=g.options.classPrefix+"poster "+g.options.classPrefix+"layer",b.appendChild(T);var bV=A.originalNode.getAttribute("poster");""!==d.options.poster&&(bV&&c.IS_IOS&&A.originalNode.removeAttribute("poster"),bV=d.options.poster),bV?g.setPoster(bV):null!==g.media.renderer&&"function"==typeof g.media.renderer.getPosterUrl?g.setPoster(g.media.renderer.getPosterUrl()):T.style.display="none",A.addEventListener("play",function(){T.style.display="none"}),A.addEventListener("playing",function(){T.style.display="none"}),d.options.showPosterWhenEnded&&d.options.autoRewind&&A.addEventListener("ended",function(){T.style.display=""}),A.addEventListener("error",function(){T.style.display="none"}),d.options.showPosterWhenPaused&&A.addEventListener("pause",function(){d.ended||(T.style.display="")})}},{key:"buildoverlays",value:function(d,E,b,A){if(d.isVideo){var g=this,T=a.default.createElement("div"),bV=a.default.createElement("div"),dO=a.default.createElement("div");T.style.display="none",T.className=g.options.classPrefix+"overlay "+g.options.classPrefix+"layer",T.innerHTML='<div class="'+g.options.classPrefix+'overlay-loading"><span class="'+g.options.classPrefix+'overlay-loading-bg-img"></span></div>',b.appendChild(T),bV.style.display="none",bV.className=g.options.classPrefix+"overlay "+g.options.classPrefix+"layer",bV.innerHTML='<div class="'+g.options.classPrefix+'overlay-error"></div>',b.appendChild(bV),dO.className=g.options.classPrefix+"overlay "+g.options.classPrefix+"layer "+g.options.classPrefix+"overlay-play",dO.innerHTML='<div class="'+g.options.classPrefix+'overlay-button" role="button" tabindex="0" aria-label="'+e.default.t("mejs.play")+'" aria-pressed="false"></div>',dO.addEventListener("click",function(){if(g.options.clickToPlayPause){var d=g.getElement(g.container).querySelector("."+g.options.classPrefix+"overlay-button"),E=d.getAttribute("aria-pressed");g.paused?g.play():g.pause(),d.setAttribute("aria-pressed",!!E),g.getElement(g.container).focus()}}),dO.addEventListener("keydown",function(d){var E=d.keyCode||d.which||0;if(13===E||c.IS_FIREFOX&&32===E){var b=(0,aA.createEvent)("click",dO);return dO.dispatchEvent(b),!1}}),b.appendChild(dO),null!==g.media.rendererName&&(/(youtube|facebook)/i.test(g.media.rendererName)&&!(g.media.originalNode.getAttribute("poster")||d.options.poster||"function"==typeof g.media.renderer.getPosterUrl&&g.media.renderer.getPosterUrl())||c.IS_STOCK_ANDROID||g.media.originalNode.getAttribute("autoplay"))&&(dO.style.display="none");var dd=!1;A.addEventListener("play",function(){dO.style.display="none",T.style.display="none",bV.style.display="none",dd=!1}),A.addEventListener("playing",function(){dO.style.display="none",T.style.display="none",bV.style.display="none",dd=!1}),A.addEventListener("seeking",function(){dO.style.display="none",T.style.display="",dd=!1}),A.addEventListener("seeked",function(){dO.style.display=g.paused&&!c.IS_STOCK_ANDROID?"":"none",T.style.display="none",dd=!1}),A.addEventListener("pause",function(){T.style.display="none",c.IS_STOCK_ANDROID||dd||(dO.style.display=""),dd=!1}),A.addEventListener("waiting",function(){T.style.display="",dd=!1}),A.addEventListener("loadeddata",function(){T.style.display="",c.IS_ANDROID&&(A.canplayTimeout=setTimeout(function(){if(a.default.createEvent){var d=a.default.createEvent("HTMLEvents");return d.initEvent("canplay",!0,!0),A.dispatchEvent(d)}},300)),dd=!1}),A.addEventListener("canplay",function(){T.style.display="none",clearTimeout(A.canplayTimeout),dd=!1}),A.addEventListener("error",function(d){g._handleError(d,g.media,g.node),T.style.display="none",dO.style.display="none",dd=!0}),A.addEventListener("loadedmetadata",function(){g.controlsEnabled||g.enableControls()}),A.addEventListener("keydown",function(E){g.onkeydown(d,A,E),dd=!1})}}},{key:"buildkeyboard",value:function(d,E,b,A){var g=this;g.getElement(g.container).addEventListener("keydown",function(){g.keyboardAction=!0}),g.globalKeydownCallback=function(E){var b=a.default.activeElement.closest("."+g.options.classPrefix+"container"),T=g.media.closest("."+g.options.classPrefix+"container");return g.hasFocus=!(!b||!T||b.id!==T.id),g.onkeydown(d,A,E)},g.globalClickCallback=function(d){g.hasFocus=!!d.target.closest("."+g.options.classPrefix+"container")},g.globalBind("keydown",g.globalKeydownCallback),g.globalBind("click",g.globalClickCallback)}},{key:"onkeydown",value:function(d,E,b){if(d.hasFocus&&d.options.enableKeyboard)for(var A=0,g=d.options.keyActions.length;A<g;A++)for(var T=d.options.keyActions[A],bV=0,dO=T.keys.length;bV<dO;bV++)if(b.keyCode===T.keys[bV])return T.action(d,E,b.keyCode,b),b.preventDefault(),void b.stopPropagation();return!0}},{key:"play",value:function(){this.proxy.play()}},{key:"pause",value:function(){this.proxy.pause()}},{key:"load",value:function(){this.proxy.load()}},{key:"setCurrentTime",value:function(d){this.proxy.setCurrentTime(d)}},{key:"getCurrentTime",value:function(){return this.proxy.currentTime}},{key:"getDuration",value:function(){return this.proxy.duration}},{key:"setVolume",value:function(d){this.proxy.volume=d}},{key:"getVolume",value:function(){return this.proxy.getVolume()}},{key:"setMuted",value:function(d){this.proxy.setMuted(d)}},{key:"setSrc",value:function(d){this.controlsEnabled||this.enableControls(),this.proxy.setSrc(d)}},{key:"getSrc",value:function(){return this.proxy.getSrc()}},{key:"canPlayType",value:function(d){return this.proxy.canPlayType(d)}},{key:"remove",value:function(){var d=this,E=d.media.rendererName,b=d.media.originalNode.src;for(var A in d.options.features){var g=d.options.features[A];if(d["clean"+g])try{d["clean"+g](d,d.getElement(d.layers),d.getElement(d.controls),d.media)}catch(d){console.error("error cleaning "+g,d)}}var bV=d.node.getAttribute("width"),dO=d.node.getAttribute("height");bV?-1===bV.indexOf("%")&&(bV+="px"):bV="auto",dO?-1===dO.indexOf("%")&&(dO+="px"):dO="auto",d.node.style.width=bV,d.node.style.height=dO,d.setPlayerSize(0,0),d.isDynamic?d.getElement(d.container).parentNode.insertBefore(d.node,d.getElement(d.container)):function(){d.node.setAttribute("controls",!0),d.node.setAttribute("id",d.node.getAttribute("id").replace("_"+E,"").replace("_from_mejs",""));var A=d.getElement(d.container).querySelector("."+d.options.classPrefix+"poster>img");A&&d.node.setAttribute("poster",A.src),delete d.node.autoplay,""!==d.media.canPlayType((0,ai.getTypeFromFile)(b))&&d.node.setAttribute("src",b),~E.indexOf("iframe")&&a.default.getElementById(d.media.id+"-iframe-overlay").remove();var g=d.node.cloneNode();if(g.style.display="",d.getElement(d.container).parentNode.insertBefore(g,d.getElement(d.container)),d.node.remove(),d.mediaFiles)for(var T=0,bV=d.mediaFiles.length;T<bV;T++){var dO=a.default.createElement("source");dO.setAttribute("src",d.mediaFiles[T].src),dO.setAttribute("type",d.mediaFiles[T].type),g.appendChild(dO)}if(d.trackFiles)for(var dd=0,W=d.trackFiles.length;dd<W;dd++)!function(E,b){var A=d.trackFiles[E],T=a.default.createElement("track");T.kind=A.kind,T.label=A.label,T.srclang=A.srclang,T.src=A.src,g.appendChild(T),T.addEventListener("load",function(){this.mode="showing",g.textTracks[E].mode="showing"})}(dd);delete d.node,delete d.mediaFiles,delete d.trackFiles}(),"function"==typeof d.media.renderer.destroy&&d.media.renderer.destroy(),delete dd.default.players[d.id],"object"===T(d.getElement(d.container))&&(d.getElement(d.container).parentNode.querySelector("."+d.options.classPrefix+"offscreen").remove(),d.getElement(d.container).remove()),d.globalUnbind("resize",d.globalResizeCallback),d.globalUnbind("keydown",d.globalKeydownCallback),d.globalUnbind("click",d.globalClickCallback),delete d.media.player}},{key:"paused",get:function(){return this.proxy.paused}},{key:"muted",get:function(){return this.proxy.muted},set:function(d){this.setMuted(d)}},{key:"ended",get:function(){return this.proxy.ended}},{key:"readyState",get:function(){return this.proxy.readyState}},{key:"currentTime",set:function(d){this.setCurrentTime(d)},get:function(){return this.getCurrentTime()}},{key:"duration",get:function(){return this.getDuration()}},{key:"volume",set:function(d){this.setVolume(d)},get:function(){return this.getVolume()}},{key:"src",set:function(d){this.setSrc(d)},get:function(){return this.getSrc()}}]),d}();dO.default.MediaElementPlayer=eI,dd.default.MediaElementPlayer=eI,b.default=eI},{17:17,2:2,25:25,26:26,27:27,28:28,3:3,30:30,5:5,6:6,7:7}],17:[function(d,E,b){"use strict";function A(d,E){if(!(d instanceof E))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(b,"__esModule",{value:!0});var g=function(){function d(d,E){for(var b=0;b<E.length;b++){var A=E[b];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(d,A.key,A)}}return function(E,b,A){return b&&d(E.prototype,b),A&&d(E,A),E}}(),T=function(d){return d&&d.__esModule?d:{default:d}}(d(3)),bV=function(){function d(E){return A(this,d),this.media=E.media,this.isVideo=E.isVideo,this.classPrefix=E.options.classPrefix,this.createIframeLayer=function(){return E.createIframeLayer()},this.setPoster=function(d){return E.setPoster(d)},this}return g(d,[{key:"play",value:function(){this.media.play()}},{key:"pause",value:function(){this.media.pause()}},{key:"load",value:function(){var d=this;d.isLoaded||d.media.load(),d.isLoaded=!0}},{key:"setCurrentTime",value:function(d){this.media.setCurrentTime(d)}},{key:"getCurrentTime",value:function(){return this.media.currentTime}},{key:"getDuration",value:function(){return this.media.getDuration()}},{key:"setVolume",value:function(d){this.media.setVolume(d)}},{key:"getVolume",value:function(){return this.media.getVolume()}},{key:"setMuted",value:function(d){this.media.setMuted(d)}},{key:"setSrc",value:function(d){var E=this,b=document.getElementById(E.media.id+"-iframe-overlay");b&&b.remove(),E.media.setSrc(d),E.createIframeLayer(),null!==E.media.renderer&&"function"==typeof E.media.renderer.getPosterUrl&&E.setPoster(E.media.renderer.getPosterUrl())}},{key:"getSrc",value:function(){return this.media.getSrc()}},{key:"canPlayType",value:function(d){return this.media.canPlayType(d)}},{key:"paused",get:function(){return this.media.paused}},{key:"muted",set:function(d){this.setMuted(d)},get:function(){return this.media.muted}},{key:"ended",get:function(){return this.media.ended}},{key:"readyState",get:function(){return this.media.readyState}},{key:"currentTime",set:function(d){this.setCurrentTime(d)},get:function(){return this.getCurrentTime()}},{key:"duration",get:function(){return this.getDuration()}},{key:"volume",set:function(d){this.setVolume(d)},get:function(){return this.getVolume()}},{key:"src",set:function(d){this.setSrc(d)},get:function(){return this.getSrc()}}]),d}();b.default=bV,T.default.DefaultPlayer=bV},{3:3}],18:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(3)),T=A(d(7)),bV=A(d(16));"undefined"!=typeof jQuery?T.default.$=g.default.jQuery=g.default.$=jQuery:"undefined"!=typeof Zepto?T.default.$=g.default.Zepto=g.default.$=Zepto:"undefined"!=typeof ender&&(T.default.$=g.default.ender=g.default.$=ender),function(d){void 0!==d&&(d.fn.mediaelementplayer=function(E){return!1===E?this.each(function(){var E=d(this).data("mediaelementplayer");E&&E.remove(),d(this).removeData("mediaelementplayer")}):this.each(function(){d(this).data("mediaelementplayer",new bV.default(this,E))}),this},d(document).ready(function(){d("."+T.default.MepDefaults.classPrefix+"player").mediaelementplayer()}))}(T.default.$)},{16:16,3:3,7:7}],19:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},T=A(d(3)),bV=A(d(7)),dO=d(8),a=d(27),dd=d(28),W=d(25),gP=d(26),e={promise:null,load:function(d){return"undefined"!=typeof dashjs?e.promise=new Promise(function(d){d()}).then(function(){e._createPlayer(d)}):(d.options.path="string"==typeof d.options.path?d.options.path:"https://cdn.dashjs.org/latest/dash.all.min.js",e.promise=e.promise||(0,gP.loadScript)(d.options.path),e.promise.then(function(){e._createPlayer(d)})),e.promise},_createPlayer:function(d){var E=dashjs.MediaPlayer().create();return T.default["__ready__"+d.id](E),E}},c={name:"native_dash",options:{prefix:"native_dash",dash:{path:"https://cdn.dashjs.org/latest/dash.all.min.js",debug:!1,drm:{},robustnessLevel:""}},canPlayType:function(d){return W.HAS_MSE&&["application/dash+xml"].indexOf(d.toLowerCase())>-1},create:function(d,E,b){var A=d.originalNode,dd=d.id+"_"+E.prefix,W=A.autoplay,gP=A.children,c=null,aA=null;A.removeAttribute("type");for(var cF=0,ai=gP.length;cF<ai;cF++)gP[cF].removeAttribute("type");c=A.cloneNode(!0),E=Object.assign(E,d.options);for(var df=bV.default.html5media.properties,eK=bV.default.html5media.events.concat(["click","mouseover","mouseout"]),eI=function(E){if("error"!==E.type){var b=(0,a.createEvent)(E.type,d);d.dispatchEvent(b)}},eU=0,gT=df.length;eU<gT;eU++)!function(d){var b=""+d.substring(0,1).toUpperCase()+d.substring(1);c["get"+b]=function(){return null!==aA?c[d]:null},c["set"+b]=function(b){if(-1===bV.default.html5media.readOnlyProperties.indexOf(d))if("src"===d){var A="object"===(void 0===b?"undefined":g(b))&&b.src?b.src:b;if(c[d]=A,null!==aA){aA.reset();for(var T=0,dO=eK.length;T<dO;T++)c.removeEventListener(eK[T],eI);aA=e._createPlayer({options:E.dash,id:dd}),b&&"object"===(void 0===b?"undefined":g(b))&&"object"===g(b.drm)&&(aA.setProtectionData(b.drm),(0,a.isString)(E.dash.robustnessLevel)&&E.dash.robustnessLevel&&aA.getProtectionController().setRobustnessLevel(E.dash.robustnessLevel)),aA.attachSource(A),W&&aA.play()}}else c[d]=b}}(df[eU]);if(T.default["__ready__"+dd]=function(b){d.dashPlayer=aA=b;for(var A=dashjs.MediaPlayer.events,T=0,dO=eK.length;T<dO;T++)!function(d){"loadedmetadata"===d&&(aA.getDebug().setLogToBrowserConsole(E.dash.debug),aA.initialize(),aA.setScheduleWhilePaused(!1),aA.setFastSwitchEnabled(!0),aA.attachView(c),aA.setAutoPlay(!1),"object"!==g(E.dash.drm)||bV.default.Utils.isObjectEmpty(E.dash.drm)||(aA.setProtectionData(E.dash.drm),(0,a.isString)(E.dash.robustnessLevel)&&E.dash.robustnessLevel&&aA.getProtectionController().setRobustnessLevel(E.dash.robustnessLevel)),aA.attachSource(c.getSrc())),c.addEventListener(d,eI)}(eK[T]);var dd=function(E,b){if("error"===E.toLowerCase())d.generateError(b.message,c.src),console.error(b);else{var A=(0,a.createEvent)(E,d);A.data=b,d.dispatchEvent(A)}};for(var W in A)A.hasOwnProperty(W)&&aA.on(A[W],function(d){for(var E=arguments.length,b=Array(E>1?E-1:0),A=1;A<E;A++)b[A-1]=arguments[A];return dd(d.type,b)})},b&&b.length>0)for(var ej=0,eh=b.length;ej<eh;ej++)if(dO.renderer.renderers[E.prefix].canPlayType(b[ej].type)){c.setAttribute("src",b[ej].src),void 0!==b[ej].drm&&(E.dash.drm=b[ej].drm);break}c.setAttribute("id",dd),A.parentNode.insertBefore(c,A),A.autoplay=!1,A.style.display="none",c.setSize=function(d,E){return c.style.width=d+"px",c.style.height=E+"px",c},c.hide=function(){return c.pause(),c.style.display="none",c},c.show=function(){return c.style.display="",c},c.destroy=function(){null!==aA&&aA.reset()};var eY=(0,a.createEvent)("rendererready",c);return d.dispatchEvent(eY),d.promises.push(e.load({options:E.dash,id:dd})),c}};dd.typeChecks.push(function(d){return~d.toLowerCase().indexOf(".mpd")?"application/dash+xml":null}),dO.renderer.add(c)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],20:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}Object.defineProperty(b,"__esModule",{value:!0}),b.PluginDetector=void 0;var g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},T=A(d(3)),bV=A(d(2)),dO=A(d(7)),a=A(d(5)),dd=d(8),W=d(27),gP=d(25),e=d(28),c=b.PluginDetector={plugins:[],hasPluginVersion:function(d,E){var b=c.plugins[d];return E[1]=E[1]||0,E[2]=E[2]||0,b[0]>E[0]||b[0]===E[0]&&b[1]>E[1]||b[0]===E[0]&&b[1]===E[1]&&b[2]>=E[2]},addPlugin:function(d,E,b,A,g){c.plugins[d]=c.detectPlugin(E,b,A,g)},detectPlugin:function(d,E,b,A){var bV=[0,0,0],dO=void 0,a=void 0;if(null!==gP.NAV.plugins&&void 0!==gP.NAV.plugins&&"object"===g(gP.NAV.plugins[d])){if((dO=gP.NAV.plugins[d].description)&&(void 0===gP.NAV.mimeTypes||!gP.NAV.mimeTypes[E]||gP.NAV.mimeTypes[E].enabledPlugin))for(var dd=0,W=(bV=dO.replace(d,"").replace(/^\s+/,"").replace(/\sr/gi,".").split(".")).length;dd<W;dd++)bV[dd]=parseInt(bV[dd].match(/\d+/),10)}else if(void 0!==T.default.ActiveXObject)try{(a=new ActiveXObject(b))&&(bV=A(a))}catch(d){}return bV}};c.addPlugin("flash","Shockwave Flash","application/x-shockwave-flash","ShockwaveFlash.ShockwaveFlash",function(d){var E=[],b=d.GetVariable("$version");return b&&(b=b.split(" ")[1].split(","),E=[parseInt(b[0],10),parseInt(b[1],10),parseInt(b[2],10)]),E});var aA={create:function(d,E,b){var A={},g=!1;A.options=E,A.id=d.id+"_"+A.options.prefix,A.mediaElement=d,A.flashState={},A.flashApi=null,A.flashApiStack=[];for(var c=dO.default.html5media.properties,aA=0,cF=c.length;aA<cF;aA++)!function(d){A.flashState[d]=null;var E=""+d.substring(0,1).toUpperCase()+d.substring(1);A["get"+E]=function(){if(null!==A.flashApi){if("function"==typeof A.flashApi["get_"+d]){var E=A.flashApi["get_"+d]();return"buffered"===d?{start:function(){return 0},end:function(){return E},length:1}:E}return null}return null},A["set"+E]=function(E){if("src"===d&&(E=(0,e.absolutizeUrl)(E)),null!==A.flashApi&&void 0!==A.flashApi["set_"+d])try{A.flashApi["set_"+d](E)}catch(d){}else A.flashApiStack.push({type:"set",propName:d,value:E})}}(c[aA]);var ai=dO.default.html5media.methods;ai.push("stop");for(var df=0,eK=ai.length;df<eK;df++)!function(d){A[d]=function(){if(g)if(null!==A.flashApi){if(A.flashApi["fire_"+d])try{A.flashApi["fire_"+d]()}catch(d){}}else A.flashApiStack.push({type:"call",methodName:d})}}(ai[df]);for(var eI=["rendererready"],eU=0,gT=eI.length;eU<gT;eU++){var ej=(0,W.createEvent)(eI[eU],A);d.dispatchEvent(ej)}T.default["__ready__"+A.id]=function(){if(A.flashReady=!0,A.flashApi=bV.default.getElementById("__"+A.id),A.flashApiStack.length)for(var d=0,E=A.flashApiStack.length;d<E;d++){var b=A.flashApiStack[d];if("set"===b.type){var g=b.propName,T=""+g.substring(0,1).toUpperCase()+g.substring(1);A["set"+T](b.value)}else"call"===b.type&&A[b.methodName]()}},T.default["__event__"+A.id]=function(d,E){var b=(0,W.createEvent)(d,A);if(E)try{b.data=JSON.parse(E),b.details.data=JSON.parse(E)}catch(d){b.message=E}A.mediaElement.dispatchEvent(b)},A.flashWrapper=bV.default.createElement("div"),-1===["always","sameDomain"].indexOf(A.options.shimScriptAccess)&&(A.options.shimScriptAccess="sameDomain");var eh=d.originalNode.autoplay,eY=["uid="+A.id,"autoplay="+eh,"allowScriptAccess="+A.options.shimScriptAccess,"preload="+(d.originalNode.getAttribute("preload")||"")],ed=null!==d.originalNode&&"video"===d.originalNode.tagName.toLowerCase(),gX=ed?d.originalNode.height:1,aW=ed?d.originalNode.width:1;d.originalNode.getAttribute("src")&&eY.push("src="+d.originalNode.getAttribute("src")),!0===A.options.enablePseudoStreaming&&(eY.push("pseudostreamstart="+A.options.pseudoStreamingStartQueryParam),eY.push("pseudostreamtype="+A.options.pseudoStreamingType)),A.options.streamDelimiter&&eY.push("streamdelimiter="+encodeURIComponent(A.options.streamDelimiter)),A.options.proxyType&&eY.push("proxytype="+A.options.proxyType),d.appendChild(A.flashWrapper),d.originalNode.style.display="none";var ddc=[];if(gP.IS_IE||gP.IS_EDGE){var Q=bV.default.createElement("div");A.flashWrapper.appendChild(Q),ddc=gP.IS_EDGE?['type="application/x-shockwave-flash"','data="'+A.options.pluginPath+A.options.filename+'"','id="__'+A.id+'"','width="'+aW+'"','height="'+gX+"'\""]:['classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"','codebase="//download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab"','id="__'+A.id+'"','width="'+aW+'"','height="'+gX+'"'],ed||ddc.push('style="clip: rect(0 0 0 0); position: absolute;"'),Q.outerHTML="<object "+ddc.join(" ")+'><param name="movie" value="'+A.options.pluginPath+A.options.filename+"?x="+new Date+'" /><param name="flashvars" value="'+eY.join("&amp;")+'" /><param name="quality" value="high" /><param name="bgcolor" value="#000000" /><param name="wmode" value="transparent" /><param name="allowScriptAccess" value="'+A.options.shimScriptAccess+'" /><param name="allowFullScreen" value="true" /><div>'+a.default.t("mejs.install-flash")+"</div></object>"}else ddc=['id="__'+A.id+'"','name="__'+A.id+'"','play="true"','loop="false"','quality="high"','bgcolor="#000000"','wmode="transparent"','allowScriptAccess="'+A.options.shimScriptAccess+'"','allowFullScreen="true"','type="application/x-shockwave-flash"','pluginspage="//www.macromedia.com/go/getflashplayer"','src="'+A.options.pluginPath+A.options.filename+'"','flashvars="'+eY.join("&")+'"'],ed?(ddc.push('width="'+aW+'"'),ddc.push('height="'+gX+'"')):ddc.push('style="position: fixed; left: -9999em; top: -9999em;"'),A.flashWrapper.innerHTML="<embed "+ddc.join(" ")+">";if(A.flashNode=A.flashWrapper.lastChild,A.hide=function(){g=!1,ed&&(A.flashNode.style.display="none")},A.show=function(){g=!0,ed&&(A.flashNode.style.display="")},A.setSize=function(d,E){A.flashNode.style.width=d+"px",A.flashNode.style.height=E+"px",null!==A.flashApi&&"function"==typeof A.flashApi.fire_setSize&&A.flashApi.fire_setSize(d,E)},A.destroy=function(){A.flashNode.remove()},b&&b.length>0)for(var dh=0,bA=b.length;dh<bA;dh++)if(dd.renderer.renderers[E.prefix].canPlayType(b[dh].type)){A.setSrc(b[dh].src);break}return A}};if(c.hasPluginVersion("flash",[10,0,0])){e.typeChecks.push(function(d){return(d=d.toLowerCase()).startsWith("rtmp")?~d.indexOf(".mp3")?"audio/rtmp":"video/rtmp":/\.og(a|g)/i.test(d)?"audio/ogg":~d.indexOf(".m3u8")?"application/x-mpegURL":~d.indexOf(".mpd")?"application/dash+xml":~d.indexOf(".flv")?"video/flv":null});var cF={name:"flash_video",options:{prefix:"flash_video",filename:"mediaelement-flash-video.swf",enablePseudoStreaming:!1,pseudoStreamingStartQueryParam:"start",pseudoStreamingType:"byte",proxyType:"",streamDelimiter:""},canPlayType:function(d){return~["video/mp4","video/rtmp","audio/rtmp","rtmp/mp4","audio/mp4","video/flv","video/x-flv"].indexOf(d.toLowerCase())},create:aA.create};dd.renderer.add(cF);var ai={name:"flash_hls",options:{prefix:"flash_hls",filename:"mediaelement-flash-video-hls.swf"},canPlayType:function(d){return~["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(d.toLowerCase())},create:aA.create};dd.renderer.add(ai);var df={name:"flash_dash",options:{prefix:"flash_dash",filename:"mediaelement-flash-video-mdash.swf"},canPlayType:function(d){return~["application/dash+xml"].indexOf(d.toLowerCase())},create:aA.create};dd.renderer.add(df);var eK={name:"flash_audio",options:{prefix:"flash_audio",filename:"mediaelement-flash-audio.swf"},canPlayType:function(d){return~["audio/mp3"].indexOf(d.toLowerCase())},create:aA.create};dd.renderer.add(eK);var eI={name:"flash_audio_ogg",options:{prefix:"flash_audio_ogg",filename:"mediaelement-flash-audio-ogg.swf"},canPlayType:function(d){return~["audio/ogg","audio/oga","audio/ogv"].indexOf(d.toLowerCase())},create:aA.create};dd.renderer.add(eI)}},{2:2,25:25,27:27,28:28,3:3,5:5,7:7,8:8}],21:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},T=A(d(3)),bV=A(d(7)),dO=d(8),a=d(27),dd=d(25),W=d(28),gP=d(26),e={promise:null,load:function(d){return"undefined"!=typeof flvjs?e.promise=new Promise(function(d){d()}).then(function(){e._createPlayer(d)}):(d.options.path="string"==typeof d.options.path?d.options.path:"https://cdnjs.cloudflare.com/ajax/libs/flv.js/1.3.3/flv.min.js",e.promise=e.promise||(0,gP.loadScript)(d.options.path),e.promise.then(function(){e._createPlayer(d)})),e.promise},_createPlayer:function(d){flvjs.LoggingControl.enableDebug=d.options.debug,flvjs.LoggingControl.enableVerbose=d.options.debug;var E=flvjs.createPlayer(d.options,d.configs);return T.default["__ready__"+d.id](E),E}},c={name:"native_flv",options:{prefix:"native_flv",flv:{path:"https://cdnjs.cloudflare.com/ajax/libs/flv.js/1.3.3/flv.min.js",cors:!0,debug:!1}},canPlayType:function(d){return dd.HAS_MSE&&["video/x-flv","video/flv"].indexOf(d.toLowerCase())>-1},create:function(d,E,b){var A=d.originalNode,dd=d.id+"_"+E.prefix,W=null,gP=null;W=A.cloneNode(!0),E=Object.assign(E,d.options);for(var c=bV.default.html5media.properties,aA=bV.default.html5media.events.concat(["click","mouseover","mouseout"]),cF=function(E){if("error"!==E.type){var b=(0,a.createEvent)(E.type,d);d.dispatchEvent(b)}},ai=0,df=c.length;ai<df;ai++)!function(d){var b=""+d.substring(0,1).toUpperCase()+d.substring(1);W["get"+b]=function(){return null!==gP?W[d]:null},W["set"+b]=function(b){if(-1===bV.default.html5media.readOnlyProperties.indexOf(d))if("src"===d){if(W[d]="object"===(void 0===b?"undefined":g(b))&&b.src?b.src:b,null!==gP){var A={};A.type="flv",A.url=b,A.cors=E.flv.cors,A.debug=E.flv.debug,A.path=E.flv.path;var T=E.flv.configs;gP.destroy();for(var dO=0,a=aA.length;dO<a;dO++)W.removeEventListener(aA[dO],cF);(gP=e._createPlayer({options:A,configs:T,id:dd})).attachMediaElement(W),gP.load()}}else W[d]=b}}(c[ai]);if(T.default["__ready__"+dd]=function(E){d.flvPlayer=gP=E;for(var b=flvjs.Events,A=0,g=aA.length;A<g;A++)!function(d){"loadedmetadata"===d&&(gP.unload(),gP.detachMediaElement(),gP.attachMediaElement(W),gP.load()),W.addEventListener(d,cF)}(aA[A]);var T=function(E,b){if("error"===E){var A=b[0]+": "+b[1]+" "+b[2].msg;d.generateError(A,W.src)}else{var g=(0,a.createEvent)(E,d);g.data=b,d.dispatchEvent(g)}};for(var bV in b)!function(d){b.hasOwnProperty(d)&&gP.on(b[d],function(){for(var E=arguments.length,A=Array(E),g=0;g<E;g++)A[g]=arguments[g];return T(b[d],A)})}(bV)},b&&b.length>0)for(var eK=0,eI=b.length;eK<eI;eK++)if(dO.renderer.renderers[E.prefix].canPlayType(b[eK].type)){W.setAttribute("src",b[eK].src);break}W.setAttribute("id",dd),A.parentNode.insertBefore(W,A),A.autoplay=!1,A.style.display="none";var eU={};eU.type="flv",eU.url=W.src,eU.cors=E.flv.cors,eU.debug=E.flv.debug,eU.path=E.flv.path;var gT=E.flv.configs;W.setSize=function(d,E){return W.style.width=d+"px",W.style.height=E+"px",W},W.hide=function(){return null!==gP&&gP.pause(),W.style.display="none",W},W.show=function(){return W.style.display="",W},W.destroy=function(){null!==gP&&gP.destroy()};var ej=(0,a.createEvent)("rendererready",W);return d.dispatchEvent(ej),d.promises.push(e.load({options:eU,configs:gT,id:dd})),W}};W.typeChecks.push(function(d){return~d.toLowerCase().indexOf(".flv")?"video/flv":null}),dO.renderer.add(c)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],22:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},T=A(d(3)),bV=A(d(7)),dO=d(8),a=d(27),dd=d(25),W=d(28),gP=d(26),e={promise:null,load:function(d){return"undefined"!=typeof Hls?e.promise=new Promise(function(d){d()}).then(function(){e._createPlayer(d)}):(d.options.path="string"==typeof d.options.path?d.options.path:"https://cdnjs.cloudflare.com/ajax/libs/hls.js/0.8.4/hls.min.js",e.promise=e.promise||(0,gP.loadScript)(d.options.path),e.promise.then(function(){e._createPlayer(d)})),e.promise},_createPlayer:function(d){var E=new Hls(d.options);return T.default["__ready__"+d.id](E),E}},c={name:"native_hls",options:{prefix:"native_hls",hls:{path:"https://cdnjs.cloudflare.com/ajax/libs/hls.js/0.8.4/hls.min.js",autoStartLoad:!1,debug:!1}},canPlayType:function(d){return dd.HAS_MSE&&["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(d.toLowerCase())>-1},create:function(d,E,b){var A=d.originalNode,dd=d.id+"_"+E.prefix,W=A.getAttribute("preload"),gP=A.autoplay,c=null,aA=null,cF=0,ai=b.length;aA=A.cloneNode(!0),(E=Object.assign(E,d.options)).hls.autoStartLoad=W&&"none"!==W||gP;for(var df=bV.default.html5media.properties,eK=bV.default.html5media.events.concat(["click","mouseover","mouseout"]),eI=function(E){if("error"!==E.type){var b=(0,a.createEvent)(E.type,d);d.dispatchEvent(b)}},eU=0,gT=df.length;eU<gT;eU++)!function(d){var b=""+d.substring(0,1).toUpperCase()+d.substring(1);aA["get"+b]=function(){return null!==c?aA[d]:null},aA["set"+b]=function(b){if(-1===bV.default.html5media.readOnlyProperties.indexOf(d))if("src"===d){if(aA[d]="object"===(void 0===b?"undefined":g(b))&&b.src?b.src:b,null!==c){c.destroy();for(var A=0,T=eK.length;A<T;A++)aA.removeEventListener(eK[A],eI);(c=e._createPlayer({options:E.hls,id:dd})).loadSource(b),c.attachMedia(aA)}}else aA[d]=b}}(df[eU]);if(T.default["__ready__"+dd]=function(E){d.hlsPlayer=c=E;for(var A=Hls.Events,g=0,T=eK.length;g<T;g++)!function(E){if("loadedmetadata"===E){var b=d.originalNode.src;c.detachMedia(),c.loadSource(b),c.attachMedia(aA)}aA.addEventListener(E,eI)}(eK[g]);var bV=void 0,dO=void 0,dd=function(E,A){if("hlsError"===E){if(console.warn(A),(A=A[1]).fatal)switch(A.type){case"mediaError":var g=(new Date).getTime();if(!bV||g-bV>3e3)bV=(new Date).getTime(),c.recoverMediaError();else if(!dO||g-dO>3e3)dO=(new Date).getTime(),console.warn("Attempting to swap Audio Codec and recover from media error"),c.swapAudioCodec(),c.recoverMediaError();else{var T="Cannot recover, last media error recovery failed";d.generateError(T,aA.src),console.error(T)}break;case"networkError":if("manifestLoadError"===A.details)if(cF<ai&&void 0!==b[cF+1])aA.setSrc(b[cF++].src),aA.load(),aA.play();else{d.generateError("Network error",b),console.error("Network error")}else{d.generateError("Network error",b),console.error("Network error")}break;default:c.destroy()}}else{var dd=(0,a.createEvent)(E,d);dd.data=A,d.dispatchEvent(dd)}};for(var W in A)!function(d){A.hasOwnProperty(d)&&c.on(A[d],function(){for(var E=arguments.length,b=Array(E),g=0;g<E;g++)b[g]=arguments[g];return dd(A[d],b)})}(W)},ai>0)for(;cF<ai;cF++)if(dO.renderer.renderers[E.prefix].canPlayType(b[cF].type)){aA.setAttribute("src",b[cF].src);break}"auto"===W||gP||(aA.addEventListener("play",function(){null!==c&&c.startLoad()}),aA.addEventListener("pause",function(){null!==c&&c.stopLoad()})),aA.setAttribute("id",dd),A.parentNode.insertBefore(aA,A),A.autoplay=!1,A.style.display="none",aA.setSize=function(d,E){return aA.style.width=d+"px",aA.style.height=E+"px",aA},aA.hide=function(){return aA.pause(),aA.style.display="none",aA},aA.show=function(){return aA.style.display="",aA},aA.destroy=function(){null!==c&&(c.stopLoad(),c.destroy())};var ej=(0,a.createEvent)("rendererready",aA);return d.dispatchEvent(ej),d.promises.push(e.load({options:E.hls,id:dd})),aA}};W.typeChecks.push(function(d){return~d.toLowerCase().indexOf(".m3u8")?"application/x-mpegURL":null}),dO.renderer.add(c)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],23:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(3)),T=A(d(2)),bV=A(d(7)),dO=d(8),a=d(27),dd=d(25),W={name:"html5",options:{prefix:"html5"},canPlayType:function(d){var E=T.default.createElement("video");return dd.IS_ANDROID&&/\/mp(3|4)$/i.test(d)||~["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(d.toLowerCase())&&dd.SUPPORTS_NATIVE_HLS?"yes":E.canPlayType?E.canPlayType(d.toLowerCase()).replace(/no/,""):""},create:function(d,E,b){var A=d.id+"_"+E.prefix,g=!1,dd=null;void 0===d.originalNode||null===d.originalNode?(dd=T.default.createElement("audio"),d.appendChild(dd)):dd=d.originalNode,dd.setAttribute("id",A);for(var W=bV.default.html5media.properties,gP=0,e=W.length;gP<e;gP++)!function(d){var E=""+d.substring(0,1).toUpperCase()+d.substring(1);dd["get"+E]=function(){return dd[d]},dd["set"+E]=function(E){-1===bV.default.html5media.readOnlyProperties.indexOf(d)&&(dd[d]=E)}}(W[gP]);for(var c=bV.default.html5media.events.concat(["click","mouseover","mouseout"]),aA=0,cF=c.length;aA<cF;aA++)!function(E){dd.addEventListener(E,function(E){if(g){var b=(0,a.createEvent)(E.type,E.target);d.dispatchEvent(b)}})}(c[aA]);dd.setSize=function(d,E){return dd.style.width=d+"px",dd.style.height=E+"px",dd},dd.hide=function(){return g=!1,dd.style.display="none",dd},dd.show=function(){return g=!0,dd.style.display="",dd};var ai=0,df=b.length;if(df>0)for(;ai<df;ai++)if(dO.renderer.renderers[E.prefix].canPlayType(b[ai].type)){dd.setAttribute("src",b[ai].src);break}dd.addEventListener("error",function(E){4===E.target.error.code&&g&&(ai<df&&void 0!==b[ai+1]?(dd.src=b[ai++].src,dd.load(),dd.play()):d.generateError("Media error: Format(s) not supported or source(s) not found",b))});var eK=(0,a.createEvent)("rendererready",dd);return d.dispatchEvent(eK),dd}};g.default.HtmlMediaElement=bV.default.HtmlMediaElement=W,dO.renderer.add(W)},{2:2,25:25,27:27,3:3,7:7,8:8}],24:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(3)),T=A(d(2)),bV=A(d(7)),dO=d(8),a=d(27),dd=d(28),W=d(26),gP={isIframeStarted:!1,isIframeLoaded:!1,iframeQueue:[],enqueueIframe:function(d){gP.isLoaded="undefined"!=typeof YT&&YT.loaded,gP.isLoaded?gP.createIframe(d):(gP.loadIframeApi(),gP.iframeQueue.push(d))},loadIframeApi:function(){gP.isIframeStarted||((0,W.loadScript)("https://www.youtube.com/player_api"),gP.isIframeStarted=!0)},iFrameReady:function(){for(gP.isLoaded=!0,gP.isIframeLoaded=!0;gP.iframeQueue.length>0;){var d=gP.iframeQueue.pop();gP.createIframe(d)}},createIframe:function(d){return new YT.Player(d.containerId,d)},getYouTubeId:function(d){var E="";return d.indexOf("?")>0?""===(E=gP.getYouTubeIdFromParam(d))&&(E=gP.getYouTubeIdFromUrl(d)):E=gP.getYouTubeIdFromUrl(d),(E=E.substring(E.lastIndexOf("../index.html")+1).split("?"))[0]},getYouTubeIdFromParam:function(d){if(void 0===d||null===d||!d.trim().length)return null;for(var E=d.split("?")[1].split("&"),b="",A=0,g=E.length;A<g;A++){var T=E[A].split("=");if("v"===T[0]){b=T[1];break}}return b},getYouTubeIdFromUrl:function(d){return void 0!==d&&null!==d&&d.trim().length?(d=d.split("?")[0]).substring(d.lastIndexOf("../index.html")+1):null},getYouTubeNoCookieUrl:function(d){if(void 0===d||null===d||!d.trim().length||-1===d.indexOf("//www.youtube"))return d;var E=d.split("../index.html");return E[2]=E[2].replace(".com","-nocookie.com"),E.join("../index.html")}},e={name:"youtube_iframe",options:{prefix:"youtube_iframe",youtube:{autoplay:0,controls:0,disablekb:1,end:0,loop:0,modestbranding:0,playsinline:0,rel:0,showinfo:0,start:0,iv_load_policy:3,nocookie:!1,imageQuality:null}},canPlayType:function(d){return~["video/youtube","video/x-youtube"].indexOf(d.toLowerCase())},create:function(d,E,b){var A={},dO=[],dd=null,W=!0,e=!1,c=null,aA=1;A.options=E,A.id=d.id+"_"+E.prefix,A.mediaElement=d;for(var cF=bV.default.html5media.properties,ai=0,df=cF.length;ai<df;ai++)!function(E){var b=""+E.substring(0,1).toUpperCase()+E.substring(1);A["get"+b]=function(){if(null!==dd){switch(E){case"currentTime":return dd.getCurrentTime();case"duration":return dd.getDuration();case"volume":return aA=dd.getVolume()/100;case"paused":return W;case"ended":return e;case"muted":return dd.isMuted();case"buffered":var d=dd.getVideoLoadedFraction(),b=dd.getDuration();return{start:function(){return 0},end:function(){return d*b},length:1};case"src":return dd.getVideoUrl();case"readyState":return 4}return null}return null},A["set"+b]=function(b){if(null!==dd)switch(E){case"src":var g="string"==typeof b?b:b[0].src,T=gP.getYouTubeId(g);d.originalNode.autoplay?dd.loadVideoById(T):dd.cueVideoById(T);break;case"currentTime":dd.seekTo(b);break;case"muted":b?dd.mute():dd.unMute(),setTimeout(function(){var E=(0,a.createEvent)("volumechange",A);d.dispatchEvent(E)},50);break;case"volume":aA=b,dd.setVolume(100*b),setTimeout(function(){var E=(0,a.createEvent)("volumechange",A);d.dispatchEvent(E)},50);break;case"readyState":var bV=(0,a.createEvent)("canplay",A);d.dispatchEvent(bV)}else dO.push({type:"set",propName:E,value:b})}}(cF[ai]);for(var eK=bV.default.html5media.methods,eI=0,eU=eK.length;eI<eU;eI++)!function(d){A[d]=function(){if(null!==dd)switch(d){case"play":return W=!1,dd.playVideo();case"pause":return W=!0,dd.pauseVideo();case"load":return null}else dO.push({type:"call",methodName:d})}}(eK[eI]);var gT=T.default.createElement("div");gT.id=A.id,A.options.youtube.nocookie&&(d.originalNode.src=gP.getYouTubeNoCookieUrl(b[0].src)),d.originalNode.parentNode.insertBefore(gT,d.originalNode),d.originalNode.style.display="none";var ej="audio"===d.originalNode.tagName.toLowerCase(),eh=ej?"1":d.originalNode.height,eY=ej?"1":d.originalNode.width,ed=gP.getYouTubeId(b[0].src),gX={id:A.id,containerId:gT.id,videoId:ed,height:eh,width:eY,playerVars:Object.assign({controls:0,rel:0,disablekb:1,showinfo:0,modestbranding:0,html5:1,iv_load_policy:3},A.options.youtube),origin:g.default.location.host,events:{onReady:function(E){if(d.youTubeApi=dd=E.target,d.youTubeState={paused:!0,ended:!1},dO.length)for(var b=0,g=dO.length;b<g;b++){var T=dO[b];if("set"===T.type){var bV=T.propName,W=""+bV.substring(0,1).toUpperCase()+bV.substring(1);A["set"+W](T.value)}else"call"===T.type&&A[T.methodName]()}c=dd.getIframe(),d.originalNode.muted&&dd.mute();for(var gP=["mouseover","mouseout"],e=0,aA=gP.length;e<aA;e++)c.addEventListener(gP[e],function(E){var b=(0,a.createEvent)(E.type,A);d.dispatchEvent(b)},!1);for(var cF=["rendererready","loadedmetadata","loadeddata","canplay"],ai=0,df=cF.length;ai<df;ai++){var eK=(0,a.createEvent)(cF[ai],A);d.dispatchEvent(eK)}},onStateChange:function(E){var b=[];switch(E.data){case-1:b=["loadedmetadata"],W=!0,e=!1;break;case 0:b=["ended"],W=!1,e=!A.options.youtube.loop,A.options.youtube.loop||A.stopInterval();break;case 1:b=["play","playing"],W=!1,e=!1,A.startInterval();break;case 2:b=["pause"],W=!0,e=!1,A.stopInterval();break;case 3:b=["progress"],e=!1;break;case 5:b=["loadeddata","loadedmetadata","canplay"],W=!0,e=!1}for(var g=0,T=b.length;g<T;g++){var bV=(0,a.createEvent)(b[g],A);d.dispatchEvent(bV)}},onError:function(E){var b=(0,a.createEvent)("error",A);b.data=E.data,d.dispatchEvent(b)}}};return(ej||d.originalNode.hasAttribute("playsinline"))&&(gX.playerVars.playsinline=1),d.originalNode.controls&&(gX.playerVars.controls=1),d.originalNode.autoplay&&(gX.playerVars.autoplay=1),d.originalNode.loop&&(gX.playerVars.loop=1),gP.enqueueIframe(gX),A.onEvent=function(E,b,A){null!==A&&void 0!==A&&(d.youTubeState=A)},A.setSize=function(d,E){null!==dd&&dd.setSize(d,E)},A.hide=function(){A.stopInterval(),A.pause(),c&&(c.style.display="none")},A.show=function(){c&&(c.style.display="")},A.destroy=function(){dd.destroy()},A.interval=null,A.startInterval=function(){A.interval=setInterval(function(){var E=(0,a.createEvent)("timeupdate",A);d.dispatchEvent(E)},250)},A.stopInterval=function(){A.interval&&clearInterval(A.interval)},A.getPosterUrl=function(){var b=E.youtube.imageQuality,A=["default","hqdefault","mqdefault","sddefault","maxresdefault"],g=gP.getYouTubeId(d.originalNode.src);return b&&A.indexOf(b)>-1&&g?"https://img.youtube.com/vi/"+g+"/"+b+".jpg":""},A}};g.default.onYouTubePlayerAPIReady=function(){gP.iFrameReady()},dd.typeChecks.push(function(d){return/\/\/(www\.youtube|youtu\.?be)/i.test(d)?"video/x-youtube":null}),dO.renderer.add(e)},{2:2,26:26,27:27,28:28,3:3,7:7,8:8}],25:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}Object.defineProperty(b,"__esModule",{value:!0}),b.cancelFullScreen=b.requestFullScreen=b.isFullScreen=b.FULLSCREEN_EVENT_NAME=b.HAS_NATIVE_FULLSCREEN_ENABLED=b.HAS_TRUE_NATIVE_FULLSCREEN=b.HAS_IOS_FULLSCREEN=b.HAS_MS_NATIVE_FULLSCREEN=b.HAS_MOZ_NATIVE_FULLSCREEN=b.HAS_WEBKIT_NATIVE_FULLSCREEN=b.HAS_NATIVE_FULLSCREEN=b.SUPPORTS_NATIVE_HLS=b.SUPPORT_PASSIVE_EVENT=b.SUPPORT_POINTER_EVENTS=b.HAS_MSE=b.IS_STOCK_ANDROID=b.IS_SAFARI=b.IS_FIREFOX=b.IS_CHROME=b.IS_EDGE=b.IS_IE=b.IS_ANDROID=b.IS_IOS=b.IS_IPOD=b.IS_IPHONE=b.IS_IPAD=b.UA=b.NAV=void 0;for(var g=A(d(3)),T=A(d(2)),bV=A(d(7)),dO=b.NAV=g.default.navigator,a=b.UA=dO.userAgent.toLowerCase(),dd=b.IS_IPAD=/ipad/i.test(a)&&!g.default.MSStream,W=b.IS_IPHONE=/iphone/i.test(a)&&!g.default.MSStream,gP=b.IS_IPOD=/ipod/i.test(a)&&!g.default.MSStream,e=(b.IS_IOS=/ipad|iphone|ipod/i.test(a)&&!g.default.MSStream,b.IS_ANDROID=/android/i.test(a)),c=b.IS_IE=/(trident|microsoft)/i.test(dO.appName),aA=(b.IS_EDGE="msLaunchUri"in dO&&!("documentMode"in T.default)),cF=b.IS_CHROME=/chrome/i.test(a),ai=b.IS_FIREFOX=/firefox/i.test(a),df=b.IS_SAFARI=/safari/i.test(a)&&!cF,eK=b.IS_STOCK_ANDROID=/^mozilla\/\d+\.\d+\s\(linux;\su;/i.test(a),eI=(b.HAS_MSE="MediaSource"in g.default),eU=b.SUPPORT_POINTER_EVENTS=function(){var d=T.default.createElement("x"),E=T.default.documentElement,b=g.default.getComputedStyle;if(!("pointerEvents"in d.style))return!1;d.style.pointerEvents="auto",d.style.pointerEvents="x",E.appendChild(d);var A=b&&"auto"===b(d,"").pointerEvents;return d.remove(),!!A}(),gT=b.SUPPORT_PASSIVE_EVENT=function(){var d=!1;try{var E=Object.defineProperty({},"passive",{get:function(){d=!0}});g.default.addEventListener("test",null,E)}catch(d){}return d}(),ej=["source","track","audio","video"],eh=void 0,eY=0,ed=ej.length;eY<ed;eY++)eh=T.default.createElement(ej[eY]);var gX=b.SUPPORTS_NATIVE_HLS=df||e&&(cF||eK)||c&&/edge/i.test(a),aW=void 0!==eh.webkitEnterFullscreen,ddc=void 0!==eh.requestFullscreen;aW&&/mac os x 10_5/i.test(a)&&(ddc=!1,aW=!1);var Q=void 0!==eh.webkitRequestFullScreen,dh=void 0!==eh.mozRequestFullScreen,bA=void 0!==eh.msRequestFullscreen,cK=Q||dh||bA,f=cK,M="",h=void 0,i=void 0,j=void 0;dh?f=T.default.mozFullScreenEnabled:bA&&(f=T.default.msFullscreenEnabled),cF&&(aW=!1),cK&&(Q?M="webkitfullscreenchange":dh?M="mozfullscreenchange":bA&&(M="MSFullscreenChange"),b.isFullScreen=h=function(){return dh?T.default.mozFullScreen:Q?T.default.webkitIsFullScreen:bA?null!==T.default.msFullscreenElement:void 0},b.requestFullScreen=i=function(d){Q?d.webkitRequestFullScreen():dh?d.mozRequestFullScreen():bA&&d.msRequestFullscreen()},b.cancelFullScreen=j=function(){Q?T.default.webkitCancelFullScreen():dh?T.default.mozCancelFullScreen():bA&&T.default.msExitFullscreen()});var k=b.HAS_NATIVE_FULLSCREEN=ddc,l=b.HAS_WEBKIT_NATIVE_FULLSCREEN=Q,m=b.HAS_MOZ_NATIVE_FULLSCREEN=dh,n=b.HAS_MS_NATIVE_FULLSCREEN=bA,o=b.HAS_IOS_FULLSCREEN=aW,p=b.HAS_TRUE_NATIVE_FULLSCREEN=cK,q=b.HAS_NATIVE_FULLSCREEN_ENABLED=f,r=b.FULLSCREEN_EVENT_NAME=M;b.isFullScreen=h,b.requestFullScreen=i,b.cancelFullScreen=j,bV.default.Features=bV.default.Features||{},bV.default.Features.isiPad=dd,bV.default.Features.isiPod=gP,bV.default.Features.isiPhone=W,bV.default.Features.isiOS=bV.default.Features.isiPhone||bV.default.Features.isiPad,bV.default.Features.isAndroid=e,bV.default.Features.isIE=c,bV.default.Features.isEdge=aA,bV.default.Features.isChrome=cF,bV.default.Features.isFirefox=ai,bV.default.Features.isSafari=df,bV.default.Features.isStockAndroid=eK,bV.default.Features.hasMSE=eI,bV.default.Features.supportsNativeHLS=gX,bV.default.Features.supportsPointerEvents=eU,bV.default.Features.supportsPassiveEvent=gT,bV.default.Features.hasiOSFullScreen=o,bV.default.Features.hasNativeFullscreen=k,bV.default.Features.hasWebkitNativeFullScreen=l,bV.default.Features.hasMozNativeFullScreen=m,bV.default.Features.hasMsNativeFullScreen=n,bV.default.Features.hasTrueNativeFullScreen=p,bV.default.Features.nativeFullScreenEnabled=q,bV.default.Features.fullScreenEventName=r,bV.default.Features.isFullScreen=h,bV.default.Features.requestFullScreen=i,bV.default.Features.cancelFullScreen=j},{2:2,3:3,7:7}],26:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}function g(d){return new Promise(function(E,b){var A=c.default.createElement("script");A.src=d,A.async=!0,A.onload=function(){A.remove(),E()},A.onerror=function(){A.remove(),b()},c.default.head.appendChild(A)})}function T(d){var E=d.getBoundingClientRect(),b=e.default.pageXOffset||c.default.documentElement.scrollLeft,A=e.default.pageYOffset||c.default.documentElement.scrollTop;return{top:E.top+A,left:E.left+b}}function bV(d,E){eK(d,E)?eU(d,E):eI(d,E)}function dO(d){var E=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,b=arguments[2];d.style.opacity||(d.style.opacity=1);var A=null;e.default.requestAnimationFrame(function g(T){var bV=T-(A=A||T),dO=parseFloat(1-bV/E,2);d.style.opacity=dO<0?0:dO,bV>E?b&&"function"==typeof b&&b():e.default.requestAnimationFrame(g)})}function a(d){var E=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,b=arguments[2];d.style.opacity||(d.style.opacity=0);var A=null;e.default.requestAnimationFrame(function g(T){var bV=T-(A=A||T),dO=parseFloat(bV/E,2);d.style.opacity=dO>1?1:dO,bV>E?b&&"function"==typeof b&&b():e.default.requestAnimationFrame(g)})}function dd(d,E){var b=[];d=d.parentNode.firstChild;do{E&&!E(d)||b.push(d)}while(d=d.nextSibling);return b}function W(d){return void 0!==d.getClientRects&&"function"===d.getClientRects?!!(d.offsetWidth||d.offsetHeight||d.getClientRects().length):!(!d.offsetWidth&&!d.offsetHeight)}function gP(d,E,b,A){var g=e.default.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),T="application/x-www-form-urlencoded; charset=UTF-8",bV=!1,dO="*/".concat("*");switch(E){case"text":T="text/plain";break;case"json":T="application/json, text/javascript";break;case"html":T="text/html";break;case"xml":T="application/xml, text/xml"}"application/x-www-form-urlencoded"!==T&&(dO=T+", */*; q=0.01"),g&&(g.open("GET.html",d,!0),g.setRequestHeader("Accept",dO),g.onreadystatechange=function(){if(!bV&&4===g.readyState)if(200===g.status){bV=!0;var d=void 0;switch(E){case"json":d=JSON.parse(g.responseText);break;case"xml":d=g.responseXML;break;default:d=g.responseText}b(d)}else"function"==typeof A&&A(g.status)},g.send())}Object.defineProperty(b,"__esModule",{value:!0}),b.removeClass=b.addClass=b.hasClass=void 0,b.loadScript=g,b.offset=T,b.toggleClass=bV,b.fadeOut=dO,b.fadeIn=a,b.siblings=dd,b.visible=W,b.ajax=gP;var e=A(d(3)),c=A(d(2)),aA=A(d(7)),cF=void 0,ai=void 0,df=void 0;"classList"in c.default.documentElement?(cF=function(d,E){return void 0!==d.classList&&d.classList.contains(E)},ai=function(d,E){return d.classList.add(E)},df=function(d,E){return d.classList.remove(E)}):(cF=function(d,E){return new RegExp("\\b"+E+"\\b").test(d.className)},ai=function(d,E){eK(d,E)||(d.className+=" "+E)},df=function(d,E){d.className=d.className.replace(new RegExp("\\b"+E+"\\b","g"),"")});var eK=b.hasClass=cF,eI=b.addClass=ai,eU=b.removeClass=df;aA.default.Utils=aA.default.Utils||{},aA.default.Utils.offset=T,aA.default.Utils.hasClass=eK,aA.default.Utils.addClass=eI,aA.default.Utils.removeClass=eU,aA.default.Utils.toggleClass=bV,aA.default.Utils.fadeIn=a,aA.default.Utils.fadeOut=dO,aA.default.Utils.siblings=dd,aA.default.Utils.visible=W,aA.default.Utils.ajax=gP,aA.default.Utils.loadScript=g},{2:2,3:3,7:7}],27:[function(d,E,b){"use strict";function A(d){if("string"!=typeof d)throw new Error("Argument passed must be a string");var E={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};return d.replace(/[&<>"]/g,function(d){return E[d]})}function g(d,E){var b=this,A=arguments,g=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("function"!=typeof d)throw new Error("First argument must be a function");if("number"!=typeof E)throw new Error("Second argument must be a numeric value");var T=void 0;return function(){var bV=b,dO=A,a=g&&!T;clearTimeout(T),T=setTimeout(function(){T=null,g||d.apply(bV,dO)},E),a&&d.apply(bV,dO)}}function T(d){return Object.getOwnPropertyNames(d).length<=0}function bV(d,E){var b=/^((after|before)print|(before)?unload|hashchange|message|o(ff|n)line|page(hide|show)|popstate|resize|storage)\b/,A={d:[],w:[]};return(d||"").split(" ").forEach(function(d){var g=d+(E?"."+E:"");g.startsWith(".")?(A.d.push(g),A.w.push(g)):A[b.test(d)?"w":"d"].push(g)}),A.d=A.d.join(" "),A.w=A.w.join(" "),A}function dO(d,E){if("string"!=typeof d)throw new Error("Event name must be a string");var b=d.match(/([a-z]+\.([a-z]+))/i),A={target:E};return null!==b&&(d=b[1],A.namespace=b[2]),new window.CustomEvent(d,{detail:A})}function a(d,E){return!!(d&&E&&2&d.compareDocumentPosition(E))}function dd(d){return"string"==typeof d}Object.defineProperty(b,"__esModule",{value:!0}),b.escapeHTML=A,b.debounce=g,b.isObjectEmpty=T,b.splitEvents=bV,b.createEvent=dO,b.isNodeAfter=a,b.isString=dd;var W=function(d){return d&&d.__esModule?d:{default:d}}(d(7));W.default.Utils=W.default.Utils||{},W.default.Utils.escapeHTML=A,W.default.Utils.debounce=g,W.default.Utils.isObjectEmpty=T,W.default.Utils.splitEvents=bV,W.default.Utils.createEvent=dO,W.default.Utils.isNodeAfter=a,W.default.Utils.isString=dd},{7:7}],28:[function(d,E,b){"use strict";function A(d){if("string"!=typeof d)throw new Error("`url` argument must be a string");var E=document.createElement("div");return E.innerHTML='<a href="'+(0,W.escapeHTML)(d)+'">x</a>',E.firstChild.href}function g(d){var E=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return d&&!E?bV(d):E}function T(d){if("string"!=typeof d)throw new Error("`type` argument must be a string");return d&&d.indexOf(";")>-1?d.substr(0,d.indexOf(";")):d}function bV(d){if("string"!=typeof d)throw new Error("`url` argument must be a string");for(var E=0,b=gP.length;E<b;E++){var A=gP[E](d);if(A)return A}var g=a(dO(d)),T="video/mp4";return g&&(~["mp4","m4v","ogg","ogv","webm","flv","mpeg","mov"].indexOf(g)?T="video/"+g:~["mp3","oga","wav","mid","midi"].indexOf(g)&&(T="audio/"+g)),T}function dO(d){if("string"!=typeof d)throw new Error("`url` argument must be a string");var E=d.split("?")[0].split("\\").pop().split("../index.html").pop();return~E.indexOf(".")?E.substring(E.lastIndexOf(".")+1):""}function a(d){if("string"!=typeof d)throw new Error("`extension` argument must be a string");switch(d){case"mp4":case"m4v":return"mp4";case"webm":case"webma":case"webmv":return"webm";case"ogg":case"oga":case"ogv":return"ogg";default:return d}}Object.defineProperty(b,"__esModule",{value:!0}),b.typeChecks=void 0,b.absolutizeUrl=A,b.formatType=g,b.getMimeFromType=T,b.getTypeFromFile=bV,b.getExtension=dO,b.normalizeExtension=a;var dd=function(d){return d&&d.__esModule?d:{default:d}}(d(7)),W=d(27),gP=b.typeChecks=[];dd.default.Utils=dd.default.Utils||{},dd.default.Utils.typeChecks=gP,dd.default.Utils.absolutizeUrl=A,dd.default.Utils.formatType=g,dd.default.Utils.getMimeFromType=T,dd.default.Utils.getTypeFromFile=bV,dd.default.Utils.getExtension=dO,dd.default.Utils.normalizeExtension=a},{27:27,7:7}],29:[function(d,E,b){"use strict";function A(d){return d&&d.__esModule?d:{default:d}}var g=A(d(2)),T=A(d(4));if([Element.prototype,CharacterData.prototype,DocumentType.prototype].forEach(function(d){d.hasOwnProperty("remove")||Object.defineProperty(d,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})}),function(){function d(d,E){E=E||{bubbles:!1,cancelable:!1,detail:void 0};var b=g.default.createEvent("CustomEvent");return b.initCustomEvent(d,E.bubbles,E.cancelable,E.detail),b}if("function"==typeof window.CustomEvent)return!1;d.prototype=window.Event.prototype,window.CustomEvent=d}(),"function"!=typeof Object.assign&&(Object.assign=function(d){if(null===d||void 0===d)throw new TypeError("Cannot convert undefined or null to object");for(var E=Object(d),b=1,A=arguments.length;b<A;b++){var g=arguments[b];if(null!==g)for(var T in g)Object.prototype.hasOwnProperty.call(g,T)&&(E[T]=g[T])}return E}),String.prototype.startsWith||(String.prototype.startsWith=function(d,E){return E=E||0,this.substr(E,d.length)===d}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(d){for(var E=(this.document||this.ownerDocument).querySelectorAll(d),b=E.length-1;--b>=0&&E.item(b)!==this;);return b>-1}),window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(d){var E=(this.document||this.ownerDocument).querySelectorAll(d),b=void 0,A=this;do{for(b=E.length;--b>=0&&E.item(b)!==A;);}while(b<0&&(A=A.parentElement));return A}),function(){for(var d=0,E=["ms","moz","webkit","o"],b=0;b<E.length&&!window.requestAnimationFrame;++b)window.requestAnimationFrame=window[E[b]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[E[b]+"CancelAnimationFrame"]||window[E[b]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(E){var b=(new Date).getTime(),A=Math.max(0,16-(b-d)),g=window.setTimeout(function(){E(b+A)},A);return d=b+A,g}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(d){clearTimeout(d)})}(),/firefox/i.test(navigator.userAgent)){var bV=window.getComputedStyle;window.getComputedStyle=function(d,E){var b=bV(d,E);return null===b?{getPropertyValue:function(){}}:b}}window.Promise||(window.Promise=T.default),function(d){d&&d.prototype&&null===d.prototype.children&&Object.defineProperty(d.prototype,"children",{get:function(){for(var d=0,E=void 0,b=this.childNodes,A=[];E=b[d++];)1===E.nodeType&&A.push(E);return A}})}(window.Node||window.Element)},{2:2,4:4}],30:[function(d,E,b){"use strict";function A(){return!((arguments.length>0&&void 0!==arguments[0]?arguments[0]:25)%1==0)}function g(d){var E=arguments.length>1&&void 0!==arguments[1]&&arguments[1],b=arguments.length>2&&void 0!==arguments[2]&&arguments[2],g=arguments.length>3&&void 0!==arguments[3]?arguments[3]:25,T=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,bV=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"mm:ss";d=!d||"number"!=typeof d||d<0?0:d;var dO=Math.round(.066666*g),a=Math.round(g),dd=24*Math.round(3600*g),W=Math.round(600*g),gP=A(g)?";":":",e=void 0,c=void 0,aA=void 0,cF=void 0,ai=Math.round(d*g);if(A(g)){ai<0&&(ai=dd+ai);var df=(ai%=dd)%W;ai+=9*dO*Math.floor(ai/W),df>dO&&(ai+=dO*Math.floor((df-dO)/Math.round(60*a-dO)));var eK=Math.floor(ai/a);e=Math.floor(Math.floor(eK/60)/60),c=Math.floor(eK/60)%60,aA=b?eK%60:(ai/a%60).toFixed(T)}else e=Math.floor(d/3600)%24,c=Math.floor(d/60)%60,aA=b?Math.floor(d%60):(d%60).toFixed(T);e=e<=0?0:e,c=c<=0?0:c,aA=aA<=0?0:aA;for(var eI=bV.split(":"),eU={},gT=0,ej=eI.length;gT<ej;++gT){for(var eh="",eY=0,ed=eI[gT].length;eY<ed;eY++)eh.indexOf(eI[gT][eY])<0&&(eh+=eI[gT][eY]);~["f","s","m","h"].indexOf(eh)&&(eU[eh]=eI[gT].length)}var gX=E||e>0?(e<10&&eU.h>1?"0"+e:e)+":":"";return gX+=(c<10&&eU.m>1?"0"+c:c)+":",gX+=""+(aA<10&&eU.s>1?"0"+aA:aA),b&&(gX+=(cF=(cF=(ai%a).toFixed(0))<=0?0:cF)<10&&eU.f?gP+"0"+cF:""+gP+cF),gX}function T(d){var E=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25;if("string"!=typeof d)throw new TypeError("Time must be a string");if(d.indexOf(";")>0&&(d=d.replace(";",":")),!/\d{2}(\:\d{2}){0,3}/i.test(d))throw new TypeError("Time code must have the format `00:00:00`");var b=d.split(":"),g=void 0,T=0,bV=0,dO=0,a=0,dd=0,W=Math.round(.066666*E),gP=Math.round(E),e=3600*gP,c=60*gP;switch(b.length){default:case 1:dO=parseInt(b[0],10);break;case 2:bV=parseInt(b[0],10),dO=parseInt(b[1],10);break;case 3:T=parseInt(b[0],10),bV=parseInt(b[1],10),dO=parseInt(b[2],10);break;case 4:T=parseInt(b[0],10),bV=parseInt(b[1],10),dO=parseInt(b[2],10),a=parseInt(b[3],10)}return g=A(E)?e*T+c*bV+gP*dO+a-W*((dd=60*T+bV)-Math.floor(dd/10)):(e*T+c*bV+E*dO+a)/E,parseFloat(g.toFixed(3))}function bV(d,E){var b=arguments.length>2&&void 0!==arguments[2]?arguments[2]:25;d=!d||"number"!=typeof d||d<0?0:d;for(var A=Math.floor(d/3600)%24,g=Math.floor(d/60)%60,T=Math.floor(d%60),bV=[[Math.floor((d%1*b).toFixed(3)),"f"],[T,"s"],[g,"m"],[A,"h"]],dO=E.timeFormat,a=dO[1]===dO[0],dd=a?2:1,W=dO.length<dd?dO[dd]:":",gP=dO[0],e=!1,c=0,aA=bV.length;c<aA;c++)if(~dO.indexOf(bV[c][1]))e=!0;else if(e){for(var cF=!1,ai=c;ai<aA;ai++)if(bV[ai][0]>0){cF=!0;break}if(!cF)break;a||(dO=gP+dO),dO=bV[c][1]+W+dO,a&&(dO=bV[c][1]+dO),gP=bV[c][1]}E.timeFormat=dO}function dO(d){if("string"!=typeof d)throw new TypeError("Argument must be a string value");for(var E=~(d=d.replace(",",".")).indexOf(".")?d.split(".")[1].length:0,b=0,A=1,g=0,T=(d=d.split(":").reverse()).length;g<T;g++)A=1,g>0&&(A=Math.pow(60,g)),b+=Number(d[g])*A;return Number(b.toFixed(E))}Object.defineProperty(b,"__esModule",{value:!0}),b.isDropFrame=A,b.secondsToTimeCode=g,b.timeCodeToSeconds=T,b.calculateTimeFormat=bV,b.convertSMPTEtoSeconds=dO;var a=function(d){return d&&d.__esModule?d:{default:d}}(d(7));a.default.Utils=a.default.Utils||{},a.default.Utils.secondsToTimeCode=g,a.default.Utils.timeCodeToSeconds=T,a.default.Utils.calculateTimeFormat=bV,a.default.Utils.convertSMPTEtoSeconds=dO},{7:7}]},{},[29,6,5,15,23,20,19,21,22,24,16,18,17,9,10,11,12,13,14]);