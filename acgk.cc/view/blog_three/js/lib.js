/*!
  hey, [be]Lazy.js - v1.4.1 - 2015.10.12
  A lazy loading and multi-serving image script
  (c) <PERSON><PERSON><PERSON> - @b<PERSON><PERSON> - http://dinbror.dk/blazy
*/
(function(a,W){"function"===typeof define&&define.amd?define(W):"object"===typeof exports?module.exports=W():a.Blazy=W()})(this,function(){function a(a){var d=a._util;d.images=[].slice.call(document.querySelectorAll(a.options.selector));d.count=d.images.length;d.destroyed&&(d.destroyed=!1,a.options.container&&e(a.options.container,function(a){b(a,"scroll",d.validateT)}),b(window,"resize",d.saveViewportOffsetT),b(window,"resize",d.validateT),b(window,"scroll",d.validateT));W(a)}function W(a){for(var W=a._util,d=0;d<W.count;d++){var S=W.images[d],b=S.getBoundingClientRect();if(b.right>=C.left&&b.bottom>=C.top&&b.left<=C.right&&b.top<=C.bottom||-1!==(" "+S.className+" ").indexOf(" "+a.options.successClass+" "))a.load(S),W.images.splice(d,1),W.count--,d--}0===W.count&&a.destroy()}function d(a,W,d){if(W||0<a.offsetWidth&&0<a.offsetHeight)if(W=a.getAttribute(g)||a.getAttribute(d.src)){W=W.split(d.separator);var S=W[di&&1<W.length?1:0];W=new Image;e(d.breakpoints,function(W){a.removeAttribute(W.src)});a.removeAttribute(d.src);W.onerror=function(){d.error&&d.error(a,"invalid");a.className=a.className+" "+d.errorClass};W.onload=function(){"img"===a.nodeName.toLowerCase()?a.src=S:a.style.backgroundImage='url("'+S+'")';a.className=a.className+" "+d.successClass;d.success&&d.success(a)};W.src=S}else d.error&&d.error(a,"missing"),a.className=a.className+" "+d.errorClass}function S(a){C.bottom=(window.innerHeight||document.documentElement.clientHeight)+a;C.right=(window.innerWidth||document.documentElement.clientWidth)+a}function b(a,W,d){a.attachEvent?a.attachEvent&&a.attachEvent("on"+W,d):a.addEventListener(W,d,!1)}function Q(a,W,d){a.detachEvent?a.detachEvent&&a.detachEvent("on"+W,d):a.removeEventListener(W,d,!1)}function e(a,W){if(a&&W)for(var d=a.length,S=0;S<d&&!1!==W(a[S],S);S++);}function R(a,W,d){var S=0;return function(){var b=+new Date;b-S<W||(S=b,a.apply(d,arguments))}}var g,C,di;return function(b){if(!document.querySelectorAll){var aJ=document.createStyleSheet();document.querySelectorAll=function(a,W,d,S,b){b=document.all;W=[];a=a.replace(/\[for\b/gi,"[htmlFor").split(",");for(d=a.length;d--;){aJ.addRule(a[d],"k:v");for(S=b.length;S--;)b[S].currentStyle.k&&W.push(b[S]);aJ.removeRule(0)}return W}}var bQ=this,bQg=bQ._util={};bQg.images=[];bQg.destroyed=!0;bQ.options=b||{};bQ.options.error=b.error||!1;bQ.options.offset=b.offset||100;bQ.options.success=b.success||!1;bQ.options.selector=b.selector||".b-lazy";bQ.options.separator=b.separator||"|";bQ.options.container=b.container?document.querySelectorAll(b.container):!1;bQ.options.errorClass=b.errorClass||"b-error";bQ.options.breakpoints=b.breakpoints||!1;bQ.options.successClass=b.successClass||"b-loaded";bQ.options.src=g=b.src||"data-src";di=1<window.devicePixelRatio;C={};C.top=0-bQ.options.offset;C.left=0-bQ.options.offset;bQ.revalidate=function(){a(this)};bQ.load=function(a,W){-1===(" "+a.className+" ").indexOf(" "+this.options.successClass+" ")&&d(a,W,this.options)};bQ.destroy=function(){var a=this._util;this.options.container&&e(this.options.container,function(W){Q(W,"scroll",a.validateT)});Q(window,"scroll",a.validateT);Q(window,"resize",a.validateT);Q(window,"resize",a.saveViewportOffsetT);a.count=0;a.images.length=0;a.destroyed=!0};bQg.validateT=R(function(){W(bQ)},25,bQ);bQg.saveViewportOffsetT=R(function(){S(bQ.options.offset)},50,bQ);S(bQ.options.offset);e(bQ.options.breakpoints,function(a){if(a.width>=window.screen.width)return g=a.src,!1});a(bQ)}});var QRCode;!function(){function a(a){this.mode=C.MODE_8BIT_BYTE,this.data=a,this.parsedData=[];for(var W=[],d=0,S=this.data.length;S>d;d++){var b=this.data.charCodeAt(d);b>65536?(W[0]=240|(1835008&b)>>>18,W[1]=128|(258048&b)>>>12,W[2]=128|(4032&b)>>>6,W[3]=128|63&b):b>2048?(W[0]=224|(61440&b)>>>12,W[1]=128|(4032&b)>>>6,W[2]=128|63&b):b>128?(W[0]=192|(1984&b)>>>6,W[1]=128|63&b):W[0]=b,this.parsedData=this.parsedData.concat(W)}this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function W(a,W){this.typeNumber=a,this.errorCorrectLevel=W,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}function d(a,W){if(void 0==a.length)throw new Error(a.length+"/"+W);for(var d=0;d<a.length&&0==a[d];)d++;this.num=new Array(a.length-d+W);for(var S=0;S<a.length-d;S++)this.num[S]=a[S+d]}function S(a,W){this.totalCount=a,this.dataCount=W}function b(){this.buffer=[],this.length=0}function Q(){return"undefined"!=typeof CanvasRenderingContext2D}function e(){var a=!1,W=navigator.userAgent;return/android/i.test(W)&&(a=!0,aMat=W.toString().match(/android ([0-9]\.[0-9])/i),aMat&&aMat[1]&&(a=parseFloat(aMat[1]))),a}function R(a,W){for(var d=1,S=g(a),b=0,Q=gC.length;Q>=b;b++){var e=0;switch(W){case di.L:e=gC[b][0];break;case di.M:e=gC[b][1];break;case di.Q:e=gC[b][2];break;case di.H:e=gC[b][3]}if(e>=S)break;d++}if(d>gC.length)throw new Error("Too long data");return d}function g(a){var W=encodeURI(a).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return W.length+(W.length!=a?3:0)}a.prototype={getLength:function(){return this.parsedData.length},write:function(a){for(var W=0,d=this.parsedData.length;d>W;W++)a.put(this.parsedData[W],8)}},W.prototype={addData:function(W){var d=new a(W);this.dataList.push(d),this.dataCache=null},isDark:function(a,W){if(0>a||this.moduleCount<=a||0>W||this.moduleCount<=W)throw new Error(a+","+W);return this.modules[a][W]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(a,d){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var S=0;S<this.moduleCount;S++){this.modules[S]=new Array(this.moduleCount);for(var b=0;b<this.moduleCount;b++)this.modules[S][b]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(a,d),this.typeNumber>=7&&this.setupTypeNumber(a),null==this.dataCache&&(this.dataCache=W.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,d)},setupPositionProbePattern:function(a,W){for(var d=-1;7>=d;d++)if(!(-1>=a+d||this.moduleCount<=a+d))for(var S=-1;7>=S;S++)-1>=W+S||this.moduleCount<=W+S||(this.modules[a+d][W+S]=d>=0&&6>=d&&(0==S||6==S)||S>=0&&6>=S&&(0==d||6==d)||d>=2&&4>=d&&S>=2&&4>=S?!0:!1)},getBestMaskPattern:function(){for(var a=0,W=0,d=0;8>d;d++){this.makeImpl(!0,d);var S=bQ.getLostPoint(this);(0==d||a>S)&&(a=S,W=d)}return W},createMovieClip:function(a,W,d){var S=a.createEmptyMovieClip(W,d),b=1;this.make();for(var Q=0;Q<this.modules.length;Q++)for(var e=Q*b,R=0;R<this.modules[Q].length;R++){var g=R*b,C=this.modules[Q][R];C&&(S.beginFill(0,100),S.moveTo(g,e),S.lineTo(g+b,e),S.lineTo(g+b,e+b),S.lineTo(g,e+b),S.endFill())}return S},setupTimingPattern:function(){for(var a=8;a<this.moduleCount-8;a++)null==this.modules[a][6]&&(this.modules[a][6]=0==a%2);for(var W=8;W<this.moduleCount-8;W++)null==this.modules[6][W]&&(this.modules[6][W]=0==W%2)},setupPositionAdjustPattern:function(){for(var a=bQ.getPatternPosition(this.typeNumber),W=0;W<a.length;W++)for(var d=0;d<a.length;d++){var S=a[W],b=a[d];if(null==this.modules[S][b])for(var Q=-2;2>=Q;Q++)for(var e=-2;2>=e;e++)this.modules[S+Q][b+e]=-2==Q||2==Q||-2==e||2==e||0==Q&&0==e?!0:!1}},setupTypeNumber:function(a){for(var W=bQ.getBCHTypeNumber(this.typeNumber),d=0;18>d;d++){var S=!a&&1==(1&W>>d);this.modules[Math.floor(d/3)][d%3+this.moduleCount-8-3]=S}for(var d=0;18>d;d++){var S=!a&&1==(1&W>>d);this.modules[d%3+this.moduleCount-8-3][Math.floor(d/3)]=S}},setupTypeInfo:function(a,W){for(var d=this.errorCorrectLevel<<3|W,S=bQ.getBCHTypeInfo(d),b=0;15>b;b++){var Q=!a&&1==(1&S>>b);6>b?this.modules[b][8]=Q:8>b?this.modules[b+1][8]=Q:this.modules[this.moduleCount-15+b][8]=Q}for(var b=0;15>b;b++){var Q=!a&&1==(1&S>>b);8>b?this.modules[8][this.moduleCount-b-1]=Q:9>b?this.modules[8][15-b-1+1]=Q:this.modules[8][15-b-1]=Q}this.modules[this.moduleCount-8][8]=!a},mapData:function(a,W){for(var d=-1,S=this.moduleCount-1,b=7,Q=0,e=this.moduleCount-1;e>0;e-=2)for(6==e&&e--;;){for(var R=0;2>R;R++)if(null==this.modules[S][e-R]){var g=!1;Q<a.length&&(g=1==(1&a[Q]>>>b));var C=bQ.getMask(W,S,e-R);C&&(g=!g),this.modules[S][e-R]=g,b--,-1==b&&(Q++,b=7)}if(S+=d,0>S||this.moduleCount<=S){S-=d,d=-d;break}}}},W.PAD0=236,W.PAD1=17,W.createData=function(a,d,Q){for(var e=S.getRSBlocks(a,d),R=new b,g=0;g<Q.length;g++){var C=Q[g];R.put(C.mode,4),R.put(C.getLength(),bQ.getLengthInBits(C.mode,a)),C.write(R)}for(var di=0,g=0;g<e.length;g++)di+=e[g].dataCount;if(R.getLengthInBits()>8*di)throw new Error("code length overflow. ("+R.getLengthInBits()+">"+8*di+")");for(R.getLengthInBits()+4<=8*di&&R.put(0,4);0!=R.getLengthInBits()%8;)R.putBit(!1);for(;;){if(R.getLengthInBits()>=8*di)break;if(R.put(W.PAD0,8),R.getLengthInBits()>=8*di)break;R.put(W.PAD1,8)}return W.createBytes(R,e)},W.createBytes=function(a,W){for(var S=0,b=0,Q=0,e=new Array(W.length),R=new Array(W.length),g=0;g<W.length;g++){var C=W[g].dataCount,di=W[g].totalCount-C;b=Math.max(b,C),Q=Math.max(Q,di),e[g]=new Array(C);for(var aJ=0;aJ<e[g].length;aJ++)e[g][aJ]=255&a.buffer[aJ+S];S+=C;var bQg=bQ.getErrorCorrectPolynomial(di),X=new d(e[g],bQg.getLength()-1),gC=X.mod(bQg);R[g]=new Array(bQg.getLength()-1);for(var aJ=0;aJ<R[g].length;aJ++){var dU=aJ+gC.getLength()-R[g].length;R[g][aJ]=dU>=0?gC.get(dU):0}}for(var c=0,aJ=0;aJ<W.length;aJ++)c+=W[aJ].totalCount;for(var j=new Array(c),eG=0,aJ=0;b>aJ;aJ++)for(var g=0;g<W.length;g++)aJ<e[g].length&&(j[eG++]=e[g][aJ]);for(var aJ=0;Q>aJ;aJ++)for(var g=0;g<W.length;g++)aJ<R[g].length&&(j[eG++]=R[g][aJ]);return j};for(var C={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},di={L:1,M:0,Q:3,H:2},aJ={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},bQ={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(a){for(var W=a<<10;bQ.getBCHDigit(W)-bQ.getBCHDigit(bQ.G15)>=0;)W^=bQ.G15<<bQ.getBCHDigit(W)-bQ.getBCHDigit(bQ.G15);return(a<<10|W)^bQ.G15_MASK},getBCHTypeNumber:function(a){for(var W=a<<12;bQ.getBCHDigit(W)-bQ.getBCHDigit(bQ.G18)>=0;)W^=bQ.G18<<bQ.getBCHDigit(W)-bQ.getBCHDigit(bQ.G18);return a<<12|W},getBCHDigit:function(a){for(var W=0;0!=a;)W++,a>>>=1;return W},getPatternPosition:function(a){return bQ.PATTERN_POSITION_TABLE[a-1]},getMask:function(a,W,d){switch(a){case aJ.PATTERN000:return 0==(W+d)%2;case aJ.PATTERN001:return 0==W%2;case aJ.PATTERN010:return 0==d%3;case aJ.PATTERN011:return 0==(W+d)%3;case aJ.PATTERN100:return 0==(Math.floor(W/2)+Math.floor(d/3))%2;case aJ.PATTERN101:return 0==W*d%2+W*d%3;case aJ.PATTERN110:return 0==(W*d%2+W*d%3)%2;case aJ.PATTERN111:return 0==(W*d%3+(W+d)%2)%2;default:throw new Error("bad maskPattern:"+a)}},getErrorCorrectPolynomial:function(a){for(var W=new d([1],0),S=0;a>S;S++)W=W.multiply(new d([1,bQg.gexp(S)],0));return W},getLengthInBits:function(a,W){if(W>=1&&10>W)switch(a){case C.MODE_NUMBER:return 10;case C.MODE_ALPHA_NUM:return 9;case C.MODE_8BIT_BYTE:return 8;case C.MODE_KANJI:return 8;default:throw new Error("mode:"+a)}else if(27>W)switch(a){case C.MODE_NUMBER:return 12;case C.MODE_ALPHA_NUM:return 11;case C.MODE_8BIT_BYTE:return 16;case C.MODE_KANJI:return 10;default:throw new Error("mode:"+a)}else{if(!(41>W))throw new Error("type:"+W);switch(a){case C.MODE_NUMBER:return 14;case C.MODE_ALPHA_NUM:return 13;case C.MODE_8BIT_BYTE:return 16;case C.MODE_KANJI:return 12;default:throw new Error("mode:"+a)}}},getLostPoint:function(a){for(var W=a.getModuleCount(),d=0,S=0;W>S;S++)for(var b=0;W>b;b++){for(var Q=0,e=a.isDark(S,b),R=-1;1>=R;R++)if(!(0>S+R||S+R>=W))for(var g=-1;1>=g;g++)0>b+g||b+g>=W||(0!=R||0!=g)&&e==a.isDark(S+R,b+g)&&Q++;Q>5&&(d+=3+Q-5)}for(var S=0;W-1>S;S++)for(var b=0;W-1>b;b++){var C=0;a.isDark(S,b)&&C++,a.isDark(S+1,b)&&C++,a.isDark(S,b+1)&&C++,a.isDark(S+1,b+1)&&C++,(0==C||4==C)&&(d+=3)}for(var S=0;W>S;S++)for(var b=0;W-6>b;b++)a.isDark(S,b)&&!a.isDark(S,b+1)&&a.isDark(S,b+2)&&a.isDark(S,b+3)&&a.isDark(S,b+4)&&!a.isDark(S,b+5)&&a.isDark(S,b+6)&&(d+=40);for(var b=0;W>b;b++)for(var S=0;W-6>S;S++)a.isDark(S,b)&&!a.isDark(S+1,b)&&a.isDark(S+2,b)&&a.isDark(S+3,b)&&a.isDark(S+4,b)&&!a.isDark(S+5,b)&&a.isDark(S+6,b)&&(d+=40);for(var di=0,b=0;W>b;b++)for(var S=0;W>S;S++)a.isDark(S,b)&&di++;var aJ=Math.abs(100*di/W/W-50)/5;return d+=10*aJ}},bQg={glog:function(a){if(1>a)throw new Error("glog("+a+")");return bQg.LOG_TABLE[a]},gexp:function(a){for(;0>a;)a+=255;for(;a>=256;)a-=255;return bQg.EXP_TABLE[a]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},X=0;8>X;X++)bQg.EXP_TABLE[X]=1<<X;for(var X=8;256>X;X++)bQg.EXP_TABLE[X]=bQg.EXP_TABLE[X-4]^bQg.EXP_TABLE[X-5]^bQg.EXP_TABLE[X-6]^bQg.EXP_TABLE[X-8];for(var X=0;255>X;X++)bQg.LOG_TABLE[bQg.EXP_TABLE[X]]=X;d.prototype={get:function(a){return this.num[a]},getLength:function(){return this.num.length},multiply:function(a){for(var W=new Array(this.getLength()+a.getLength()-1),S=0;S<this.getLength();S++)for(var b=0;b<a.getLength();b++)W[S+b]^=bQg.gexp(bQg.glog(this.get(S))+bQg.glog(a.get(b)));return new d(W,0)},mod:function(a){if(this.getLength()-a.getLength()<0)return this;for(var W=bQg.glog(this.get(0))-bQg.glog(a.get(0)),S=new Array(this.getLength()),b=0;b<this.getLength();b++)S[b]=this.get(b);for(var b=0;b<a.getLength();b++)S[b]^=bQg.gexp(bQg.glog(a.get(b))+W);return new d(S,0).mod(a)}},S.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],S.getRSBlocks=function(a,W){var d=S.getRsBlockTable(a,W);if(void 0==d)throw new Error("bad rs block @ typeNumber:"+a+"/errorCorrectLevel:"+W);for(var b=d.length/3,Q=[],e=0;b>e;e++)for(var R=d[3*e+0],g=d[3*e+1],C=d[3*e+2],di=0;R>di;di++)Q.push(new S(g,C));return Q},S.getRsBlockTable=function(a,W){switch(W){case di.L:return S.RS_BLOCK_TABLE[4*(a-1)+0];case di.M:return S.RS_BLOCK_TABLE[4*(a-1)+1];case di.Q:return S.RS_BLOCK_TABLE[4*(a-1)+2];case di.H:return S.RS_BLOCK_TABLE[4*(a-1)+3];default:return void 0}},b.prototype={get:function(a){var W=Math.floor(a/8);return 1==(1&this.buffer[W]>>>7-a%8)},put:function(a,W){for(var d=0;W>d;d++)this.putBit(1==(1&a>>>W-d-1))},getLengthInBits:function(){return this.length},putBit:function(a){var W=Math.floor(this.length/8);this.buffer.length<=W&&this.buffer.push(0),a&&(this.buffer[W]|=128>>>this.length%8),this.length++}};var gC=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]],dU=function(){var a=function(a,W){this._el=a,this._htOption=W};return a.prototype.draw=function(a){function W(a,W){var d=document.createElementNS("http://www.w3.org/2000/svg",a);for(var S in W)W.hasOwnProperty(S)&&d.setAttribute(S,W[S]);return d}var d=this._htOption,S=this._el,b=a.getModuleCount();Math.floor(d.width/b),Math.floor(d.height/b),this.clear();var Q=W("svg",{viewBox:"0 0 "+String(b)+" "+String(b),width:"100%",height:"100%",fill:d.colorLight});Q.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),S.appendChild(Q),Q.appendChild(W("rect",{fill:d.colorDark,width:"1",height:"1",id:"template"}));for(var e=0;b>e;e++)for(var R=0;b>R;R++)if(a.isDark(e,R)){var g=W("use",{x:String(e),y:String(R)});g.setAttributeNS("http://www.w3.org/1999/xlink","href","#template"),Q.appendChild(g)}},a.prototype.clear=function(){for(;this._el.hasChildNodes();)this._el.removeChild(this._el.lastChild)},a}(),c="svg"===document.documentElement.tagName.toLowerCase(),j=c?dU:Q()?function(){function a(){this._elImage.src=this._elCanvas.toDataURL("image/png"),this._elImage.style.display="block",this._elCanvas.style.display="none"}function W(a,W){var d=this;if(d._fFail=W,d._fSuccess=a,null===d._bSupportDataURI){var S=document.createElement("img"),b=function(){d._bSupportDataURI=!1,d._fFail&&_fFail.call(d)},Q=function(){d._bSupportDataURI=!0,d._fSuccess&&d._fSuccess.call(d)};return S.onabort=b,S.onerror=b,S.onload=Q,S.src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==",void 0}d._bSupportDataURI===!0&&d._fSuccess?d._fSuccess.call(d):d._bSupportDataURI===!1&&d._fFail&&d._fFail.call(d)}if(this._android&&this._android<=2.1){var d=1/window.devicePixelRatio,S=CanvasRenderingContext2D.prototype.drawImage;CanvasRenderingContext2D.prototype.drawImage=function(a,W,b,Q,e,R,g,C){if("nodeName"in a&&/img/i.test(a.nodeName))for(var di=arguments.length-1;di>=1;di--)arguments[di]=arguments[di]*d;else"undefined"==typeof C&&(arguments[1]*=d,arguments[2]*=d,arguments[3]*=d,arguments[4]*=d);S.apply(this,arguments)}}var b=function(a,W){this._bIsPainted=!1,this._android=e(),this._htOption=W,this._elCanvas=document.createElement("canvas"),this._elCanvas.width=W.width,this._elCanvas.height=W.height,a.appendChild(this._elCanvas),this._el=a,this._oContext=this._elCanvas.getContext("2d"),this._bIsPainted=!1,this._elImage=document.createElement("img"),this._elImage.style.display="none",this._el.appendChild(this._elImage),this._bSupportDataURI=null};return b.prototype.draw=function(a){var W=this._elImage,d=this._oContext,S=this._htOption,b=a.getModuleCount(),Q=S.width/b,e=S.height/b,R=Math.round(Q),g=Math.round(e);W.style.display="none",this.clear();for(var C=0;b>C;C++)for(var di=0;b>di;di++){var aJ=a.isDark(C,di),bQ=di*Q,bQg=C*e;d.strokeStyle=aJ?S.colorDark:S.colorLight,d.lineWidth=1,d.fillStyle=aJ?S.colorDark:S.colorLight,d.fillRect(bQ,bQg,Q,e),d.strokeRect(Math.floor(bQ)+.5,Math.floor(bQg)+.5,R,g),d.strokeRect(Math.ceil(bQ)-.5,Math.ceil(bQg)-.5,R,g)}this._bIsPainted=!0},b.prototype.makeImage=function(){this._bIsPainted&&W.call(this,a)},b.prototype.isPainted=function(){return this._bIsPainted},b.prototype.clear=function(){this._oContext.clearRect(0,0,this._elCanvas.width,this._elCanvas.height),this._bIsPainted=!1},b.prototype.round=function(a){return a?Math.floor(1e3*a)/1e3:a},b}():function(){var a=function(a,W){this._el=a,this._htOption=W};return a.prototype.draw=function(a){for(var W=this._htOption,d=this._el,S=a.getModuleCount(),b=Math.floor(W.width/S),Q=Math.floor(W.height/S),e=['<table style="border:0;border-collapse:collapse;">'],R=0;S>R;R++){e.push("<tr>");for(var g=0;S>g;g++)e.push('<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:'+b+"px;height:"+Q+"px;background-color:"+(a.isDark(R,g)?W.colorDark:W.colorLight)+';"></td>');e.push("</tr>")}e.push("</table>"),d.innerHTML=e.join("");var C=d.childNodes[0],di=(W.width-C.offsetWidth)/2,aJ=(W.height-C.offsetHeight)/2;di>0&&aJ>0&&(C.style.margin=aJ+"px "+di+"px")},a.prototype.clear=function(){this._el.innerHTML=""},a}();QRCode=function(a,W){if(this._htOption={width:256,height:256,typeNumber:4,colorDark:"#000000",colorLight:"#ffffff",correctLevel:di.H},"string"==typeof W&&(W={text:W}),W)for(var d in W)this._htOption[d]=W[d];"string"==typeof a&&(a=document.getElementById(a)),this._android=e(),this._el=a,this._oQRCode=null,this._oDrawing=new j(this._el,this._htOption),this._htOption.text&&this.makeCode(this._htOption.text)},QRCode.prototype.makeCode=function(a){this._oQRCode=new W(R(a,this._htOption.correctLevel),this._htOption.correctLevel),this._oQRCode.addData(a),this._oQRCode.make(),this._el.title=a,this._oDrawing.draw(this._oQRCode),this.makeImage()},QRCode.prototype.makeImage=function(){"function"==typeof this._oDrawing.makeImage&&(!this._android||this._android>=3)&&this._oDrawing.makeImage()},QRCode.prototype.clear=function(){this._oDrawing.clear()},QRCode.CorrectLevel=di}();!function(a,W,d,S){function b(W,d){this.settings=null,this.options=a.extend({},b.Defaults,d),this.$element=a(W),this._handlers={},this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._widths=[],this._invalidated={},this._pipe=[],this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null},this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}},a.each(["onResize","onThrottledResize"],a.proxy(function(W,d){this._handlers[d]=a.proxy(this[d],this)},this)),a.each(b.Plugins,a.proxy(function(a,W){this._plugins[a.charAt(0).toLowerCase()+a.slice(1)]=new W(this)},this)),a.each(b.Workers,a.proxy(function(W,d){this._pipe.push({filter:d.filter,run:a.proxy(d.run,this)})},this)),this.setup(),this.initialize()}b.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:W,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"},b.Width={Default:"default",Inner:"inner",Outer:"outer"},b.Type={Event:"event",State:"state"},b.Plugins={},b.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(a){a.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(a){var W=this.settings.margin||"",d=!this.settings.autoWidth,S=this.settings.rtl,b={width:"auto","margin-left":S?W:"","margin-right":S?"":W};!d&&this.$stage.children().css(b),a.css=b}},{filter:["width","items","settings"],run:function(a){var W=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,d=null,S=this._items.length,b=!this.settings.autoWidth,Q=[];for(a.items={merge:!1,width:W};S--;)d=this._mergers[S],d=this.settings.mergeFit&&Math.min(d,this.settings.items)||d,a.items.merge=d>1||a.items.merge,Q[S]=b?W*d:this._items[S].width();this._widths=Q}},{filter:["items","settings"],run:function(){var W=[],d=this._items,S=this.settings,b=Math.max(2*S.items,4),Q=2*Math.ceil(d.length/2),e=S.loop&&d.length?S.rewind?b:Math.max(b,Q):0,R="",g="";for(e/=2;e>0;)W.push(this.normalize(W.length/2,!0)),R+=d[W[W.length-1]][0].outerHTML,W.push(this.normalize(d.length-1-(W.length-1)/2,!0)),g=d[W[W.length-1]][0].outerHTML+g,e-=1;this._clones=W,a(R).addClass("cloned").appendTo(this.$stage),a(g).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var a=this.settings.rtl?1:-1,W=this._clones.length+this._items.length,d=-1,S=0,b=0,Q=[];++d<W;)S=Q[d-1]||0,b=this._widths[this.relative(d)]+this.settings.margin,Q.push(S+b*a);this._coordinates=Q}},{filter:["width","items","settings"],run:function(){var a=this.settings.stagePadding,W=this._coordinates,d={width:Math.ceil(Math.abs(W[W.length-1]))+2*a,"padding-left":a||"","padding-right":a||""};this.$stage.css(d)}},{filter:["width","items","settings"],run:function(a){var W=this._coordinates.length,d=!this.settings.autoWidth,S=this.$stage.children();if(d&&a.items.merge)for(;W--;)a.css.width=this._widths[this.relative(W)],S.eq(W).css(a.css);else d&&(a.css.width=a.items.width,S.css(a.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(a){a.current=a.current?this.$stage.children().index(a.current):0,a.current=Math.max(this.minimum(),Math.min(this.maximum(),a.current)),this.reset(a.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){var a,W,d,S,b=this.settings.rtl?1:-1,Q=2*this.settings.stagePadding,e=this.coordinates(this.current())+Q,R=e+this.width()*b,g=[];for(d=0,S=this._coordinates.length;d<S;d++)a=this._coordinates[d-1]||0,W=Math.abs(this._coordinates[d])+Q*b,(this.op(a,"<=",e)&&this.op(a,">",R)||this.op(W,"<",e)&&this.op(W,">",R))&&g.push(d);this.$stage.children(".active").removeClass("active"),this.$stage.children(":eq("+g.join("), :eq(")+")").addClass("active"),this.$stage.children(".center").removeClass("center"),this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}],b.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass),this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=a("<"+this.settings.stageElement+">",{class:this.settings.stageClass}).wrap(a("<div/>",{class:this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))},b.prototype.initializeItems=function(){var W=this.$element.find(".owl-item");if(W.length)return this._items=W.get().map(function(W){return a(W)}),this._mergers=this._items.map(function(){return 1}),void this.refresh();this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass)},b.prototype.initialize=function(){if(this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading")){var a,W,d;a=this.$element.find("img"),W=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:S,d=this.$element.children(W).width(),a.length&&d<=0&&this.preloadAutoWidthImages(a)}this.initializeStage(),this.initializeItems(),this.registerEventHandlers(),this.leave("initializing"),this.trigger("initialized")},b.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")},b.prototype.setup=function(){var W=this.viewport(),d=this.options.responsive,S=-1,b=null;d?(a.each(d,function(a){a<=W&&a>S&&(S=Number(a))}),b=a.extend({},this.options,d[S]),"function"==typeof b.stagePadding&&(b.stagePadding=b.stagePadding()),delete b.responsive,b.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+S))):b=a.extend({},this.options),this.trigger("change",{property:{name:"settings",value:b}}),this._breakpoint=S,this.settings=b,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}})},b.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},b.prototype.prepare=function(W){var d=this.trigger("prepare",{content:W});return d.data||(d.data=a("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(W)),this.trigger("prepared",{content:d.data}),d.data},b.prototype.update=function(){for(var W=0,d=this._pipe.length,S=a.proxy(function(a){return this[a]},this._invalidated),b={};W<d;)(this._invalidated.all||a.grep(this._pipe[W].filter,S).length>0)&&this._pipe[W].run(b),W++;this._invalidated={},!this.is("valid")&&this.enter("valid")},b.prototype.width=function(a){switch(a=a||b.Width.Default){case b.Width.Inner:case b.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},b.prototype.refresh=function(){this.enter("refreshing"),this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$element.addClass(this.options.refreshClass),this.update(),this.$element.removeClass(this.options.refreshClass),this.leave("refreshing"),this.trigger("refreshed")},b.prototype.onThrottledResize=function(){W.clearTimeout(this.resizeTimer),this.resizeTimer=W.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)},b.prototype.onResize=function(){return!!this._items.length&&(this._width!==this.$element.width()&&(!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))))},b.prototype.registerEventHandlers=function(){a.support.transition&&this.$stage.on(a.support.transition.end+".owl.core",a.proxy(this.onTransitionEnd,this)),!1!==this.settings.responsive&&this.on(W,"resize",this._handlers.onThrottledResize),this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",a.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1})),this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",a.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",a.proxy(this.onDragEnd,this)))},b.prototype.onDragStart=function(W){var S=null;3!==W.which&&(a.support.transform?(S=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","),S={x:S[16===S.length?12:4],y:S[16===S.length?13:5]}):(S=this.$stage.position(),S={x:this.settings.rtl?S.left+this.$stage.width()-this.width()+this.settings.margin:S.left,y:S.top}),this.is("animating")&&(a.support.transform?this.animate(S.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===W.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=a(W.target),this._drag.stage.start=S,this._drag.stage.current=S,this._drag.pointer=this.pointer(W),a(d).on("mouseup.owl.core touchend.owl.core",a.proxy(this.onDragEnd,this)),a(d).one("mousemove.owl.core touchmove.owl.core",a.proxy(function(W){var S=this.difference(this._drag.pointer,this.pointer(W));a(d).on("mousemove.owl.core touchmove.owl.core",a.proxy(this.onDragMove,this)),Math.abs(S.x)<Math.abs(S.y)&&this.is("valid")||(W.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))},b.prototype.onDragMove=function(a){var W=null,d=null,S=null,b=this.difference(this._drag.pointer,this.pointer(a)),Q=this.difference(this._drag.stage.start,b);this.is("dragging")&&(a.preventDefault(),this.settings.loop?(W=this.coordinates(this.minimum()),d=this.coordinates(this.maximum()+1)-W,Q.x=((Q.x-W)%d+d)%d+W):(W=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),d=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),S=this.settings.pullDrag?-1*b.x/5:0,Q.x=Math.max(Math.min(Q.x,W+S),d+S)),this._drag.stage.current=Q,this.animate(Q.x))},b.prototype.onDragEnd=function(W){var S=this.difference(this._drag.pointer,this.pointer(W)),b=this._drag.stage.current,Q=S.x>0^this.settings.rtl?"left":"right";a(d).off(".owl.core"),this.$element.removeClass(this.options.grabClass),(0!==S.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(b.x,0!==S.x?Q:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=Q,(Math.abs(S.x)>3||(new Date).getTime()-this._drag.time>300)&&this._drag.target.one("click.owl.core",function(){return!1})),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))},b.prototype.closest=function(W,d){var b=-1,Q=30,e=this.width(),R=this.coordinates();return this.settings.freeDrag||a.each(R,a.proxy(function(a,g){return"left"===d&&W>g-Q&&W<g+Q?b=a:"right"===d&&W>g-e-Q&&W<g-e+Q?b=a+1:this.op(W,"<",g)&&this.op(W,">",R[a+1]!==S?R[a+1]:g-e)&&(b="left"===d?a+1:a),-1===b},this)),this.settings.loop||(this.op(W,">",R[this.minimum()])?b=W=this.minimum():this.op(W,"<",R[this.maximum()])&&(b=W=this.maximum())),b},b.prototype.animate=function(W){var d=this.speed()>0;this.is("animating")&&this.onTransitionEnd(),d&&(this.enter("animating"),this.trigger("translate")),a.support.transform3d&&a.support.transition?this.$stage.css({transform:"translate3d("+W+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):d?this.$stage.animate({left:W+"px"},this.speed(),this.settings.fallbackEasing,a.proxy(this.onTransitionEnd,this)):this.$stage.css({left:W+"px"})},b.prototype.is=function(a){return this._states.current[a]&&this._states.current[a]>0},b.prototype.current=function(a){if(a===S)return this._current;if(0===this._items.length)return S;if(a=this.normalize(a),this._current!==a){var W=this.trigger("change",{property:{name:"position",value:a}});W.data!==S&&(a=this.normalize(W.data)),this._current=a,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current},b.prototype.invalidate=function(W){return"string"===a.type(W)&&(this._invalidated[W]=!0,this.is("valid")&&this.leave("valid")),a.map(this._invalidated,function(a,W){return W})},b.prototype.reset=function(a){(a=this.normalize(a))!==S&&(this._speed=0,this._current=a,this.suppress(["translate","translated"]),this.animate(this.coordinates(a)),this.release(["translate","translated"]))},b.prototype.normalize=function(a,W){var d=this._items.length,b=W?0:this._clones.length;return!this.isNumeric(a)||d<1?a=S:(a<0||a>=d+b)&&(a=((a-b/2)%d+d)%d+b/2),a},b.prototype.relative=function(a){return a-=this._clones.length/2,this.normalize(a,!0)},b.prototype.maximum=function(a){var W,d,S,b=this.settings,Q=this._coordinates.length;if(b.loop)Q=this._clones.length/2+this._items.length-1;else if(b.autoWidth||b.merge){if(W=this._items.length)for(d=this._items[--W].width(),S=this.$element.width();W--&&!((d+=this._items[W].width()+this.settings.margin)>S););Q=W+1}else Q=b.center?this._items.length-1:this._items.length-b.items;return a&&(Q-=this._clones.length/2),Math.max(Q,0)},b.prototype.minimum=function(a){return a?0:this._clones.length/2},b.prototype.items=function(a){return a===S?this._items.slice():(a=this.normalize(a,!0),this._items[a])},b.prototype.mergers=function(a){return a===S?this._mergers.slice():(a=this.normalize(a,!0),this._mergers[a])},b.prototype.clones=function(W){var d=this._clones.length/2,b=d+this._items.length,Q=function(a){return a%2==0?b+a/2:d-(a+1)/2};return W===S?a.map(this._clones,function(a,W){return Q(W)}):a.map(this._clones,function(a,d){return a===W?Q(d):null})},b.prototype.speed=function(a){return a!==S&&(this._speed=a),this._speed},b.prototype.coordinates=function(W){var d,b=1,Q=W-1;return W===S?a.map(this._coordinates,a.proxy(function(a,W){return this.coordinates(W)},this)):(this.settings.center?(this.settings.rtl&&(b=-1,Q=W+1),d=this._coordinates[W],d+=(this.width()-d+(this._coordinates[Q]||0))/2*b):d=this._coordinates[Q]||0,d=Math.ceil(d))},b.prototype.duration=function(a,W,d){return 0===d?0:Math.min(Math.max(Math.abs(W-a),1),6)*Math.abs(d||this.settings.smartSpeed)},b.prototype.to=function(a,W){var d=this.current(),S=null,b=a-this.relative(d),Q=(b>0)-(b<0),e=this._items.length,R=this.minimum(),g=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(b)>e/2&&(b+=-1*Q*e),a=d+b,(S=((a-R)%e+e)%e+R)!==a&&S-b<=g&&S-b>0&&(d=S-b,a=S,this.reset(d))):this.settings.rewind?(g+=1,a=(a%g+g)%g):a=Math.max(R,Math.min(g,a)),this.speed(this.duration(d,a,W)),this.current(a),this.isVisible()&&this.update()},b.prototype.next=function(a){a=a||!1,this.to(this.relative(this.current())+1,a)},b.prototype.prev=function(a){a=a||!1,this.to(this.relative(this.current())-1,a)},b.prototype.onTransitionEnd=function(a){if(a!==S&&(a.stopPropagation(),(a.target||a.srcElement||a.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating"),this.trigger("translated")},b.prototype.viewport=function(){var S;return this.options.responsiveBaseElement!==W?S=a(this.options.responsiveBaseElement).width():W.innerWidth?S=W.innerWidth:d.documentElement&&d.documentElement.clientWidth?S=d.documentElement.clientWidth:console.warn("Can not detect viewport width."),S},b.prototype.replace=function(W){this.$stage.empty(),this._items=[],W&&(W=W instanceof jQuery?W:a(W)),this.settings.nestedItemSelector&&(W=W.find("."+this.settings.nestedItemSelector)),W.filter(function(){return 1===this.nodeType}).each(a.proxy(function(a,W){W=this.prepare(W),this.$stage.append(W),this._items.push(W),this._mergers.push(1*W.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)},this)),this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},b.prototype.add=function(W,d){var b=this.relative(this._current);d=d===S?this._items.length:this.normalize(d,!0),W=W instanceof jQuery?W:a(W),this.trigger("add",{content:W,position:d}),W=this.prepare(W),0===this._items.length||d===this._items.length?(0===this._items.length&&this.$stage.append(W),0!==this._items.length&&this._items[d-1].after(W),this._items.push(W),this._mergers.push(1*W.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[d].before(W),this._items.splice(d,0,W),this._mergers.splice(d,0,1*W.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)),this._items[b]&&this.reset(this._items[b].index()),this.invalidate("items"),this.trigger("added",{content:W,position:d})},b.prototype.remove=function(a){(a=this.normalize(a,!0))!==S&&(this.trigger("remove",{content:this._items[a],position:a}),this._items[a].remove(),this._items.splice(a,1),this._mergers.splice(a,1),this.invalidate("items"),this.trigger("removed",{content:null,position:a}))},b.prototype.preloadAutoWidthImages=function(W){W.each(a.proxy(function(W,d){this.enter("pre-loading"),d=a(d),a(new Image).one("load",a.proxy(function(a){d.attr("src",a.target.src),d.css("opacity",1),this.leave("pre-loading"),!this.is("pre-loading")&&!this.is("initializing")&&this.refresh()},this)).attr("src",d.attr("src")||d.attr("data-src")||d.attr("data-src-retina"))},this))},b.prototype.destroy=function(){this.$element.off(".owl.core"),this.$stage.off(".owl.core"),a(d).off(".owl.core"),!1!==this.settings.responsive&&(W.clearTimeout(this.resizeTimer),this.off(W,"resize",this._handlers.onThrottledResize));for(var S in this._plugins)this._plugins[S].destroy();this.$stage.children(".cloned").remove(),this.$stage.unwrap(),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.remove(),this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")},b.prototype.op=function(a,W,d){var S=this.settings.rtl;switch(W){case"<":return S?a>d:a<d;case">":return S?a<d:a>d;case">=":return S?a<=d:a>=d;case"<=":return S?a>=d:a<=d}},b.prototype.on=function(a,W,d,S){a.addEventListener?a.addEventListener(W,d,S):a.attachEvent&&a.attachEvent("on"+W,d)},b.prototype.off=function(a,W,d,S){a.removeEventListener?a.removeEventListener(W,d,S):a.detachEvent&&a.detachEvent("on"+W,d)},b.prototype.trigger=function(W,d,S,Q,e){var R={item:{count:this._items.length,index:this.current()}},g=a.camelCase(a.grep(["on",W,S],function(a){return a}).join("-").toLowerCase()),C=a.Event([W,"owl",S||"carousel"].join(".").toLowerCase(),a.extend({relatedTarget:this},R,d));return this._supress[W]||(a.each(this._plugins,function(a,W){W.onTrigger&&W.onTrigger(C)}),this.register({type:b.Type.Event,name:W}),this.$element.trigger(C),this.settings&&"function"==typeof this.settings[g]&&this.settings[g].call(this,C)),C},b.prototype.enter=function(W){a.each([W].concat(this._states.tags[W]||[]),a.proxy(function(a,W){this._states.current[W]===S&&(this._states.current[W]=0),this._states.current[W]++},this))},b.prototype.leave=function(W){a.each([W].concat(this._states.tags[W]||[]),a.proxy(function(a,W){this._states.current[W]--},this))},b.prototype.register=function(W){if(W.type===b.Type.Event){if(a.event.special[W.name]||(a.event.special[W.name]={}),!a.event.special[W.name].owl){var d=a.event.special[W.name]._default;a.event.special[W.name]._default=function(a){return!d||!d.apply||a.namespace&&-1!==a.namespace.indexOf("owl")?a.namespace&&a.namespace.indexOf("owl")>-1:d.apply(this,arguments)},a.event.special[W.name].owl=!0}}else W.type===b.Type.State&&(this._states.tags[W.name]?this._states.tags[W.name]=this._states.tags[W.name].concat(W.tags):this._states.tags[W.name]=W.tags,this._states.tags[W.name]=a.grep(this._states.tags[W.name],a.proxy(function(d,S){return a.inArray(d,this._states.tags[W.name])===S},this)))},b.prototype.suppress=function(W){a.each(W,a.proxy(function(a,W){this._supress[W]=!0},this))},b.prototype.release=function(W){a.each(W,a.proxy(function(a,W){delete this._supress[W]},this))},b.prototype.pointer=function(a){var d={x:null,y:null};return a=a.originalEvent||a||W.event,a=a.touches&&a.touches.length?a.touches[0]:a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:a,a.pageX?(d.x=a.pageX,d.y=a.pageY):(d.x=a.clientX,d.y=a.clientY),d},b.prototype.isNumeric=function(a){return!isNaN(parseFloat(a))},b.prototype.difference=function(a,W){return{x:a.x-W.x,y:a.y-W.y}},a.fn.owlCarousel=function(W){var d=Array.prototype.slice.call(arguments,1);return this.each(function(){var S=a(this),Q=S.data("owl.carousel");Q||(Q=new b(this,"object"==typeof W&&W),S.data("owl.carousel",Q),a.each(["next","prev","to","destroy","refresh","replace","add","remove"],function(W,d){Q.register({type:b.Type.Event,name:d}),Q.$element.on(d+".owl.carousel.core",a.proxy(function(a){a.namespace&&a.relatedTarget!==this&&(this.suppress([d]),Q[d].apply(this,[].slice.call(arguments,1)),this.release([d]))},Q))})),"string"==typeof W&&"_"!==W.charAt(0)&&Q[W].apply(Q,d)})},a.fn.owlCarousel.Constructor=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){var b=function(W){this._core=W,this._interval=null,this._visible=null,this._handlers={"initialized.owl.carousel":a.proxy(function(a){a.namespace&&this._core.settings.autoRefresh&&this.watch()},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this._core.$element.on(this._handlers)};b.Defaults={autoRefresh:!0,autoRefreshInterval:500},b.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=W.setInterval(a.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))},b.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())},b.prototype.destroy=function(){var a,d;W.clearInterval(this._interval);for(a in this._handlers)this._core.$element.off(a,this._handlers[a]);for(d in Object.getOwnPropertyNames(this))"function"!=typeof this[d]&&(this[d]=null)},a.fn.owlCarousel.Constructor.Plugins.AutoRefresh=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){var b=function(W){this._core=W,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":a.proxy(function(W){if(W.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(W.property&&"position"==W.property.name||"initialized"==W.type)){var d=this._core.settings,b=d.center&&Math.ceil(d.items/2)||d.items,Q=d.center&&-1*b||0,e=(W.property&&W.property.value!==S?W.property.value:this._core.current())+Q,R=this._core.clones().length,g=a.proxy(function(a,W){this.load(W)},this);for(d.lazyLoadEager>0&&(b+=d.lazyLoadEager,d.loop&&(e-=d.lazyLoadEager,b++));Q++<b;)this.load(R/2+this._core.relative(e)),R&&a.each(this._core.clones(this._core.relative(e)),g),e++}},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this._core.$element.on(this._handlers)};b.Defaults={lazyLoad:!1,lazyLoadEager:0},b.prototype.load=function(d){var S=this._core.$stage.children().eq(d),b=S&&S.find(".owl-lazy");!b||a.inArray(S.get(0),this._loaded)>-1||(b.each(a.proxy(function(d,S){var b,Q=a(S),e=W.devicePixelRatio>1&&Q.attr("data-src-retina")||Q.attr("data-src")||Q.attr("data-srcset");this._core.trigger("load",{element:Q,url:e},"lazy"),Q.is("img")?Q.one("load.owl.lazy",a.proxy(function(){Q.css("opacity",1),this._core.trigger("loaded",{element:Q,url:e},"lazy")},this)).attr("src",e):Q.is("source")?Q.one("load.owl.lazy",a.proxy(function(){this._core.trigger("loaded",{element:Q,url:e},"lazy")},this)).attr("srcset",e):(b=new Image,b.onload=a.proxy(function(){Q.css({"background-image":'url("'+e+'")',opacity:"1"}),this._core.trigger("loaded",{element:Q,url:e},"lazy")},this),b.src=e)},this)),this._loaded.push(S.get(0)))},b.prototype.destroy=function(){var a,W;for(a in this.handlers)this._core.$element.off(a,this.handlers[a]);for(W in Object.getOwnPropertyNames(this))"function"!=typeof this[W]&&(this[W]=null)},a.fn.owlCarousel.Constructor.Plugins.Lazy=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){var b=function(d){this._core=d,this._previousHeight=null,this._handlers={"initialized.owl.carousel refreshed.owl.carousel":a.proxy(function(a){a.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":a.proxy(function(a){a.namespace&&this._core.settings.autoHeight&&"position"===a.property.name&&this.update()},this),"loaded.owl.lazy":a.proxy(function(a){a.namespace&&this._core.settings.autoHeight&&a.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this._core.$element.on(this._handlers),this._intervalId=null;var S=this;a(W).on("load",function(){S._core.settings.autoHeight&&S.update()}),a(W).resize(function(){S._core.settings.autoHeight&&(null!=S._intervalId&&clearTimeout(S._intervalId),S._intervalId=setTimeout(function(){S.update()},250))})};b.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},b.prototype.update=function(){var W=this._core._current,d=W+this._core.settings.items,S=this._core.settings.lazyLoad,b=this._core.$stage.children().toArray().slice(W,d),Q=[],e=0;a.each(b,function(W,d){Q.push(a(d).height())}),e=Math.max.apply(null,Q),e<=1&&S&&this._previousHeight&&(e=this._previousHeight),this._previousHeight=e,this._core.$stage.parent().height(e).addClass(this._core.settings.autoHeightClass)},b.prototype.destroy=function(){var a,W;for(a in this._handlers)this._core.$element.off(a,this._handlers[a]);for(W in Object.getOwnPropertyNames(this))"function"!=typeof this[W]&&(this[W]=null)},a.fn.owlCarousel.Constructor.Plugins.AutoHeight=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){var b=function(W){this._core=W,this._videos={},this._playing=null,this._handlers={"initialized.owl.carousel":a.proxy(function(a){a.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":a.proxy(function(a){a.namespace&&this._core.settings.video&&this.isInFullScreen()&&a.preventDefault()},this),"refreshed.owl.carousel":a.proxy(function(a){a.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},this),"changed.owl.carousel":a.proxy(function(a){a.namespace&&"position"===a.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":a.proxy(function(W){if(W.namespace){var d=a(W.content).find(".owl-video");d.length&&(d.css("display","none"),this.fetch(d,a(W.content)))}},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",a.proxy(function(a){this.play(a)},this))};b.Defaults={video:!1,videoHeight:!1,videoWidth:!1},b.prototype.fetch=function(a,W){var d=function(){return a.attr("data-vimeo-id")?"vimeo":a.attr("data-vzaar-id")?"vzaar":"youtube"}(),S=a.attr("data-vimeo-id")||a.attr("data-youtube-id")||a.attr("data-vzaar-id"),b=a.attr("data-width")||this._core.settings.videoWidth,Q=a.attr("data-height")||this._core.settings.videoHeight,e=a.attr("href");if(!e)throw new Error("Missing video URL.");if(S=e.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/),S[3].indexOf("youtu")>-1)d="youtube";else if(S[3].indexOf("vimeo")>-1)d="vimeo";else{if(!(S[3].indexOf("vzaar")>-1))throw new Error("Video URL not supported.");d="vzaar"}S=S[6],this._videos[e]={type:d,id:S,width:b,height:Q},W.attr("data-video",e),this.thumbnail(a,this._videos[e])},b.prototype.thumbnail=function(W,d){var S,b,Q,e=d.width&&d.height?"width:"+d.width+"px;height:"+d.height+"px;":"",R=W.find("img"),g="src",C="",di=this._core.settings,aJ=function(d){b='<div class="owl-video-play-icon"></div>',S=di.lazyLoad?a("<div/>",{class:"owl-video-tn "+C,srcType:d}):a("<div/>",{class:"owl-video-tn",style:"opacity:1;background-image:url("+d+")"}),W.after(S),W.after(b)};if(W.wrap(a("<div/>",{class:"owl-video-wrapper",style:e})),this._core.settings.lazyLoad&&(g="data-src",C="owl-lazy"),R.length)return aJ(R.attr(g)),R.remove(),!1;"youtube"===d.type?(Q="//img.youtube.com/vi/"+d.id+"/hqdefault.jpg",aJ(Q)):"vimeo"===d.type?a.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+d.id+".json",jsonp:"callback",dataType:"jsonp",success:function(a){Q=a[0].thumbnail_large,aJ(Q)}}):"vzaar"===d.type&&a.ajax({type:"GET",url:"//vzaar.com/api/videos/"+d.id+".json",jsonp:"callback",dataType:"jsonp",success:function(a){Q=a.framegrab_url,aJ(Q)}})},b.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null,this._core.leave("playing"),this._core.trigger("stopped",null,"video")},b.prototype.play=function(W){var d,S=a(W.target),b=S.closest("."+this._core.settings.itemClass),Q=this._videos[b.attr("data-video")],e=Q.width||"100%",R=Q.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),b=this._core.items(this._core.relative(b.index())),this._core.reset(b.index()),d=a('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ></iframe>'),d.attr("height",R),d.attr("width",e),"youtube"===Q.type?d.attr("src","//www.youtube.com/embed/"+Q.id+"?autoplay=1&rel=0&v="+Q.id):"vimeo"===Q.type?d.attr("src","//player.vimeo.com/video/"+Q.id+"?autoplay=1"):"vzaar"===Q.type&&d.attr("src","//view.vzaar.com/"+Q.id+"/player?autoplay=true"),a(d).wrap('<div class="owl-video-frame" />').insertAfter(b.find(".owl-video")),this._playing=b.addClass("owl-video-playing"))},b.prototype.isInFullScreen=function(){var W=d.fullscreenElement||d.mozFullScreenElement||d.webkitFullscreenElement;return W&&a(W).parent().hasClass("owl-video-frame")},b.prototype.destroy=function(){var a,W;this._core.$element.off("click.owl.video");for(a in this._handlers)this._core.$element.off(a,this._handlers[a]);for(W in Object.getOwnPropertyNames(this))"function"!=typeof this[W]&&(this[W]=null)},a.fn.owlCarousel.Constructor.Plugins.Video=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){var b=function(W){this.core=W,this.core.options=a.extend({},b.Defaults,this.core.options),this.swapping=!0,this.previous=S,this.next=S,this.handlers={"change.owl.carousel":a.proxy(function(a){a.namespace&&"position"==a.property.name&&(this.previous=this.core.current(),this.next=a.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":a.proxy(function(a){a.namespace&&(this.swapping="translated"==a.type)},this),"translate.owl.carousel":a.proxy(function(a){a.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)},this.core.$element.on(this.handlers)};b.Defaults={animateOut:!1,animateIn:!1},b.prototype.swap=function(){if(1===this.core.settings.items&&a.support.animation&&a.support.transition){this.core.speed(0);var W,d=a.proxy(this.clear,this),S=this.core.$stage.children().eq(this.previous),b=this.core.$stage.children().eq(this.next),Q=this.core.settings.animateIn,e=this.core.settings.animateOut;this.core.current()!==this.previous&&(e&&(W=this.core.coordinates(this.previous)-this.core.coordinates(this.next),S.one(a.support.animation.end,d).css({left:W+"px"}).addClass("animated owl-animated-out").addClass(e)),Q&&b.one(a.support.animation.end,d).addClass("animated owl-animated-in").addClass(Q))}},b.prototype.clear=function(W){a(W.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.onTransitionEnd()},b.prototype.destroy=function(){var a,W;for(a in this.handlers)this.core.$element.off(a,this.handlers[a]);for(W in Object.getOwnPropertyNames(this))"function"!=typeof this[W]&&(this[W]=null)},a.fn.owlCarousel.Constructor.Plugins.Animate=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){var b=function(W){this._core=W,this._call=null,this._time=0,this._timeout=0,this._paused=!0,this._handlers={"changed.owl.carousel":a.proxy(function(a){a.namespace&&"settings"===a.property.name?this._core.settings.autoplay?this.play():this.stop():a.namespace&&"position"===a.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":a.proxy(function(a){a.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":a.proxy(function(a,W,d){a.namespace&&this.play(W,d)},this),"stop.owl.autoplay":a.proxy(function(a){a.namespace&&this.stop()},this),"mouseover.owl.autoplay":a.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":a.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":a.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":a.proxy(function(){this._core.settings.autoplayHoverPause&&this.play()},this)},this._core.$element.on(this._handlers),this._core.options=a.extend({},b.Defaults,this._core.options)};b.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},b.prototype._next=function(S){this._call=W.setTimeout(a.proxy(this._next,this,S),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read()),this._core.is("interacting")||d.hidden||this._core.next(S||this._core.settings.autoplaySpeed)},b.prototype.read=function(){return(new Date).getTime()-this._time},b.prototype.play=function(d,S){var b;this._core.is("rotating")||this._core.enter("rotating"),d=d||this._core.settings.autoplayTimeout,b=Math.min(this._time%(this._timeout||d),d),this._paused?(this._time=this.read(),this._paused=!1):W.clearTimeout(this._call),this._time+=this.read()%d-b,this._timeout=d,this._call=W.setTimeout(a.proxy(this._next,this,S),d-b)},b.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,W.clearTimeout(this._call),this._core.leave("rotating"))},b.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,W.clearTimeout(this._call))},b.prototype.destroy=function(){var a,W;this.stop();for(a in this._handlers)this._core.$element.off(a,this._handlers[a]);for(W in Object.getOwnPropertyNames(this))"function"!=typeof this[W]&&(this[W]=null)},a.fn.owlCarousel.Constructor.Plugins.autoplay=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){"use strict";var b=function(W){this._core=W,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":a.proxy(function(W){W.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+a(W.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"</div>")},this),"added.owl.carousel":a.proxy(function(a){a.namespace&&this._core.settings.dotsData&&this._templates.splice(a.position,0,this._templates.pop())},this),"remove.owl.carousel":a.proxy(function(a){a.namespace&&this._core.settings.dotsData&&this._templates.splice(a.position,1)},this),"changed.owl.carousel":a.proxy(function(a){a.namespace&&"position"==a.property.name&&this.draw()},this),"initialized.owl.carousel":a.proxy(function(a){a.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":a.proxy(function(a){a.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this.$element.on(this._handlers)};b.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;</span>','<span aria-label="Next">&#x203a;</span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1},b.prototype.initialize=function(){var W,d=this._core.settings;this._controls.$relative=(d.navContainer?a(d.navContainer):a("<div>").addClass(d.navContainerClass).appendTo(this.$element)).addClass("disabled"),this._controls.$previous=a("<"+d.navElement+">").addClass(d.navClass[0]).html(d.navText[0]).prependTo(this._controls.$relative).on("click",a.proxy(function(a){this.prev(d.navSpeed)},this)),this._controls.$next=a("<"+d.navElement+">").addClass(d.navClass[1]).html(d.navText[1]).appendTo(this._controls.$relative).on("click",a.proxy(function(a){this.next(d.navSpeed)},this)),d.dotsData||(this._templates=[a('<button role="button">').addClass(d.dotClass).append(a("<span>")).prop("outerHTML")]),this._controls.$absolute=(d.dotsContainer?a(d.dotsContainer):a("<div>").addClass(d.dotsClass).appendTo(this.$element)).addClass("disabled"),this._controls.$absolute.on("click","button",a.proxy(function(W){var S=a(W.target).parent().is(this._controls.$absolute)?a(W.target).index():a(W.target).parent().index();W.preventDefault(),this.to(S,d.dotsSpeed)},this));for(W in this._overrides)this._core[W]=a.proxy(this[W],this)},b.prototype.destroy=function(){var a,W,d,S,b;b=this._core.settings;for(a in this._handlers)this.$element.off(a,this._handlers[a]);for(W in this._controls)"$relative"===W&&b.navContainer?this._controls[W].html(""):this._controls[W].remove();for(S in this.overides)this._core[S]=this._overrides[S];for(d in Object.getOwnPropertyNames(this))"function"!=typeof this[d]&&(this[d]=null)},b.prototype.update=function(){var a,W,d,S=this._core.clones().length/2,b=S+this._core.items().length,Q=this._core.maximum(!0),e=this._core.settings,R=e.center||e.autoWidth||e.dotsData?1:e.dotsEach||e.items;if("page"!==e.slideBy&&(e.slideBy=Math.min(e.slideBy,e.items)),e.dots||"page"==e.slideBy)for(this._pages=[],a=S,W=0,d=0;a<b;a++){if(W>=R||0===W){if(this._pages.push({start:Math.min(Q,a-S),end:a-S+R-1}),Math.min(Q,a-S)===Q)break;W=0,++d}W+=this._core.mergers(this._core.relative(a))}},b.prototype.draw=function(){var W,d=this._core.settings,S=this._core.items().length<=d.items,b=this._core.relative(this._core.current()),Q=d.loop||d.rewind;this._controls.$relative.toggleClass("disabled",!d.nav||S),d.nav&&(this._controls.$previous.toggleClass("disabled",!Q&&b<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!Q&&b>=this._core.maximum(!0))),this._controls.$absolute.toggleClass("disabled",!d.dots||S),d.dots&&(W=this._pages.length-this._controls.$absolute.children().length,d.dotsData&&0!==W?this._controls.$absolute.html(this._templates.join("")):W>0?this._controls.$absolute.append(new Array(W+1).join(this._templates[0])):W<0&&this._controls.$absolute.children().slice(W).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(a.inArray(this.current(),this._pages)).addClass("active"))},b.prototype.onTrigger=function(W){var d=this._core.settings;W.page={index:a.inArray(this.current(),this._pages),count:this._pages.length,size:d&&(d.center||d.autoWidth||d.dotsData?1:d.dotsEach||d.items)}},b.prototype.current=function(){var W=this._core.relative(this._core.current());return a.grep(this._pages,a.proxy(function(a,d){return a.start<=W&&a.end>=W},this)).pop()},b.prototype.getPosition=function(W){var d,S,b=this._core.settings;return"page"==b.slideBy?(d=a.inArray(this.current(),this._pages),S=this._pages.length,W?++d:--d,d=this._pages[(d%S+S)%S].start):(d=this._core.relative(this._core.current()),S=this._core.items().length,W?d+=b.slideBy:d-=b.slideBy),d},b.prototype.next=function(W){a.proxy(this._overrides.to,this._core)(this.getPosition(!0),W)},b.prototype.prev=function(W){a.proxy(this._overrides.to,this._core)(this.getPosition(!1),W)},b.prototype.to=function(W,d,S){var b;!S&&this._pages.length?(b=this._pages.length,a.proxy(this._overrides.to,this._core)(this._pages[(W%b+b)%b].start,d)):a.proxy(this._overrides.to,this._core)(W,d)},a.fn.owlCarousel.Constructor.Plugins.Navigation=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){"use strict";var b=function(d){this._core=d,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":a.proxy(function(d){d.namespace&&"URLHash"===this._core.settings.startPosition&&a(W).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":a.proxy(function(W){if(W.namespace){var d=a(W.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");if(!d)return;this._hashes[d]=W.content}},this),"changed.owl.carousel":a.proxy(function(d){if(d.namespace&&"position"===d.property.name){var S=this._core.items(this._core.relative(this._core.current())),b=a.map(this._hashes,function(a,W){return a===S?W:null}).join();if(!b||W.location.hash.slice(1)===b)return;W.location.hash=b}},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this.$element.on(this._handlers),a(W).on("hashchange.owl.navigation",a.proxy(function(a){var d=W.location.hash.substring(1),b=this._core.$stage.children(),Q=this._hashes[d]&&b.index(this._hashes[d]);Q!==S&&Q!==this._core.current()&&this._core.to(this._core.relative(Q),!1,!0)},this))};b.Defaults={URLhashListener:!1},b.prototype.destroy=function(){var d,S;a(W).off("hashchange.owl.navigation");for(d in this._handlers)this._core.$element.off(d,this._handlers[d]);for(S in Object.getOwnPropertyNames(this))"function"!=typeof this[S]&&(this[S]=null)},a.fn.owlCarousel.Constructor.Plugins.Hash=b}(window.Zepto||window.jQuery,window,document),function(a,W,d,S){function b(W,d){var b=!1,Q=W.charAt(0).toUpperCase()+W.slice(1);return a.each((W+" "+R.join(Q+" ")+Q).split(" "),function(a,W){if(e[W]!==S)return b=!d||W,!1}),b}function Q(a){return b(a,!0)}var e=a("<support>").get(0).style,R="Webkit Moz O ms".split(" "),g={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},C={csstransforms:function(){return!!b("transform")},csstransforms3d:function(){return!!b("perspective")},csstransitions:function(){return!!b("transition")},cssanimations:function(){return!!b("animation")}};C.csstransitions()&&(a.support.transition=new String(Q("transition")),a.support.transition.end=g.transition.end[a.support.transition]),C.cssanimations()&&(a.support.animation=new String(Q("animation")),a.support.animation.end=g.animation.end[a.support.animation]),C.csstransforms()&&(a.support.transform=new String(Q("transform")),a.support.transform3d=C.csstransforms3d())}(window.Zepto||window.jQuery,window,document);
/*! Copyright (c) 2011 Piotr Rochala (http://rocha.la)
* Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
* and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.
*ae>>saS¨
* Version: 1.3.3
*
*/
/*! Copyright (c) 2011 Piotr Rochala (http://rocha.la)
* Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
* and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.
*ae>>saS¨
* Version: 1.3.3
*
*/
(function(a){a.fn.extend({slimScroll:function(W){var d={width:"auto",height:"250px",size:"7px",color:"#000",position:"right",distance:"1px",start:"top",opacity:.4,alwaysVisible:false,disableFadeOut:false,railVisible:false,railColor:"#333",railOpacity:.2,railDraggable:true,railClass:"slimScrollRail",barClass:"slimScrollBar",wrapperClass:"slimScrollDiv",allowPageScroll:false,wheelStep:20,touchScrollStep:200,borderRadius:"7px",railBorderRadius:"7px"};var S=a.extend(d,W);this.each(function(){var d,b,Q,e,R,g,C,di,aJ="<div></div>",bQ=30,bQg=false;var X=a(this);if(X.parent().hasClass(S.wrapperClass)){var gC=X.scrollTop();eG=X.parent().find("."+S.barClass);j=X.parent().find("."+S.railClass);fE();if(a.isPlainObject(W)){if("height"in W&&W.height=="auto"){X.parent().css("height","auto");X.css("height","auto");var dU=X.parent().parent().height();X.parent().css("height",dU);X.css("height",dU)}if("scrollTo"in W){gC=parseInt(S.scrollTo)}else if("scrollBy"in W){gC+=parseInt(S.scrollBy)}else if("destroy"in W){eG.remove();j.remove();X.unwrap();return}eL(gC,false,true)}return}else if(a.isPlainObject(W)){if("destroy"in W){return}}S.height=S.height=="auto"?X.parent().height():S.height;var c=a(aJ).addClass(S.wrapperClass).css({position:"relative",overflow:"hidden",width:S.width,height:S.height});X.css({overflow:"hidden",width:S.width,height:S.height});var j=a(aJ).addClass(S.railClass).css({width:S.size,height:"100%",position:"absolute",top:0,display:S.alwaysVisible&&S.railVisible?"block":"none","border-radius":S.railBorderRadius,background:S.railColor,opacity:S.railOpacity,zIndex:90});var eG=a(aJ).addClass(S.barClass).css({background:S.color,width:S.size,position:"absolute",top:0,opacity:S.opacity,display:S.alwaysVisible?"block":"none","border-radius":S.borderRadius,BorderRadius:S.borderRadius,MozBorderRadius:S.borderRadius,WebkitBorderRadius:S.borderRadius,zIndex:99});var f=S.position=="right"?{right:S.distance}:{left:S.distance};j.css(f);eG.css(f);X.wrap(c);X.parent().append(eG);X.parent().append(j);if(S.railDraggable){eG.bind("mousedown",function(W){var d=a(document);Q=true;t=parseFloat(eG.css("top"));pageY=W.pageY;d.bind("mousemove.slimscroll",function(a){currTop=t+a.pageY-pageY;eG.css("top",currTop);eL(0,eG.position().top,false)});d.bind("mouseup.slimscroll",function(a){Q=false;dd();d.unbind(".slimscroll")});return false}).bind("selectstart.slimscroll",function(a){a.stopPropagation();a.preventDefault();return false})}j.hover(function(){fJ()},function(){dd()});eG.hover(function(){b=true},function(){b=false});X.hover(function(){d=true;fJ();dd()},function(){d=false;dd()});X.bind("touchstart",function(a,W){if(a.originalEvent.touches.length){R=a.originalEvent.touches[0].pageY}});X.bind("touchmove",function(a){if(!bQg){a.originalEvent.preventDefault()}if(a.originalEvent.touches.length){var W=(R-a.originalEvent.touches[0].pageY)/S.touchScrollStep;eL(W,true);R=a.originalEvent.touches[0].pageY}});fE();if(S.start==="bottom"){eG.css({top:X.outerHeight()-eG.outerHeight()});eL(0,true)}else if(S.start!=="top"){eL(a(S.start).position().top,null,true);if(!S.alwaysVisible){eG.hide()}}aI();function U(W){if(!d){return}var W=W||window.event;var b=0;if(W.wheelDelta){b=-W.wheelDelta/120}if(W.detail){b=W.detail/3}var Q=W.target||W.srcTarget||W.srcElement;if(a(Q).closest("."+S.wrapperClass).is(X.parent())){eL(b,true)}if(W.preventDefault&&!bQg){W.preventDefault()}if(!bQg){W.returnValue=false}}function eL(a,W,d){bQg=false;var b=a;var Q=X.outerHeight()-eG.outerHeight();if(W){b=parseInt(eG.css("top"))+a*parseInt(S.wheelStep)/100*eG.outerHeight();b=Math.min(Math.max(b,0),Q);b=a>0?Math.ceil(b):Math.floor(b);eG.css({top:b+"px"})}C=parseInt(eG.css("top"))/(X.outerHeight()-eG.outerHeight());b=C*(X[0].scrollHeight-X.outerHeight());if(d){b=a;var e=b/X[0].scrollHeight*X.outerHeight();e=Math.min(Math.max(e,0),Q);eG.css({top:e+"px"})}X.scrollTop(b);X.trigger("slimscrolling",~~b);fJ();dd()}function aI(){if(window.addEventListener){this.addEventListener("DOMMouseScroll",U,false);this.addEventListener("mousewheel",U,false)}else{document.attachEvent("onmousewheel",U)}}function fE(){g=Math.max(X.outerHeight()/X[0].scrollHeight*X.outerHeight(),bQ);eG.css({height:g+"px"});var a=g==X.outerHeight()?"none":"block";eG.css({display:a})}function fJ(){fE();clearTimeout(e);if(C==~~C){bQg=S.allowPageScroll;if(di!=C){var a=~~C==0?"top":"bottom";X.trigger("slimscroll",a)}}else{bQg=false}di=C;if(g>=X.outerHeight()){bQg=true;return}eG.stop(true,true).fadeIn("fast");if(S.railVisible){j.stop(true,true).fadeIn("fast")}}function dd(){if(!S.alwaysVisible){e=setTimeout(function(){if(!(S.disableFadeOut&&d)&&!b&&!Q){eG.fadeOut("slow");j.fadeOut("slow")}},1e3)}}});return this}});a.fn.extend({slimscroll:a.fn.slimScroll})})(jQuery);
/*!
 * HC-Sticky
 * =========
 * Version: 2.2.3
 * Author: Some Web Media
 * Author URL: http://somewebmedia.com
 * Plugin URL: https://github.com/somewebmedia/hc-sticky
 * Description: Cross-browser plugin that makes any element on your page visible while you scroll
 * License: MIT
 */function _typeof(a){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}!function(a,W){"use strict";if("object"===("undefined"==typeof module?"undefined":_typeof(module))&&"object"===_typeof(module.exports)){if(!a.document)throw new Error("HC-Sticky requires a browser to run.");module.exports=W(a)}else"function"==typeof define&&define.amd?define("hcSticky",[],W(a)):W(a)}("undefined"!=typeof window?window:this,function(a){"use strict";var W={top:0,bottom:0,bottomEnd:0,innerTop:0,innerSticker:null,stickyClass:"sticky",stickTo:null,followScroll:!0,responsive:null,mobileFirst:!1,onStart:null,onStop:null,onBeforeResize:null,onResize:null,resizeDebounce:100,disable:!1,queries:null,queryFlow:"down"},d=function(a,W,d){console.warn("%cHC Sticky:%c "+d+"%c '"+a+"'%c is now deprecated and will be removed. Use%c '"+W+"'%c instead.","color: #fa253b","color: default","color: #5595c6","color: default","color: #5595c6","color: default")},S=a.document,b=function(Q){var e=this,R=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if("string"==typeof Q&&(Q=S.querySelector(Q)),!Q)return!1;R.queries&&d("queries","responsive","option"),R.queryFlow&&d("queryFlow","mobileFirst","option");var g={},C=b.Helpers,di=Q.parentNode;"static"===C.getStyle(di,"position")&&(di.style.position="relative");var aJ,bQ,bQg,X,gC,dU,c,j,eG,f,U,eL,aI,fE,fJ,dd,eY,gX,aa,ee=function(){var a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};C.isEmptyObject(a)&&!C.isEmptyObject(g)||(g=Object.assign({},W,g,a))},dN=function(){return g.disable},fJc=function(){var d,S=g.responsive||g.queries;if(S){var b=a.innerWidth;if(d=R,(g=Object.assign({},W,d||{})).mobileFirst)for(var Q in S)Q<=b&&!C.isEmptyObject(S[Q])&&ee(S[Q]);else{var e=[];for(var di in S){var aJ={};aJ[di]=S[di],e.push(aJ)}for(var bQ=e.length-1;0<=bQ;bQ--){var bQg=e[bQ],X=Object.keys(bQg)[0];b<=X&&!C.isEmptyObject(bQg[X])&&ee(bQg[X])}}}},bf={css:{},position:null,stick:function(){var a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};C.hasClass(Q,g.stickyClass)||(!1===K.isAttached&&K.attach(),bf.position="fixed",Q.style.position="fixed",Q.style.left=K.offsetLeft+"px",Q.style.width=K.width,void 0===a.bottom?Q.style.bottom="auto":Q.style.bottom=a.bottom+"px",void 0===a.top?Q.style.top="auto":Q.style.top=a.top+"px",Q.classList?Q.classList.add(g.stickyClass):Q.className+=" "+g.stickyClass,g.onStart&&g.onStart.call(Q,Object.assign({},g)))},release:function(){var a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};if(a.stop=a.stop||!1,!0===a.stop||"fixed"===bf.position||null===bf.position||!(void 0===a.top&&void 0===a.bottom||void 0!==a.top&&(parseInt(C.getStyle(Q,"top"))||0)===a.top||void 0!==a.bottom&&(parseInt(C.getStyle(Q,"bottom"))||0)===a.bottom)){!0===a.stop?!0===K.isAttached&&K.detach():!1===K.isAttached&&K.attach();var W=a.position||bf.css.position;bf.position=W,Q.style.position=W,Q.style.left=!0===a.stop?bf.css.left:K.positionLeft+"px",Q.style.width="absolute"!==W?bf.css.width:K.width,void 0===a.bottom?Q.style.bottom=!0===a.stop?"":"auto":Q.style.bottom=a.bottom+"px",void 0===a.top?Q.style.top=!0===a.stop?"":"auto":Q.style.top=a.top+"px",Q.classList?Q.classList.remove(g.stickyClass):Q.className=Q.className.replace(new RegExp("(^|\\b)"+g.stickyClass.split(" ").join("|")+"(\\b|$)","gi")," "),g.onStop&&g.onStop.call(Q,Object.assign({},g))}}},K={el:S.createElement("div"),offsetLeft:null,positionLeft:null,width:null,isAttached:!1,init:function(){for(var a in K.el.className="sticky-spacer",bf.css)K.el.style[a]=bf.css[a];K.el.style["z-index"]="-1";var W=C.getStyle(Q);K.offsetLeft=C.offset(Q).left-(parseInt(W.marginLeft)||0),K.positionLeft=C.position(Q).left,K.width=C.getStyle(Q,"width")},attach:function(){di.insertBefore(K.el,Q),K.isAttached=!0},detach:function(){K.el=di.removeChild(K.el),K.isAttached=!1}},cG=function(){var W,d,b,e;bf.css=(W=Q,d=C.getCascadedStyle(W),b=C.getStyle(W),e={height:W.offsetHeight+"px",left:d.left,right:d.right,top:d.top,bottom:d.bottom,position:b.position,display:b.display,verticalAlign:b.verticalAlign,boxSizing:b.boxSizing,marginLeft:d.marginLeft,marginRight:d.marginRight,marginTop:d.marginTop,marginBottom:d.marginBottom,paddingLeft:d.paddingLeft,paddingRight:d.paddingRight},d.float&&(e.float=d.float||"none"),d.cssFloat&&(e.cssFloat=d.cssFloat||"none"),b.MozBoxSizing&&(e.MozBoxSizing=b.MozBoxSizing),e.width="auto"!==d.width?d.width:"border-box"===e.boxSizing||"border-box"===e.MozBoxSizing?W.offsetWidth+"px":b.width,e),K.init(),aJ=!(!g.stickTo||!("document"===g.stickTo||g.stickTo.nodeType&&9===g.stickTo.nodeType||"object"===_typeof(g.stickTo)&&g.stickTo instanceof("undefined"!=typeof HTMLDocument?HTMLDocument:Document))),bQ=g.stickTo?aJ?S:"string"==typeof g.stickTo?S.querySelector(g.stickTo):g.stickTo:di,fJ=(gX=function(){var a=Q.offsetHeight+(parseInt(bf.css.marginTop)||0)+(parseInt(bf.css.marginBottom)||0),W=(fJ||0)-a;return-1<=W&&W<=1?fJ:a})(),X=(eY=function(){return aJ?Math.max(S.documentElement.clientHeight,S.body.scrollHeight,S.documentElement.scrollHeight,S.body.offsetHeight,S.documentElement.offsetHeight):bQ.offsetHeight})(),gC=aJ?0:C.offset(bQ).top,dU=g.stickTo?aJ?0:C.offset(di).top:gC,c=a.innerHeight,dd=Q.offsetTop-(parseInt(bf.css.marginTop)||0),bQg=g.innerSticker?"string"==typeof g.innerSticker?S.querySelector(g.innerSticker):g.innerSticker:null,j=isNaN(g.top)&&-1<g.top.indexOf("%")?parseFloat(g.top)/100*c:g.top,eG=isNaN(g.bottom)&&-1<g.bottom.indexOf("%")?parseFloat(g.bottom)/100*c:g.bottom,f=bQg?bQg.offsetTop:g.innerTop?g.innerTop:0,U=isNaN(g.bottomEnd)&&-1<g.bottomEnd.indexOf("%")?parseFloat(g.bottomEnd)/100*c:g.bottomEnd,eL=gC-j+f+dd},h=a.pageYOffset||S.documentElement.scrollTop,i=0,k=function(){fJ=gX(),X=eY(),aI=gC+X-j-U,fE=c<fJ;var W,d=a.pageYOffset||S.documentElement.scrollTop,b=C.offset(Q).top,e=b-d;aa=d<h?"up":"down",i=d-h,eL<(h=d)?aI+j+(fE?eG:0)-(g.followScroll&&fE?0:j)<=d+fJ-f-(c-(eL-f)<fJ-f&&g.followScroll&&0<(W=fJ-c-f)?W:0)?bf.release({position:"absolute",bottom:dU+di.offsetHeight-aI-j}):fE&&g.followScroll?"down"===aa?e+fJ+eG<=c+.9?bf.stick({bottom:eG}):"fixed"===bf.position&&bf.release({position:"absolute",top:b-j-eL-i+f}):Math.ceil(e+f)<0&&"fixed"===bf.position?bf.release({position:"absolute",top:b-j-eL+f-i}):d+j-f<=b&&bf.stick({top:j-f}):bf.stick({top:j-f}):bf.release({stop:!0})},l=!1,m=!1,n=function(){l&&(C.event.unbind(a,"scroll",k),l=!1)},o=function(){null!==Q.offsetParent&&"none"!==C.getStyle(Q,"display")?(cG(),X<=fJ?n():(k(),l||(C.event.bind(a,"scroll",k),l=!0))):n()},p=function(){Q.style.position="",Q.style.left="",Q.style.top="",Q.style.bottom="",Q.style.width="",Q.classList?Q.classList.remove(g.stickyClass):Q.className=Q.className.replace(new RegExp("(^|\\b)"+g.stickyClass.split(" ").join("|")+"(\\b|$)","gi")," "),bf.css={},!(bf.position=null)===K.isAttached&&K.detach()},q=function(){p(),fJc(),dN()?n():o()},r=function(){g.onBeforeResize&&g.onBeforeResize.call(Q,Object.assign({},g)),q(),g.onResize&&g.onResize.call(Q,Object.assign({},g))},s=g.resizeDebounce?C.debounce(r,g.resizeDebounce):r,t=function(){m&&(C.event.unbind(a,"resize",s),m=!1),n()},u=function(){m||(C.event.bind(a,"resize",s),m=!0),fJc(),dN()?n():o()};this.options=function(a){return a?g[a]:Object.assign({},g)},this.refresh=q,this.update=function(a){ee(a),R=Object.assign({},R,a||{}),q()},this.attach=u,this.detach=t,this.destroy=function(){t(),p()},this.triggerMethod=function(a,W){"function"==typeof e[a]&&e[a](W)},this.reinit=function(){d("reinit","refresh","method"),q()},ee(R),u(),C.event.bind(a,"load",q)};if(void 0!==a.jQuery){var Q=a.jQuery,e="hcSticky";Q.fn.extend({hcSticky:function(a,W){return this.length?"options"===a?Q.data(this.get(0),e).options():this.each(function(){var d=Q.data(this,e);d?d.triggerMethod(a,W):(d=new b(this,a),Q.data(this,e,d))}):this}})}return a.hcSticky=a.hcSticky||b,b}),function(a){"use strict";var W=a.hcSticky,d=a.document;"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(a,W){if(null==a)throw new TypeError("Cannot convert undefined or null to object");for(var d=Object(a),S=1;S<arguments.length;S++){var b=arguments[S];if(null!=b)for(var Q in b)Object.prototype.hasOwnProperty.call(b,Q)&&(d[Q]=b[Q])}return d},writable:!0,configurable:!0}),Array.prototype.forEach||(Array.prototype.forEach=function(a){var W,d;if(null==this)throw new TypeError("this is null or not defined");var S=Object(this),b=S.length>>>0;if("function"!=typeof a)throw new TypeError(a+" is not a function");for(1<arguments.length&&(W=arguments[1]),d=0;d<b;){var Q;d in S&&(Q=S[d],a.call(W,Q,d,S)),d++}});var S=function(){var W=d.documentElement,S=function(){};function b(W){var d=a.event;return d.target=d.target||d.srcElement||W,d}W.addEventListener?S=function(a,W,d){a.addEventListener(W,d,!1)}:W.attachEvent&&(S=function(a,W,d){a[W+d]=d.handleEvent?function(){var W=b(a);d.handleEvent.call(d,W)}:function(){var W=b(a);d.call(a,W)},a.attachEvent("on"+W,a[W+d])});var Q=function(){};return W.removeEventListener?Q=function(a,W,d){a.removeEventListener(W,d,!1)}:W.detachEvent&&(Q=function(a,W,d){a.detachEvent("on"+W,a[W+d]);try{delete a[W+d]}catch(S){a[W+d]=void 0}}),{bind:S,unbind:Q}}(),b=function(W,S){return a.getComputedStyle?S?d.defaultView.getComputedStyle(W,null).getPropertyValue(S):d.defaultView.getComputedStyle(W,null):W.currentStyle?S?W.currentStyle[S.replace(/-\w/g,function(a){return a.toUpperCase().replace("-","")})]:W.currentStyle:void 0},Q=function(W){var S=W.getBoundingClientRect(),b=a.pageYOffset||d.documentElement.scrollTop,Q=a.pageXOffset||d.documentElement.scrollLeft;return{top:S.top+b,left:S.left+Q}};W.Helpers={isEmptyObject:function(a){for(var W in a)return!1;return!0},debounce:function(a,W,d){var S;return function(){var b=this,Q=arguments,e=d&&!S;clearTimeout(S),S=setTimeout(function(){S=null,d||a.apply(b,Q)},W),e&&a.apply(b,Q)}},hasClass:function(a,W){return a.classList?a.classList.contains(W):new RegExp("(^| )"+W+"( |$)","gi").test(a.className)},offset:Q,position:function(a){var W=a.offsetParent,d=Q(W),S=Q(a),e=b(W),R=b(a);return d.top+=parseInt(e.borderTopWidth)||0,d.left+=parseInt(e.borderLeftWidth)||0,{top:S.top-d.top-(parseInt(R.marginTop)||0),left:S.left-d.left-(parseInt(R.marginLeft)||0)}},getStyle:b,getCascadedStyle:function(W){var S,b=W.cloneNode(!0);b.style.display="none",Array.prototype.slice.call(b.querySelectorAll('input[type="radio"]')).forEach(function(a){a.removeAttribute("name")}),W.parentNode.insertBefore(b,W.nextSibling),b.currentStyle?S=b.currentStyle:a.getComputedStyle&&(S=d.defaultView.getComputedStyle(b,null));var Q={};for(var e in S)!isNaN(e)||"string"!=typeof S[e]&&"number"!=typeof S[e]||(Q[e]=S[e]);if(Object.keys(Q).length<3)for(var R in Q={},S)isNaN(R)||(Q[S[R].replace(/-\w/g,function(a){return a.toUpperCase().replace("-","")})]=S.getPropertyValue(S[R]));if(Q.margin||"auto"!==Q.marginLeft?Q.margin||Q.marginLeft!==Q.marginRight||Q.marginLeft!==Q.marginTop||Q.marginLeft!==Q.marginBottom||(Q.margin=Q.marginLeft):Q.margin="auto",!Q.margin&&"0px"===Q.marginLeft&&"0px"===Q.marginRight){var g=W.offsetLeft-W.parentNode.offsetLeft,C=g-(parseInt(Q.left)||0)-(parseInt(Q.right)||0),di=W.parentNode.offsetWidth-W.offsetWidth-g-(parseInt(Q.right)||0)+(parseInt(Q.left)||0)-C;0!==di&&1!==di||(Q.margin="auto")}return b.parentNode.removeChild(b),b=null,Q},event:S}}(window);$(function(){var a=$(document).scrollTop();var W=$(".header").outerHeight();$(window).scroll(function(){var d=$(document).scrollTop();if(d>W){$(".header").addClass("fixedOut")}else{$(".header").removeClass("fixedOut")}if(d>a){$(".header").removeClass("fixedIn")}else{$(".header").addClass("fixedIn")}a=$(document).scrollTop()})});
/*! Magnific Popup - v1.1.0 - 2016-02-20
* http://dimsemenov.com/plugins/magnific-popup/
* Copyright (c) 2016 Dmitry Semenov; */!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a("object"==typeof exports?require("jquery"):window.jQuery||window.Zepto)}(function(a){var W,d,S,b,Q,e,R="Close",g="BeforeClose",C="AfterClose",di="BeforeAppend",aJ="MarkupParse",bQ="Open",bQg="Change",X="mfp",gC="."+X,dU="mfp-ready",c="mfp-removing",j="mfp-prevent-close",eG=function(){},f=!!window.jQuery,U=a(window),eL=function(a,d){W.ev.on(X+a+gC,d)},aI=function(W,d,S,b){var Q=document.createElement("div");return Q.className="mfp-"+W,S&&(Q.innerHTML=S),b?d&&d.appendChild(Q):(Q=a(Q),d&&Q.appendTo(d)),Q},fE=function(d,S){W.ev.triggerHandler(X+d,S),W.st.callbacks&&(d=d.charAt(0).toLowerCase()+d.slice(1),W.st.callbacks[d]&&W.st.callbacks[d].apply(W,a.isArray(S)?S:[S]))},fJ=function(d){return d===e&&W.currTemplate.closeBtn||(W.currTemplate.closeBtn=a(W.st.closeMarkup.replace("%title%",W.st.tClose)),e=d),W.currTemplate.closeBtn},dd=function(){a.magnificPopup.instance||(W=new eG,W.init(),a.magnificPopup.instance=W)},eY=function(){var a=document.createElement("p").style,W=["ms","O","Moz","Webkit"];if(void 0!==a.transition)return!0;for(;W.length;)if(W.pop()+"Transition"in a)return!0;return!1};eG.prototype={constructor:eG,init:function(){var d=navigator.appVersion;W.isLowIE=W.isIE8=document.all&&!document.addEventListener,W.isAndroid=/android/gi.test(d),W.isIOS=/iphone|ipad|ipod/gi.test(d),W.supportsTransition=eY(),W.probablyMobile=W.isAndroid||W.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),S=a(document),W.popupsCache={}},open:function(d){var b;if(d.isObj===!1){W.items=d.items.toArray(),W.index=0;var e,R=d.items;for(b=0;b<R.length;b++)if(e=R[b],e.parsed&&(e=e.el[0]),e===d.el[0]){W.index=b;break}}else W.items=a.isArray(d.items)?d.items:[d.items],W.index=d.index||0;if(W.isOpen)return void W.updateItemHTML();W.types=[],Q="",d.mainEl&&d.mainEl.length?W.ev=d.mainEl.eq(0):W.ev=S,d.key?(W.popupsCache[d.key]||(W.popupsCache[d.key]={}),W.currTemplate=W.popupsCache[d.key]):W.currTemplate={},W.st=a.extend(!0,{},a.magnificPopup.defaults,d),W.fixedContentPos="auto"===W.st.fixedContentPos?!W.probablyMobile:W.st.fixedContentPos,W.st.modal&&(W.st.closeOnContentClick=!1,W.st.closeOnBgClick=!1,W.st.showCloseBtn=!1,W.st.enableEscapeKey=!1),W.bgOverlay||(W.bgOverlay=aI("bg").on("click"+gC,function(){W.close()}),W.wrap=aI("wrap").attr("tabindex",-1).on("click"+gC,function(a){W._checkIfClose(a.target)&&W.close()}),W.container=aI("container",W.wrap)),W.contentContainer=aI("content"),W.st.preloader&&(W.preloader=aI("preloader",W.container,W.st.tLoading));var g=a.magnificPopup.modules;for(b=0;b<g.length;b++){var C=g[b];C=C.charAt(0).toUpperCase()+C.slice(1),W["init"+C].call(W)}fE("BeforeOpen"),W.st.showCloseBtn&&(W.st.closeBtnInside?(eL(aJ,function(a,W,d,S){d.close_replaceWith=fJ(S.type)}),Q+=" mfp-close-btn-in"):W.wrap.append(fJ())),W.st.alignTop&&(Q+=" mfp-align-top"),W.fixedContentPos?W.wrap.css({overflow:W.st.overflowY,overflowX:"hidden",overflowY:W.st.overflowY}):W.wrap.css({top:U.scrollTop(),position:"absolute"}),(W.st.fixedBgPos===!1||"auto"===W.st.fixedBgPos&&!W.fixedContentPos)&&W.bgOverlay.css({height:S.height(),position:"absolute"}),W.st.enableEscapeKey&&S.on("keyup"+gC,function(a){27===a.keyCode&&W.close()}),U.on("resize"+gC,function(){W.updateSize()}),W.st.closeOnContentClick||(Q+=" mfp-auto-cursor"),Q&&W.wrap.addClass(Q);var di=W.wH=U.height(),bQg={};if(W.fixedContentPos&&W._hasScrollBar(di)){var X=W._getScrollbarSize();X&&(bQg.marginRight=X)}W.fixedContentPos&&(W.isIE7?a("body, html").css("overflow","hidden"):bQg.overflow="hidden");var c=W.st.mainClass;return W.isIE7&&(c+=" mfp-ie7"),c&&W._addClassToMFP(c),W.updateItemHTML(),fE("BuildControls"),a("html").css(bQg),W.bgOverlay.add(W.wrap).prependTo(W.st.prependTo||a(document.body)),W._lastFocusedEl=document.activeElement,setTimeout(function(){W.content?(W._addClassToMFP(dU),W._setFocus()):W.bgOverlay.addClass(dU),S.on("focusin"+gC,W._onFocusIn)},16),W.isOpen=!0,W.updateSize(di),fE(bQ),d},close:function(){W.isOpen&&(fE(g),W.isOpen=!1,W.st.removalDelay&&!W.isLowIE&&W.supportsTransition?(W._addClassToMFP(c),setTimeout(function(){W._close()},W.st.removalDelay)):W._close())},_close:function(){fE(R);var d=c+" "+dU+" ";if(W.bgOverlay.detach(),W.wrap.detach(),W.container.empty(),W.st.mainClass&&(d+=W.st.mainClass+" "),W._removeClassFromMFP(d),W.fixedContentPos){var b={marginRight:""};W.isIE7?a("body, html").css("overflow",""):b.overflow="",a("html").css(b)}S.off("keyup"+gC+" focusin"+gC),W.ev.off(gC),W.wrap.attr("class","mfp-wrap").removeAttr("style"),W.bgOverlay.attr("class","mfp-bg"),W.container.attr("class","mfp-container"),!W.st.showCloseBtn||W.st.closeBtnInside&&W.currTemplate[W.currItem.type]!==!0||W.currTemplate.closeBtn&&W.currTemplate.closeBtn.detach(),W.st.autoFocusLast&&W._lastFocusedEl&&a(W._lastFocusedEl).focus(),W.currItem=null,W.content=null,W.currTemplate=null,W.prevHeight=0,fE(C)},updateSize:function(a){if(W.isIOS){var d=document.documentElement.clientWidth/window.innerWidth,S=window.innerHeight*d;W.wrap.css("height",S),W.wH=S}else W.wH=a||U.height();W.fixedContentPos||W.wrap.css("height",W.wH),fE("Resize")},updateItemHTML:function(){var d=W.items[W.index];W.contentContainer.detach(),W.content&&W.content.detach(),d.parsed||(d=W.parseEl(W.index));var S=d.type;if(fE("BeforeChange",[W.currItem?W.currItem.type:"",S]),W.currItem=d,!W.currTemplate[S]){var Q=W.st[S]?W.st[S].markup:!1;fE("FirstMarkupParse",Q),Q?W.currTemplate[S]=a(Q):W.currTemplate[S]=!0}b&&b!==d.type&&W.container.removeClass("mfp-"+b+"-holder");var e=W["get"+S.charAt(0).toUpperCase()+S.slice(1)](d,W.currTemplate[S]);W.appendContent(e,S),d.preloaded=!0,fE(bQg,d),b=d.type,W.container.prepend(W.contentContainer),fE("AfterChange")},appendContent:function(a,d){W.content=a,a?W.st.showCloseBtn&&W.st.closeBtnInside&&W.currTemplate[d]===!0?W.content.find(".mfp-close").length||W.content.append(fJ()):W.content=a:W.content="",fE(di),W.container.addClass("mfp-"+d+"-holder"),W.contentContainer.append(W.content)},parseEl:function(d){var S,b=W.items[d];if(b.tagName?b={el:a(b)}:(S=b.type,b={data:b,src:b.src}),b.el){for(var Q=W.types,e=0;e<Q.length;e++)if(b.el.hasClass("mfp-"+Q[e])){S=Q[e];break}b.src=b.el.attr("data-mfp-src"),b.src||(b.src=b.el.attr("href"))}return b.type=S||W.st.type||"inline",b.index=d,b.parsed=!0,W.items[d]=b,fE("ElementParse",b),W.items[d]},addGroup:function(a,d){var S=function(S){S.mfpEl=this,W._openClick(S,a,d)};d||(d={});var b="click.magnificPopup";d.mainEl=a,d.items?(d.isObj=!0,a.off(b).on(b,S)):(d.isObj=!1,d.delegate?a.off(b).on(b,d.delegate,S):(d.items=a,a.off(b).on(b,S)))},_openClick:function(d,S,b){var Q=void 0!==b.midClick?b.midClick:a.magnificPopup.defaults.midClick;if(Q||!(2===d.which||d.ctrlKey||d.metaKey||d.altKey||d.shiftKey)){var e=void 0!==b.disableOn?b.disableOn:a.magnificPopup.defaults.disableOn;if(e)if(a.isFunction(e)){if(!e.call(W))return!0}else if(U.width()<e)return!0;d.type&&(d.preventDefault(),W.isOpen&&d.stopPropagation()),b.el=a(d.mfpEl),b.delegate&&(b.items=S.find(b.delegate)),W.open(b)}},updateStatus:function(a,S){if(W.preloader){d!==a&&W.container.removeClass("mfp-s-"+d),S||"loading"!==a||(S=W.st.tLoading);var b={status:a,text:S};fE("UpdateStatus",b),a=b.status,S=b.text,W.preloader.html(S),W.preloader.find("a").on("click",function(a){a.stopImmediatePropagation()}),W.container.addClass("mfp-s-"+a),d=a}},_checkIfClose:function(d){if(!a(d).hasClass(j)){var S=W.st.closeOnContentClick,b=W.st.closeOnBgClick;if(S&&b)return!0;if(!W.content||a(d).hasClass("mfp-close")||W.preloader&&d===W.preloader[0])return!0;if(d===W.content[0]||a.contains(W.content[0],d)){if(S)return!0}else if(b&&a.contains(document,d))return!0;return!1}},_addClassToMFP:function(a){W.bgOverlay.addClass(a),W.wrap.addClass(a)},_removeClassFromMFP:function(a){this.bgOverlay.removeClass(a),W.wrap.removeClass(a)},_hasScrollBar:function(a){return(W.isIE7?S.height():document.body.scrollHeight)>(a||U.height())},_setFocus:function(){(W.st.focus?W.content.find(W.st.focus).eq(0):W.wrap).focus()},_onFocusIn:function(d){return d.target===W.wrap[0]||a.contains(W.wrap[0],d.target)?void 0:(W._setFocus(),!1)},_parseMarkup:function(W,d,S){var b;S.data&&(d=a.extend(S.data,d)),fE(aJ,[W,d,S]),a.each(d,function(d,S){if(void 0===S||S===!1)return!0;if(b=d.split("_"),b.length>1){var Q=W.find(gC+"-"+b[0]);if(Q.length>0){var e=b[1];"replaceWith"===e?Q[0]!==S[0]&&Q.replaceWith(S):"img"===e?Q.is("img")?Q.attr("src",S):Q.replaceWith(a("<img>").attr("src",S).attr("class",Q.attr("class"))):Q.attr(b[1],S)}}else W.find(gC+"-"+d).html(S)})},_getScrollbarSize:function(){if(void 0===W.scrollbarSize){var a=document.createElement("div");a.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(a),W.scrollbarSize=a.offsetWidth-a.clientWidth,document.body.removeChild(a)}return W.scrollbarSize}},a.magnificPopup={instance:null,proto:eG.prototype,modules:[],open:function(W,d){return dd(),W=W?a.extend(!0,{},W):{},W.isObj=!0,W.index=d||0,this.instance.open(W)},close:function(){return a.magnificPopup.instance&&a.magnificPopup.instance.close()},registerModule:function(W,d){d.options&&(a.magnificPopup.defaults[W]=d.options),a.extend(this.proto,d.proto),this.modules.push(W)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&#215;</button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0}},a.fn.magnificPopup=function(d){dd();var S=a(this);if("string"==typeof d)if("open"===d){var b,Q=f?S.data("magnificPopup"):S[0].magnificPopup,e=parseInt(arguments[1],10)||0;Q.items?b=Q.items[e]:(b=S,Q.delegate&&(b=b.find(Q.delegate)),b=b.eq(e)),W._openClick({mfpEl:b},S,Q)}else W.isOpen&&W[d].apply(W,Array.prototype.slice.call(arguments,1));else d=a.extend(!0,{},d),f?S.data("magnificPopup",d):S[0].magnificPopup=d,W.addGroup(S,d);return S};var gX,aa,ee,dN="inline",fJc=function(){ee&&(aa.after(ee.addClass(gX)).detach(),ee=null)};a.magnificPopup.registerModule(dN,{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){W.types.push(dN),eL(R+"."+dN,function(){fJc()})},getInline:function(d,S){if(fJc(),d.src){var b=W.st.inline,Q=a(d.src);if(Q.length){var e=Q[0].parentNode;e&&e.tagName&&(aa||(gX=b.hiddenClass,aa=aI(gX),gX="mfp-"+gX),ee=Q.after(aa).detach().removeClass(gX)),W.updateStatus("ready")}else W.updateStatus("error",b.tNotFound),Q=a("<div>");return d.inlineElement=Q,Q}return W.updateStatus("ready"),W._parseMarkup(S,{},d),S}}});var bf,K="ajax",cG=function(){bf&&a(document.body).removeClass(bf)},h=function(){cG(),W.req&&W.req.abort()};a.magnificPopup.registerModule(K,{options:{settings:null,cursor:"mfp-ajax-cur",tError:'<a href="%url%">The content</a> could not be loaded.'},proto:{initAjax:function(){W.types.push(K),bf=W.st.ajax.cursor,eL(R+"."+K,h),eL("BeforeChange."+K,h)},getAjax:function(d){bf&&a(document.body).addClass(bf),W.updateStatus("loading");var S=a.extend({url:d.src,success:function(S,b,Q){var e={data:S,xhr:Q};fE("ParseAjax",e),W.appendContent(a(e.data),K),d.finished=!0,cG(),W._setFocus(),setTimeout(function(){W.wrap.addClass(dU)},16),W.updateStatus("ready"),fE("AjaxContentAdded")},error:function(){cG(),d.finished=d.loadError=!0,W.updateStatus("error",W.st.ajax.tError.replace("%url%",d.src))}},W.st.ajax.settings);return W.req=a.ajax(S),""}}});var i,k=function(d){if(d.data&&void 0!==d.data.title)return d.data.title;var S=W.st.image.titleSrc;if(S){if(a.isFunction(S))return S.call(W,d);if(d.el)return d.el.attr(S)||""}return""};a.magnificPopup.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:'<a href="%url%">The image</a> could not be loaded.'},proto:{initImage:function(){var d=W.st.image,S=".image";W.types.push("image"),eL(bQ+S,function(){"image"===W.currItem.type&&d.cursor&&a(document.body).addClass(d.cursor)}),eL(R+S,function(){d.cursor&&a(document.body).removeClass(d.cursor),U.off("resize"+gC)}),eL("Resize"+S,W.resizeImage),W.isLowIE&&eL("AfterChange",W.resizeImage)},resizeImage:function(){var a=W.currItem;if(a&&a.img&&W.st.image.verticalFit){var d=0;W.isLowIE&&(d=parseInt(a.img.css("padding-top"),10)+parseInt(a.img.css("padding-bottom"),10)),a.img.css("max-height",W.wH-d)}},_onImageHasSize:function(a){a.img&&(a.hasSize=!0,i&&clearInterval(i),a.isCheckingImgSize=!1,fE("ImageHasSize",a),a.imgHidden&&(W.content&&W.content.removeClass("mfp-loading"),a.imgHidden=!1))},findImageSize:function(a){var d=0,S=a.img[0],b=function(Q){i&&clearInterval(i),i=setInterval(function(){return S.naturalWidth>0?void W._onImageHasSize(a):(d>200&&clearInterval(i),d++,void(3===d?b(10):40===d?b(50):100===d&&b(500)))},Q)};b(1)},getImage:function(d,S){var b=0,Q=function(){d&&(d.img[0].complete?(d.img.off(".mfploader"),d===W.currItem&&(W._onImageHasSize(d),W.updateStatus("ready")),d.hasSize=!0,d.loaded=!0,fE("ImageLoadComplete")):(b++,200>b?setTimeout(Q,100):e()))},e=function(){d&&(d.img.off(".mfploader"),d===W.currItem&&(W._onImageHasSize(d),W.updateStatus("error",R.tError.replace("%url%",d.src))),d.hasSize=!0,d.loaded=!0,d.loadError=!0)},R=W.st.image,g=S.find(".mfp-img");if(g.length){var C=document.createElement("img");C.className="mfp-img",d.el&&d.el.find("img").length&&(C.alt=d.el.find("img").attr("alt")),d.img=a(C).on("load.mfploader",Q).on("error.mfploader",e),C.src=d.src,g.is("img")&&(d.img=d.img.clone()),C=d.img[0],C.naturalWidth>0?d.hasSize=!0:C.width||(d.hasSize=!1)}return W._parseMarkup(S,{title:k(d),img_replaceWith:d.img},d),W.resizeImage(),d.hasSize?(i&&clearInterval(i),d.loadError?(S.addClass("mfp-loading"),W.updateStatus("error",R.tError.replace("%url%",d.src))):(S.removeClass("mfp-loading"),W.updateStatus("ready")),S):(W.updateStatus("loading"),d.loading=!0,d.hasSize||(d.imgHidden=!0,S.addClass("mfp-loading"),W.findImageSize(d)),S)}}});var l,m=function(){return void 0===l&&(l=void 0!==document.createElement("p").style.MozTransform),l};a.magnificPopup.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(a){return a.is("img")?a:a.find("img")}},proto:{initZoom:function(){var a,d=W.st.zoom,S=".zoom";if(d.enabled&&W.supportsTransition){var b,Q,e=d.duration,C=function(a){var W=a.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),S="all "+d.duration/1e3+"s "+d.easing,b={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},Q="transition";return b["-webkit-"+Q]=b["-moz-"+Q]=b["-o-"+Q]=b[Q]=S,W.css(b),W},di=function(){W.content.css("visibility","visible")};eL("BuildControls"+S,function(){if(W._allowZoom()){if(clearTimeout(b),W.content.css("visibility","hidden"),a=W._getItemToZoom(),!a)return void di();Q=C(a),Q.css(W._getOffset()),W.wrap.append(Q),b=setTimeout(function(){Q.css(W._getOffset(!0)),b=setTimeout(function(){di(),setTimeout(function(){Q.remove(),a=Q=null,fE("ZoomAnimationEnded")},16)},e)},16)}}),eL(g+S,function(){if(W._allowZoom()){if(clearTimeout(b),W.st.removalDelay=e,!a){if(a=W._getItemToZoom(),!a)return;Q=C(a)}Q.css(W._getOffset(!0)),W.wrap.append(Q),W.content.css("visibility","hidden"),setTimeout(function(){Q.css(W._getOffset())},16)}}),eL(R+S,function(){W._allowZoom()&&(di(),Q&&Q.remove(),a=null)})}},_allowZoom:function(){return"image"===W.currItem.type},_getItemToZoom:function(){return W.currItem.hasSize?W.currItem.img:!1},_getOffset:function(d){var S;S=d?W.currItem.img:W.st.zoom.opener(W.currItem.el||W.currItem);var b=S.offset(),Q=parseInt(S.css("padding-top"),10),e=parseInt(S.css("padding-bottom"),10);b.top-=a(window).scrollTop()-Q;var R={width:S.width(),height:(f?S.innerHeight():S[0].offsetHeight)-e-Q};return m()?R["-moz-transform"]=R.transform="translate("+b.left+"px,"+b.top+"px)":(R.left=b.left,R.top=b.top),R}}});var n="iframe",o="//about:blank",p=function(a){if(W.currTemplate[n]){var d=W.currTemplate[n].find("iframe");d.length&&(a||(d[0].src=o),W.isIE8&&d.css("display",a?"block":"none"))}};a.magnificPopup.registerModule(n,{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){W.types.push(n),eL("BeforeChange",function(a,W,d){W!==d&&(W===n?p():d===n&&p(!0))}),eL(R+"."+n,function(){p()})},getIframe:function(d,S){var b=d.src,Q=W.st.iframe;a.each(Q.patterns,function(){return b.indexOf(this.index)>-1?(this.id&&(b="string"==typeof this.id?b.substr(b.lastIndexOf(this.id)+this.id.length,b.length):this.id.call(this,b)),b=this.src.replace("%id%",b),!1):void 0});var e={};return Q.srcAction&&(e[Q.srcAction]=b),W._parseMarkup(S,e,d),W.updateStatus("ready"),S}}});var q=function(a){var d=W.items.length;return a>d-1?a-d:0>a?d+a:a},r=function(a,W,d){return a.replace(/%curr%/gi,W+1).replace(/%total%/gi,d)};a.magnificPopup.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%"},proto:{initGallery:function(){var d=W.st.gallery,b=".mfp-gallery";return W.direction=!0,d&&d.enabled?(Q+=" mfp-gallery",eL(bQ+b,function(){d.navigateByImgClick&&W.wrap.on("click"+b,".mfp-img",function(){return W.items.length>1?(W.next(),!1):void 0}),S.on("keydown"+b,function(a){37===a.keyCode?W.prev():39===a.keyCode&&W.next()})}),eL("UpdateStatus"+b,function(a,d){d.text&&(d.text=r(d.text,W.currItem.index,W.items.length))}),eL(aJ+b,function(a,S,b,Q){var e=W.items.length;b.counter=e>1?r(d.tCounter,Q.index,e):""}),eL("BuildControls"+b,function(){if(W.items.length>1&&d.arrows&&!W.arrowLeft){var S=d.arrowMarkup,b=W.arrowLeft=a(S.replace(/%title%/gi,d.tPrev).replace(/%dir%/gi,"left")).addClass(j),Q=W.arrowRight=a(S.replace(/%title%/gi,d.tNext).replace(/%dir%/gi,"right")).addClass(j);b.click(function(){W.prev()}),Q.click(function(){W.next()}),W.container.append(b.add(Q))}}),eL(bQg+b,function(){W._preloadTimeout&&clearTimeout(W._preloadTimeout),W._preloadTimeout=setTimeout(function(){W.preloadNearbyImages(),W._preloadTimeout=null},16)}),void eL(R+b,function(){S.off(b),W.wrap.off("click"+b),W.arrowRight=W.arrowLeft=null})):!1},next:function(){W.direction=!0,W.index=q(W.index+1),W.updateItemHTML()},prev:function(){W.direction=!1,W.index=q(W.index-1),W.updateItemHTML()},goTo:function(a){W.direction=a>=W.index,W.index=a,W.updateItemHTML()},preloadNearbyImages:function(){var a,d=W.st.gallery.preload,S=Math.min(d[0],W.items.length),b=Math.min(d[1],W.items.length);for(a=1;a<=(W.direction?b:S);a++)W._preloadItem(W.index+a);for(a=1;a<=(W.direction?S:b);a++)W._preloadItem(W.index-a)},_preloadItem:function(d){if(d=q(d),!W.items[d].preloaded){var S=W.items[d];S.parsed||(S=W.parseEl(d)),fE("LazyLoad",S),"image"===S.type&&(S.img=a('<img class="mfp-img" />').on("load.mfploader",function(){S.hasSize=!0}).on("error.mfploader",function(){S.hasSize=!0,S.loadError=!0,fE("LazyLoadError",S)}).attr("src",S.src)),S.preloaded=!0}}}});var s="retina";a.magnificPopup.registerModule(s,{options:{replaceSrc:function(a){return a.src.replace(/\.\w+$/,function(a){return"@2x"+a})},ratio:1},proto:{initRetina:function(){if(window.devicePixelRatio>1){var a=W.st.retina,d=a.ratio;d=isNaN(d)?d():d,d>1&&(eL("ImageHasSize."+s,function(a,W){W.img.css({"max-width":W.img[0].naturalWidth/d,width:"100%"})}),eL("ElementParse."+s,function(W,S){S.src=a.replaceSrc(S,d)}))}}}}),dd()});