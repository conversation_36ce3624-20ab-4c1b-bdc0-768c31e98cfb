$(function() {
	var f = new Blazy({
		breakpoints: [{}],
		success: function(f) {
			setTimeout(function() {
				var d = f.parentNode;
				d.className = d.className.replace(/\bloading\b/, "")
			},
			200)
		}
	});
	if ($("video,audio").length > 0) {
		$("video,audio").mediaelementplayer()
	}
	$(".sbarBoxRight,.sbarBoxleft").hcSticky({
		stickTo: ".orw",
		innerTop: -56,
		responsive: {
			920 : {
				disable: true
			}
		}
	});
	$(".zoom").magnificPopup({
		disableOn: 200,
		type: "iframe",
		mainClass: "mfp-fade",
		removalDelay: 160,
		preloader: true,
		fixedContentPos: true
	});
	$(".header").after('<div id="leftNav" class="leftNav"><div class="mNav"></div><div class="mNavBtn"><i class="icon iconfont"></i></div>');
	$(".headBox .nav").clone(false).appendTo(".mNav");
	$(".header .mNavBtn").click(function() {
		$(".leftNav").addClass("mOpen").siblings(".leftNav").removeClass("mOpen");
		$(".mask").fadeIn(300);
		$("body").toggleClass("open").siblings("body").removeClass("open");
		$("html").toggleClass("ov").siblings("ov").removeClass("ov")
	});
	$(".leftNav .mNavBtn").click(function() {
		$(".leftNav").removeClass("mOpen");
		$(".mask").fadeOut(300);
		$("body").removeClass("open");
		$("html").removeClass("ov")
	});
	$(".dot1").click(function(f) {
		console.log("");
		d($(this), ".sub1");
		f.stopPropagation()
	});
	$(".dot2").click(function(f) {
		d($(this), ".sub2");
		f.stopPropagation()
	});
	$(".navBar li,.umUser").hover(function() {
		$(this).addClass("on")
	},
	function() {
		$(this).removeClass("on")
	});
	function d(f, d) {
		f.next().slideToggle();
		f.parent().siblings().find(".iconfont").removeClass("open");
		f.parent().siblings().find(d).slideUp();
		var e = f.find(".iconfont");
		if (e.hasClass("open")) {
			e.removeClass("open")
		} else {
			e.addClass("open")
		}
	}
	var e = $(".post-body").outerHeight(true);
	var M = $(".post-body").data("hight");
	if (e > M) {
		$(".post-body").css("height", M);
		$(".post-body").addClass("umHight");
		$(".post-body").append('<div class="readmore"><a href="javascript:void(0);" target="_self">阅读全文</a></div>')
	}
	$(".readmore a").on("click",
	function() {
		$(this).parents().find(".post-body").css("height", "auto");
		$(this).parents().find(".post-body").css("padding-bottom", "0");
		$(this).parent().remove()
	});
	$(".ssBtn").on("click",
	function() {
		var f = $(this);
		if (f.hasClass("off")) {
			f.removeClass("off").addClass("no");
			$(".ssFrom").css("right", "0");
			$(".ssFrom").css("visibility", "visible");
			$("html").addClass("ov");
			$(".mask").fadeIn(300)
		} else {
			f.removeClass("no").addClass("off");
			$(".ssFrom").css("right", "-100%");
			$(".ssFrom").css("visibility", "hidden");
			$("html").removeClass("ov")
		}
	});
	$(".fullRead").on("click",
	function() {
		var f = $(this);
		if (f.hasClass("off")) {
			f.removeClass("off").addClass("no");
			$("body").addClass("reading")
		} else {
			f.removeClass("no").addClass("off");
			$("body").removeClass("reading")
		}
	});
	$(".mask").click(function() {
		$(this).fadeOut(300);
		$(".leftNav").removeClass("mOpen");
		$("body").removeClass("open");
		$("#reward").fadeOut(0);
		$(".ssFrom").css("right", "-180%");
		$(".ssFrom").css("visibility", "hidden");
		$("html").removeClass("ov");
		$(".ssBtn").removeClass("no").addClass("off")
	});
	$(".ssFrom .close").click(function() {
		$(".ssFrom").css("right", "-100%");
		$(".ssFrom").css("visibility", "hidden");
		$("html").removeClass("ov");
		$(".ssBtn").removeClass("no").addClass("off");
		$(".mask").fadeOut("100")
	});
	$(window).scroll(function() {
		if ($(window).scrollTop() > 500) {
			$(".gotop").fadeIn("500")
		} else {
			$(".gotop").fadeOut("300")
		}
	});
	$(".gotop").click(function() {
		$("body,html").animate({
			scrollTop: 0
		},
		1500);
		return false
	})
});
jQuery(document).ready(function(f) {
	var d = f("#navBox").attr("data-type");
	f(".nav>li").each(function() {
		try {
			var e = f(this).attr("id");
			if ("index" == d) {
				if (e == "nvabar-item-index") {
					f("#nvabar-item-index").addClass("active")
				}
			} else if ("category" == d) {
				var M = f("#navBox").attr("data-infoid");
				if (M != null) {
					var a = M.split(" ");
					for (var H = 0; H < a.length; H++) {
						if (e == "navbar-category-" + a[H]) {
							f("#navbar-category-" + a[H] + "").addClass("active")
						}
					}
				}
			} else if ("article" == d) {
				var M = f("#navBox").attr("data-infoid");
				if (M != null) {
					var a = M.split(" ");
					for (var H = 0; H < a.length; H++) {
						if (e == "navbar-category-" + a[H]) {
							f("#navbar-category-" + a[H] + "").addClass("active")
						}
					}
				}
			} else if ("page" == d) {
				var M = f("#navBox").attr("data-infoid");
				if (M != null) {
					if (e == "navbar-page-" + M) {
						f("#navbar-page-" + M + "").addClass("active")
					}
				}
			} else if ("tag" == d) {
				var M = f("#navBox").attr("data-infoid");
				if (M != null) {
					if (e == "navbar-tag-" + M) {
						f("#navbar-tag-" + M + "").addClass("active")
					}
				}
			}
		} catch(f) {}
	});
	f("#navBox").delegate("a", "click",
	function() {
		f(".nav>li").each(function() {
			f(this).removeClass("active")
		});
		if (f(this).closest("ul") != null && f(this).closest("ul").length != 0) {
			if (f(this).closest("ul").attr("id") == "munavber") {
				f(this).addClass("active")
			} else {
				f(this).closest("ul").closest("li").addClass("active")
			}
		}
	})
});
jQuery(document).ready(function(f) {
	var d = f("#subcate").attr("data-type");
	f(".subcate li").each(function() {
		try {
			var e = f(this).attr("id");
			if ("category" == d) {
				var M = f("#subcate").attr("data-infoid");
				if (M != null) {
					var a = M.split(" ");
					for (var H = 0; H < a.length; H++) {
						if (e == "cate-category-" + a[H]) {
							f("#cate-category-" + a[H] + "").addClass("active")
						}
					}
				}
			}
		} catch(f) {}
	});
	f("#subcate").delegate("a", "click",
	function() {
		f(".subcate li").each(function() {
			f(this).removeClass("active")
		});
		if (f(this).closest("ul") != null && f(this).closest("ul").length != 0) {
			if (f(this).closest("ul").attr("id") == "subcate") {
				f(this).addClass("active")
			} else {
				f(this).closest("ul").closest("li").addClass("active")
			}
		}
	})
}); !
function(f) {
	var d = f.find(".order a"),
	e = f.find("[name=order]"),
	M = f.find("[name=sort]");
	d.click(function() {
		var d = $(this).data("type");
		if (d === e.val()) {
			M.val(M.val().toString() === "1" ? 0 : 1)
		} else {
			M.val("" === e.val() && !$(this).index() ? 1 : 0);
			e.val(d)
		}
		f.submit();
		return false
	})
}
$(function() {
	$(window).scroll(function(){		
		if($(window).scrollTop()>500){		
			$(".gotop").fadeIn('500');
		}else{
			$(".gotop").fadeOut('300');
		}
	});		
	$(".gotop").click(function(){
		$("body,html").animate({scrollTop:0},1500);
		return false;
	});		
});	

	$('.owl').owlCarousel({
	 loop:true,
		autoplay:true,
		autoplayTimeout:5000,
		autoplayHoverPause:true,
	 responsiveClass:false,
	 items:1,
		nav:false,
		autoHeight:false,
		dots:true,
		lazyLoad: true,
  lazyLoadEager: 1,
		navText : ["<i class='iconfont bx-prev'>上一张</i>", "<i class='iconfont bx-next'>下一张</i>"],
	});
	
	$('.swiperBn').owlCarousel({
	 loop:true,
		autoplay:true,
		autoplayTimeout:5000,
		autoplayHoverPause:true,
	 responsiveClass:false,
	 items:1,
		nav:false,
		autoHeight:false,
		dots:true,
		lazyLoad: true,
	 lazyLoadEager: 1,
		navText : ["<i class='iconfont bx-prev'>上一张</i>", "<i class='iconfont bx-next'>下一张</i>"],
	});