@charset "utf-8";
/*
Theme ID: umFresh
Author: umTheme
Author URI: http://www.umTheme.com/
*/
 html{-webkit-text-size-adjust:none; /*解决chrome浏览器下字体不能小于12px*/font-size:62.5%;}
 body{ font-size: 1.4rem;color:#333; font-family:"SF Pro SC", "SF Pro Text", "SF Pro Icons", PingFang SC, Lantinghei SC, Microsoft Yahei, Hiragino Sans GB, Microsoft Sans Serif, sans-serif;-moz-osx-font-smoothing: grayscale;overflow-x: hidden;}
*:before,*:after{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box} 
*{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}
html{zoom:1;}html *{outline:0;zoom:1;} html button::-moz-focus-inner{border-color:transparent!important;}
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0;} table{/*border-collapse:collapse;border-spacing:0;*/} fieldset,a img{border:0;} address,caption,cite,code,dfn,em,th,var{font-style:normal;font-weight:normal;} li{list-style:none;} caption,th{text-align:left;} h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;} q:before,q:after{content:'';}
input[type="submit"], input[type="reset"], input[type="button"], button { -webkit-appearance: none; /*去掉苹果的默认UI来渲染按钮*/} em,i{ font-style:normal;}
.input:after{ clear: both;content: ""; display: block; height: 0;visibility: hidden;}
em,i{ font-style:normal;}
a{margin:0;padding:0;background:0 0;color:#081018;vertical-align:baseline;text-decoration:none;outline: medium none; }
a:link {text-decoration: none;}
a:visited {text-decoration: none;}
a:hover {color:#00ADA7;text-decoration:none;}
a:active {text-decoration: none;}
a:focus,input:focus{outline:none;}
.clearfix:after {content:"."; display:block; height:0; clear:both; visibility:hidden; }.clearfix {display:block;}.clear{ clear:both;}/* 清除浮动*/
.colwrapper { overflow:hidden; zoom:1 /*for ie*/; margin:5px auto; }/* 高度自适应 */ 
.strong{ font-weight: bold;} .fl{ float: left;} .fr{ float: right;} .center{ margin:0 auto; text-align:center;}
.show{ display:block; visibility:visible;}.hide{ display: none; visibility:hidden;}
.block{ display:block;} .inline{ display:inline;}
.op{filter:alpha(opacity=50); -moz-opacity:0.5;/** Firefox 3.5即将原生支持opacity属性，所以本条属性只在Firefox3以下版本有效 ***/ -khtml-opacity: 0.5; opacity: 0.5; } 
.break{ word-wrap:break-word;overflow:hidden; /*word-break:break-all;*/}
.tl{ text-align:left} .tr{ text-align:right;}
.fl {float:left;}
.fr {float:right;}
.clear::before,.clear::after {content:'';display:table}
.clear::after {clear:both}
a{blr: expression(this.onFocus=this.blur());-webkit-tap-highlight-color: transparent;-moz-tap-highlight-color: transparent}
:focus {outline: 0}
a,div {-moz-tap-highlight-color: transparent;-webkit-tap-highlight-color: rgba(0, 0, 0, 0);tap-highlight-color: transparent}
body{ background:#f1f2f9;}
html.ov,html.ov body{ overflow: hidden;width:100%; height:100%;}
@font-face {
  font-family: 'iconfont';
  src: url('fonts/iconfont.eot');
  src: url('fonts/iconfontd41d.eot?#iefix') format('embedded-opentype'),
  url('fonts/iconfont.woff2') format('woff2'),
  url('fonts/iconfont.woff') format('woff'),
  url('fonts/iconfont.ttf') format('truetype'),
  url('fonts/iconfont.html#iconfont') format('svg');
}
/*@font-face {
  //font-family: 'iconfont';  project id 934715 
  src: url('//at.alicdn.com/t/font_934715_j1f7oih259p.eot');
  src: url('//at.alicdn.com/t/font_934715_j1f7oih259p.eot?#iefix') format('embedded-opentype'),
  url('//at.alicdn.com/t/font_934715_j1f7oih259p.woff2') format('woff2'),
  url('//at.alicdn.com/t/font_934715_j1f7oih259p.woff') format('woff'),
  url('//at.alicdn.com/t/font_934715_j1f7oih259p.ttf') format('truetype'),
  url('//at.alicdn.com/t/font_934715_j1f7oih259p.svg#iconfont') format('svg');
}*/
.iconfont,.leftBox i{
    font-family:"iconfont" !important;
    font-size:16px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;}
/*文字两侧对齐*/
.justify {text-align:justify;text-justify:distribute-all-lines;/*ie6-8*/text-align-last:justify;/* ie9*/-moz-text-align-last:justify;/*ff*/-webkit-text-align-last:justify;/*chrome 20+*/}
input:-webkit-autofill,select:-webkit-autofill {-webkit-box-shadow: 0 0 0px 1000px white  inset !important;} 
input,textarea{outline-color: invert ;outline-style: none ;outline-width: 0px ;border: none ;border-style: none ;text-shadow: none ;-webkit-appearance: none ;-webkit-user-select: text ;outline-color: transparent ;box-shadow: none;}
/*超出省略号*/
.tn{word-break:keep-all;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}
.container:before, .container:after,.warp:after,.main:before, .main:after,.orw:before,.orw:after,.sidebar:after,.sidebar:before{content: "";
display: block;height: 0;overflow: hidden;}
.container:after,.main:after,.orw:after{ clear:both;}
.container{max-width:1260px; padding: 0 15px; margin: 0 auto; position:relative; clear:both;}
.warp{ margin-top:90px;-webkit-transition:margin-top .3s ease;-o-transition:margin-top .3s ease;transition:margin-top .3s ease;}
.category.cateBn .warp{margin-top:0px;}
.orw{ display: block; overflow: hidden;}
*,:after,:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
/*----------- 头部 -----------*/
.header{display:block; height:66px; background:#FFFFFF; position:relative;-webkit-transition:top .3s ease;-o-transition:top .3s ease;transition:top .3s ease; z-index:4; box-shadow:0 2px 20px rgba(0,0,0,.05)}
.header.fixed{ position:fixed; width:100%; left:0; top:0;-webkit-transition: left .3s ease, top .5s ease, opacity .5s ease;-moz-transition: left .3s ease, top .5s ease, opacity .5s ease;transition: left .3s ease, top .5s ease, opacity .5s ease;}
.header.fixed.fixedOut{ top:-160px;opacity:0}
.header.fixed.fixedIn.fixedOut{ top:0px;opacity:1}
.header .container{margin:0 auto}
.header .logo{ float:left;overflow:hidden; height:66px;}
.header .logo h1{height:100%; display:block;}
.header .logo a{height:100%; display:block; position:relative; font-size:0; overflow:hidden;}
.header .logo a img{ height:100%;}
.header .navBar{ float:left; margin-left:4.6%}
.header .navBar li{ display:inline-block; height:66px; line-height:66px; position:relative; vertical-align:top;}
.header .navBar li a{ display:inline-block; padding:0 15px; color:#0C0B0D; font-size:16px; position:relative; z-index:2; min-width:62px; text-align:center;-webkit-transition:color .3s ease;-o-transition:color .3s ease;transition:color .3s ease;}
.header .navBar li em[class*="dot"]{ display:inline-block;position:absolute; right:0px;z-index:1; width:16px;}
.header .navBar li em[class*="dot"] .iconfont{ color:#0C0B0D; font-size:16px; width:16px; display:block;}
.header .navBar li em[class*="dot"] .iconfont:before,.leftNav li em[class*="dot"] .iconfont:before{content: "\e629"}
.header .navBar li.active a,.header .navBar li a:hover,.header .navBar li.active em[class*="dot"] .iconfont,
.header .navBar li.on a,
.header .navBar li.on em[class*="dot"] .iconfont
{color:#00ADA7;}
.header .navBar li:before{width:5px; height:5px; position: absolute; bottom:12px; left:50%; transform:translateX(-50%); background-color: #00ADA7; content: ''; border-radius:50px; opacity: 0;}
.header .navBar li.active:before{ opacity: 1}
.header .navBar ul li.on>ul li a{color:rgba(255,255,255,0.8);}
.header .navBar ul li > ul li.active:before{ display:none}
/*二级高亮*/
.header .navBar ul li>ul{ width:100%; min-width:120px; position:absolute; top:100%; left:50%; transform:translateX(-50%); right:0;background:rgba(0,0,0,0.68); z-index:888; visibility:hidden; opacity:0;box-shadow:0px 8px 20px 0px rgba(0,0,0,0);-webkit-transition:all .3s ease;-o-transition:all .3s ease;transition:all .3s ease;}
.header .navBar ul li>ul li{ width:100%; height:40px; line-height:40px; display:block;}
.header .navBar ul li>ul li a,.header .navBar ul li.active > ul li a{ width:100%; text-align:center;height:40px; line-height:40px; font-size:14px; color:rgba(255,255,255,0.8); opacity:1;-webkit-transition:all .3s ease;-o-transition:all .3s ease;transition:all .3s ease;}
.header .navBar ul li>ul li.on a,.header .navBar ul li>ul li.on a:hover{ background:#00ADA7;color:#fff;}
.header .navBar ul li.on>ul{ visibility:visible; opacity:1;}
.header .navBar ul li>ul li em[class*="dot"]{ position:absolute; right:5px; left:auto; top:0; z-index:3;-webkit-transform: rotate(-90deg);-ms-transform: rotate(-90deg);-o-transform: rotate(-90deg);transform: rotate(-90deg);}
.header .navBar ul li.on>ul li em[class*="dot"] .iconfont{ color:rgba(255,255,255,0.8);}
/*三级高亮*/
.header .navBar ul li>ul li .sub2{visibility:hidden; opacity:0; display:none; left:-100%; transform:none;}
.header .navBar ul li>ul li.on .sub2{ right:-100%; left:auto; top:0; visibility:visible; opacity:1; display:block;}
.header .navBar ul li>ul li.on .sub2 li a,.header .navBar ul li>ul li.on .sub2 li a:hover{ background:rgba(0,0,0,0); color:rgba(255,255,255,0.8);}
.header .navBar ul li>ul li.on .sub2 li.on a,.header .navBar ul li>ul li.on .sub2 li.on a:hover{background:#00ADA7; color:rgba(255,255,255,0.8);}
/*移动端*/
.mNavBtn{ position:absolute; top:0; bottom:0; left:0px; padding:0 15px; height:100%; display:none; line-height:48px; cursor:pointer;}
.mNavBtn i{color:#0C0B0D; opacity:0.68; font-size:20px;webkit-transition:opacity .3s ease;-o-transition:opacity .3s ease;transition:opacity .3s ease; }
.headBox .mNavBtn i:before{content: "\e696";font-family: "iconfont" !important;}
.mNavBtn:hover i{color:#00ADA7;}
.mOpen .mNavBtn i{color:#fff}
.mOpen .mNavBtn i:before,.ssFrom .close:before{content: "\e600";}
.leftNav{ width:190px; background:#00ADA7; position:fixed; top:0; bottom:0; left:-190px;-webkit-transition:all .3s ease;-o-transition:all .3s ease;transition:all .3s ease; z-index:999}
.leftNav .mNavBtn{ height:48px; line-height:48px; left:auto; right:-45px; display:none;}
.leftNav ul{ padding-top:0px;}
.leftNav li{ width:100%; position:relative;}
.leftNav li a{ padding: 0 25px; line-height:48px; color:#FFFFFF; display:block; border-bottom:1px solid rgba(255,255,255,0.08);}
.leftNav li a:hover{ background:rgba(0,0,0,0.1);}
.leftNav ul li>ul{ display:none;}
.leftNav li em[class*="dot"]{position: absolute;top:0px;right:0px; width:48px; height:48px; text-align:center; border-left:1px solid rgba(255,255,255,0); cursor:pointer;}
.leftNav li em[class*="dot"] .iconfont{font-size: 18px;color: #FFFFFF;-webkit-transition: all .3s ease;-o-transition: all .3s ease;transition: all .3s ease; position:absolute;width:48px;line-height:48px; top:0; left:0;}
.leftNav li em[class*="dot"] .iconfont.open{-webkit-transform: rotate(180deg);-ms-transform: rotate(180deg);-o-transform: rotate(180deg);transform: rotate(180deg);}
.leftNav ul li>ul li a{border-bottom:1px solid rgba(255,255,255,0.05); opacity:0.8; font-size:12px; text-indent:1em}
/*搜索*/
.search{ float:right;margin-left:5px; position:relative; z-index: 3}
.search i.icon{ color:#0C0B0D; line-height:66px; opacity:0.68; font-size:22px; padding-left:10px;-webkit-transition:opacity .3s ease;-o-transition:opacity .3s ease;transition:opacity .3s ease; cursor:pointer; display:block;}
.search i.icon:hover{color:#00ADA7;}
.search i.ssBtn.off:before,.ssFrom button i:before{content: "\ec3f";}
.search i.ssBtn.no:before {content: "\e600";}
.ssFrom{ padding:48px 0px 25px 25px;line-height:none; background:#fff;box-shadow:0px 8px 20px 0px rgba(0,0,0,0.06); z-index:1000;-webkit-transition:all .3s ease;-o-transition:all .3s ease;transition:all .3s ease;position:fixed; width: 360px; top: 0; bottom: 0; right:-100%; visibility: hidden}
.ssFrom .ssBox{height:100%; padding-right: 25px;}
.ssFrom .sform{width:100%; padding-right:58px; position:relative; max-width: 600px; margin: 0 auto; border:1px solid #00ADA7;}
.ssFrom button{height: 100%; border:medium none; background:#00ADA7; position:absolute;top:0; right:0; bottom: 0; cursor:pointer;opacity:1;width: 60px;-webkit-transition:all .3s ease;-o-transition:all .3s ease;transition:all .3s ease;}
.ssFrom button i{ color:#fff; font-size:18px; height:32px; line-height:32px;}
.ssFrom button:hover{ opacity:.8}
.ssFrom .sinput{ padding:0px 10px 0 10px; height:40px; line-height:40px; width:100%; border:medium none; background:#f8f8f8; font-size: 14px;}
.ssFrom .close{ position:absolute; top:15px; left:21px; color:#9ca0ad; font-size:20px; cursor: pointer;}
.ssFrom .close:hover{color:#00ADA7;}
.ssArr{padding-top: 25px; font-size: 0}
.ssArr li{ padding: 15px 0px 15px 70px; width: 100%; display: flex;align-items: center;justify-content: center; float: left; overflow: hidden;}
.ssArr li.h{ width: 100%; text-align: center; font-size: 18px; padding: 10px 15px 10px 15px; margin-bottom: 15px; position: relative}
.ssArr .ssImg,.ssArr .ssText{font-size:0; display: inline-block; vertical-align: middle;}
.ssArr .ssImg{ position: relative; margin-left: -80px;}
.ssArr .ssImg img{ width:60px; height:60px; object-fit: cover; border-radius: 3px;}
.ssArr .ssText{ width: 100%; padding-left: 12px;}
.ssArr .ssText h3{ font-size: 14px; line-height: 1.5; max-height: 22px; overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.ssArr .ssText .ssInfo{font-size: 12px; color: #9ca0ad; margin-top: 2px; max-height: 35px; overflow: hidden;}
.ssArr li.h:after {content: "";width:30px;height:2px;background: #3690F6;position: absolute;left:50%; margin-left: -15px; bottom:2px;border-radius: 30px;}
/*login*/
.umUser{float: right;position: relative;padding: 17px 0;margin-left: 15px;}
.umUser .userPic{ width: 32px; height: 32px; font-size:0; border-radius: 50px; display: block; overflow: hidden;}
.umUser .userPic img{width: 32px; height: 32px; border-radius: 50px; cursor: pointer;}
.umUser .login{ padding:15px 20px; background: #fff; position: absolute; right: 0; top:78px; visibility: hidden;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; min-width: 180px;box-shadow: 0 2px 20px rgba(0,0,0,0.08); width: 100%;border-radius: 4px;opacity: 0;}
.umUser.on .login{ visibility: visible; top:66px;opacity: 1;}
.umUser .login .wdl{ width: 100%; text-align: center; display: block;}
.umUser .login .wdl a{ padding: 0 10px;}
.umUser .login .ydl a{ padding: 0 5px 0 24px; position: relative; margin-top:10px; display:inline-block;}
.umUser .login .ydl a i{ position: absolute; top:-3px; left: -2px; font-size: 24px; opacity: .6}
.umUser .login .ydl a i:before {content: "\e628";}
.umUser .login i.dot{ position: absolute; top: -14px; right: 7px; color: #fff; font-size: 22px;}
.umUser .login i.dot:before {content: "\e72a";}
/*----------头部样式结束----------*/
.main {width: 100%; float: left;}
/*-----侧栏-----*/
.sidebar { float: left; margin-left: -320px; width: 320px;}
.leftBox .sidebar {margin-left: -280px; width: 280px;}
.widget{background:rgba(255,255,255,1); padding:20px;box-shadow: 0 2px 15px rgba(0,0,0,0.02); margin-bottom:20px}
.widget:last-child{}
.widget.rbox h3{padding-bottom:5px;margin-bottom:10px;border-bottom:0px solid #eee;font-size:18px;position:relative; text-indent:15px; line-height: 18px;}
.widget.rbox h3:after{content:"";width:4px;height:16px;background:#00ADA7;position:absolute;left:0; top:1px; border-radius:30px;}
.widget ul{ font-size:0; display:block;}
.widget li{ height:30px; line-height:30px; overflow:hidden; font-size:14px;text-overflow: ellipsis;white-space: nowrap;}
/*标签*/
.widget.divTags li{ display:inline-block;-ms-transition:.3s;-moz-transition:.3s;-webkit-transition:.3s; overflow:visible; height:auto;}
.widget.divTags li a{font-size:12px;font-weight:normal;padding:0px 10px;border-radius:0px; display:block;margin:3px 4px 3px 0; height: 32px; line-height:30px; -ms-transition:.3s;-moz-transition:.3s;-webkit-transition:.3s; border:1px solid #f3f4f9}
.widget.divTags li a span{ font-family:Roboto-Light; font-size:12px;}
.widget.divTags li a:hover{color:#fff; background: #282828; border: 1px solid #282828;}
.widget.divSearchPanel .scForm,.widget.divSearchPanel form{ position: relative;}
.widget.divSearchPanel form input[type=text]{ width: 100%; border-radius: 50px; border: medium none; padding:0px 46px 0px 16px; line-height: 36px; background:#edeef0;}
.widget.divSearchPanel form input[type=submit]{ position: absolute; border: medium none;font-size: 14px; background:#00ADA7;height: 36px;line-height: 36px;color: #fff;border-radius: 30px; padding:0 15px; top:0px; right:0px; cursor: pointer}
/*最新评论*/
.widget.divComments li{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;padding-bottom:3px;padding-top:3px;margin:1px 0 3px 0; height: auto;}
.widget.divComments li{ line-height:24px;padding-top:5px; padding-bottom:5px;}
.widget.divComments li i{ font-style:normal; color:#9ca0ad; font-size: 12px;font-family: Roboto-Light;}
.widget.divComments li a{ position:relative; top:0px;}
.widget.divComments li .author{float:left;margin:0 8px 0px 0;padding:0px;width:48px; height: 48px;font-size: 0; -webkit-border-radius:50px;-moz-border-radius:50px;border-radius:50px;-webkit-box-shadow:inset 0 -1px 0 #3333sf;box-shadow:inset 0 -1px 0 #3333sf;-webkit-transition:0.4s;-webkit-transition:-webkit-transform 0.4s ease-out;-moz-transition:-moz-transform 0.4s ease-out;transition:transform 0.4s ease-out; overflow: hidden;background: #fafafa url(images/lay.gif) center center no-repeat;}
.widget.divComments li:hover .author{-webkit-box-shadow:0 0 10px #fff;box-shadow:0 0 10px #fff;-webkit-transform:rotateZ(360deg);-moz-transform:rotateZ(360deg);transform:rotateZ(360deg);}
/*热门文章*/
.widget.ummodule2 li{ padding-left: 30px; margin:7px 0;position: relative;}
.widget.ummodule2 li .li-icon{ width: 24px; height: 24px; line-height: 24px; text-align: center; display: block; position: absolute; left: 0; top: 2px; background: #f3f4f9; border-radius: 50px; font-size: 14px; font-family: Roboto-Light; color: #50555a;}
.widget.ummodule2 li .icon-1,.widget.ummodule2 li .icon-2,.widget.ummodule2 li .icon-3{ color: #fff;}
.widget.ummodule2 li .icon-1{background-color: #FF6B57;}
.widget.ummodule2 li .icon-2{background-color: #2ea7e0;}
.widget.ummodule2 li .icon-3{background-color: #6bc30d;}
/*最新发表*/
.widget.divPrevious li,.ummodule3 li{ padding-left:15px; margin: 0px 0; position: relative;}
.widget.divPrevious li i,.ummodule3 i{ color: #9ca0ad; position:absolute; left: -6px; top: 1px; opacity: .6;}
.widget.divPrevious li i.iconfont:before,.ummodule3 i.iconfont:before{content: "\e62e";}
/*divMisc*/
.widget.divMisc li{ height: auto; line-height: normal; font-size: 0; margin: 3px 0}
.widget.divMisc img{ max-height: 30px; max-width: none; width: auto;}
/*日历*/
.widget.divCalendar table{ width: 100%;}
.widget.divCalendar table th{ color: #9ca0ad;}
.widget.divCalendar table th,.widget.divCalendar table td{ text-align: center;padding: 10px 10px; border: none;background: #f9fafc; font-family: Roboto-Light; }
.widget.divCalendar table caption{text-align: center; padding: 10px 0;background: #f9fafc; font-family: Roboto-Light; }
.widget.divCalendar table td a{color: #3690F6; opacity: .8; transition:.3s;-ms-transition:.3s;-moz-transition:.3s;-webkit-transition:.3s;}
.widget.divCalendar table td a:hover{opacity: 1;}
/*关于作者*/
.widget.umUser{overflow: hidden; position: relative;float: none; margin-left:0}
.widget.umUser h3{ text-align: center;}
.widget.umUser h3:after{ display: none;}
.widget.umUser div{text-align: center; background-color: #f9fafc;}
.widget.umUser .userBg{max-width:100%; height: 100px; object-fit: cover;object-position: center;width: 100%;overflow: hidden; border-top-right-radius: 3px;border-top-left-radius: 3px; background: none}
.widget.umUser .userImg{ width: 68px; height: 68px; margin: -40px auto 0 auto; display: block; background: none}
.widget.umUser .userImg img{width: 68px; height: 68px; border: 4px solid #fff!important; border-radius: 50px; box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);transition:.6s;-ms-transition:.3s;-moz-transition:.6s;-webkit-transition:.6s;}
.widget.umUser .userImg:hover img{-webkit-transform: rotateZ(360deg);-moz-transform: rotateZ(360deg);transform: rotateZ(360deg);}
.widget.umUser .userImg mip-i-space{width: 68px!important; height: 68px!important;border-radius: 50px; padding: 0!important}
.widget.umUser .userUrl{ display: inline-block;width:100%;}
.widget.umUser .userTit{ font-size: 18px; padding: 15px 15px;}
.widget.umUser .userText{font-size: 14px; color: #50555a; line-height: 24px;padding: 0px 10px;}
.widget.umUser ul{ padding: 25px 0}
.widget.umUser li,.umTj li{width: 33.3333%; display: inline-block; text-align: center; height: auto; line-height: 22px; position: relative;}
.widget.umUser li span,.umTj li span{ display: block; width: 100%; clear: both; color: #9ca0ad; font-size: 12px;}
.widget.umUser li span.s1,.umTj li span.s1{ font-size: 16px;font-family: Roboto-Light; color: #50555a; font-weight: 100}
.widget.umUser li:after,.umTj li:after{content: "";width:1px;height: 33px;background: #f1f1f1;position: absolute;right: 0;top: 6px;}
.widget.umUser li.last:after,.umTj li.last:after{ display: none;}
.widget.umUser .divlist{width:50px; display: inline-block; text-align: center; padding: 5px 0 20px; position: relative; z-index: 1}
.widget.umUser .divlist a,.widget.umUser .divlist.weixin span{ width: 32px; height: 32px; line-height: 32px; text-align: center; margin: 0 auto;display: block;border-radius: 50px; opacity: .8;transition:.3s;-ms-transition:.3s;-moz-transition:.3s;-webkit-transition:.3s;}
.widget.umUser .divlist a:hover,.widget.umUser .divlist.weixin span:hover{opacity: 1}
.widget.umUser .divlist a i{ font-size: 18px;transition:.3s;-ms-transition:.3s;-moz-transition:.3s;-webkit-transition:.3s;}
.widget.umUser .divlist.qq a i{color: #1ea6e9;}
.widget.umUser .divlist.weibo a i{color: #f78585;}
.widget.umUser .divlist.weixin span i{color: #6bc30d;}
.widget.umUser .divlist.tencent a i{color: #00C5FF;}
.widget.umUser .divlist.qq a:hover i{ color: #fff;}
.widget.umUser .divlist.weibo a:hover i{ color: #fff;}
.widget.umUser .divlist.weixin span:hover i{ color: #fff;}
.widget.umUser .divlist.tencent a:hover i{ color: #fff;}
.widget.umUser .divlist.qq a { border: solid 1px #1ea6e9;}
.widget.umUser .divlist.weibo a { border: solid 1px #f78585;}
.widget.umUser .divlist.weixin span { border: solid 1px #6bc30d;cursor: pointer}
.widget.umUser .divlist.tencent a { border: solid 1px #00C5FF;}
.widget.umUser .divlist.qq a:hover{ background: #1ea6e9;}
.widget.umUser .divlist.weibo a:hover{ background: #f78585; }
.widget.umUser .divlist.weixin span:hover{ background: #6bc30d;}
.widget.umUser .divlist.tencent a:hover{ background: #00C5FF;}
.widget.umUser .divlist.weixin span .ewmBx{ position: absolute; width: 180px; height: 180px; max-width: none; min-height: auto; padding:5px; background: #fff; box-shadow: 5px 0 15px rgba(0,0,0,.1); top: -160px; left: 50%; margin-left: -90px; opacity: 0; visibility: hidden;-ms-transition:.3s;-moz-transition:.3s;-webkit-transition:.3s;transition:.3s;}
.widget.umUser .divlist.weixin span:hover .ewmBx{opacity: 1; visibility:visible;top: -195px;}
.widget.umUser .divlist.weixin span .ewmBx .dot{ position: absolute; bottom: -13px; width: 26px; height: 26px; left: 50%; margin-left: -13px; font-size: 30px; color: #fff;}
.widget.umUser .divlist.weixin span .ewmBx img{width: 100%;max-width: 100%;}
.ummodule1 p{ text-align: center; padding: 10px 0;color: #50555a; font-size: 12px;}
.ummodule1 img{max-width:100%;margin: 0px auto; display:block;}
.ummodule1.small img{max-width:68%;}
.ummodule1 h4{ text-align:center; font-size:16px; padding:5px 0 10px 0;}
/*微语*/
.widget.ummodule4 li{ height: auto; line-height: 24px; padding-left:18px; margin:8px 0 0;position: relative;white-space:inherit; overflow: inherit}
.widget.ummodule4 li.nolist{padding-left:14px; color: #9ca0ad;}
.widget.ummodule4 .time{ font-size: 12px; color: #9ca0ad}
.widget.ummodule4 .dot,.artBox .weiyu .post .dot{ width: 2px; position: absolute; top:10px; bottom:-15px; left:1px; background: #f5f5f7}
.widget.ummodule4 .dot{ background: none;}
.widget.ummodule4 li:last-child .dot,.artBox .weiyu .wy:nth-last-child(2) .dot{background:none}
.widget.ummodule4 .dot:before,.widget.ummodule4 .dot:after,.artBox .weiyu .post .dot:before,.artBox .weiyu .post .dot:after{content: '';width:100%;height:100%;position: absolute; display: block; border-radius: 30px; left:-3px; top:-2px;background:rgba(255,255,255,0.85); }
.widget.ummodule4 .dot:before,.artBox .weiyu .post .dot:before{ width:8px; height:8px; border: 1px solid #00ADA7; z-index: 2;background:rgba(255,255,255,1);opacity: .68;}
.widget.ummodule4 .dot:after,.artBox .weiyu .post .dot:after{width:12px; height:12px; z-index: 1; left: -5px; top: -4px;}
.widget.ummodule4 .in .dot:after{width:20px; height:20px; background: #00ADA7; left: -9px; top: -8px; opacity: .2;border: 3px solid #fff;}
.widget.ummodule4 .info{ display: none; font-size: 13px; line-height: 1.6; color: #9ca0ad;word-wrap:break-word;-webkit-hyphens:auto;-ms-hyphens:auto;hyphens:auto;text-align:justify;text-justify:distribute-all-lines;-webkit-text-align-last:justify;}
.widget.ummodule4 .info a{color: #9ca0ad;}
.widget.ummodule4 .info a:hover{color: #00ADA7;}
/*列表*/
.artBox,.sortBox{min-height:30rem;margin-bottom:20px;margin-right:280px;padding-left:30px; padding-right:30px; position: relative;background:rgba(255,255,255,1);box-shadow: 0 2px 15px rgba(0,0,0,0.02);}
.artBox .noArr{ padding: 100px 0; text-align: center;}
.artBox .noArr img{width:200px;}
.artBox .noArr p{ opacity: .6;}
.artBox .noArr i{ font-size: 68px; margin-bottom: 10px; display: block;word-wrap: break-word !important;}
.artBox .post{ padding:30px 0;position:relative; border-bottom: 1px solid #f3f4f9;display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;min-width: 0;word-wrap: break-word;-ms-flex-direction: row;flex-direction: row;}
.artBox .post.last{border-bottom: 1px solid #fff;}
.post .post-media { width:33.333%; height:auto;position:relative;font-size: 0;overflow: hidden;-ms-flex-negative: 0;flex-shrink: 0;}
.post .post-media .post-pic{ margin-left:13px}
.post.big .post-media .post-pic{ margin-left:0px;}
.post .post-media img {width: 100%;height: 100%;position: absolute;top: 50%;left: 50%;object-fit:cover;object-position:center;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;transform: translate(-50%,-50%) scale(1);}
.post .post-media img:hover{ transform: translate(-50%,-50%) scale(1.1);}
.artBox .weiyu{background:rgba(255,255,255,0.85);position:relative; padding:6px 0 25px;}
.artBox .weiyu .post{ background: none; margin-bottom: 0; box-shadow: 0 0 20px rgba(0,0,0,0); padding: 20px 0px 20px 25px; overflow: inherit; border: none}
.artBox .weiyu .post .post-title{ margin-top: 6px; font-size: 16px}
.artBox .weiyu .post .post-content{ color: #9ca0ad; padding-right:0}
.artBox .weiyu .date{ color: #00ADA7}
.artBox .weiyu .post .dot{ left: 5px; top: 29px; bottom: -24px;}
.artBox .weiyu .ias-trigger a,.artBox .weiyu .ias-noneleft{ margin: 5px auto 0px; background: none; padding-bottom:0;}
.post .post-content{ font-size:1.4rem; line-height:1.6; min-width: 0; overflow:hidden;display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;-ms-flex: 1 1 auto;flex: 1 1 auto;-ms-flex-pack: center;justify-content: center; padding-right: 20px;}
.post .post-content .umInfo{text-align: justify; display:block; max-height: 44px;overflow:hidden;color: #50555a;}
.post .post-info{margin: 1rem 0 0 0;text-align: left; font-size: 0; }
.post .post-info i{ vertical-align:-1px; padding-right:3px;}
.post .post-info em{font-size:1.2rem; color:#9ca0ad; letter-spacing:0rem; margin-right:1rem;}
.post .post-info em.ceta a{color: #9ca0ad;}
.post .post-info em.ceta i{font-size:1.9rem;vertical-align: -2px;}
.post .post-info em.ceta i:before{content: "\ebc6";}
.post .post-info em.date i:before{content: "\e62c";}
.post .post-info em.coms i:before{content: "\e63b";}
.post .post-info em.view i:before{content: "\e65a";}
.post.big .post-info{margin:1.6rem 0 0 -4px;}
.post .post-title {margin:0px 0 10px;font-size:2rem;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;}
.post .post-title a {-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;}
.post .post-title em{ color:#f66}
.post .post-title b{ color:#50c692; font-weight:normal;}
.post .noMedia .post-title{margin:0px 0 10px;}
.post .post-title em.isTop{ padding:1px 4px; border:0px solid currentColor;font-size: 12px; font-weight: 500; vertical-align: 3px; margin-right: 5px; display: inline-block; background:currentColor; position: relative; color: #f66; border-radius: 10px; border-bottom-left-radius: 0;}
.post .post-title em.isTop span{color:#fff;}
.post.umTop .istTop{color: #f66; font-size:18px; height: 24px; line-height: 24px; vertical-align:1px; display: inline-block; margin-right: 2px; margin-left: -2px;}
.post.umTop .istTop:before {content: "\e623";}
.post .post-title em.isTop:after{content: '';position: absolute;border-width: 0 0 4px 8px;border-style: solid;left: 0;top: 100%;border-color: transparent currentColor;}
/*大图*/
.artBox .post.big { display: block; padding-top:20px}
.post.big .post-media,.post.big .post-media .movie{ width:100%;margin-right:0;margin-left:0;}
.post.big .post-media img{transform:translate(-50%,-50%) scale(1);;}
.post.big .post-media img:hover,.post.big .post-media .movie a:hover img{transform:translate(-50%,-50%) scale(1.02);}
.post.big .post-content{ margin-bottom:15px;padding-right: 0;}
.post .post-content.noMedia{ clear:both; padding-left: 0;padding-right: 0;}
.post.big .post-title{margin-bottom:10px;}
/*单页面*/
.umpage.orw .main{ float: right;}
.umpage .artBox{ margin:0 0 24px 0px; padding-left: 210px; box-shadow: 0 0 20px rgba(0,0,0,0.01)}
.umpage .artBox .post{margin:0; background:#fff; box-shadow:none; display: block;}
.umpage .sidebar{margin:0px ;width: 210px;position: absolute;bottom: 24px;top:0px;background:#f9fafc; border-right: 1px solid rgba(227,229,236,.26);}
.umpage .sidebar li{ line-height:1.8; position: relative; opacity: 0.8}
.umpage .sidebar li a{ display: block; padding:10px 15px; border-bottom: 1px solid rgba(227,229,236,.4);}
.umpage .sidebar li a i{ margin-right: 3px;}
.umpage .umBnGg{ margin:0; padding: 0 20px;}
.umpage .sbarBoxa .slide1,.umpage .sbarBoxa .slide2{ opacity:0; background:none;position: absolute;display: inline-block;height:48px;transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1.05); border-bottom:none;}
.umpage .sbarBoxa .slide2:after{width:3px;height:100%;position: absolute;top: 0px;left: 0;background-color: #00ADA7;content: '';border-top-right-radius:0px;border-bottom-right-radius:0px; display:block; z-index: 2; opacity: 0.4}
.umpage .sbarBoxa li.active{ opacity:1}
.umpage .sbarBoxa li.active a,.umpage .sbarBoxa li.active a i,.umpage .sbarBoxa li a:hover i,.umpage .sbarBoxa li a:hover{ color:#00ADA7}
.umpage .sbarBoxa li.active a{background: rgba(255,255,255,1); width:210px}
.umpage .sbarBoxa li.active a:before{width:3px;height:100%; position: absolute;top:0px;left:0;background-color: #00ADA7;content: ''; border-top-right-radius:0px;border-bottom-right-radius:0px;}
.umpage .sbarBoxa:hover li.active a:before{opacity:0;}
.umpage .sbarBoxa  li.active a:before{opacity:.6;-webkit-transition: all .3s ease-in-out;-o-transition: all .3s ease-in-out;transition: all .3s ease-in-out;}
.artBox .post.umArtPost{ display: block; padding-top: 26px;}
.single .umpage .post .post-title{ padding: 0 0 20px 0}
/*预加载*/
.imgLazy{ display: block;background: #fafafa url(images/lay.gif) center center no-repeat; height: 100%}
.b-lazy {max-width: 100%;-webkit-transition: opacity 500ms ease-in-out;-moz-transition: opacity 500ms ease-in-out;-o-transition: opacity 500ms ease-in-out;transition: opacity 500ms ease-in-out;filter: alpha(opacity=0);opacity: 0;}
.b-lazy.b-loaded {vertical-align: middle;filter: alpha(opacity=100);opacity: 1;}
.post-pic a,.post-media .movie a{width: 100%;height: 100%;position: absolute;}
.post-pic,.post-media .movie{font-size: 0;position: relative;overflow: hidden;margin-left: 10px;}
.post-pic:after,.post-media .movie:after,.post.big .post-pic3 .post-pic:after{content: '';display: block;padding-top: 75%;}
.post.big .post-pic:after,.post.big .post-media .movie:after{padding-top: 56%;}
.post.big .post-pic3.post-media{width:calc(100% + 20px);margin-left: -20px;display: flex}
.post.big .post-pic3 .post-pic{width: calc(100% / 3);display: inline-block; margin-left:20px;}
.post.big .post-media.post-custom img{position: relative;transform: translate(0%,0%) scale(1);top: 0;left: 0;}
.post.big .post-media.post-custom .post-pic a:hover img{transform: translate(0%,0%) scale(1.02);}
.post.big .post-media.post-custom .post-pic a{position: relative;}
.post.big .post-media.post-custom .post-pic:after{display:none}
/*面包屑*/
.umCrumb{width:100%;max-width:1260px; margin:0 auto; padding:0px 16px 20px 16px; font-size:12px;position:relative;z-index: 2;color:#50555a; display:none}
.umCrumb a{font-size:12px;color:#50555a;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;}
.umCrumb a:hover{color:#00ADA7;}
.umCrumb i{font-size:12px;color:#aaa;}
.umCrumb i:before{content: "\ec25";}
.umCrumb i.home:before{content: "\e6a9";}
.umCrumb.show{display:block}
/*热门专题*/
.tagTops{ width:100%;padding:15px 0;}
.tagTops ul{ display:block; font-size:0; margin:0 -10px;}
.tagTops li{ width:100%; padding:20px 10px 0px; display:inline-block; height: auto; line-height: normal}
.tagTops li a{ display:block; position:relative; text-align:center; color:#fff;}
.tagTops li a h4{padding:0px 10px; font-size:14px; text-shadow:0 5px 15px rgba(51,51,75,0.15)}
.tagTops li .tagImg{ height:80px; background-size:cover; background-position:center center; background-repeat:no-repeat;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; overflow: hidden;}
/*.tagTops li a:hover .tagImg{box-shadow: 0 -20px 0 -10px rgba(51,51,75,.05),0 -38px 0 -20px rgba(51,51,75,.02),0 20px 30px rgba(51,51,75,.2);}*/
.tagTops li .tagText{position:absolute; top:50%; left:20px; right: 20px; transform:translateY(-50%); z-index:2}
.tagTops li a .tagImg:after{content: '';width:100%;height:100%;position: absolute;left: 0px;top:0px;background:#000; opacity: .25;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; z-index:1;}
.tagTops li a:hover .tagImg:after{ opacity:0.6;}
.tagTops li a span{ font-size:12px; margin-top:10px; display:block; opacity:0.68}
/*列表子分类*/
.sortBox{padding:20px; min-height: auto;background:#fff;box-shadow: 0 2px 15px rgba(0,0,0,0.02); margin-bottom:20px;border-bottom: 1px solid #f3f4f9; margin-top:0px}
.sortBox ul{ font-size: 0;}
.sortBox li{ display: inline-block; font-size: 14px; position: relative;}
.sortBox li a,.listSort span,.listSort a{ line-height: 36px; padding:0 10px;}
.sortBox li span,.listSort span{background:#f3f4f9;padding: 4px 8px;border-radius: 3px;}
/*.sortBox li.filter:after{ display: none}
.sortBox li:after{content: '/'; position: absolute; top: 8px; right: 8px; color: #9ca0ad; opacity: .28}
.sortBox li:last-child{ padding: 0 0 0 16px;}*/
.sortBox li:last-child:after{ display: none}
.sortBox li.active a,.listSort a.active{color: #00ADA7}
.sortBox li.filter{padding:0 12px 0 0;}
.sortBox li a i,.listSort a i,.listSort span i,
.sortBox li.filter i{vertical-align: -1px;padding-left: 2px; display: inline-block;}
.sortBox li.filter i,.listSort span i{padding-right: 2px;padding-left: 0px;width: 16px;}
.sortBox li.filter i.iconfont:before{content: "\ebc6";}
.listSort.order span i.iconfont:before{content: "\e759";}
.listSort a i.iconfont:before{content: "\ec4a";}
.listSort a.active i.iconfont:before{content: "\ec48";}
.listSort{font-size:0px;}
.listSort a,.listSort span{font-size:14px;}
.listSort a{ padding:0 8px;}
.listSort span{margin-right: 14px;}
/*----------- 分页 -----------*/
.pageNav{text-align:center;padding:2rem 1rem;display:block; clear:both;}
.pageNav a,.pageNav span{text-align:center; min-width:3rem;height:3rem;line-height:3rem;margin: 0 0.3rem; display:inline-block;color:#50555a;background:#f1f2f7;border-radius:100rem;vertical-align:top;font-size: 1.4rem;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;}
.pageNav .v{line-height:3rem;}
.pageNav a:hover{background:#00ADA7;color:#fff;text-decoration:none;filter: alpha(opacity=68);opacity: 0.68;}
.pageNav span{color:#fff;background:#00ADA7;filter: alpha(opacity=60);opacity: 0.68;}
.pager{padding-left:0;margin:22px 0;text-align:center;list-style:none}
.pager li{display:inline}
.pager li>a,.pager li>span{display:inline-block;padding:5px 14px;background-color:transparent;border:1px solid #e4eaec;border-radius:3px}
.pager li>a:focus,.pager li>a:hover{text-decoration:none;background-color:#fff}
.pager .next>a,.pager .next>span{float:right}
.pager .previous>a,.pager .previous>span{float:left}
.pager .disabled>a,.pager .disabled>a:focus,.pager .disabled>a:hover,.pager .disabled>span{color:#ccd5db;cursor:not-allowed;background-color:transparent}
.pages{margin:30px 0;font-size:14px;height:50px;line-height:30px;text-align:center}
.pages a,.pages a:visited,.pages b,.pages span{border:1px solid #ddd;background:#fff;color:#808080;padding:8px 12px;margin:5px;border-radius:2px;transition:all 0.2s}
.pages a:hover{border:1px solid #38739F;background:#E5F3FF;color:#000;font-weight:400}
.pages b{border:1px solid #BDD7F2!important;background:#CBE0F7!important}
.pages span{font-weight:bold}
.commentpagebar .pageNav{ display: block!important}
.ias-trigger{ width: 100%;}
.ias-trigger a,.ias-noneleft{width: 120px; height:100%; line-height: 4rem; display: block;border-radius:3px; /*background: #f3f4f9; */color: #50555a; text-align: center; cursor: pointer; opacity: .8;margin: 20px auto 0 auto; padding-bottom:20px}
.ias-trigger a:hover{ opacity: 1;}
.ias-spinner{background-image:url(images/loading.gif); background-repeat: no-repeat; background-position:center;background-size:60px auto; width:100%;margin:20px auto 15px auto;height: 4rem;display: inline-block;}
.ias-trigger-next{cursor:pointer;float:inherit;text-align:center;}
.ias-noneleft{ cursor: default;color: #888; opacity: .5; width: 130px;}
.relevant .ias-noneleft, .sPage .ias-noneleft, .relevant .ias-trigger,.relevant .ias-spinner{ display: none!important}
/*sp*/
code,pre{tab-size:4}.mfp-preloader{font-size:13px}.get-code-window{position:relative;background:#FFF;padding:2em 3em;width:auto;margin:20px auto;max-width:600px}#magnific_popup_documentation{font-size:3em;margin-bottom:1em;font-weight:700;text-align:center}.grid-c{clear:both}.grid-c p{margin-bottom:.5em}.grid-c{overflow:hidden;margin:0 -1em}.gc3{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;width:50%;float:left;padding:1em;overflow:hidden}.grid-c .gc3:nth-of-type(2n+1){clear:left}#broken-glass{height:100%;-webkit-tap-highlight-color:transparent;position:absolute;left:0;top:0;width:100%}#header-links{font-size:16px}#markdown-toc{position:fixed;left:0;top:50px;padding:20px;background:rgba(255,255,255,.71);-webkit-backface-visibility:hidden;list-style:none}#markdown-toc a[href="#magnific-popup-docs"]{display:none}#markdown-toc:before{content:'Table of contents';font-weight:700;display:block;margin-bottom:10px}@media all and (max-width:75em){#markdown-toc{position:static;padding:0;background:0 0}}#markdown-toc code,pre{font-family:Consolas,"Liberation Mono",Courier,monospace}code{background:#F8F8F8;padding:.1em .4em;color:#c82829;font-size:13px}pre{background:0 0;line-height:18px;overflow:auto;padding:20px 25px;border-radius:2px}pre code{border:0;padding:0;background:0 0;color:#000;font-size:13px}.highlight{position:relative;margin-bottom:.5em;margin-left:-1.5em;width:100%;padding:0 1.5em;background-color:#F5FAFC}pre code:before{display:block;position:absolute;right:3px;top:6px;padding:3px 7px 0;color:#889499;font-size:12px;line-height:13px}code.html:before{content:'HTML'}code.javascript:before{content:'JS'}code.css:before{content:'CSS'}#mc_embed_signup{max-width:350px;padding:32px;background:#EEE}#mc_embed_signup input[type=email]{border:1px solid #CCC;border-top:1px solid #9ca0ad;padding:5px;font-size:18px;width:200px;margin-right:10px;height:25px;transition:all .3s ease;-moz-transition:all .3s ease;-webkit-transition:all .3s ease;border-radius:2px;-moz-border-radius:2px;-webkit-border-radius:2px}#mc_embed_signup input[type=email]:focus{background-color:#FFF;border:1px solid #3169B3;box-shadow:#3169B3 0 0 5px;-moz-box-shadow:#3169B3 0 0 5px;-webkit-box-shadow:#3169B3 0 0 5px;outline:0}#mc_embed_signup input[type=submit]{border:1px solid #3169B3;font-size:13px;font-weight:700;color:#FFF;height:auto;padding:8px 13px;cursor:pointer;background-color:#3169B3;display:inline-block;width:auto;-webkit-appearance:none;border-radius:2px;-moz-border-radius:2px;-webkit-border-radius:2px;vertical-align:top}.embed-form{position:relative}#mc_embed_signup #main-wrapper{background:0 0;max-width:800px;width:100%;margin:2em auto 4em;padding:0 3em 3em;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;position:relative}.white-popup-block{background:#FFF;padding:20px 30px;text-align:left;max-width:650px;margin:40px auto;position:relative}#examples:after{content:'to view source click on the title of example';opacity:.4;font-weight:400;font-size:14px;margin-top:13px;float:right}.example{margin-bottom:20px;position:relative}.square-tmb{margin:0 10px 0 0;cursor:pointer}.zoom-cursor{cursor:-webkit-zoom-in;cursor:-moz-zoom-in;cursor:zoom-in}.example a,a.popup-link{text-decoration:none;border-bottom:1px dotted}.example a:hover,a.popup-link:hover{text-decoration:none}#image-gallery a,#single-image{border-bottom:none}.not-ready-yet-notice{padding:20px;background:#EEE}#footer{border-top:1px solid #DDD;padding-top:3em;margin:5em 0 0;width:100%;text-align:center;opacity:.9}#conditional-lightbox-notice{display:none}#logo-status{opacity:0;-webkit-transition:opacity .5s;-moz-transition:opacity .5s;transition:opacity .5s;width:100%;text-align:center}#logo-status.down{opacity:1}code.def{padding:0;background:#FFF;border:0;display:block;margin-bottom:8px;margin-top:-10px;color:#A3A3A3}@media all and (max-width:50em){#logo}@media all and (max-width:30em){#examples:after{display:none}.gc3{width:100%}.grid-c .gc3:nth-of-type(2n+1){clear:none}#main-wrapper{padding:1em;margin-top:0}.highlight{padding:.2em 1em;margin:1em -1em}}@media all and (max-width:700px){.zoom-cursor{cursor:pointer}#conditional-lightbox-notice{display:block;padding:10px;background:#FFEAEA}}#logo-overlay{width:100%;height:75px;background:red;position:absolute;left:0;top:0;opacity:0}#mfp-build-tool{background:#FFF;padding:30px 40px 40px;max-width:500px;text-align:left;margin:10px auto;position:relative}#mfp-build-tool #mfp-build-form label{display:block;margin-bottom:5px;min-height:18px;padding-left:18px}#mfp-build-form input[type=checkbox]{margin:3px 5px 3px -18px;line-height:normal;cursor:pointer;width:auto;float:left}#mfp-build-status{min-height:40px}#mfp-build-status .error{color:#830C0C}#mfp-build-status .success{color:#014B04}#mfp-build-status .progress{color:#000}#smashing{text-align:center;font-weight:700}#smashing strong{color:#EF4A35}.smashing-link{margin-left:29px;position:relative}.smashing-link:before{content:'';display:inline-block;width:24px;height:24px;background:url(../../../../../dimsemenov.com/images/sm-logo-24x24.png);position:absolute;top:-4px;left:-28px}#hackernews{margin-left:24px}#hackernews:before{background:url(http://dimsemenov.com/images/hn-logo-18x18.gif);width:18px;height:18px;top:-1px;left:-22px}.share-buttons{text-align:center}.share-buttons .share-buttons{position:relative;margin:70px 0}#tweet{background:#0096c4}#like{background:#3b5998}#gplus{background:#d34836}#vkcom{background:#6e8fb1}pre .comment,pre .diff .header,pre .javadoc,pre .template_comment{color:#998;font-style:italic}pre .css .rule .keyword,pre .javascript .title,pre .keyword,pre .nginx .title,pre .request,pre .status,pre .subst,pre .winutils{color:#333;font-weight:700}pre .hexcolor,pre .number,pre .ruby .constant{color:#099}pre .phpdoc,pre .string,pre .tag .value,pre .tex .formula{color:#D01040}pre .id,pre .title{color:#900;font-weight:700}pre .clojure .title,pre .javascript .title,pre .lisp .title,pre .subst{font-weight:400}pre .class .title,pre .haskell .type,pre .tex .command,pre .vhdl .literal{color:#458;font-weight:700}pre .django .tag .keyword,pre .rules .property,pre .tag,pre .tag .title{color:navy;font-weight:400}pre .attribute,pre .lisp .body,pre .variable{color:teal}pre .regexp{color:#009926}pre .class{color:#458;font-weight:700}pre .built_in,pre .clojure .built_in,pre .lisp .title{color:#0086b3}pre .cdata,pre .doctype,pre .pi,pre .preprocessor,pre .shebang{color:#999;font-weight:700}pre .deletion{background:#fdd}pre .addition{background:#dfd}pre .diff .change{background:#0086b3}pre .chunk{color:#aaa}#documentation-intro{background:#2b2b2b;text-align:center;padding:3em;width:100%;margin-left:-3em;margin-bottom:3em}#documentation-intro #id1{display:none}.hll{background-color:#ffc}.c{color:#998;font-style:italic}.err{color:#a61717;background-color:#e3d2d2}.k,.o{color:#000;font-weight:700}.cm{color:#998;font-style:italic}.cp{color:#999;font-weight:700;font-style:italic}.c1{color:#998;font-style:italic}.cs{color:#999;font-weight:700;font-style:italic}.gd{color:#000;background-color:#fdd}.ge{color:#000;font-style:italic}.gr{color:#a00}.gh{color:#999}.gi{color:#000;background-color:#dfd}.go{color:#888}.gp{color:#555}.gs{font-weight:700}.gu{color:#aaa}.gt{color:#a00}.kc,.kd,.kn,.kp,.kr{color:#000;font-weight:700}.kt{color:#458;font-weight:700}.m{color:#099}.s{color:#d01040}.na{color:teal}.nb{color:#0086b3}.nc{color:#458;font-weight:700}.no{color:teal}.nd{color:#3c5d5d;font-weight:700}.ni{color:purple}.ne,.nf,.nl{color:#900;font-weight:700}.nn{color:#555}.nt{color:navy}.nv{color:teal}.ow{color:#000;font-weight:700}.w{color:#bbb}.mf,.mh,.mi,.mo{color:#099}.s2,.sb,.sc,.sd,.se,.sh,.si,.sx{color:#d01040}.sr{color:#009926}.s1{color:#d01040}.ss{color:#990073}.bp{color:#999}.vc,.vg,.vi{color:teal}.il{color:#099}.mfp-bg{top:0;left:0;width:100%;height:100%;z-index:1042;overflow:hidden;position:fixed;background:#0b0b0b;opacity:.8;filter:alpha(opacity=80)}.mfp-wrap{top:0;left:0;width:100%;height:100%;z-index:1043;position:fixed;outline:0!important;-webkit-backface-visibility:hidden}.mfp-container{text-align:center;position:absolute;width:100%;height:100%;left:0;top:0;padding:0 8px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.mfp-container:before{content:'';display:inline-block;height:100%;vertical-align:middle}.mfp-align-top .mfp-container:before{display:none}.mfp-content{position:relative;display:inline-block;vertical-align:middle;margin:0 auto;text-align:left;z-index:1045}.mfp-ajax-holder .mfp-content,.mfp-inline-holder .mfp-content{width:100%;cursor:auto}.mfp-ajax-cur{cursor:progress}.mfp-zoom-out-cur,.mfp-zoom-out-cur .mfp-image-holder .mfp-close{cursor:-moz-zoom-out;cursor:-webkit-zoom-out;cursor:zoom-out}.mfp-zoom{cursor:pointer;cursor:-webkit-zoom-in;cursor:-moz-zoom-in;cursor:zoom-in}.mfp-auto-cursor .mfp-content{cursor:auto}.mfp-arrow,.mfp-close,.mfp-counter,.mfp-preloader{-webkit-user-select:none;-moz-user-select:none;user-select:none}.mfp-loading.mfp-figure{display:none}.mfp-hide{display:none!important}.mfp-preloader{color:#CCC;position:absolute;top:50%;width:auto;text-align:center;margin-top:-.8em;left:8px;right:8px;z-index:1044}.mfp-s-error .mfp-content,.mfp-s-ready .mfp-preloader{display:none}button.mfp-arrow,button.mfp-close{overflow:visible;cursor:pointer;background:0 0;border:0;-webkit-appearance:none;display:block;outline:0;padding:0;z-index:1046;-webkit-box-shadow:none;box-shadow:none}button::-moz-focus-inner{padding:0;border:0}.mfp-close{width:44px;height:44px;line-height:44px;position:absolute;right:0;top:0;text-decoration:none;text-align:center;opacity:.65;filter:alpha(opacity=65);padding:0 0 18px 10px;color:#FFF;font-style:normal;font-size:28px;font-family:Arial,Baskerville,monospace}.mfp-close:focus,.mfp-close:hover{opacity:1;filter:alpha(opacity=100)}.mfp-close:active{top:1px}.mfp-close-btn-in .mfp-close{color:#333}.mfp-iframe-holder .mfp-close,.mfp-image-holder .mfp-close{color:#FFF;right:-6px;text-align:right;padding-right:6px;width:100%}.mfp-counter{position:absolute;top:0;right:0;color:#CCC;font-size:12px;line-height:18px;white-space:nowrap}.mfp-arrow{position:absolute;opacity:.65;filter:alpha(opacity=65);margin:-55px 0 0;top:50%;padding:0;width:90px;height:110px;-webkit-tap-highlight-color:transparent}.mfp-arrow:active{margin-top:-54px}.mfp-arrow:focus,.mfp-arrow:hover{opacity:1;filter:alpha(opacity=100)}.mfp-arrow .mfp-a,.mfp-arrow .mfp-b,.mfp-arrow:after,.mfp-arrow:before{content:'';display:block;width:0;height:0;position:absolute;left:0;top:0;margin-top:35px;margin-left:35px;border:inset transparent}.mfp-arrow .mfp-a,.mfp-arrow:after{border-top-width:13px;border-bottom-width:13px;top:8px}.mfp-arrow .mfp-b,.mfp-arrow:before{border-top-width:21px;border-bottom-width:21px;opacity:.7}.mfp-arrow-left{left:0}.mfp-arrow-left .mfp-a,.mfp-arrow-left:after{border-right:17px solid #FFF;margin-left:31px}.mfp-arrow-left .mfp-b,.mfp-arrow-left:before{margin-left:25px;border-right:27px solid #3F3F3F}.mfp-arrow-right{right:0}.mfp-arrow-right .mfp-a,.mfp-arrow-right:after{border-left:17px solid #FFF;margin-left:39px}.mfp-arrow-right .mfp-b,.mfp-arrow-right:before{border-left:27px solid #3F3F3F}.mfp-iframe-holder{padding-top:40px;padding-bottom:40px}.mfp-iframe-holder .mfp-content{line-height:0;width:100%;max-width:900px}.mfp-iframe-holder .mfp-close{top:-40px}.mfp-iframe-scaler{width:100%;height:0;overflow:hidden;padding-top:56.25%}.mfp-iframe-scaler iframe{position:absolute;display:block;top:0;left:0;width:100%;height:100%;box-shadow:0 0 8px rgba(0,0,0,.6);background:#000}img.mfp-img{width:auto;max-width:100%;height:auto;display:block;line-height:0;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:40px 0;margin:0 auto}.mfp-figure{line-height:0}.mfp-figure:after{content:'';position:absolute;left:0;top:40px;bottom:40px;display:block;right:0;width:auto;height:auto;z-index:-1;box-shadow:0 0 8px rgba(0,0,0,.6);background:#444}.mfp-figure small{color:#BDBDBD;display:block;font-size:12px;line-height:14px}.mfp-figure figure{margin:0}.mfp-bottom-bar{margin-top:-36px;position:absolute;top:100%;left:0;width:100%;cursor:auto}.mfp-title{text-align:left;line-height:18px;color:#F3F3F3;word-wrap:break-word;padding-right:36px}.mfp-image-holder .mfp-content{max-width:100%}.mfp-gallery .mfp-image-holder .mfp-figure{cursor:pointer}@media screen and (max-width:800px) and (orientation:landscape),screen and (max-height:300px){.mfp-img-mobile .mfp-image-holder{padding-left:0;padding-right:0}.mfp-img-mobile img.mfp-img{padding:0}.mfp-img-mobile .mfp-figure:after{top:0;bottom:0}.mfp-img-mobile .mfp-figure small{display:inline;margin-left:5px}.mfp-img-mobile .mfp-bottom-bar{background:rgba(0,0,0,.6);bottom:0;margin:0;top:auto;padding:3px 5px;position:fixed;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.mfp-img-mobile .mfp-bottom-bar:empty{padding:0}.mfp-img-mobile .mfp-counter{right:5px;top:3px}.mfp-img-mobile .mfp-close{top:0;right:0;width:35px;height:35px;line-height:35px;background:rgba(0,0,0,.6);position:fixed;text-align:center;padding:0}}@media all and (max-width:900px){.mfp-arrow{-webkit-transform:scale(.75);transform:scale(.75)}.mfp-arrow-left{-webkit-transform-origin:0;transform-origin:0}.mfp-arrow-right{-webkit-transform-origin:100%;transform-origin:100%}.mfp-container{padding-left:6px;padding-right:6px}}.mfp-ie7 .mfp-img{padding:0}.mfp-ie7 .mfp-bottom-bar{width:600px;left:50%;margin-left:-300px;margin-top:5px;padding-bottom:5px}.mfp-ie7 .mfp-container{padding:0}.mfp-ie7 .mfp-content{padding-top:44px}.mfp-ie7 .mfp-close{top:0;right:0;padding-top:0}
.umDiy video{width: 60%;}
.post .post-media .movie .icon{ width:48px; height: 48px; line-height: 48px; position: absolute; top: 50%; left: 50%; margin:-24px 0 0 -24px; background:rgba(0,0,0,.6);border-radius: 100px; text-align: center; box-shadow: 0 0 15px rgba(0,0,0,.2);-webkit-transition:all .3s;-moz-transition:all .3s ease-out;transition:all .3s ease-out;}
.post .post-media .movie .icon i{color: #fff; font-size: 30px;}
.post .post-media .movie .icon i:before{content: "\e62e";}
.post .post-media .movie a:hover .icon{background:#E94C3D;box-shadow: 0 10px 20px rgba(0,0,0,.3);}
.post .post-media .movie a:hover img{transform:translate(-50%,-50%) scale(1.1);}
.mfp-iframe-scaler iframe body{padding: 0; margin: 0; display: block; overflow: hidden;}
/*---------------------------文章内容------------------------*/
.single .post .umArtTit{position: relative;}
.single .artBox a.fullRead{ position:absolute; top:0px; right:0px; font-size:25px;-webkit-transition:all .3s;-moz-transition:all .3s ease-out;transition:all .3s ease-out;}
.single .artBox a.fullRead i{font-size:25px;}
.single .artBox a.fullRead{ color:#9ca0ad;opacity:0.6;}
.single .artBox a:hover .fullRead{ color:#00ADA7; opacity:1}
.single .post .post-title{margin:0;padding:0 35px .5rem 0;font-size:2.2rem;color:#333;width:100%;;white-space: normal; font-weight: 500;}
.single .post .post-date{height:1.4rem;line-height:1.4rem;margin:2.0rem 0 .5rem 0;font-size:1.4rem;color:#50555a; font-family: politicaregular;}
.single .post .post-date.date,.post .post-content p em.date{font-size:1.2rem; font-family:Verdana, Arial, Helvetica, sans-serif; letter-spacing:0;color:#9ca0ad;}
.single .post .post-footer{width:100%;margin:0;padding:5px 0 20px 0;text-align:left; display:block; font-size:0;}
.single .post .post-footer span{ margin-right:15px;font-size:12px;color:#9ca0ad;}
.single .post .post-footer span i{ margin-right:5px; vertical-align:-1px;}
.single .post .post-footer a{color:#9ca0ad;}
.single .post .post-footer a:hover{color:#00ADA7;}
.single .post .post-tags{width:100%;margin:0;padding:2rem 0 0 0;font-size:1.4rem;color:#50555a;text-align:left;text-indent:0}
.single .post .post-tags a{ margin-right:10px;display:inline-block;color:#666;font-size:12px;padding:4px 6px;border-radius:8px;margin-bottom:2px;border:1px solid #666;margin-top:16px;}
.single .post .post-tags a:hover {color:#fff;background-color:#00A4A0;border:1px solid #00a4a0;}
.single .post .post-tags i{ margin-right:.8rem; color: #50555a; vertical-align: -1px}
.single .post .post-body{width:100%;padding:0px 0 0 0;font-size:1.5rem;text-align:justify;clear:both;word-break:break-all;color: #50555a; line-height:1.8;overflow: hidden;position: relative;}
.single .post-body a{text-decoration:none;color: #00ADA7;}
.single .post-body a:hover{text-decoration:none;color:#00ADA7}
.single .post-body em{font-style: italic;}
.single .post-body p{margin:0 0 20px 0;padding:0;text-indent:0;border: none; color: #50555a;word-wrap:break-word;-webkit-hyphens:auto;-ms-hyphens:auto;hyphens:auto;text-align:justify;text-justify:distribute-all-lines;-webkit-text-align-last:justify;}
.single .post.single .post-title{ width:100%; text-align:left;}
.single .post .post-body.umHight{ padding-bottom: 60px;}
.single .post .post-body.umHight .readmore{ position: absolute; height: 150px; bottom: 0; right: 0; left: 0; text-align: center;background: linear-gradient(to bottom, rgba(255,255,255,0), #fff 50%); z-index:3}
.single .post .post-body.umHight .readmore a{ display: inline-block; background: rgba(255,255,255,0); margin-top: 100px; padding: 0 20px; height: 36px; line-height: 34px; border: 1px solid #00ADA7; border-radius:50px; color: #00ADA7;-webkit-transition:all .3s;-moz-transition:all .3s ease-out;transition:all .3s ease-out;}
.single .post .post-body.umHight .readmore a:hover{border: 1px solid #00ADA7;  background: #00ADA7; color: #fff;}
.iAuthor i.iconfont:before{content: "\eb07"}
.iCats i.iconfont:before{content: "\ebc6"}
.iDate i.iconfont:before{content:"\e62c"}
.iView i.iconfont:before{content:"\e65a"}
.iComs i.iconfont:before{content:"\e63b"}
.single .artBox a.fullRead.off i:before{content:"\e625"}
.single .artBox a.fullRead.no i:before{content:"\e622"}
div.post-body .umDes i.left:before{content:"\e6b7"}
div.post-body .umDes i.right:before{content:"\ec26"}
.post-like a i:before{content:"\e643"}
.post-like a.reward i:before{content:"\e641"}
.post-like a.comiis_poster_a i:before{content:"\e65b"}
.single .post .post-tags i:before{content:"\e610"}
.reading .orw{overflow: inherit;}
.reading:before {position: fixed;z-index: 9998; width: 100%;height: 100%;left: 0;top: 0;right: 0;bottom: 0; background:#f3f4f9;content: "";}
.reading .main .artBox{position:relative;z-index: 10000;max-width: 860px;top:-20px; left:50%;transform:translateX(-50%);transition: all .3s ease-out 0s;}
.reading .commBox,.reading .header,.reading .footer,.reading .sidebar,.reading .umCrumb,.reading .sidebar.phone{display:none}
body.reading{ padding-top:0;}
.reading .artBox .post{ padding: 30px; background: #fff;}
.reading .artBox{ margin-right:0; padding-right: 0;padding-left: 0;}
.reading .gotop{ z-index:10001}
.reading .warp{ margin-top:60px;}
.reading .relevant{ padding: 30px; border-radius: 5px;}
.reading.single .artBox .fullRead{ right: 0px; top: 0px;}
/*----------文章中引用code------*/
div.post-body div.syntaxhighlighter{margin:.5em 1em .5em 2em!important;width:auto!important}
div.post-body li p{overflow:visible;text-indent:0;margin:3px 0}
div.post-body ol,div.post-body ul{margin-left:3rem;margin-bottom: 2rem;}
div.post-body li{list-style: inherit;}
div.post-body code{width:93%;padding:5px;background:#f9fafc;}
div.post-body blockquote{width: 100%;background:#f9fafc;border-left:0.3rem solid #ecedf3;margin: 2rem 0px 2.5rem;padding:.5rem 2rem; border-radius: 5px;}
div.post-body img{max-width:100%;width:auto;height:auto; vertical-align: -.6rem}
div.post-body table{border-collapse:collapse;background:#fff;line-height:140%;width: 100%;}
div.post-body td,th{margin:0;padding:10px 10px;text-indent:0em;border: 1px solid #ecedf3;background:#fff}
div.post-body h1,div.post-body h2,div.post-body h3,div.post-body h4,div.post-body h5,div.post-body h6{color:#333;margin:10px 0 5px 0;font-weight: 500;}
div.post-body h1{padding:0;font-size:22px}
div.post-body h2{padding:0;font-size:20px}
div.post-body h3{padding:0;font-size:18px}
div.post-body h4{padding:0;font-size:16px}
div.post-body h5{padding:0;font-size:15px}
div.post-body h6{padding:0;font-size:14px}
div.post-body .firstRow td,div.post-body .firstRow th{background: #f9fafc;}
div.post-body .umDes{ padding:15px 50px; background:#f9fafc; border-left:3px solid #ecedf3; color:#50555a; margin:10px 0 25px 0; font-size:14px; position:relative;word-wrap:break-word;-webkit-hyphens:auto;-ms-hyphens:auto;hyphens:auto;text-align:justify;text-justify:distribute-all-lines;-webkit-text-align-last:justify;}
div.post-body .umDes i{ position:absolute; font-size:24px; color:#ecedf3;}
div.post-body .umDes i.left{ top:8px; left:15px;}
div.post-body .umDes i.right{ bottom:4px; right:15px;}
.single div.post-body blockquote p{ margin:10px 0;}
div.post-body .rslist{ margin:0 -8px; display:block; font-size:0;}
div.post-body .rslist li{list-style: none; display:inline-block; width:25%; padding:8px;}
div.post-body .rslist li a {border:1px solid rgba(227,229,236,0);border-radius:0px;display:block;padding:10px;position:relative;overflow:hidden;line-height:20px;-moz-transition:ease-in-out 0.3s;-webkit-transition:ease-in-out 0.3s;-o-transition:ease-in-out 0.3s;-ms-transition:ease-in-out 0.3s;transition:ease-in-out 0.3s;box-shadow:0 5px 15px 0 rgba(0,0,0,.00); background:#f5f5f9}
div.post-body .rslist li a:hover {border:1px solid rgba(227,229,236,.5);box-shadow: 0px 10px 20px rgba(227,229,236,0.2);-webkit-transform: translate(0, -6px);-ms-transform: translate(0, -6px);-o-transform: translate(0, -6px);transform: translate(0, -6px);}
div.post-body .rslist li img{width:48px; height:48px; border: 2px solid #fff;border-radius:50px;box-shadow: 0 3px 15px rgba(0,0,0,0.08);float: left;margin-right: 12px; background:#fafaf8}
div.post-body .rslist li p{ margin:0; font-size:12px; color:#9ca0ad}
div.post-body .rslist li h6 {font-size:14px;margin:3px 0 0 0;height:22px;line-height:22px;overflow:hidden;color:#333;border-left:0;padding-left:0px;white-space: nowrap;word-wrap: normal;text-overflow: ellipsis;overflow: hidden;}
/*视频弹窗*/
.mejs__offscreen{border:0;clip:rect(1px,1px,1px,1px);-webkit-clip-path:inset(50%);clip-path:inset(50%);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;word-wrap:normal}.mejs__container{background:#000;box-sizing:border-box;font-family:Helvetica,Arial,serif;position:relative;text-align:left;text-indent:0;vertical-align:top}.mejs__container *{box-sizing:border-box}.mejs__container video::-webkit-media-controls,.mejs__container video::-webkit-media-controls-panel,.mejs__container video::-webkit-media-controls-panel-container,.mejs__container video::-webkit-media-controls-start-playback-button{-webkit-appearance:none;display:none!important}.mejs__fill-container,.mejs__fill-container .mejs__container{height:100%;width:100%}.mejs__fill-container{background:transparent;margin:0 auto;overflow:hidden;position:relative}.mejs__container:focus{outline:none}.mejs__iframe-overlay{height:100%;position:absolute;width:100%}.mejs__embed,.mejs__embed body{background:#000;height:100%;margin:0;overflow:hidden;padding:0;width:100%}.mejs__fullscreen{overflow:hidden!important}.mejs__container-fullscreen{bottom:0;left:0;overflow:hidden;position:fixed;right:0;top:0;z-index:1000}.mejs__container-fullscreen .mejs__mediaelement,.mejs__container-fullscreen video{height:100%!important;width:100%!important}.mejs__background{left:0;position:absolute;top:0}.mejs__mediaelement{height:100%;left:0;position:absolute;top:0;width:100%;z-index:0}.mejs__poster{background-position:50% 50%;background-repeat:no-repeat;background-size:cover;left:0;position:absolute;top:0;z-index:1}:root .mejs__poster-img{display:none}.mejs__poster-img{border:0;padding:0}.mejs__overlay{-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;left:0;position:absolute;top:0}.mejs__layer{z-index:1}.mejs__overlay-play{cursor:pointer}.mejs__overlay-button{background:url(images/mejs-controls.svg) no-repeat;background-position:0 -39px;height:80px;width:80px}.mejs__overlay:hover>.mejs__overlay-button{background-position:-80px -39px}.mejs__overlay-loading{height:80px;width:80px}.mejs__overlay-loading-bg-img{-webkit-animation:mejs__loading-spinner 1s linear infinite;animation:mejs__loading-spinner 1s linear infinite;background:transparent url(images/mejs-controls.svg) -160px -40px no-repeat;display:block;height:80px;width:80px;z-index:1}@-webkit-keyframes mejs__loading-spinner{to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes mejs__loading-spinner{to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.mejs__controls{bottom:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:40px;left:0;list-style-type:none;margin:0;padding:0 10px;position:absolute;width:100%;z-index:3}.mejs__controls:not([style*="display: none"]){background:rgba(255,0,0,.7);background:-webkit-linear-gradient(transparent,rgba(0,0,0,.35));background:linear-gradient(transparent,rgba(0,0,0,.35))}.mejs__button,.mejs__time,.mejs__time-rail{font-size:10px;height:40px;line-height:10px;margin:0;width:32px}.mejs__button>button{background:transparent url(images/mejs-controls.svg);border:0;cursor:pointer;display:block;font-size:0;height:20px;line-height:0;margin:10px 6px;overflow:hidden;padding:0;position:absolute;text-decoration:none;width:20px}.mejs__button>button:focus{outline:1px dotted #999}.mejs__container-keyboard-inactive [role=slider],.mejs__container-keyboard-inactive [role=slider]:focus,.mejs__container-keyboard-inactive a,.mejs__container-keyboard-inactive a:focus,.mejs__container-keyboard-inactive button,.mejs__container-keyboard-inactive button:focus{outline:0}.mejs__time{box-sizing:content-box;color:#fff;font-size:11px;font-weight:700;height:24px;overflow:hidden;padding:16px 6px 0;text-align:center;width:auto}.mejs__play>button{background-position:0 0}.mejs__pause>button{background-position:-20px 0}.mejs__replay>button{background-position:-160px 0}.mejs__time-rail{direction:ltr;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;height:40px;margin:0 10px;padding-top:10px;position:relative}.mejs__time-buffering,.mejs__time-current,.mejs__time-float,.mejs__time-float-corner,.mejs__time-float-current,.mejs__time-hovered,.mejs__time-loaded,.mejs__time-marker,.mejs__time-total{border-radius:2px;cursor:pointer;display:block;height:10px;position:absolute}.mejs__time-total{background:hsla(0,0%,100%,.3);margin:5px 0 0;width:100%}.mejs__time-buffering{-webkit-animation:buffering-stripes 2s linear infinite;animation:buffering-stripes 2s linear infinite;background:-webkit-linear-gradient(135deg,hsla(0,0%,100%,.4) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.4) 0,hsla(0,0%,100%,.4) 75%,transparent 0,transparent);background:linear-gradient(-45deg,hsla(0,0%,100%,.4) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.4) 0,hsla(0,0%,100%,.4) 75%,transparent 0,transparent);background-size:15px 15px;width:100%}@-webkit-keyframes buffering-stripes{0%{background-position:0 0}to{background-position:30px 0}}@keyframes buffering-stripes{0%{background-position:0 0}to{background-position:30px 0}}.mejs__time-loaded{background:hsla(0,0%,100%,.3)}.mejs__time-current,.mejs__time-handle-content{background:hsla(0,0%,100%,.9)}.mejs__time-hovered{background:hsla(0,0%,100%,.5);z-index:10}.mejs__time-hovered.negative{background:rgba(0,0,0,.2)}.mejs__time-buffering,.mejs__time-current,.mejs__time-hovered,.mejs__time-loaded{left:0;-webkit-transform:scaleX(0);-ms-transform:scaleX(0);transform:scaleX(0);-webkit-transform-origin:0 0;-ms-transform-origin:0 0;transform-origin:0 0;-webkit-transition:all .15s ease-in;transition:all .15s ease-in;width:100%}.mejs__time-buffering{-webkit-transform:scaleX(1);-ms-transform:scaleX(1);transform:scaleX(1)}.mejs__time-hovered{-webkit-transition:height .1s cubic-bezier(.44,0,1,1);transition:height .1s cubic-bezier(.44,0,1,1)}.mejs__time-hovered.no-hover{-webkit-transform:scaleX(0)!important;-ms-transform:scaleX(0)!important;transform:scaleX(0)!important}.mejs__time-handle,.mejs__time-handle-content{border:4px solid transparent;cursor:pointer;left:0;position:absolute;-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0);z-index:11}.mejs__time-handle-content{border:4px solid hsla(0,0%,100%,.9);border-radius:50%;height:10px;left:-7px;top:-4px;-webkit-transform:scale(0);-ms-transform:scale(0);transform:scale(0);width:10px}.mejs__time-rail .mejs__time-handle-content:active,.mejs__time-rail .mejs__time-handle-content:focus,.mejs__time-rail:hover .mejs__time-handle-content{-webkit-transform:scale(1);-ms-transform:scale(1);transform:scale(1)}.mejs__time-float{background:#eee;border:1px solid #333;bottom:100%;color:#111;display:none;height:17px;margin-bottom:9px;position:absolute;text-align:center;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);width:36px}.mejs__time-float-current{display:block;left:0;margin:2px;text-align:center;width:30px}.mejs__time-float-corner{border:5px solid transparent;border-top-color:#eee;border-radius:0;display:block;height:0;left:50%;line-height:0;position:absolute;top:100%;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);width:0}.mejs__long-video .mejs__time-float{margin-left:-23px;width:64px}.mejs__long-video .mejs__time-float-current{width:60px}.mejs__broadcast{color:#fff;height:10px;position:absolute;top:15px;width:100%}.mejs__fullscreen-button>button{background-position:-80px 0}.mejs__unfullscreen>button{background-position:-100px 0}.mejs__mute>button{background-position:-60px 0}.mejs__unmute>button{background-position:-40px 0}.mejs__volume-button{position:relative}.mejs__volume-button>.mejs__volume-slider{-webkit-backface-visibility:hidden;background:rgba(50,50,50,.7);border-radius:0;bottom:100%;display:none;height:115px;left:50%;margin:0;position:absolute;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);width:25px;z-index:1}.mejs__volume-button:hover{border-radius:0 0 4px 4px}.mejs__volume-total{background:hsla(0,0%,100%,.5);height:100px;left:50%;margin:0;position:absolute;top:8px;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);width:2px}.mejs__volume-current{background:hsla(0,0%,100%,.9);left:0;margin:0;position:absolute;width:100%}.mejs__volume-handle{background:hsla(0,0%,100%,.9);border-radius:1px;cursor:ns-resize;height:6px;left:50%;position:absolute;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);width:16px}.mejs__horizontal-volume-slider{display:block;height:36px;position:relative;vertical-align:middle;width:56px}.mejs__horizontal-volume-total{background:rgba(50,50,50,.8);border-radius:2px;font-size:1px;height:8px;left:0;margin:0;padding:0;position:absolute;top:16px;width:50px}.mejs__horizontal-volume-current{background:hsla(0,0%,100%,.8);border-radius:2px;font-size:1px;height:100%;left:0;margin:0;padding:0;position:absolute;top:0;width:100%}.mejs__horizontal-volume-handle{display:none}.mejs__captions-button,.mejs__chapters-button{position:relative}.mejs__captions-button>button{background-position:-140px 0}.mejs__chapters-button>button{background-position:-180px 0}.mejs__captions-button>.mejs__captions-selector,.mejs__chapters-button>.mejs__chapters-selector{background:rgba(50,50,50,.7);border:1px solid transparent;border-radius:0;bottom:100%;margin-right:-43px;overflow:hidden;padding:0;position:absolute;right:50%;visibility:visible;width:86px}.mejs__chapters-button>.mejs__chapters-selector{margin-right:-55px;width:110px}.mejs__captions-selector-list,.mejs__chapters-selector-list{list-style-type:none!important;margin:0;overflow:hidden;padding:0}.mejs__captions-selector-list-item,.mejs__chapters-selector-list-item{color:#fff;cursor:pointer;display:block;list-style-type:none!important;margin:0 0 6px;overflow:hidden;padding:0}.mejs__captions-selector-list-item:hover,.mejs__chapters-selector-list-item:hover{background-color:#c8c8c8!important;background-color:hsla(0,0%,100%,.4)!important}.mejs__captions-selector-input,.mejs__chapters-selector-input{clear:both;float:left;left:-1000px;margin:3px 3px 0 5px;position:absolute}.mejs__captions-selector-label,.mejs__chapters-selector-label{cursor:pointer;float:left;font-size:10px;line-height:15px;padding:4px 10px 0;width:100%}.mejs__captions-selected,.mejs__chapters-selected{color:#21f8f8}.mejs__captions-translations{font-size:10px;margin:0 0 5px}.mejs__captions-layer{bottom:0;color:#fff;font-size:16px;left:0;line-height:20px;position:absolute;text-align:center}.mejs__captions-layer a{color:#fff;text-decoration:underline}.mejs__captions-layer[lang=ar]{font-size:20px;font-weight:400}.mejs__captions-position{bottom:15px;left:0;position:absolute;width:100%}.mejs__captions-position-hover{bottom:35px}.mejs__captions-text,.mejs__captions-text *{background:rgba(20,20,20,.5);box-shadow:5px 0 0 rgba(20,20,20,.5),-5px 0 0 rgba(20,20,20,.5);padding:0;white-space:pre-wrap}.mejs__container.mejs__hide-cues video::-webkit-media-text-track-container{display:none}.mejs__overlay-error{position:relative}.mejs__overlay-error>img{left:0;max-width:100%;position:absolute;top:0;z-index:-1}.mejs__cannotplay,.mejs__cannotplay a{color:#fff;font-size:.8em}.mejs__cannotplay{position:relative}.mejs__cannotplay a,.mejs__cannotplay p{display:inline-block;padding:0 15px;width:100%}
.umPlay{ background:#12121D; padding:116px 0 50px; min-height:200px; margin-bottom:25px; position:relative; z-index:1;-webkit-transition:padding .3s;-moz-transition:padding .3s ease-out;transition:padding .3s ease-out;}
.umPlay .umPlayBox{ width:100%;max-width: 100%; height:auto;}
.umPlay .iframePlay{position: relative;padding-bottom: 56.25%;height: 0;overflow: hidden;}
.umPlay .umPlayBox iframe{position: absolute;top: 0;left: 0;width: 100%;height: 100%;}
.warp.play{ margin-top:0}
.post-body .mejs__container{ width:100% !important;max-width: 100%;min-width: 100%;}
.reading .umPlay{z-index:10001;padding:50px 0 50px; margin-bottom:25px;}
.reading .mfp-bg{z-index:10001;}
.reading .mfp-wrap{z-index:10002;}
/*----------- 底部 -----------*/
/*footer*/
.footer {background-color:#1b1b1b;margin-top:40px;}
.footer .footer_wrap {padding:30px!important;}
.footer_contact {width:33.3%;}
.follow_us {width:25%;}
.footer_about {width:41%;}
.footer_title {font-size:18px;color:#888;padding-bottom:16px;}
.follow_us img {width:100px;height:100px;}
.footer_contact {font-size:15px;color:#888;}
.footer_about p {font-size:15px;color:#888;}
.footer_about .copyright {color:#888;font-size:13px;padding-top:8px;}
.footer_about .copyright a {color:#999;}
/*底部链接*/
.link{padding:0 0 2rem; overflow: hidden; display:inline-block; vertical-align:top;}
.link ul{ padding-right:15px; display:inline-block;}
.link ul li{ display: inline-block;}
.link ul li.h{ display:block; font-size:16px; margin-bottom:10px;}
.link ul li a{ margin:.5rem 1rem .5rem 0; color: #82819C;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;}
.link ul li a:hover{color: #fff}
.umPostBox .umTextarea .umItemIn .hide{display:block;visibility: visible;}
/**
 * Owl Carousel v2.3.4
 * Copyright 2013-2018 David Deutsch
 * Licensed under: SEE LICENSE IN https://github.com/OwlCarousel2/OwlCarousel2/blob/master/LICENSE
 */
.owl-carousel,.owl-carousel .owl-item{-webkit-tap-highlight-color:transparent;position:relative}.owl-carousel{display:none;width:100%;z-index:1}.owl-carousel .owl-stage{position:relative;-ms-touch-action:pan-Y;touch-action:manipulation;-moz-backface-visibility:hidden}.owl-carousel .owl-stage:after{content:".";display:block;clear:both;visibility:hidden;line-height:0;height:0}.owl-carousel .owl-stage-outer{position:relative;overflow:hidden;-webkit-transform:translate3d(0,0,0)}.owl-carousel .owl-item,.owl-carousel .owl-wrapper{-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;-ms-backface-visibility:hidden;-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0)}.owl-carousel .owl-item{min-height:1px;float:left;-webkit-backface-visibility:hidden;-webkit-touch-callout:none}.owl-carousel .owl-item img{display:block;width:100%}.owl-carousel .owl-dots.disabled,.owl-carousel .owl-nav.disabled{display:none}.no-js .owl-carousel,.owl-carousel.owl-loaded{display:block}.owl-carousel .owl-dot,.owl-carousel .owl-nav .owl-next,.owl-carousel .owl-nav .owl-prev{cursor:pointer;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.owl-carousel .owl-nav button.owl-next,.owl-carousel .owl-nav button.owl-prev,.owl-carousel button.owl-dot{background:0 0;color:inherit;border:none;padding:0!important;font:inherit}.owl-carousel.owl-loading{opacity:0;display:block}.owl-carousel.owl-hidden{opacity:0}.owl-carousel.owl-refresh .owl-item{visibility:hidden}.owl-carousel.owl-drag .owl-item{-ms-touch-action:pan-y;touch-action:pan-y;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.owl-carousel.owl-grab{cursor:move;cursor:grab}.owl-carousel.owl-rtl{direction:rtl}.owl-carousel.owl-rtl .owl-item{float:right}.owl-carousel .animated{animation-duration:1s;animation-fill-mode:both}.owl-carousel .owl-animated-in{z-index:0}.owl-carousel .owl-animated-out{z-index:1}.owl-carousel .fadeOut{animation-name:fadeOut}@keyframes fadeOut{0%{opacity:1}100%{opacity:0}}.owl-height{transition:height .5s ease-in-out}.owl-carousel .owl-item .owl-lazy{opacity:0;transition:opacity .4s ease}.owl-carousel .owl-item .owl-lazy:not([src]),.owl-carousel .owl-item .owl-lazy[src^=""]{max-height:0}.owl-carousel .owl-item img.owl-lazy{transform-style:preserve-3d}.owl-carousel .owl-video-wrapper{position:relative;height:100%;background:#000}.owl-carousel .owl-video-play-icon{position:absolute;height:80px;width:80px;left:50%;top:50%;margin-left:-40px;margin-top:-40px;background:url(owl.video.play.html) no-repeat;cursor:pointer;z-index:1;-webkit-backface-visibility:hidden;transition:transform .1s ease}.owl-carousel .owl-video-play-icon:hover{-ms-transform:scale(1.3,1.3);transform:scale(1.3,1.3)}.owl-carousel .owl-video-playing .owl-video-play-icon,.owl-carousel .owl-video-playing .owl-video-tn{display:none}.owl-carousel .owl-video-tn{opacity:0;height:100%;background-position:center center;background-repeat:no-repeat;background-size:contain;transition:opacity .4s ease}.owl-carousel .owl-video-frame{position:relative;z-index:1;height:100%;width:100%}
.owl-theme .owl-dots,.owl-theme .owl-nav{text-align:center;-webkit-tap-highlight-color:transparent}.owl-theme .owl-nav{margin-top:0px}.owl-theme .owl-nav [class*=owl-]{color:#FFF;font-size:14px;margin:5px;padding:4px 7px;background:#D6D6D6;display:inline-block;cursor:pointer;border-radius:3px}.owl-theme .owl-nav [class*=owl-]:hover{background:#869791;color:#FFF;text-decoration:none}.owl-theme .owl-nav .disabled{opacity:.5;cursor:default}.owl-theme .owl-nav.disabled+.owl-dots{margin-top:10px}.owl-theme .owl-dots .owl-dot{display:inline-block;zoom:1}.owl-theme .owl-dots .owl-dot span{width:10px;height:10px;margin:5px 7px;background:#D6D6D6;display:block;-webkit-backface-visibility:visible;transition:opacity .2s ease;border-radius:30px}.owl-theme .owl-dots .owl-dot.active span,.owl-theme .owl-dots .owl-dot:hover span{background:#869791}
.owl-theme .owl-nav.disabled + .owl-dots{margin:10px 0 5px 0}
.banner .owl-theme .owl-nav.disabled + .owl-dots{margin:10px 0 5px 0;display: inline-block;position: absolute;top: 0;left: 10px;}
/*自定义*/
.owl-theme{ cursor: grab;margin: 0;}
.owl-theme .owl-nav [class*="owl-"]{ margin: 0; border-radius: 0;-moz-transition: ease-in-out 0.3s;
-webkit-transition: ease-in-out 0.3s;
-o-transition: ease-in-out 0.3s;
-ms-transition: ease-in-out 0.3s;
transition: ease-in-out 0.3s;}
.owl-theme .owl-nav [class*="owl-"]:hover{ background: rgba(255,255,255,.6); cursor: pointer;}
.owl-theme .owl-nav{ display: block; position: absolute; bottom: 10px; left: 50%; transform: translateX(-50%)}
.owl-theme .owl-nav{transform: none; height: 0; position: static;}
.owl-theme .owl-dots .owl-dot span{background: rgba(255,255,255,.6); margin: .5rem .5rem}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span{background: rgba(255,255,255,1)}
.owl-theme .owl-dots .owl-dot span{ width: .6rem; height: .6rem}
.owl-theme .owl-dots{ margin-top: 2rem;}
.owl-theme .owl-nav button{ position: absolute; top: 50%; transform: translateY(-50%);opacity: 0;}
.owl-theme .owl-nav button.owl-prev,.owl-theme .owl-nav button.owl-next{ background: rgba(0,0,0,0.35); opacity:0;}
.owl-theme .owl-nav button.owl-prev{ left: 10px;}
.owl-theme .owl-nav button.owl-next{ right: 10px;}
.owl-theme:hover .owl-nav button{ opacity: 1;}
.owl-theme:hover .owl-nav button.owl-prev{left: 0px;}
.owl-theme:hover .owl-nav button.owl-next{right: 0px;}
.owl-carousel .owl-stage-outer{border-radius:3px;box-shadow:0 15px 25px rgba(0,0,0,.0);}
.owl-carousel.owl-drag .owl-item,.banner .bannerLeft{background: #000; border-radius: 4px;overflow: hidden;}
.owl-carousel .owl-item img{  height: 360px; object-fit: cover;object-position: center;}
.bx-prev,.bx-next { line-height: 6rem; text-align: center;cursor: pointer;color: #fff;font-size:2.4rem; margin: 0; padding:0px 1rem}
.owl-theme .title{ width: 100%; height: 10rem; line-height: 4.6rem; position: absolute; bottom: 0; left: 0; right: 0; background-image: linear-gradient(-180deg,transparent,rgba(0,0,0,.6) 100%); margin-bottom: 0; color: #fff; font-size: 1.6rem;}
.owl-theme .title h4{ padding: 5rem 1.8rem 0; max-width: 80%; display: block;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;}
/*----------- comm -----------*/	
.commBox{padding:1.5rem 0 2rem; background:rgba(255,255,255,.85); padding:20px 0;}
.commBox .postText{ padding:5px 0 0}
.cmBox{ margin-bottom:10px;}
.cmBox.tit{padding:1rem 0; font-size:1.8rem; margin-bottom:2rem; position:relative;}
.cmBox.tit:before{position:absolute;width:3rem;height:2px;display:block;content:" ";border-radius:5rem; background:#00ADA7; left:0; bottom:0rem; opacity:.8;}
.msgArticle{padding:1rem .2rem}
.avatar{float:left;position:relative;-webkit-border-radius:50px;border-radius:50px;background-color:#fff;margin-right:1rem}
.avatar img{width:4rem;height:4rem;-webkit-transition:.4s;-webkit-transition:-webkit-transform .4s ease-out;transition:transform .4s ease-out;-moz-transition:-moz-transform .4s ease-out;border-radius:30px;-webkit-border-radius:30px;-moz-border-radius:30px;}
.avatar img:hover{transform:rotateZ(360deg);-webkit-transform:rotateZ(360deg);-moz-transform:rotateZ(360deg);}
.commBody{position:relative;margin-left:6.2rem;border:1px solid rgba(227,229,236,.5);padding:0 1rem 1.5rem!important;border-radius:.3rem; background:rgba(255,255,255,.5);}
.commBody:before{border-right-color:#eee!important;z-index:1}
.commBody:after{border-right-color:#fafafa!important;margin-left:1px;z-index:2}
.commBody:after,.commBody:before{position:absolute;top:.8rem;left:-.8rem;right:100%;width:0;height:0;display:block;content:" ";border-color:transparent;border-style:solid solid solid;border-width:.8rem .8rem .8rem 0;pointer-events:none}
.commBody .commInfo{font-size:1.4rem;color:#50555a;line-height:2rem;margin:.5rem 0;word-wrap:break-word}
.commBody .commInfo a{ margin-right: 4px; color: #50555a}
.commBody .commInfo a:hover{ color: #00ADA7}
.commTop{padding:1rem;margin-left:-1rem;margin-right:-1rem;margin-bottom:1.5rem;border-bottom:1px solid rgba(227,229,236,.4);font-weight:500;font-size:1.4rem;background:#f9fafc;}
.commTop a{color:#9ca0ad;font-size:12px;margin-right:.8rem}
.commFooter{font-size:12px;line-height:16px;margin-top:.5rem;}
.commTime{font-size:12px;margin-right:.8rem;color:#9ca0ad}
.commReply{font-size:12px;color:#9ca0ad}
.msgname .msgname,div.children{margin-left:2.5rem}
div.children > div.children{margin-left:3rem}
.msgname .msgname .avatar,div.children .avatar,div.children .avatar img,.msgname .msgname .avatar img{width:3rem;height:3rem;margin-left: 0px;}
#divCommentPost p.postTop{width:100%;line-height:3rem;font-size:12px;clear:both;position:relative;z-index:5;overflow:hidden}
#divCommentPost p a{margin-right:.6rem;cursor:pointer;text-decoration:none;color:#50555a;float:right;}
#divCommentPost p.postTop small{width:6.5rem;position:relative;font-size:12px;cursor:pointer;text-align:right;float:right;}
.pinglun{font-size:12px;z-index:2;position:relative;clear:both;padding:0;margin:0;vertical-align:baseline;font:inherit;line-height:inherit;background:0 0;width:auto;float:none;transition:none;overflow: hidden;}
#txaArticle{position:relative;z-index:1;padding:5px;height:14rem;min-height:14rem;margin:0;resize:none;outline:0;width:100%;min-width:100%;border:1px solid rgba(227,229,236,.4);background:#f9fafc;border-radius:0.4rem; font-size:14px; line-height:1.6}
.post-toolbar{margin-top:1rem;overflow: hidden;float: right;}
input.button{float:right;height:3.6rem;width:10rem;border-radius:0.4rem;text-align:center;font-size: 14px;font-weight:700;border:none;color:#FFF;background-color:#00ADA7;box-shadow:none;cursor:pointer;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;}
input.button:hover{ opacity:.8}
.postText{padding:2rem 0 0;color:#50555a}
.postText ul{ display:block; font-size:0; margin:0 -5px;}
.postText ul li{margin-bottom:10px;text-align:left;display: inline-block; width:33.333%; padding:0 5px;}
.postText.isVerify ul li{ width:50%;}
.postText ul li.verify{ position:relative}
.postText ul li.verify img{ position:absolute; top:.3rem; right:.8rem}
input.text{margin-right:1rem;position:relative;border:1px solid rgba(227,229,236,.4);border-radius:0rem;height:3.6rem;padding:.3rem .5rem;outline:0;width:100%;box-shadow:none; background:#f9fafc}
.postText ul li label{color:#bbb}
.children .commBody{margin-left:5.2rem;}
/*相关推荐*/
.relevant{width: 100%;background:rgba(255,255,255,0.85);margin:0px;padding:20px 0;}
.relevant .title h4{padding:0 0 1rem 0;font-size: 1.8rem;margin-bottom: 0rem;position: relative; }
.relevant .title h4:before{position:absolute;width:3rem;height:2px;display:block;content:" ";border-radius:5rem; background:#00ADA7; left:0; bottom:0rem; opacity:.8;}
.relevant .noList{ padding:30px 0; text-align:center; color:#9ca0ad;}
.relevant .noList i{ font-size:72px;}
.artBox .relevant .post{ padding:30px 0; box-shadow:none; background:none;}
.single .relevant .post .post-title{margin: 0px 0 10px;font-size: 2rem;font-weight: 400;white-space: nowrap;}
.single .relevant .post .post-media{width: 26%;}
.relevant .ias-noneleft,.sPage .ias-noneleft{ display:none !important;}
.single.page{ background: #f3f4f9;}
.single.page .umpage{display: inline-block; width:100%;}
.single.page .umpage .sidebar{ z-index: 2}
.umpage .artBox{ background: #fff;padding-left: 250px;}
/*点赞，打赏*/
.post-like{text-align: center; margin:30px -10px 10px;font-size:0px;}
.post-like a{ min-width:80px; height:38px; line-height:38px; margin:8px; padding: 0 18px; background: #79A3F1; display: inline-block; font-size:14px; color: #fff; cursor:pointer; opacity: 1;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; box-shadow:0 20px 25px rgba(0,0,0,0.03)}
.post-like a i{ font-size:22px; vertical-align:-3px;}
.post-like a span{ margin-left: .5rem}
.post-like a.reward{ background:#F27272}
.post-like a.reward i{ margin-right: 3px;}
.post-like a.comiis_poster_a{ background:#20b767}
.post-like a:hover{ opacity: .8;}
#reward{ width: 300px; min-height: 280px; padding: 20px; background: #fff; border-radius:.5rem; position: fixed; top: 50%; left: 50%; transform: translate(-50%,-50%); z-index: 1001; display: none; text-align: left;}
#reward ul {margin: 0 0 15px 0px;font-size: 0; display: block;}
#reward .list { text-align: center;}
#reward .list li{ display: inline-block; font-size: 14px; margin: 5px 15px 0 5px; position: relative; padding-left: 20px; cursor: pointer; color: #9ca0ad}
#reward .list li.cur{ color: #333;}
#reward .list li i{ position: absolute; display: block; top: -4px; left: -4px; font-size: 25px; color: #AAA8A8;}
#reward .list li.cur i{ color: #22ab38}
#reward #listCon{position: relative;}
#reward #listCon div{opacity: 1;display: none;}
#reward #listCon div.cur{ opacity: 1; display: block;}
#reward #listCon div img{border-radius:0px; overflow: hidden; display: block;margin: 0;width: 100%;}
#reward p{ text-align: center; padding-top: 15px;font-size: 14px;}
.reading #reward{z-index:10002}
.reading .post-like{text-align: center; margin:30px -20px 10px;}
.reading .post-like a{margin:8px 8px}
/*上下篇*/
.single .postNav{ float: right; margin-top: 2rem}
.single .postNav.fn{ float: none;}
.single .postNav div{ display: inline-block;}
.single .postNav div a{ height: 3.2rem;font-size:14px;line-height: 3.0rem; border: 1px solid rgba(0,0,0,.1); border-radius: 50rem; display: block; padding: 0 1.5rem;margin: .6rem 0rem .6rem 0.3rem; color: #9ca0ad;-webkit-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;}
.single .postNav div a:hover{border: 1px solid #00ADA7; background: #00ADA7; color: #fff;}
.single .postNav.fn div a{margin: .6rem 0.3rem .6rem 0rem; }
/*上下篇 new*/
.single .post-navigation {overflow:hidden;margin:10px 0 0;padding:20px 0;}
.single .post-navigation div {/*position:relative;*/display:block;width:38%;color:#999;font-size:14px;}
.single .post-navigation div span {display:block;color:#999;font-size:90%;padding-bottom:8px;}
.single .post-navigation div a {color:#333;font-size:16px;}
.single .post-previous a::after {left:0;content:'<<';font-family:"Microsoft Yahei";}
.single .post-next a::after {right:0;content:'>>';font-family:"Microsoft Yahei";}
.single .post-navigation div a::after {position:absolute;top:34%;margin-top:-11px;height:22px;color:#EDEDED;font-size:48px;line-height:22px;}
.single .post-previous {float:left;padding-left:40px;text-align:left;}
.single .post-next {float:right;padding-right:40px;text-align:right;}
/*版权*/
.umCopyright{line-height: 22px; display: inline-block; padding:15px 20px; font-size: 12px; color: #9ca0ad;background:#f9fafc; border-radius: 3px; width: 100%; margin: 25px 0 0;}
.umCopyright a{color: #F6491E;opacity:.45;transition:.3s;-ms-transition:.3s;-moz-transition:.3s;-webkit-transition:.3s;}
.umCopyright a:hover{opacity:1;}
/*其它*/
.mask{position: fixed;left: 0;top: 0;width: 100%;height: 100%;background: rgba(0,0,0,.7);display: none; z-index:998}
.umBnGg{background: rgba(255,255,255,0.85);box-shadow: 0 0 20px rgba(0,0,0,0.01);display: block; margin-top: 2rem; overflow: hidden; font-size:0; padding:0px;}
.umBnGg a *{ font-size:14px;}
.reading .mask{z-index:10001}
.gotop{ width: 36px; height: 36px; border-radius:0rem;text-align: center;line-height: 34px;cursor: pointer;-webkit-transition: all 0.3s ease;-o-transition: all 0.3s ease;transition: all 0.3s ease;color: #fff;background-color: #48484d; position: fixed; right:15px; bottom: 90px; display: none;border-radius: 3px;z-index: 2;}
.gotop:hover{ color: #fff; background-color:#00ADA7;opacity:1;}
.gotop i.backTop:before{content: "\e66d";}
.post-media video{min-width: 100%;height: auto;width:100%;}
.moviePlay{ width:64px; height:64px; line-height:64px;border: 2px rgba(204,172,130,.6) solid;background:rgba(0,0,0,0.65); border-radius:100%;position:absolute; top:50%; left:50%; transform:translate(-50%,-50%);text-align:center; cursor:pointer; z-index:4; display:none;} 
.phoneNav{position: fixed; bottom:0; left:0; height:0;width: 100%; height:52px;display: none;background:#fff;box-shadow: 0 -3px 10px rgba(0,0,0,.05);z-index:2;}
.phoneNav > ul{display: -webkit-box;
display: -webkit-flex;
-webkit-align-content: flex-start;
-webkit-align-items: stretch;
-webkit-box-align: stretch;
-webkit-box-direction: normal;
-webkit-box-lines: single;
-webkit-box-orient: horizontal;
-webkit-box-pack: justify;
-webkit-flex-direction: row;
-webkit-flex-wrap: nowrap;
-webkit-justify-content: space-between;font-size:0; padding:0px}
.phoneNav li{width: 100%;line-height: 52px;font-size:14px;display: inline-block;position:relative;padding:0 5px;text-align: center;}
.phoneNav li a{display:block;cursor: pointer;}
.phoneNav li:before{content: '';display: block;background-color: #f5f5f8;position: absolute;right: 0;top:50%; margin-top:-10px;width:1px;height: 20px;}
.phoneNav li:last-child:before{display: none;}
.phoneNav > ul > li > ul{position: absolute;min-width: max-content;bottom:52px;left: 50%;transform: translateX(-50%) scale(0);background:#fff;box-shadow: 0 0px 5px rgba(0,0,0,.15);opacity: 0;visibility: hidden;-webkit-transition: all 0.2s ease;-o-transition: all 0.2s ease;transition: all 0.2s ease;transform-origin:bottom center 0;}
.phoneNav > ul > li > ul li{line-height: 48px;display: block; padding:0 10px;min-width: 100px;border-bottom: 1px solid #f5f5f8;}
.phoneNav > ul > li > a i{ font-size:10px;margin-left: 2px;width: 10px;display: inline-block;vertical-align: middle;-webkit-transition: all 0.2s ease;-o-transition: all 0.2s ease;transition: all 0.2s ease;}
.phoneNav > ul > li.cur > a i{transform: rotate(-180deg);
-ms-transform: rotate(-180deg);
-moz-transform: rotate(-180deg);
-webkit-transform: rotate(-180deg);
-o-transform: rotate(-180deg);}
.phoneNav > ul > li.cur > ul{transform: translateX(-50%) scale(1);opacity: 1;visibility: visible;}
.heightNav{width: 100%;height: 52px;display: none;}
.banner{ padding:30px 0 30px 0;border-bottom: 1px solid #f3f4f9;}
.bannerBox{display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;}
.bannerLeft{-ms-flex: 0 0 75%;flex: 0 0 75%;max-width: 75%;}
.bannerRight{-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%; padding-left: 13px;}
.bannerRight ul{display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap; margin: -6px;}
.bannerRight ul li{-ms-flex: 0 0 100%;flex: 0 0 100%; max-width: 100%;}
.bannerRight ul li .item{position: relative;display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;min-width: 0;word-wrap: break-word; background: #cfe2e8; height: 112px; margin: 6px; overflow: hidden;}
.bannerRight ul li .item img{ width: 100%; height: 100%;object-fit: cover;object-position: center; position:absolute; top: 0; left: 0; z-index: 1;}
.bannerRight ul li .item a{ display: block; height: 100%;font-size: 0;}
.bannerRight ul li .item a h4{font-size: 14px;position: absolute; z-index: 3; bottom: 15px; left: 15px; right: 15px; opacity: 0; color: #fff;-webkit-transition: all 0.3s ease;-o-transition: all 0.3s ease;transition: all 0.3s ease;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;}
.bannerRight ul li .item a:hover h4{opacity: 1;}
.bannerRight ul li .item a:after{content: '';display: block; position: absolute; z-index: 2; left: 0; right:0; bottom: 0; top: 0; background:rgba(0,0,0,0);-webkit-transition: all 0.3s ease;-o-transition: all 0.3s ease;transition: all 0.3s ease;}
.bannerRight ul li .item a:hover:after{background:rgba(0,0,0,.6);}
.artBox,.sortBox{ margin-left:0px;margin-right:320px; padding-left: 30px;}
.leftBox .artBox,.leftBox .sortBox{ margin-left: 200px;margin-right: 280px; padding-left: 30px;}
.leftBox .sbarBoxleft .widget.tagTops h3{ padding-bottom: 0; margin-bottom: 0px;}
.leftBox .sbarBoxleft .widget,
.leftBox .sbarBoxleft .widget:last-child{padding:15px}
.leftBox .widget.randTag ul,.widget.divLinkage ul{ font-size: 0; display: block; margin: 0 -3px;}
.leftBox .widget.randTag li,.widget.divLinkage li{width: calc(100% / 2 - 6px); display: inline-block; margin: 3px; height: 30px;text-align: center;}
.leftBox .widget.randTag li a,.widget.divLinkage li a{ font-size: 12px; display: block; padding: 0 8px; background: #fff;-webkit-transition: all 0.3s ease;-o-transition: all 0.3s ease;transition: all 0.3s ease;text-overflow: ellipsis;white-space: nowrap; overflow: hidden;color: #fff;}
.leftBox .widget.randTag li:nth-child(16n) a{background: #f28484;}
.leftBox .widget.randTag li:nth-child(16n+1) a{background: #f2bd3f;}
.leftBox .widget.randTag li:nth-child(16n+2) a{background: #f096e2;}
.leftBox .widget.randTag li:nth-child(16n+3) a{background: #97a1d7;}
.leftBox .widget.randTag li:nth-child(16n+4) a{background: #5bc5ee;}
.leftBox .widget.randTag li:nth-child(16n+5) a{background: #a9db21;}
.leftBox .widget.randTag li:nth-child(16n+6) a{background: #3ed7b3;}
.leftBox .widget.randTag li:nth-child(16n+7) a{background: #3ccedd;}
.leftBox .widget.randTag li:nth-child(16n+8) a{background: #6488f4;}
.leftBox .widget.randTag li:nth-child(16n+9) a{background: #ad8ff0;}
.leftBox .widget.randTag li:nth-child(16n+10) a{background: #fb8f8f;}
.leftBox .widget.randTag li:nth-child(16n+11) a{background: #cf73f0;}
.leftBox .widget.randTag li:nth-child(16n+12) a{background: #6dd273;}
.leftBox .widget.randTag li:nth-child(16n+13) a{background: #969cf4;}
.leftBox .widget.randTag li:nth-child(16n+14) a{background: #f477be;}
.leftBox .widget.randTag li:nth-child(16n+15) a{background: #fba74f;}
.leftBox .widget.randTag li a:hover,.widget.divLinkage li a:hover{ color: #fff;background: #282828;}
.leftBox .widget.navTops ul{ margin: -10px 0;}
.leftBox .widget.navTops li { margin: 10px 0; height: 36px; line-height: 34px;background: #f3f4f9;position: relative;overflow: hidden;}
.leftBox .widget.navTops li a{ display: block; padding: 0 10px;text-overflow: ellipsis;white-space: nowrap;overflow: hidden; border-left: 2px solid #00ADA7;position: relative;z-index: 1;-webkit-transition: all 0.3s ease;-o-transition: all 0.3s ease;transition: all 0.3s ease;}
.leftBox .widget.navTops li a i{font-size: 18px;padding-right: 5px; opacity: .8;vertical-align: middle;}
.leftBox .widget.navTops li:after{position: absolute;top: 0;content: "";background:#00ADA7; width:0%; height:36px;display: block;-webkit-transition: all 0.3s ease;-o-transition: all 0.3s ease;transition: all 0.3s ease;}
.leftBox .widget.navTops li:hover:after{ width:100%;}
.leftBox .widget.navTops li a:hover{color:#fff;}
.sidebar{ position: relative}
.sidebar .sbarBox{ padding-left: 20px;}
.sbarBoxleft{ padding:0;}
.sidebar.leftSidebar{ width: 180px; float: left;margin-right: -180px; margin-left: 0; padding-left: 0;}
.sidebar.leftSidebar:after{ display: none}
.sidebar.phone .sbarBox,
.sidebar.phone .widget{padding-left: 0px;}
.cateText{ background-color:#f1f2f9; padding: 128px 0 70px; text-align: center; margin-bottom:20px}
.cateText .container{position: relative;z-index: 2}
.cateText[style^=background-image]{ color: #fff;background-position: center;background-color:#1d1d1d; background-size: cover; position: relative;}
.cateText[style^=background-image]:after{content: ''; background-color:#000; opacity: .5; z-index: 1; position: absolute; top: 0; left: 0; bottom: 0; right: 0;}
.cateText h2{ font-size: 2.8rem; font-weight: 500; line-height: 2; max-width:500px; padding: 0 40px; margin: 0 auto;}
.cateText .desc{ font-size: 1.4rem; line-height: 1.8; max-width:600px; padding: 0 40px; margin: 0 auto;}
body.jumpUrl{ background: #fff; position: static}
body.jumpUrl .warp{ margin: 0;}
body.jumpUrl .container{ width: 100%; max-width: 780px; text-align: center; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%)}
body.jumpUrl .container .content{ background: #fff; padding: 0px;}
body.jumpUrl .container .img{ max-width: 160px; width: 100%; margin: 0 auto 20px}
body.jumpUrl .container .img img{width: 100%;}
body.jumpUrl .container .title{ font-size: 20px; line-height: 1.6; font-weight:600;}
body.jumpUrl .container .url{ margin: 10px 0 20px; font-size: 16px; line-height:1.6; opacity: .6}
body.jumpUrl .container .btnBox{ margin-top: 10px;display: inline-block;}
body.jumpUrl .container .btn{ display: inline-block; color: #fff; background: #ec414d;border: 1px solid #ec414d; margin: 0 8px; font-size: 16px; font-weight: 500; line-height: 36px; padding: 0 15px;}
body.jumpUrl .container .btn.goHome{ background: #fff; color: #ec414d; border: 1px solid #ec414d}
@media screen and (max-width:1279px){
/*头部*/
.search .ssFrom{ right:15px;}
/*头部END*/
.umPlay{padding:66px 0 0px;}
.umPlay .container{ padding:0}
.reading .umPlay{padding:0px;}
.bannerLeft {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}
.bannerRight {-ms-flex: 0 0 100%;flex: 0 0 100%;padding-left: 0; padding-top: 12px;max-width: 100%;}
.bannerRight ul li {-ms-flex: 0 0 33.333%;flex: 0 0 33.333%;}
}
@media screen and (max-width:1024px){
	.owl-carousel .owl-item img{height: 320px;}
}
@media screen and (max-width:992px){
	.artBox,.sortBox{margin-right: 250px; padding-right: 15px;padding-left: 15px;}
	.artBox .post,.artBox .post.umArtPost{padding-top: 20px;}
 .umpage .artBox{padding-right: 40px}
	.banner{ padding:20px 0 30px 0}
    .leftBox .artBox,.leftBox .artBox, .leftBox .sortBox{margin-right: 250px; margin-left: 0; padding-right: 20px;padding-left: 20px;}
    .sidebar { width: 250px;margin-left: -250px;}
    .widget{ padding:20px 15px 10px 15px}
    .sidebar .sbarBox:after{ display: none}
	   .tagTops li .tagImg{ min-height: 60px;}
    .sidebar.leftSidebar{ display: none}
    .container,.umCrumb{ max-width: 920px;}
}
@media screen and (max-width:920px){
.container{ padding: 0 40px;}
.umCrumb{padding: 0px 40px 15px;}
.owl-carousel .owl-item img{height: 330px;}
.bannerRight ul li .item{ height: 102px;}
/*头部*/
.header,.header .logo{ height:48px; display:inline-block; float:none; margin:0 auto; font-size:0;}
.header .headBox{ text-align:center;}
.navBar{  display: none}
.search,.umUser{ position: absolute; top: 0; right: 38px; float: none}
.search{right: 80px;}
.search i.icon,.header .navBar li{ height:48px; line-height:48px;}
.umUser .login{font-size: 12px;text-align: left;}
.umUser .userPic,.umUser .userPic img{width: 28px;height: 28px;}
.umUser{padding: 10px 0;}
.umUser.on .login{top: 48px;}
.warp{margin-top: 68px;}
.mNavBtn{ display:block; left: 23px;}
body{ position:relative; left:0;overflow-x: hidden;-webkit-transition: all .3s ease;-moz-transition: all .3s ease;transition: all .3s ease;}
body.open{overflow:hidden;}
body .warp{position:relative;left:0px;-webkit-transition: all .3s ease;-moz-transition: all .3s ease;transition: all .3s ease;}
body .phoneNav{left:0px;-webkit-transition: all .3s ease;-moz-transition: all .3s ease;transition: all .3s ease;}
body.open .header,body.open .warp,body.open .phoneNav{left:190px;}
body.open .leftNav{ left:0px;}
body.open .mNavBtn{ display:none;}
body.open .leftNav .mNavBtn{ display:block;}
/*头部END*/
.umPlay{padding:48px 0 0px;}
.umPlay .container{ padding:0}
	.artBox,.leftBox .artBox,.leftBox .sortBox,.sortBox{margin-right:0;}
 .artBox:after,.sidebar {display: none}
	.sidebar.phone{display:block; margin:0; width:100%;}
	.sidebar.phone .widget{right: 0;padding: 15px;margin-bottom: 20px;}
	.tagTops li .tagImg{ min-height: 120px;}
	.umpage .artBox{padding-left:220px;}
	.umpage .sidebar,.umpage .sbarBoxa li.active a{ display: block; width:180px;}
	div.post-body .rslist{ margin: 0 -4px;}
	div.post-body .rslist li{width: 33.333%; padding: 4px;}
	div.post-body .rslist li img{ margin-right: 10px;}
}
@media screen and (max-width:768px){
.post .post-title{ margin-top: 7px;}
.umpage .artBox{padding-left: 190px;padding-right: 30px;}
.umpage .sidebar,.umpage .sbarBoxa li.active a{ width:160px;}
div.post-body .rslist li{width:50%; padding: 4px;}
}
@media screen and (max-width:640px){
.container{ padding: 0 25px;}
.umCrumb {padding: 0px 25px 15px;}
.artBox .post,.artBox .relevant .post{ font-size: 0; padding: 20px 0;}
.mNavBtn{ left: 10px;}
.search{ right: 65px;}
.umUser{right: 20px;padding: 13px 0;}
.umUser .userPic, .umUser .userPic img{width: 22px;height: 22px;}
.banner{padding: 25px 0}
.relevant,.commBox{padding: 16px 0;}
.post .post-media{margin-right:0px;display: inline-block; float: none; vertical-align: middle;}
.post.big .post-content,.post .post-content.noMedia,.artBox .weiyu .post .post-content{ padding-left: 0;}
.post .post-title{ margin-top:0px;white-space:normal; font-size: 18px;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;}
.post .post-content .umInfo{ display: none}
.post.big .post-content,.post .post-content.noMedia{ width: 100%}
.post.big .post-content .umInfo,.post .post-content.noMedia .umInfo{ display: block;}
.post.big .post-title{margin-bottom:8px;}
.post .post-content{padding-right: 10px;}
.post .post-media .post-pic{margin-left: 5px;}
.post.big .post-pic3.post-media {width: calc(100% + 10px);margin-left: -10px;}
.post.big .post-pic3 .post-pic{margin-left: 10px;}
	.post .post-media .movie .icon{ width: 32px; height: 32px; line-height: 32px; margin: -16px 0 0 -16px;}
	.post .post-media .movie .icon i{ font-size: 26px;}
	.post.umTop .istTop{ font-size: 18px; vertical-align: 1px;}
	.artBox .weiyu .post .post-content{ width: 100%}
	.artBox .weiyu .post{padding: 15px 30px 15px 48px;}
	.artBox .weiyu .post .dot{ top: 24px;}
	.sortBox{ padding: 15px 12px}
	.umpage .artBox{padding-left: 140px;}
	.umpage .sidebar,.umpage .sbarBoxa li.active a{ width:140px;}
	.umpage .sidebar{ width: 100%; position: relative; background: #fff; margin-bottom: 20px;}
	.umpage .sidebar li{ display: inline-block;}
	.umpage .sbarBoxa{ padding: 10px 0}
	.umpage .sbarBoxa li.active a:before,.umpage .sbarBoxa .slide1, .umpage .sbarBoxa .slide2{ display: none;}
	.umpage .sidebar li a{ border: none; padding: 4px 15px;}
	.umpage .sbarBoxa li.active a{ width: auto}
	.umpage .artBox{ padding-left: 20px; padding-right: 20px;}
	.single .artBox .fullRead{ right:18px; top: 20px;}
	.tagTops li{ width: 50%}
	.tagTops li .tagImg{ min-height: 155px;}
	.ssArr li{ padding: 10px 10px 10px 48px;}
	.ssArr .ssImg{ margin-left: -48px;}
	.ssArr .ssImg img{ width: 48px; height: 48px;}
	.ssArr .ssText h3{ max-height: 42px; white-space:normal;}
	.ssArr .ssText .ssInfo{ display: none}
	.owl-theme .owl-nav.disabled + .owl-dots{margin:10px 0 0px 0}
	.post.big .post-title{margin-bottom:5px;}
	.umPlay{ margin-bottom:20px;}
	.phoneNav,.heightNav{display: block;}
   body.jumpUrl .container .title{ font-size: 18px;}
   body.jumpUrl .container .url{font-size: 14px;}
}
@media screen and (max-width:520px){
.postText.isVerify ul li,.postText ul li{width:100%;}
.container{ padding:0 15px;}
.umCrumb{padding: 0px 15px 15px;}
  .owl-carousel .owl-item img{ height: 300px;}
  .bannerRight ul li .item{ height: 92px;}
  .widget{margin-bottom:15px;}
  .mNavBtn{ left:0px;}
  .search{ right: 45px;}
		.umUser {right: 9px;}
		.post .post-content{padding-right: 10px;}
  .post .post-content p em{ display: none;}
  .post .post-content p em.date{ display: block}
		.post .post-info em.ceta,.post .post-info em:nth-child(2){display: none;}
}
@media screen and (max-width:480px){
.container{ padding:0 10px;}
.umCrumb{padding: 0px 10px 15px;}
.artBox{margin-bottom: 15px;}
.sortBox{margin-bottom: 15px;}
.banner{ padding: 20px 0}
.artBox .post, .widget{margin-bottom:0px;}
.post .post-content p em.ceta{ display: none}
.post .post-title,.single .relevant .post .post-title{ margin-bottom: 0; font-size: 1.6rem;line-height: 1.5;}
.post .post-title em.isTop {padding: 0px 3px 0px;vertical-align: 2px;}
.post.umTop .istTop{ font-size: 16px;}
	.single .relevant .post .post-title{padding:0;}
	.single .post-body p{margin: 0 0 15px 0;}
	div.post-body .umDes{padding: 15px 45px;}
	div.post-body .umDes i.left{ left: 10px;}
	div.post-body .umDes i.right{ right: 10px;}
	.tagTops li .tagImg{ min-height: 135px;}
	.single .postNav{float: none;margin-top:.5rem;}
	.single .postNav div a{margin: .6rem 1rem .6rem 0rem;}
	.owl-carousel .owl-item img{ height:200px;}
  .bannerRight ul li .item{ height: 60px;}
	.umPlay{ margin-bottom:15px;}
}
@media screen and (max-width:440px){
	.tagTops li .tagImg{ min-height: 120px;}
	.ssArr li{ width: 100%; height: 100%; padding: 8px 10px 8px 40px;}
	.ssArr .ssImg{ margin-left: -40px;}
 .reading .post-like a{margin:8px 6px; padding:0 12px;}
 .ssFrom{width: 320px;}
	.post .post-info{margin: .5rem 0 0 0;}
}
@media screen and (max-width:375px){
	.ssFrom{width: 300px;}
	.post .post-media .post-pic{margin-left: 2px;}
	.post.big .post-pic3 .post-pic{margin-left: 3px;}
	.post.big .post-pic3.post-media {width: calc(100% + 3px);margin-left: -3px;}
}
.artBox,.owl-carousel .owl-stage-outer,.post .post-media,.widget,div.post-body img,.post-like a,.bannerRight ul li .item,.tagTops li .tagImg,.widget.umUser div,.leftBox .widget.randTag li a,.widget.divLinkage li a,.tagTops li a .tagImg:after,.leftBox .widget.navTops li,.widget.divTags li a,.post-pic,.post-media .movie,body.jumpUrl .container .btn,.sortBox,.bannerRight ul li .item img{ border-radius: 4px;}