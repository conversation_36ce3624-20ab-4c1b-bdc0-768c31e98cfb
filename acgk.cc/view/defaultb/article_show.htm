<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>{$gdata[title]} - 快萌ACG/南+社区</title>
{if:$gdata[seo_keywords]}
<meta name="keywords" content="{$gdata[seo_keywords]}" />
{elseif:$gdata[tags]}
<meta name="keywords" content="{php}echo implode(',',$gdata['tags']);{/php}" />
{else}
<meta name="keywords" content="{$cfg[seo_keywords]}" />
{/if}
<meta name="description" content="{$cfg[seo_description]}">
<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1"/>
<meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width">
<meta name="apple-mobile-web-app-title" content="">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="green">
<meta name="format-detection" content="telphone=no, email=no">
<meta name="HandheldFriendly" content="true">
<meta name="screen-orientation" content="portrait">
<meta name="x5-orientation" content="portrait">
<meta name="full-screen" content="yes">
<meta name="x5-fullscreen" content="true">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="renderer" content="webkit">
<script src="{$cfg[tpl]}assets/script/font_3913661_iikaqjykdll.js"></script>
<link href="{$cfg[tpl]}assets/style/font_3913661_iikaqjykdll.css" rel="stylesheet">
<link href="{$cfg[tpl]}assets/style/font-awesome.min.css" rel="stylesheet">
<style type="text/css">
.iconfont {
    font-size: inherit;
}
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
.th_padding, .left_padding {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 992px) {
  .col-md-12 {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  @media only screen and (min-width: 320px) and (max-width: 992px) {
    .th_padding {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  .pagebar {
    text-align: center;
    margin-top: 15px;
  }
 }
.pagebar {
  text-align: center;
  margin-top: 15px;
}
  
  .pagebar a, .pagebar b {
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 16px;
  margin: 0px 5px;
  border: 1px solid #f0f0f5;
  color: #666;
  padding: 0px 8px;
  border-radius: 2px;
  box-sizing: border-box;
}
  .pagebar b {
  background: #7cbeff;
  border: 1px solid #7cbeff;
  color: #fff;
  font-weight: normal;
}
</style>
<link href="{$cfg[tpl]}assets/style/style.css?2024" rel="stylesheet">
<script src="{$cfg[tpl]}assets/script/jquery.min.js"></script>
<div class="ui progress" style="position: fixed; top: 0; left: 0; z-index: 999; width: 100vw;">
  <div id="page-reading-percent" class="percent" style="width: 0;"></div>
</div>
<script>
    $(function (){
         $(document).scroll(function (){
             let height = $(this).height() - $(window).height();
             let top = $(this).scrollTop();
             let percent = top / height * 100;
             $("#page-reading-percent").width(percent.toFixed(2) + "%");
        });
    });
     function scrollToTop(){
        $("html, body").animate({ scrollTop:0 }, 500);
    }
     function showModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("show");
        setTimeout(() => {
            modalPageDiv.addClass("open");
        }, 50);
    }
     function hideModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("close");
        setTimeout(() => {
            modalPageDiv.removeClass("show");
            modalPageDiv.removeClass("open");
            modalPageDiv.removeClass("close");
        }, 500);
    }
</script>
<div class="ui modal-page right" id="modal-page"> 
   <div class="modal-background" onclick="hideModal()"></div>
   <div class="modal-container padding">
    <div class="ui flex-item padding">
      <div class="center"></div>
      <div class="end">
        <div class="ui button circle outline" onclick="hideModal()"><i class="iconfont icon-close"></i></div>
      </div>
    </div>
     <div class="panel-block">
      <div class="title">导航菜单</div>
      <div class="ui tree linear-split" style="padding: 20px 0;"> {block:navigate}
        <ul>
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold"><a href="/" class="padding-half">首页</a></div>
            </div>
          </li>
          {loop:$data $v}
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold"><a href="{$v[url]}" class="padding-half">{$v[name]}</a></div>
            </div>
          </li>
          {/loop}
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold">
               {if:$_uid}
                    <a href="/my-index.html" class="padding-half">个人中心</a>
                {else}
                    <a href="/user-login.html" class="padding-half">登录/注册</a>
                {/if}</div>
            </div>
          </li>
        </ul>
        {/block} </div>
    </div>
  </div>
</div>
<header>
  <div class="container auto-margin ui flex-item"> <a class="start web-logo" href="{$cfg[webroot]}"><img src="{$cfg[tpl]}assets/images/logo.png" alt="{$cfg[webname]}"></a> <a class="start web-name padding-left" href="/">快萌ACG</a>
<div class="search center padding-left justify-center">
      <div class="ui input radius">
        <input type="text" placeholder="前往acgk.cc搜索,本站待修复">
        <div class="">
          <div class="ui button clear" ><i class="iconfont icon-sousuo"></i></div>
        </div>
      </div>
    </div>
    {block:navigate}
    <div class="end justify-center">
      <nav> <a class="item" href="/">首页</a> {loop:$data $v} <a class="item" href="{$v[url]}">{$v[name]}</a>{/loop} 
      {if:$_uid}
                    <a href="/my-index.html" class="item">个人中心</a>
                {else}
                    <a href="/user-login.html" class="item">登录/注册</a>
      {/if}
      </nav>
    </div>
    {/block}
    <div class="end ext-menu justify-center">
      <div class="ui button circle" onclick="showModal()"><i class="fa fa-list"></i></div>
    </div>
  </div>
</header>


{block:global_show show_prev_next="1" dateformat="Y-m-d"}{/block}
<div class="detail-page">
  <main>
    <div class="article-title ui flex-item padding-start padding-end">
      <div class="block text-large"> 资源 </div>
      <div class="center padding-left">
        <div class="ui flex-item">
          <h1 class="center text-bigger">{$gdata[title]}</h1>
          <div class="end"> <a class="ui button tiny clear text-small"><i class="iconfont icon-Share"></i> 分享</a> <a class="ui button tiny clear text-small"><i class="iconfont icon-jubao"></i> 举报</a> </div>
        </div>
        <div class="ui flex-item padding-top-half">
          <div class="start">
            <div class="ui avatar tiny circle"><img src="assets/images/face.jpg" alt=""></div>
          </div>
          <div class="start justify-center padding-left-half">{$gdata[author]}</div>
          <div class="center medium-text justify-center padding-left">时间：{$gdata[date]} 阅读：{$gdata[views]}</div>
          <div class="end justify-center">
            <div class="danger-background text-small radius" style="padding:3px 5px; line-height: 1em;"><i class="fa fa-warning"></i> 老资源可能失效,选择下载！！</div>
          </div>
        </div>
      </div>
    </div>
     <style>
      img{width:100%;height:auto;display:block;clip-path:inset(0 0 25% 0)}.notice{background:linear-gradient(135deg,#007bff,#00c6ff);color:white;border-radius:10px;padding:20px;text-align:center;margin:20px 0;box-shadow:0 4px 15px rgba(0,123,255,0.5);transition:transform 0.3s}.notice a{color:white;text-decoration:none;font-size:18px;font-weight:bold;display:inline-block;padding:10px 20px;border-radius:5px;background-color:rgba(255,255,255,0.2);transition:background-color 0.3s}.notice a:hover{background-color:rgba(255,255,255,0.4);text-decoration:none}.notice:hover{transform:scale(1.02)}
    </style>
    <div class="article-content">
      <div class="ui flex-item">
        <div class="start text-bigger"><i class="iconfont icon-qiangdadaan"></i> </div>
        <div class="start text-bigger padding-left">帖子内容</div>
      </div>
      <!-- 内容 -->
      {if:$_uid}
      <div class="article-html"> 
        <div class="notice">
        <a href="https://www.777723.xyz/" target="_blank" rel="noopener noreferrer">
            本站体验不佳,点我前往新网站,账号密码同步,无需注册即可登录,同步数据
        </a>
		</div>
      {$gdata[content]} 
      <br /><br /><p>{hook:favorites.htm}</p><br /><br /><br />
      </div>
      {else}请先 <a href="/user-login.html">登录</a> 或者 <a href="/user-register.html">注册</a> 后在进行查看哦！{/if}
    </div>
{if:isset($gdata[prev][url]) || isset($gdata[next][url])}
    <div class="padding ui flex-item border-top"> {if:isset($gdata[prev][url])}
      <p><span>上一篇：</span><a href='{$gdata[prev][url]}'>{$gdata[prev][title]}</a></p>
      {else}
      <p><span>上一篇：</span><a>没有了</a></p>
      {/if}
      {if:isset($gdata[next][url])}
      <p><span>下一篇：</span><a href='{$gdata[next][url]}'>{$gdata[next][title]}</a></p>
      {else}
      <p><span>下一篇：</span><a>没有了</a></p>
      {/if} </div>
    {/if} 
    
    <!-- 关联内容 -->
    <div class="panel-block no-prefix padding-bottom-half border-top">
      <div class="title ui flex-item" style="font-weight: normal;">
        <div class="start"><i class="iconfont icon-receipt"></i></div>
        <div class="center padding-left-half">相关内容</div>
      </div>
      {block:taglike type="0" limit="12" dateformat="Y-m-d"}
      {if:$data[list]}
      {loop:$data[list] $v}
      <ul class="text-list">
        <li><a href="{$v[url]}"  target="_blank" title="{$v[title]}">{$v[title]}</a></li>
      </ul>
      {/loop}
      {/if}
      {/block} </div>
  </main>
  <!-- 侧栏 --> 
  {inc:celan.htm} </div>
<script>
window.onload = function() {
    var elements = document.getElementsByClassName('hidden-password');
    Array.prototype.forEach.call(elements, function(el) {
        var time = el.getAttribute('data-time');
        var countdown = el.getElementsByClassName('countdown')[0];
        var interval = setInterval(function() {
            time--;
            countdown.innerText = time;
            if(time === 0) {
                clearInterval(interval);
                el.style.display = 'none';
            }
        }, 1000);
    });
}
</script>
<script type="text/javascript" src="{$cfg[webdir]}static/layui/lib/layui/layers-V2.8.js"></script>
{hook:article_show_after.htm}
{inc:footer.htm} 