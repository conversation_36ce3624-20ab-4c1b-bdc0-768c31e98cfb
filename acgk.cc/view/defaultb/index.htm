{inc:header.htm}
<div class="main-warp">
  <div class="container auto-margin">
    {block:content_total_by_date mid="2" type="today"}<div class="today-posts" style="background-color: #fff;color: #343a40;border-radius: 5px;text-align: center;font-size: 1.9rem;transition: background-color 0.3s;font-weight: 900;">今日发布：<m style="font-size: 2.8rem;color: red;"> {$data} </m>篇</div>{/block}
    <div class="slide-wrap">
      <div class="slide">
        <div class="text">二次元社区</div>
        <div class="image"><img src="{$cfg[tpl]}assets/images/bg1.png" alt=""></div>
      </div>
      <div class="panel-block notice">
        <div class="title">Top</div>
        <div class="padding-half"></div>
        <ul class="text-list">
          {block:list_rand orderby="views" limit="8" titlenum="24"}
          {php}$rank = 1;{/php}
          
          {loop:$data[list] $v}
          <li><span class="tag">{$rank}</span> <a href="{$v[url]}" title="">{$v[title]}</a></li>
          {php}$rank++;{/php}
          {/loop}
          {/block}
        </ul>
      </div>
    </div>
    <div class="tab-list margin-top padding-top" id="tab-list">
      <div class="tab-row">
          <div class="col active" data-tab="tab-wealth">
              <div class="name">game</div>
              <div class="icon">
                  游戏 
              </div>
          </div>
          <div class="col" data-tab="tab-loan">
              <div class="name">anime</div>
              <div class="icon">
                  动漫 
              </div>
          </div>
          <div class="col" data-tab="tab-stock">
              <div class="name">comics</div>
              <div class="icon">
                  漫画
              </div>
          </div>
          <div class="col" data-tab="tab-insurance">
              <div class="name">tutorial</div>
              <div class="icon">
                  教程
              </div>
          </div>
      </div>
      <div class="tab-content" id="tab-wealth">
        <ul class="text-list">
          <!-- orderby="dateline" 最新日期，ID2内容 内容数量16 标题长度50--> 
          {block:list orderby="dateline"  cid="1" limit="16" titlenum="80"}
          {loop:$data[list] $v}
          <li><span class="time"><i class="iconfont icon-receipt"></i></span> <a href="{$v[url]}" title="">{$v[subject]}</a></li>
          {/loop}
          {/block}
        </ul>
      </div>
      <div class="tab-content" id="tab-loan" style="display:none;">
        <ul class="text-list">
          {block:list orderby="dateline"  cid="2" limit="16" titlenum="80"}
          {loop:$data[list] $v}
          <li><span class="time"><i class="iconfont icon-receipt"></i></span> <a href="{$v[url]}" title="">{$v[subject]}</a></li>
          {/loop}
          {/block}
        </ul>
      </div>
      <div class="tab-content" id="tab-stock" style="display:none;">
        <ul class="text-list">
          {block:list orderby="dateline"  cid="3" limit="16" titlenum="80"}
          {loop:$data[list] $v}
          <li><span class="time"><i class="iconfont icon-receipt"></i></span> <a href="{$v[url]}" title="">{$v[subject]}</a></li>
          {/loop}
          {/block}
        </ul>
      </div>
      <div class="tab-content" id="tab-insurance" style="display:none;">
        <ul class="text-list">
          {block:list orderby="dateline"  cid="6" limit="16" titlenum="80"}
          {loop:$data[list] $v}
          <li><span class="time"><i class="iconfont icon-receipt"></i></span> <a href="{$v[url]}" title="">{$v[subject]}</a></li>
          {/loop}
          {/block}
        </ul>
      </div>
    </div>
    <script>
document.addEventListener('DOMContentLoaded', function() {
  const tabs = document.querySelectorAll('.tab-row .col');
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      tabs.forEach(t => t.classList.remove('active'));
      const contents = document.querySelectorAll('.tab-content');
      contents.forEach(c => c.style.display = 'none');
      this.classList.add('active');
      const activeTab = this.getAttribute('data-tab');
      document.getElementById(activeTab).style.display = 'block';
    });
  });
});
</script>
    <div class="panel-block margin-top">
      <div class="title">友情链接</div>
      <div class="padding-half"></div>
      <div class="links"> {block:links}
        {loop:$data $v} <a href="{$v[url]}">{$v[name]}</a> {/loop}
        {/block} </div>
    </div>
  </div>
</div>
{inc:footer.htm} 