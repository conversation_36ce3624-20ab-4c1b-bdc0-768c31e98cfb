<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>快萌ACG - 南+社区</title>
<meta name="keyword" content="快萌ACG,南+社区,快萌论坛,游戏,百合,acg,和谐,同人,C94,韩漫,游戏,百合,萝莉,魔法少女,初音,东方,伪娘,末途,校园奴隶契约,驱灵师,小黄游,HS2,游戏合集包,漫画合集包,写真,套图">
<meta name="description" content="{$cfg[seo_description]}">
<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1"/>
<meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width">
<meta name="apple-mobile-web-app-title" content="">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="green">
<meta name="format-detection" content="telphone=no, email=no">
<meta name="HandheldFriendly" content="true">
<meta name="screen-orientation" content="portrait">
<meta name="x5-orientation" content="portrait">
<meta name="full-screen" content="yes">
<meta name="x5-fullscreen" content="true">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="renderer" content="webkit">
<script src="{$cfg[tpl]}assets/script/font_3913661_iikaqjykdll.js"></script>
<link href="{$cfg[tpl]}assets/style/font_3913661_iikaqjykdll.css" rel="stylesheet">
<link href="{$cfg[tpl]}assets/style/font-awesome.min.css" rel="stylesheet">
<style type="text/css">
.iconfont {
    font-size: inherit;
}
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
.th_padding, .left_padding {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 992px) {
  .col-md-12 {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  @media only screen and (min-width: 320px) and (max-width: 992px) {
    .th_padding {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  .pagebar {
    text-align: center;
    margin-top: 15px;
  }
 }
.pagebar {
  text-align: center;
  margin-top: 15px;
}
  
  .pagebar a, .pagebar b {
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 16px;
  margin: 0px 5px;
  border: 1px solid #f0f0f5;
  color: #666;
  padding: 0px 8px;
  border-radius: 2px;
  box-sizing: border-box;
}
  .pagebar b {
  background: #7cbeff;
  border: 1px solid #7cbeff;
  color: #fff;
  font-weight: normal;
}
</style>
<link href="{$cfg[tpl]}assets/style/style.css?2024" rel="stylesheet">
<script src="{$cfg[tpl]}assets/script/jquery.min.js"></script>
<div class="ui progress" style="position: fixed; top: 0; left: 0; z-index: 999; width: 100vw;">
  <div id="page-reading-percent" class="percent" style="width: 0;"></div>
</div>
<script>
    $(function (){
         $(document).scroll(function (){
             let height = $(this).height() - $(window).height();
             let top = $(this).scrollTop();
             let percent = top / height * 100;
             $("#page-reading-percent").width(percent.toFixed(2) + "%");
        });
    });
     function scrollToTop(){
        $("html, body").animate({ scrollTop:0 }, 500);
    }
     function showModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("show");
        setTimeout(() => {
            modalPageDiv.addClass("open");
        }, 50);
    }
     function hideModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("close");
        setTimeout(() => {
            modalPageDiv.removeClass("show");
            modalPageDiv.removeClass("open");
            modalPageDiv.removeClass("close");
        }, 500);
    }
</script>
<div class="ui modal-page right" id="modal-page"> 
   <div class="modal-background" onclick="hideModal()"></div>
   <div class="modal-container padding">
    <div class="ui flex-item padding">
      <div class="center"></div>
      <div class="end">
        <div class="ui button circle outline" onclick="hideModal()"><i class="iconfont icon-close"></i></div>
      </div>
    </div>
     <div class="panel-block">
      <div class="title">导航菜单</div>
      <div class="ui tree linear-split" style="padding: 20px 0;"> {block:navigate}
        <ul>
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold"><a href="/" class="padding-half">首页</a></div>
            </div>
          </li>
          {loop:$data $v}
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold"><a href="{$v[url]}" class="padding-half">{$v[name]}</a></div>
            </div>
          </li>
          {/loop}
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold">
               {if:$_uid}
                    <a href="/my-index.html" class="padding-half">个人中心</a>
                {else}
                    <a href="/user-login.html" class="padding-half">登录/注册</a>
                {/if}</div>
            </div>
          </li>
        </ul>
        {/block} </div>
    </div>
  </div>
</div>
<header>
  <div class="container auto-margin ui flex-item"> <a class="start web-logo" href="{$cfg[webroot]}"><img src="{$cfg[tpl]}assets/images/logo.png" alt="{$cfg[webname]}"></a> <a class="start web-name padding-left" href="/">快萌ACG</a>
<div class="search center padding-left justify-center">
      <div class="ui input radius">
        <input type="text" placeholder="前往acgk.cc搜索,本站待修复">
        <div class="">
          <div class="ui button clear" ><i class="iconfont icon-sousuo"></i></div>
        </div>
      </div>
    </div>
    {block:navigate}
    <div class="end justify-center">
      <nav> <a class="item" href="/">首页</a> {loop:$data $v} <a class="item" href="{$v[url]}">{$v[name]}</a>{/loop} 
      {if:$_uid}
                    <a href="/my-index.html" class="item">个人中心</a>
                {else}
                    <a href="/user-login.html" class="item">登录/注册</a>
      {/if}
      </nav>
    </div>
    {/block}
    <div class="end ext-menu justify-center">
      <div class="ui button circle" onclick="showModal()"><i class="fa fa-list"></i></div>
    </div>
  </div>
</header>
