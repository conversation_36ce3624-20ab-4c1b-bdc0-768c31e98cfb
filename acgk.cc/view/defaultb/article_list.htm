{inc:header.htm}
<link href="{$cfg[tpl]}assets/style/fenye.css?2024" rel="stylesheet">
<style type="text/css">
.tupiansa{display:flex;flex-wrap:wrap}.tupiansa img{width:110px;margin-bottom:10px;border-radius:5px;clip-path:inset(0 0 10% 0);height:80px;margin-left:2px;margin-right:2px}@media (max-width:768px){.tupiansa img{width:calc(30%);clip-path:inset(0 0 15% 0)}}
</style>
<div class="list-page">
  <main>
    <div class="title">{$cfg_var[place][0][name]}</div>
    <div class="padding-half"></div>
    <div class="ui list linear-split double-item-space"> {block:global_cate pagenum="20" showviews="1" showcate="1" dateformat="Y-m-d" showmaxpage="10000"}
      {if:empty($gdata[list])}
      <div class="empty-list text-center medium-text" style="padding: 50px;">
        <div class="text-maximum"><i class="iconfont icon-receipt"></i></div>
        <div class="padding-top">没有文章内容</div>
      </div>
      {else}
      {loop:$gdata[list] $v}
      <div class="item">
        <div class="detail">
          <div class="tupias"> <a class="text-bold max-two-line-text" href="{$v[url]}">{$v[title]}</a> 
            {if:$_uid}
            <div class="tupiansa">
            {loop:$v[piclist] $src}
            <img src="{$src}" alt="{$v[title]}">
            {/loop}
            </div>
            {else}{/if}
          </div>
          <div class="ui flex-itemss medium-text padding-top">
            <div class="ui avatar small circle"><img src="assets/images/face.jpg" alt=""></div>
            <div class="justify-center padding-start">作者：{$v[author]}</div>
            <div class="justify-center padding-start">阅读：{$v[views]}</div>
            <div class="center"></div>
            <div class="end justify-center" data-date="{$v[date]}">发布时间：{$v[date]}</div>
          </div>
        </div>
      </div>
      {/loop}
      {/if}
      {/block}
      <div class="pages">

							<div class="col-md-12 col-xs-12  th_padding">
								<div class="list-title pagebar">
									{$gdata[pages]}
								</div>
							</div>

	 </div>
    </div>
  </main>
  {inc:celan.htm} </div>
{inc:footer.htm} 
<script>
// 获取当前日期，格式化为 YYYY-MM-DD
const today = new Date();
const todayFormatted = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');

// 获取包含 data-date 属性的元素
const dateElement = document.querySelector('.end[data-date]');

// 获取该元素的发布时间
const publishDate = dateElement.getAttribute('data-date');

// 判断发布时间是否为今天，如果是，改变字体颜色为红色
if (publishDate === todayFormatted) {
    dateElement.style.color = 'red';
}
</script>