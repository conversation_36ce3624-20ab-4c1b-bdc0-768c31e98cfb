.elem-dark {
	display: inherit;
}
.elem-light {
	display: none;
}
body {
	--color-primary: var(--primary, #fe4f70);
	--color-primary-light: var(--primary-light, #ff7f97);
	--color-primary-contrast: var(--primary-contrast, #ffffff);
	--color-light: #ffffff;
	--color-light-light: #ffffff;
	--color-light-contrast: #203656;
	--background: #f9f9f9;
	--background-light: #ffffff;
	--color-medium: #8595b0;
	--color-medium-light: #ccd0d9;
	--color-medium-contrast: #ffffff;
	--color-dark: #142030;
	--color-dark-light: #203656;
	--color-dark-contrast: #ffffff;
	--color-danger: #e4393c;
	--color-danger-light: #e75356;
	--color-danger-contrast: #ffffff;
	--color-base: var(--color-light);
	--color-base-light: var(--color-light-light);
	--color-base-contrast: var(--color-light-contrast);
	--border-color: #dddddd;
	--font-size: 14px;
	--font-size-tiny: 9px;
	--font-size-small: 12px;
	--font-size-large: 20px;
	--font-size-bigger: 26px;
	--font-size-maximum: 36px;
	--space-width: 12px;
	--space-width-half: 6px;
	--radius-width: 10px;
}
@media (prefers-color-scheme: dark) {
	body {
		--color-light: #142030;
		--color-light-light: #203656;
		--color-light-contrast: #ffffff;
		--color-dark: #ffffff;
		--color-dark-light: #ffffff;
		--color-dark-contrast: #203656;
		--border-color: rgba(235, 235, 235, .2);
		--background: #142030;
		--background-light: #203656;
	}
	.elem-dark {
		display: none;
	}
	.elem-light {
		display: inherit;
	}
}
body.dark {
	--border-color: rgba(235, 235, 235, .2);
}
body.dark .elem-dark {
	display: none;
}
body.dark .elem-light {
	display: inherit;
}
body.light {
	--color-light: #ffffff;
	--color-light-light: #ffffff;
	--color-light-contrast: #203656;
	--color-dark: #142030;
	--color-dark-light: #203656;
	--color-dark-contrast: #ffffff;
}
.text-maximum {
	font-size: var(--font-size-maximum) !important;
	--font-size: var(--font-size-maximum)!important;
}
.text-bigger {
	font-size: var(--font-size-bigger) !important;
	--font-size: var(--font-size-bigger)!important;
}
.text-large {
	font-size: var(--font-size-large) !important;
	--font-size: var(--font-size-large)!important;
}
.text-small {
	font-size: var(--font-size-small) !important;
	--font-size: var(--font-size-small)!important;
}
.text-tiny {
	font-size: var(--font-size-tiny) !important;
	--font-size: var(--font-size-tiny)!important;
}
.primary {
	--color-base: var(--color-primary);
	--color-base-light: var(--color-primary-light);
	--color-base-contrast: var(--color-primary-contrast);
}
.primary-text {
	color: var(--color-primary);
}
.primary-border {
	--border-color: var(--color-primary);
}
.primary-background {
	--background-color: var(--color-primary);
	background-color: var(--color-primary);
	color: var(--color-primary-contrast);
}
.light {
	--color-base: var(--color-light);
	--color-base-light: var(--color-light-light);
	--color-base-contrast: var(--color-light-contrast);
}
.light-text {
	color: var(--color-light);
}
.light-border {
	--border-color: var(--color-light);
}
.light-background {
	--background-color: var(--color-light);
	background-color: var(--color-light);
	color: var(--color-light-contrast);
}
.medium {
	--color-base: var(--color-medium);
	--color-base-light: var(--color-medium-light);
	--color-base-contrast: var(--color-medium-contrast);
}
.medium-text {
	color: var(--color-medium);
}
.medium-border {
	--border-color: var(--color-medium);
}
.medium-background {
	--background-color: var(--color-medium);
	background-color: var(--color-medium);
	color: var(--color-medium-contrast);
}
.dark {
	--color-base: var(--color-dark);
	--color-base-light: var(--color-dark-light);
	--color-base-contrast: var(--color-dark-contrast);
}
.dark-text {
	color: var(--color-dark);
}
.dark-border {
	--border-color: var(--color-dark);
}
.dark-background {
	--background-color: var(--color-dark);
	background-color: var(--color-dark);
	color: var(--color-dark-contrast);
}
.danger {
	--color-base: var(--color-danger);
	--color-base-light: var(--color-danger-light);
	--color-base-contrast: var(--color-danger-contrast);
}
.danger-text {
	color: var(--color-danger);
}
.danger-border {
	--border-color: var(--color-danger);
}
.danger-background {
	--background-color: var(--color-danger);
	background-color: var(--color-danger);
	color: var(--color-danger-contrast);
}
* {
	box-sizing: border-box;
}
.ui {
	--button-height-maximum: 96px;
	--button-height-bigger: 72px;
	--button-height-large: 48px;
	--button-height: 38px;
	--button-height-small: 30px;
	--button-height-tiny: 24px;
}
.ui.flex-item {
	display: flex;
	flex-flow: row;
}
.ui.flex-itemss {
	display: flex;
	flex-flow: row;
}
.ui.flex-item .center {
	flex: 1;
}
.ui.progress {
	width: 100px;
	height: 3px;
}
.ui.progress .percent {
	height: 3px;
	background: var(--color-primary);
	transition: width 0.3s;
}
.ui.button {
	border: none;
	cursor: pointer;
	text-decoration: none;
	user-select: none;
	display: inline-block;
	white-space: nowrap;
	overflow: hidden;
	position: relative;
	text-align: center;
	vertical-align: middle;
	background: var(--color-primary);
	color: var(--color-primary-contrast);
	border-radius: var(--radius-width);
	padding: 0 1em;
	line-height: var(--button-height);
	height: var(--button-height);
	transition: all 0.3s ease-in-out;
}
.ui.button.primary {
	--color-primary: var(--color-primary);
	--color-primary-light: var(--color-primary-light);
	--color-primary-contrast: var(--color-primary-contrast);
}
.ui.button.light {
	--color-primary: var(--color-light);
	--color-primary-light: var(--color-light-light);
	--color-primary-contrast: var(--color-light-contrast);
}
.ui.button.medium {
	--color-primary: var(--color-medium);
	--color-primary-light: var(--color-medium-light);
	--color-primary-contrast: var(--color-medium-contrast);
}
.ui.button.dark {
	--color-primary: var(--color-dark);
	--color-primary-light: var(--color-dark-light);
	--color-primary-contrast: var(--color-dark-contrast);
}
.ui.button.danger {
	--color-primary: var(--color-danger);
	--color-primary-light: var(--color-danger-light);
	--color-primary-contrast: var(--color-danger-contrast);
}
.ui.button.maximum {
	--button-height: var(--button-height-maximum);
}
.ui.button.bigger {
	--button-height: var(--button-height-bigger);
}
.ui.button.large {
	--button-height: var(--button-height-large);
}
.ui.button.small {
	--button-height: var(--button-height-small);
}
.ui.button.tiny {
	--button-height: var(--button-height-tiny);
}
.ui.button:active {
	background: var(--color-primary-light);
}
.ui.button.clear {
	background: transparent;
	color: var(--color-primary);
}
.ui.button.clear:active {
	color: var(--color-primary-light);
}
.ui.button.linear {
	background-image: linear-gradient(to right, var(--color-primary) 0%, var(--color-primary-light) 100%);
	background-size: 200% auto;
}
.ui.button.linear:hover {
	background-position: right center;
}
.ui.button.loading::before {
	content: "";
	display: inline-block;
	width: 0.5rem;
	height: 0.5rem;
	border-radius: 50%;
	overflow: hidden;
	border-width: 1px;
	border-style: solid;
	border-color: var(--color-primary-contrast) var(--color-primary-contrast) transparent transparent;
	animation: rotate 1s infinite linear;
	margin-right: 0.5rem;
	transition: all 0.3s ease-in-out;
}
.ui.button.arrow-down::after {
	vertical-align: middle;
	margin-top: -0.5rem;
	content: "";
	display: inline-block;
	width: 0.3rem;
	height: 0.3rem;
	overflow: hidden;
	border-width: 1px;
	border-style: solid;
	border-color: transparent var(--color-primary-contrast) var(--color-primary-contrast) transparent;
	transform: rotate(45deg);
	margin-left: 0.5rem;
	transition: all 0.3s ease-in-out;
}
.ui.button.waiting {
	line-height: calc(var(--button-height) - 2px);
	background: transparent;
	border: solid 1px var(--color-medium);
	color: var(--color-medium);
}
.ui.button.waiting.clear {
	border-color: transparent !important;
}
.ui.button.waiting.loading::before {
	border-color: var(--color-medium) var(--color-medium) transparent transparent;
}
.ui.button.waiting.arrow-down::after {
	border-color: transparent var(--color-medium) var(--color-medium) transparent;
}
.ui.button.waiting:hover {
	border-color: var(--color-primary);
	color: var(--color-primary);
}
.ui.button.waiting:hover.loading::before {
	border-color: var(--color-primary) var(--color-primary) transparent transparent;
}
.ui.button.waiting:hover.arrow-down::after {
	border-color: transparent var(--color-primary) var(--color-primary) transparent;
}
.ui.button.waiting:active {
	border-color: var(--color-primary-light);
	color: var(--color-primary-light);
}
.ui.button.waiting:active.loading::before {
	border-color: var(--color-primary-light) var(--color-primary-light) transparent transparent;
}
.ui.button.waiting:active.arrow-down::after {
	border-color: transparent var(--color-primary-light) var(--color-primary-light) transparent;
}
.ui.button.outline {
	line-height: calc(var(--button-height) - 2px);
	background: transparent;
	border: solid 1px var(--color-primary);
	color: var(--color-primary);
}
.ui.button.outline:hover {
	background: var(--color-primary);
	color: var(--color-primary-contrast);
}
.ui.button.round {
	border-radius: 30px;
}
.ui.button.circle {
	width: var(--button-height);
	padding: 0;
	border-radius: 50%;
}
.ui.button.block {
	display: block;
}
.ui.pic-1b1 {
	position: relative;
	overflow: hidden;
	padding-top: 100%;
	background-position: center;
	background-size: cover;
	display: block;
}
.ui.pic-1b1 .wrap {
	background-position: center;
	background-size: cover;
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}
.ui.pic-1b1 .wrap img {
	display: block;
	width: 100%;
	height: 100%;
}
.ui.pic-1b1.circle {
	border-radius: 50%;
}
.ui.pic-4b3 {
	position: relative;
	overflow: hidden;
	padding-top: 75%;
	background-position: center;
	background-size: cover;
	display: block;
}
.ui.pic-4b3 .wrap {
	transition: all 0.3s;
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-flow: column;
	justify-content: center;
	background-position: center;
	background-size: cover;
}
.ui.pic-4b3 .wrap img {
	display: block;
	margin: 0 auto;
	max-width: 100%;
	height: auto;
}
.ui.pic-4b3.hover-zoom:hover .wrap {
	transform: scale(1.1);
}
.ui.list .item {
	position: relative;
	padding: var(--space-width);
	display: flex;
	flex-flow: row;
}
.ui.list .item .detail {
	flex: 1;
}
.ui.list.double-item-space .item {
	padding: calc(2 * var(--space-width));
}
.ui.list.linear-split .item::after {
	position: absolute;
	content: "";
	display: block;
	height: 1px;
	bottom: 0;
	left: var(--space-width);
	width: calc(100% - 2 * var(--space-width));
	background: var(--border-color);
	background: -webkit-linear-gradient(right, var(--border-color) 0%, transparent 100%);
	background: linear-gradient(to left, var(--border-color) 0%, transparent 100%);
}
.ui.list.linear-split .item:last-child::after {
	display: none;
}
.ui.list.linear-split.double-item-space .item::after {
	left: calc(2 * var(--space-width));
	width: calc(100% - 4 * var(--space-width));
}
.ui.avatar {
	width: var(--button-height);
	height: var(--button-height);
	overflow: hidden;
}
.ui.avatar img {
	width: var(--button-height);
	height: var(--button-height);
}
.ui.avatar.maximum {
	--button-height: var(--button-height-maximum);
}
.ui.avatar.bigger {
	--button-height: var(--button-height-bigger);
}
.ui.avatar.large {
	--button-height: var(--button-height-large);
}
.ui.avatar.small {
	--button-height: var(--button-height-small);
}
.ui.avatar.tiny {
	--button-height: var(--button-height-tiny);
}
.ui.cards {
	overflow: auto;
	padding: var(--space-width-half);
}
.ui.cards .card {
	width: calc(50% - var(--space-width));
	float: left;
	margin: var(--space-width-half);
}
.ui.cards .card .preview {
	display: block;
}
.ui.cards.double-space {
	padding: var(--space-width);
}
.ui.cards.double-space .card {
	margin: var(--space-width);
	width: calc(50% - 2 * var(--space-width));
}
.ui.badge {
	background: var(--color-base);
	color: var(--color-base-contrast);
	width: var(--button-height-small);
	height: var(--button-height-small);
	text-align: center;
	display: flex;
	flex-flow: column;
	justify-content: center;
	font-size: var(--font-size-small);
}
.ui.modal-page {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 1000;
	overflow: hidden;
	display: none;
}
.ui.modal-page .modal-background {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	transition: all 0.3s;
	z-index: 1001;
	opacity: 0;
}
.ui.modal-page .modal-container {
	position: fixed;
	z-index: 1002;
	left: -300px;
	width: 300px;
	height: 100%;
	background: var(--color-base);
	color: var(--color-base-contrast);
	overflow: hidden;
	transition: all 0.3s;
}
.ui.modal-page.show {
	display: block;
}
.ui.modal-page.open .modal-background {
	opacity: 1;
}
.ui.modal-page.open .modal-container {
	left: 0;
}
.ui.modal-page.close .modal-background {
	opacity: 0;
}
.ui.modal-page.close .modal-container {
	left: -300px;
}
.ui.modal-page.right .modal-container {
	left: auto;
	right: -300px;
}
.ui.modal-page.right.open .modal-container {
	right: 0;
}
.ui.modal-page.right.close .modal-container {
	right: -300px;
}
.ui.input {
	height: var(--button-height);
	border: solid 1px var(--color-medium);
	background: transparent;
	transition: all 0.3s;
	display: flex;
	flex-flow: row;
}
.ui.input input {
	flex: 1;
	background: transparent;
	border: none;
	outline: none;
	height: 100%;
	padding: 0 1rem;
	color: var(--base-contrast, var(--color-base-contrast));
}
.ui.input:focus-within {
	border-color: var(--color-primary);
	box-shadow: 0 0 5px var(--color-primary);
}
.ui.tree ul {
	margin: 0;
	padding: 0;
}
.ui.tree ul li {
	list-style-type: none;
}
.ui.tree ul li .item {
	position: relative;
}
.ui.tree ul li ul {
	padding-left: 20px;
}
.ui.tree.linear-split .item::after {
	position: absolute;
	content: "";
	display: block;
	height: 1px;
	bottom: 0;
	left: var(--space-width);
	width: calc(100% - 2 * var(--space-width));
	background: var(--border-color);
	background: -webkit-linear-gradient(right, var(--border-color) 0%, transparent 100%);
	background: linear-gradient(to left, var(--border-color) 0%, transparent 100%);
}
.ui.breadcrumb .item {
	display: inline-block;
	color: var(--color-medium);
}
.ui.breadcrumb .item a {
	color: var(--color-medium);
}
.ui.breadcrumb .item a:hover {
	color: var(--color-primary);
}
.ui.breadcrumb .item::after {
	content: " / ";
	color: var(--color-medium);
	margin: 0 5px;
}
.ui.breadcrumb .item:last-child::after {
	display: none;
}
.ui.breadcrumb .item:last-child {
	color: var(--color-base-contrast);
	font-weight: bold;
}
.ui.divider {
	display: flex;
	white-space: nowrap;
	text-align: center;
}
.ui.divider::before, .ui.divider::after {
	position: relative;
	content: "";
	display: inline-block;
	border-top: solid 1px var(--border-color);
	width: 50%;
	transform: translateY(50%);
}
.ui.divider::before {
	margin-right: var(--space-width);
}
.ui.divider::after {
	margin-left: var(--space-width);
}
.ui.divider.mini {
	display: inline-flex;
}
.ui.divider.mini::before, .ui.divider.mini::after {
	width: 30px;
}
.ui.pagination .page-item {
	display: inline-flex;
	flex-flow: column;
	justify-content: center;
	height: 32px;
	border: solid 1px var(--border-color);
	background: var(--light-background);
	text-align: center;
	width: 32px;
	border-radius: var(--radius-width);
	cursor: pointer;
	transition: 300ms;
	margin: 0 3px;
	line-height: 30px;
}
.ui.pagination .page-item:hover, .ui.pagination .page-item.current {
	border-color: var(--color-primary);
	color: var(--color-primary);
	text-decoration: none;
}
.ui.pagination .page-item.current {
	background: var(--color-primary);
	color: var(--color-primary-contrast);
}
@keyframes rotate {
	100% {
		transform: rotate(360deg);
	}
}
.padding {
	padding: var(--space-width);
}
.padding-start, .padding-left {
	padding-left: var(--space-width);
}
.padding-end, .padding-right {
	padding-right: var(--space-width);
}
.padding-top {
	padding-top: var(--space-width);
}
.padding-bottom {
	padding-bottom: var(--space-width);
}
.padding-half {
	padding: var(--space-width-half);
}
.padding-start-half, .padding-left-half {
	padding-left: var(--space-width-half);
}
.padding-end-half, .padding-right-half {
	padding-right: var(--space-width-half);
}
.padding-top-half {
	padding-top: var(--space-width-half);
}
.padding-bottom-half {
	padding-bottom: var(--space-width-half);
}
.margin {
	margin: var(--space-width);
}
.margin-start, .margin-left {
	margin-left: var(--space-width);
}
.margin-end, .margin-right {
	margin-right: var(--space-width);
}
.margin-top {
	margin-top: var(--space-width);
}
.margin-bottom {
	margin-bottom: var(--space-width);
}
.margin-half {
	margin: var(--space-width-half);
}
.margin-start-half, .margin-left-half {
	margin-left: var(--space-width-half);
}
.margin-end-half, .margin-right-half {
	margin-right: var(--space-width-half);
}
.margin-top-half {
	margin-top: var(--space-width-half);
}
.margin-bottom-half {
	margin-bottom: var(--space-width-half);
}
.auto-margin {
	margin: 0 auto;
}
.overflow-hidden {
	overflow: hidden;
}
.justify-center {
	display: flex;
	flex-flow: column;
	justify-content: center;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}
.text-bold {
	font-weight: bold;
}
.delete-text {
	text-decoration: line-through;
}
.max-one-line-text, .text-list li {
	overflow: hidden;
	white-space: normal;
	text-overflow: ellipsis;
	word-break: break-all;
	display: -webkit-box !important;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}
.max-two-line-text {
	overflow: hidden;
	white-space: normal;
	text-overflow: ellipsis;
	word-break: break-all;
	display: -webkit-box !important;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.max-three-line-text {
	overflow: hidden;
	white-space: normal;
	text-overflow: ellipsis;
	word-break: break-all;
	display: -webkit-box !important;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}
.border {
	border: solid 1px var(--border-color);
}
.border-top {
	border-top: solid 1px var(--border-color);
}
.border-bottom {
	border-bottom: solid 1px var(--border-color);
}
.border-left, .border-start {
	border-left: solid 1px var(--border-color);
}
.border-right, .border-end {
	border-right: solid 1px var(--border-color);
}
.radius {
	border-radius: var(--radius-width);
	overflow: hidden;
}
.round {
	border-radius: 100px;
	overflow: hidden;
}
.circle {
	border-radius: 50%;
	overflow: hidden;
}
.inline-flex {
	display: inline-flex !important;
}
.fixed-menu {
	position: fixed;
	z-index: 10;
	right: 50px;
	bottom: 100px;
	width: 48px;
}
.fixed-menu .item {
	height: 48px;
	background: var(--background-light);
	border: solid 1px var(--border-color);
	border-bottom: none;
	display: flex;
	flex-flow: column;
	justify-content: center;
	text-align: center;
	cursor: pointer;
	color: var(--color-dark);
	transition: 300ms;
	position: relative;
}
.fixed-menu .item .hover-display {
	display: none;
	position: absolute;
	right: 48px;
	top: 0;
	width: 150px;
	height: 150px;
	background: #fff;
}
.fixed-menu .item .hover-display img {
	width: 100%;
	height: 100%;
	display: block;
	margin: 0;
}
.fixed-menu .item .hover-display.url-qrcode {
	padding: var(--space-width);
}
.fixed-menu .item:last-child {
	border-bottom: solid 1px var(--border-color);
}
.fixed-menu .item:hover {
	background: var(--color-primary);
	color: var(--color-primary-contrast);
	border-color: var(--color-primary);
}
.fixed-menu .item:hover .hover-display {
	display: block;
}
body {
	margin: 0;
	padding: 0;
	font-size: 14px;
	--font-size: 14px;
	--color-primary: #534686;
	--color-primary-light: #8071bd;
	--color-primary-contrast: #ffffff;
	--background: #eef2f8;
	background: var(--background);
	--radius-width: 3px;
	color: var(--color-dark);
}
.text-default-size {
	font-size: 14px;
}
ul {
	list-style-type: none;
	margin: 0;
	padding: 0;
}
a {
	color: var(--color-dark);
	text-decoration: none;
	transition: 300ms;
}
a:hover {
	color: var(--color-primary);
	text-decoration: underline;
}
.container {
	max-width: 1100px;
	width: 100%;
	margin: 0 auto;
}
header {
	background: var(--background-light);
	padding: 20px var(--space-width);
}
header .container {
	height: 45px;
	overflow: visible;
}
header .web-logo img {
	height: 45px;
	display: block;
	margin: 0;
}
header .web-name {
	display: flex;
	flex-flow: column;
	justify-content: center;
	font-size: 36px;
}
header .web-name:hover {
	text-decoration: none;
}
header nav {
	padding-left: 20px;
}
header nav .item {
	display: inline-flex;
	height: 36px;
	flex-flow: column;
	justify-content: center;
	position: relative;
	padding: 0 15px;
	transition: 300ms;
	font-size: 16px;
	color: var(--color-medium);
}
header nav .item:hover {
	text-decoration: none;
	color: var(--color-dark);
}
header nav .item:after {
	display: block;
	content: "";
	position: absolute;
	bottom: 0;
	width: calc(100% - 30px);
	left: 15px;
	height: 1px;
	background: transparent;
	transition: 300ms;
}
header nav .item.active {
	color: var(--color-primary);
	font-weight: bold;
}
header nav .item.active:after {
	background: var(--color-primary);
}
footer {
	background: var(--background-light);
	padding: 20px var(--space-width);
}
.links {
	padding: var(--space-width-half);
}
.links a {
	margin: var(--space-width-half);
	display: inline-block;
}
.panel-block {
	background: var(--background-light);
	border-radius: var(--radius-width);
	padding-top: var(--space-width);
}
.panel-block .title {
	font-size: 20px;
	font-weight: bold;
	margin: 0 var(--space-width);
	padding-left: var(--space-width);
	border-left: solid 3px var(--color-primary);
	line-height: 22px;
}
.panel-block.no-prefix .title {
	padding-left: 0;
	border-left: none;
}
.main-warp {
	padding: var(--space-width);
}
.slide-wrap {
	display: flex;
	flex-flow: row;
}
.slide-wrap .slide {
	margin-right: var(--space-width);
	flex: 1;
	position: relative;
}
.slide-wrap .slide:before {
	content: "";
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	z-index: 0;
	height: calc(100% - 20px);
	background: var(--color-primary);
	border-radius: var(--radius-width);
}
.slide-wrap .slide .text {
	position: absolute;
	z-index: 2;
	left: 0;
	top: 0;
	padding-left: 30px;
	color: var(--color-primary-contrast);
	width: 100%;
	height: 100%;
	font-size: 36px;
	display: flex;
	flex-flow: column;
	justify-content: center;
}
.slide-wrap .slide .image {
	text-align: right;
	position: relative;
	z-index: 1;
}
.slide-wrap .panel-block {
	margin-top: 20px;
	width: 30%;
}
.text-list {
	list-style-type: none;
	margin: 0;
	padding: 0;
}
.text-list li {
	margin: var(--space-width);
	vertical-align: top;
	line-height: 16px;
}
.text-list li .tag {
	display: inline-flex;
	width: 20px;
	font-size: 0.8em;
	background: var(--color-medium);
	color: var(--color-medium-contrast);
	text-align: center;
	flex-flow: column;
	justify-content: center;
}
.text-list li:nth-child(1) .tag {
	background: red;
	color: #fff;
}
.text-list li:nth-child(2) .tag {
	background: #ff7300;
	color: #fff;
}
.text-list li:nth-child(3) .tag {
	background: #24850d;
	color: #fff;
}
.tab-list .tab-row {
	display: flex;
	flex-flow: row;
}
.tab-list .tab-row .col {
	flex: 1;
	height: 60px;
	display: flex;
	flex-flow: row;
	transition: 300ms;
	cursor: pointer;
	margin: 0 var(--space-width-half);
	padding: 0 15px;
	border-radius: 10px 10px 0 0;
}
.tab-list .tab-row .col:first-child {
	margin-left: 0;
}
.tab-list .tab-row .col:last-child {
	margin-right: 0;
}
.tab-list .tab-row .col .name {
	flex: 1;
	font-size: 24px;
	display: none !important;
	position: relative;
	padding-left: 15px;
	font-weight: bold;
}
.tab-list .tab-row .col .name:before {
	content: "";
	display: block;
	position: absolute;
	left: 0;
	height: 22px;
	width: 3px;
	background: var(--color-primary);
	color: var(--color-primary);
}
.tab-list .tab-row .col .icon {
	font-size: 20px;
	text-align: center;
	flex: 1;
}
.tab-list .tab-row .col .icon .svg-icon {
	margin: 0 auto;
}
.tab-list .tab-row .col:hover {
	background: rgba(255, 255, 255, 0.5);
}
.tab-list .tab-row .col .name, .tab-list .tab-row .col .icon {
	display: flex;
	flex-flow: column;
	justify-content: center;
}
.tab-list .tab-row .col.active {
	cursor: default;
	background: var(--background-light) !important;
}
.tab-list .tab-row .col.active .name {
	display: flex !important;
}
.tab-list .tab-row .col.active .icon {
	flex: unset;
}
.tab-list .tab-content {
	padding: var(--space-width-half) 0;
	background: var(--background-light);
}
.tab-list .tab-content .text-list {
	display: block;
	overflow: auto;
	padding: var(--space-width-half);
}
.tab-list .tab-content .text-list li {
	width: calc(50% - var(--space-width));
	float: left;
	margin: var(--space-width-half);
}
.tab-list .tab-content .text-list.active {
	display: block;
}
.list-page, .detail-page {
	padding: var(--space-width);
	width: 100%;
	max-width: 1100px;
	margin: 0 auto;
	display: flex;
	flex-flow: row;
}
.list-page main, .detail-page main {
	flex: 1;
	background: var(--background-light);
	border-radius: var(--radius-width);
	margin-right: var(--space-width);
	padding-top: var(--space-width);
}
.list-page aside, .detail-page aside {
	width: 30%;
}
.list-page main .title {
	font-size: 20px;
	font-weight: bold;
	margin: 0 var(--space-width);
	padding-left: var(--space-width);
	border-left: solid 3px var(--color-primary);
	line-height: 22px;
}
.list-page main .list .item {
	transition: 300ms;
	background-image: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 100%);
}
.list-page main .list .item:hover {
	background-image: linear-gradient(to right, #ede9f8 0%, rgba(255, 255, 255, 0) 100%);
}
.dk-list .item {
	border-bottom: dotted 1px var(--border-color);
	padding: var(--space-width);
	display: flex;
	flex-flow: row;
	transition: 300ms;
	background-image: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 100%);
}
.dk-list .item:hover {
	background-image: linear-gradient(to right, #ede9f8 0%, rgba(255, 255, 255, 0) 100%);
}
.dk-list .item:last-child {
	border-bottom: none;
}
.dk-list .item .center {
	flex: 1;
	padding: 0 var(--space-width);
}
.detail-page main .text-list {
	overflow: auto;
	padding: var(--space-width-half);
}
.detail-page main .text-list li {
	width: calc(50% - var(--space-width));
	float: left;
	margin: var(--space-width-half);
}
.detail-page .article-title {
	padding-bottom: var(--space-width);
}
.detail-page .article-title .block {
	background: var(--color-primary);
	color: var(--color-primary-contrast);
	text-align: center;
	padding: var(--space-width) 0;
	display: flex;
	flex-flow: column;
	justify-content: center;
	min-width: 60px;
	border-radius: var(--radius-width);
}
.detail-page .article-content {
	background-image: linear-gradient(to bottom, #ff9868 0%, #ffbea0 10px, var(--background-light) 120px);
	padding: var(--space-width);
	margin: 0 var(--space-width);
	border-radius: var(--radius-width);
}
@media (prefers-color-scheme: dark) {
	body {
		--background: #142030;
		--color-primary: #fe4f70;
		--color-primary-light: #ff7f97;
		--color-primary-contrast: #ffffff;
	}
	.detail-page .article-content {
		background-image: linear-gradient(to bottom, var(--color-primary) 0%, var(--color-primary-light) 10px, var(--background-light) 120px);
	}
	.list-page main .list .item:hover, .dk-list .item:hover {
		background-image: linear-gradient(to right, rgba(254, 79, 112, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
	}
	.tab-list .tab-row .col:hover {
		background: rgba(254, 79, 112, 0.1);
	}
}
header .ext-menu {
	display: none;
}
@media (max-width: 920px) {
	header .search .input {
		display: none !important;
	}
}
@media (max-width: 690px) {
	header nav {
		display: none;
	}
	header .ext-menu {
		display: flex;
	}
	.slide-wrap .slide {
		display: none;
	}
	.slide-wrap .panel-block {
		width: 100%;
		margin-top: 0;
	}
	.tab-list .tab-row .col .name {
		display: none !important;
	}
	.tab-list .tab-row .col.active .name {
		display: none !important;
	}
	.tab-list .tab-row .col.active .icon {
		flex: 1;
	}
	.tab-list .tab-content .text-list li, .detail-page main .text-list li {
		margin: var(--space-width) var(--space-width-half);
		width: calc(100% - var(--space-width));
		float: none;
	}
	.fixed-menu {
		right: 10px;
	}
	.detail-page, .list-page {
		display: block;
	}
	.detail-page main, .list-page main {
		margin-right: 0;
		margin-bottom: var(--space-width);
	}
	.detail-page aside, .list-page aside {
		width: 100%;
	}
	.list-page .list .item .start {
		width: 80px !important;
	}
	.list-page .list .item .detail .flex-item:last-child {
		display: none;
	}
	.detail-page .article-title {
		display: block;
		padding-bottom: 0;
	}
	.detail-page .article-title .center {
		padding: var(--space-width) 0;
	}
	.detail-page .article-title .center .flex-item {
		display: block;
		text-align: center;
	}
	.detail-page .article-title .center .flex-item .avatar {
		margin: 5px auto;
	}
	.detail-page .article-content .flex-item {
		display: block;
		text-align: center;
	}
	.detail-page .article-content .flex-item * {
		padding: 0;
	}
}
/*# sourceMappingURL=style.css.map */
