.pages {
    color: purple;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pages span {
    margin: 0 5px; /* 给span元素添加一些边距 */
}

.pages .total {
    color: red;
}

/* 分页按钮的基础样式 */
.pages .page-button {
    padding: 8px 15px; /* 添加内边距 */
    margin: 0 3px; /* 按钮之间的间距 */
    background-color: #e0e0e0; /* 背景色 */
    border: none; /* 无边框 */
    border-radius: 4px; /* 边框圆角 */
    cursor: pointer; /* 鼠标悬停时显示为手形 */
    display: inline-block; /* 内联块显示 */
    text-align: center; /* 文本居中 */
}

/* 鼠标悬停在分页按钮上时的样式 */
.pages .page-button:hover {
    background-color: #d1d1d1; /* 鼠标悬停时的背景色 */
}

/* 当前激活的分页按钮的样式 */
.pages .page-button.active {
    background-color: #4CAF50; /* 激活状态的背景色 */
    color: white; /* 文本颜色 */
}