@import "../../../../ui/ui";
body{
  margin: 0;
  padding: 0;
  font-size: 14px;
  --font-size: 14px;
  //主色
  --color-primary: #534686;
  --color-primary-light: #8071bd;
  --color-primary-contrast: #ffffff;
  //定义新色
  --background: #eef2f8;
  background: var(--background);
  --radius-width: 3px;
  color: var(--color-dark);
}
.text-default-size{
  font-size: 14px;
}
ul{
  list-style-type: none;
  margin: 0;
  padding: 0;
}
a{
  color: var(--color-dark);
  text-decoration: none;
  transition: 300ms;
  &:hover{
    color: var(--color-primary);
    text-decoration: underline;
  }
}
.container{
  max-width: 1100px;
  width: 100%;
  margin: 0 auto;
}

header{
  background: var(--background-light);
  padding: 20px var(--space-width);
  .container{height: 45px;overflow: visible;}
  .web-logo{
    img{height: 45px;display: block;margin: 0;}
  }
  .web-name{
    display: flex;
    flex-flow: column;
    justify-content: center;
    font-size: 36px;
    &:hover{
      text-decoration: none;
    }
  }
  .search{

  }
  nav{
    padding-left: 20px;
    .item{display: inline-flex;
      height: 36px;
      flex-flow: column;
      justify-content: center;
      position: relative;
      padding: 0 15px;
      transition: 300ms;
      font-size: 16px;
      color: var(--color-medium);
      &:hover{text-decoration: none;color: var(--color-dark);}
      &:after{
        display: block;
        content: '';
        position: absolute;
        bottom: 0;
        width: calc(100% - 30px);
        left: 15px;
        height: 1px;
        background: transparent;
        transition: 300ms;
      }
      &.active{
        color: var(--color-primary);
        font-weight: bold;
        &:after{
          background: var(--color-primary);
        }
      }
    }
  }
}
footer{
  background: var(--background-light);
  padding: 20px var(--space-width);
}
.links{
  padding: var(--space-width-half);
  a{margin: var(--space-width-half);display: inline-block;}
}
.panel-block{
  background: var(--background-light);
  border-radius: var(--radius-width);
  padding-top: var(--space-width);
  .title{
    font-size: 20px;
    font-weight: bold;
    margin: 0 var(--space-width);
    padding-left: var(--space-width);
    border-left: solid 3px var(--color-primary);
    line-height: 22px;
  }
  &.no-prefix{
    .title{
      padding-left: 0;
      border-left: none;
    }
  }
}
.main-warp{
  padding: var(--space-width);
}
.slide-wrap{
  display: flex;
  flex-flow: row;
  .slide{
    margin-right: var(--space-width);
    flex: 1;
    position: relative;
    &:before{
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      z-index: 0;
      height: calc(100% - 20px);
      background: var(--color-primary);
      border-radius: var(--radius-width);
    }
    .text{
      position: absolute;
      z-index: 2;
      left: 0;
      top: 0;padding-left: 30px;
      color: var(--color-primary-contrast);
      width: 100%;
      height: 100%;
      font-size: 36px;
      display: flex;
      flex-flow: column;
      justify-content: center;
    }
    .image{text-align: right;position: relative;z-index: 1;}
  }
  .panel-block{
    margin-top: 20px;
    width: 30%;
  }
}

.text-list{
  list-style-type: none;
  margin: 0;
  padding: 0;
  li{
    margin: var(--space-width);
    vertical-align: top;
    @extend .max-one-line-text;
    line-height: 16px;
    .tag{
      display: inline-flex;
      width: 20px;
      font-size: .8em;
      background: var(--color-medium);
      color: var(--color-medium-contrast);
      text-align: center;
      flex-flow: column;
      justify-content: center;
    }
    &:nth-child(1) .tag{background:red;color: #fff;}
    &:nth-child(2) .tag{background: #ff7300;color: #fff;}
    &:nth-child(3) .tag{background: #24850d;color: #fff;}
  }
}

.tab-list{
  .tab-row{
    display: flex;
    flex-flow: row;
    .col{
      flex: 1;
      height: 60px;
      display: flex;
      flex-flow: row;
      transition: 300ms;
      cursor: pointer;
      margin: 0 var(--space-width-half);
      &:first-child{margin-left: 0;}
      &:last-child{margin-right: 0;}
      .name{
        flex: 1;
        font-size: 24px;
        display: none!important;
        position: relative;
        padding-left: 15px;
        font-weight: bold;
        &:before{
          content: '';
          display: block;
          position: absolute;
          left: 0;
          height: 22px;
          width: 3px;
          background: var(--color-primary);
          color: var(--color-primary);
        }
      }
      .icon{
        font-size: 36px;
        text-align: center;
        flex: 1;
        .svg-icon{
          margin: 0 auto;
        }
      }
      &:hover{
        background: rgba(255,255,255,.5);
      }
      .name, .icon{
        display: flex;
        flex-flow: column;
        justify-content: center;
      }
      padding: 0 15px;
      border-radius: 10px 10px 0 0;
      &.active{
        cursor: default;
        background: var(--background-light)!important;
        .name{
          display: flex!important;
        }
        .icon{
          flex: unset;
        }
      }
    }
  }
  .tab-content{
    padding: var(--space-width-half) 0;
    background: var(--background-light);
    .text-list{
      display: none;
      overflow: auto;
      padding: var(--space-width-half);
      li{
        width: calc(50% - var(--space-width));float: left;
        margin: var(--space-width-half);
      }
      &.active{
        display: block;
      }
    }
  }
}

.list-page, .detail-page{
  padding: var(--space-width);
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  display: flex;
  flex-flow: row;
  main{
    flex: 1;
    background: var(--background-light);
    border-radius: var(--radius-width);
    margin-right: var(--space-width);
    padding-top: var(--space-width);
  }
  aside{
    width: 30%;
  }
}
//列表页
.list-page{
  main{
    .title{
      font-size: 20px;
      font-weight: bold;
      margin: 0 var(--space-width);
      padding-left: var(--space-width);
      border-left: solid 3px var(--color-primary);
      line-height: 22px;
    }
    .list{
      .item{
        transition: 300ms;
        background-image: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 100%);
        &:hover{
          background-image: linear-gradient(to right, #ede9f8 0%, rgba(255,255,255,0) 100%);
        }
      }
    }
  }
}

.dk-list{
  .item{
    border-bottom: dotted 1px var(--border-color);
    padding: var(--space-width);
    display: flex;
    flex-flow: row;
    transition: 300ms;
    background-image: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 100%);
    &:hover{
      background-image: linear-gradient(to right, #ede9f8 0%, rgba(255,255,255,0) 100%);
    }
    &:last-child{
      border-bottom: none;
    }
    .center{flex: 1;padding: 0 var(--space-width);}
  }
}

.detail-page{
  main{
    .text-list{
      overflow: auto;
      padding: var(--space-width-half);
      li{
        width: calc(50% - var(--space-width));float: left;
        margin: var(--space-width-half);
      }
    }
  }
  .article-title{
    padding-bottom: var(--space-width);
    .block{
      background: var(--color-primary);
      color: var(--color-primary-contrast);
      text-align: center;
      padding: var(--space-width) 0;
      display: flex;
      flex-flow: column;
      justify-content: center;
      min-width: 60px;
      border-radius: var(--radius-width);
    }
  }
  .article-content{
    background-image: linear-gradient(to bottom, #ff9868 0%, #ffbea0 10px, var(--background-light) 120px);
    padding: var(--space-width);
    margin: 0 var(--space-width);
    border-radius: var(--radius-width);
  }
}

@media (prefers-color-scheme: dark) {
  body {
    --background: #142030;
    //主色
    --color-primary: #fe4f70;
    --color-primary-light: #ff7f97;
    --color-primary-contrast: #ffffff;
  }
  .detail-page .article-content{
    background-image: linear-gradient(to bottom, var(--color-primary) 0%, var(--color-primary-light) 10px, var(--background-light) 120px);
  }
  .list-page main .list .item, .dk-list .item{
    &:hover{
      background-image: linear-gradient(to right, rgba(254, 79, 112, 0.1) 0%, rgba(255,255,255,0) 100%);
    }
  }
  .tab-list .tab-row .col:hover{
    background: rgba(254, 79, 112, 0.1);
  }
}

header .ext-menu{display: none;}
@media (max-width: 920px) {
  header .search .input{display: none!important;}
}
@media (max-width: 690px) {
  header nav{display: none;}
  header .ext-menu{display: flex;}
  .slide-wrap .slide{display: none;}
  .slide-wrap .panel-block{width: 100%;margin-top: 0;}
  //拆分
  .tab-list .tab-row .col .name{display: none!important;}
  .tab-list .tab-row .col.active .name{display: none!important;}
  .tab-list .tab-row .col.active .icon{flex: 1;}
  .tab-list .tab-content .text-list li, .detail-page main .text-list li{
    margin: var(--space-width) var(--space-width-half);
    width: calc(100% - var(--space-width));
    float: none;
  }
  .fixed-menu{
    right: 10px;
  }
  .detail-page, .list-page{
    display: block;
    main{margin-right: 0; margin-bottom: var(--space-width);}
    aside{width: 100%;}
  }
  .list-page .list .item .start{
    width: 80px!important;
  }
  .list-page .list .item .detail .flex-item:last-child{
    display: none;
  }
  .detail-page .article-title{
    display: block;
    padding-bottom: 0;
    .center{
      padding: var(--space-width) 0;
      .flex-item{display: block;text-align: center;
        .avatar{margin: 5px auto;}
      }
    }
  }
  .detail-page .article-content{
    .flex-item{
      display: block;
      text-align: center;
      *{padding: 0;}
    }
  }
}