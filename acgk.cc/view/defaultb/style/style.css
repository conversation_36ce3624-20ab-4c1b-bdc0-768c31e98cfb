html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
}

article,
footer,
header,
main,
menu,
nav,
section,
summary {
  display: block;
}

ul,
li {
  list-style: none;
}

img {
  border: 0;
}

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button {
  text-transform: none;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

html {
  font-size: 10px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857143;
  color: #333333;
  background-color: #f2f5f7;
}

input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

a {
  color: #333;
  text-decoration: none;
}

a:hover,
a:focus {
  color: #3297fc;
  text-decoration: none;
}

a:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

figure {
  margin: 0;
}

img {
  vertical-align: middle;
}


h4,
.h4 {
  font-size: 18px;
}

h5,
.h5 {
  font-size: 14px;
}

h6,
.h6 {
  font-size: 12px;
}

p {
  margin: 0 0 10px;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.text-muted {
  color: #777777;
}

.text-primary {
  color: #337ab7;
}

a.text-primary:hover,
a.text-primary:focus {
  color: #286090;
}

.text-success {
  color: #3c763d;
}

a.text-success:hover,
a.text-success:focus {
  color: #2b542c;
}

.text-info {
  color: #31708f;
}

a.text-info:hover,
a.text-info:focus {
  color: #245269;
}

.text-warning {
  color: #8a6d3b;
}

a.text-warning:hover,
a.text-warning:focus {
  color: #66512c;
}

.text-danger {
  color: #a94442;
}

a.text-danger:hover,
a.text-danger:focus {
  color: #843534;
}

.bg-primary {
  color: #fff;
  background-color: #337ab7;
}

a.bg-primary:hover,
a.bg-primary:focus {
  background-color: #286090;
}

.bg-success {
  background-color: #dff0d8;
}

a.bg-success:hover,
a.bg-success:focus {
  background-color: #c1e2b3;
}

.bg-info {
  background-color: #d9edf7;
}

a.bg-info:hover,
a.bg-info:focus {
  background-color: #afd9ee;
}

.bg-warning {
  background-color: #fcf8e3;
}

a.bg-warning:hover,
a.bg-warning:focus {
  background-color: #f7ecb5;
}

.bg-danger {
  background-color: #f2dede;
}

a.bg-danger:hover,
a.bg-danger:focus {
  background-color: #e4b9b9;
}

ul,
ol {
  margin-top: 0;
  margin-bottom: 10px;
}

ul ul,
ol ul,
ul ol,
ol ol {
  margin-bottom: 0;
}


dl {
  margin-top: 0;
  margin-bottom: 20px;
}

dt,
dd {
  line-height: 1.42857143;
}

dt {
  font-weight: bold;
}

dd {
  margin-left: 0;
}

.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}

@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}

@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}

@media (min-width: 1200px) {
  .container {
    width: 1180px;
  }
}



.row {
  margin-left: -15px;
  margin-right: -15px;
}

.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 5px;
  padding-right: 5px;
}

.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
  float: left;
}

.col-xs-12 {
  width: 100%;
}

.col-xs-11 {
  width: 91.66666667%;
}

.col-xs-10 {
  width: 83.33333333%;
}

.col-xs-9 {
  width: 75%;
}

.col-xs-8 {
  width: 66.66666667%;
}

.col-xs-7 {
  width: 58.33333333%;
}

.col-xs-6 {
  width: 50%;
}

.col-xs-5 {
  width: 41.66666667%;
}

.col-xs-4 {
  width: 33.33333333%;
}

.col-xs-3 {
  width: 25%;
}

.col-xs-2 {
  width: 16.66666667%;
}

.col-xs-1 {
  width: 8.33333333%;
}

.col-xs-pull-12 {
  right: 100%;
}

.col-xs-pull-11 {
  right: 91.66666667%;
}

.col-xs-pull-10 {
  right: 83.33333333%;
}

.col-xs-pull-9 {
  right: 75%;
}

.col-xs-pull-8 {
  right: 66.66666667%;
}

.col-xs-pull-7 {
  right: 58.33333333%;
}

.col-xs-pull-6 {
  right: 50%;
}

.col-xs-pull-5 {
  right: 41.66666667%;
}

.col-xs-pull-4 {
  right: 33.33333333%;
}

.col-xs-pull-3 {
  right: 25%;
}

.col-xs-pull-2 {
  right: 16.66666667%;
}

.col-xs-pull-1 {
  right: 8.33333333%;
}

.col-xs-pull-0 {
  right: auto;
}

.col-xs-push-12 {
  left: 100%;
}

.col-xs-push-11 {
  left: 91.66666667%;
}

.col-xs-push-10 {
  left: 83.33333333%;
}

.col-xs-push-9 {
  left: 75%;
}

.col-xs-push-8 {
  left: 66.66666667%;
}

.col-xs-push-7 {
  left: 58.33333333%;
}

.col-xs-push-6 {
  left: 50%;
}

.col-xs-push-5 {
  left: 41.66666667%;
}

.col-xs-push-4 {
  left: 33.33333333%;
}

.col-xs-push-3 {
  left: 25%;
}

.col-xs-push-2 {
  left: 16.66666667%;
}

.col-xs-push-1 {
  left: 8.33333333%;
}

.col-xs-push-0 {
  left: auto;
}

.col-xs-offset-12 {
  margin-left: 100%;
}

.col-xs-offset-11 {
  margin-left: 91.66666667%;
}

.col-xs-offset-10 {
  margin-left: 83.33333333%;
}

.col-xs-offset-9 {
  margin-left: 75%;
}

.col-xs-offset-8 {
  margin-left: 66.66666667%;
}

.col-xs-offset-7 {
  margin-left: 58.33333333%;
}

.col-xs-offset-6 {
  margin-left: 50%;
}

.col-xs-offset-5 {
  margin-left: 41.66666667%;
}

.col-xs-offset-4 {
  margin-left: 33.33333333%;
}

.col-xs-offset-3 {
  margin-left: 25%;
}

.col-xs-offset-2 {
  margin-left: 16.66666667%;
}

.col-xs-offset-1 {
  margin-left: 8.33333333%;
}

.col-xs-offset-0 {
  margin-left: 0%;
}

@media (min-width: 992px) {

  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12 {
    float: left;
  }

  .col-md-12 {
    width: 100%;
  }

  .col-md-11 {
    width: 91.66666667%;
  }

  .col-md-10 {
    width: 83.33333333%;
  }

  .col-md-9 {
    width: 75%;
  }

  .col-md-8 {
    width: 66.66666667%;
  }

  .col-md-7 {
    width: 58.33333333%;
  }

  .col-md-6 {
    width: 50%;
  }

  .col-md-5 {
    width: 41.66666667%;
  }

  .col-md-4 {
    width: 33.33333333%;
  }

  .col-md-3 {
    width: 25%;
  }

  .col-md-2 {
    width: 16.66666667%;
  }

  .col-md-1 {
    width: 8.33333333%;
  }

  .col-md-pull-12 {
    right: 100%;
  }

  .col-md-pull-11 {
    right: 91.66666667%;
  }

  .col-md-pull-10 {
    right: 83.33333333%;
  }

  .col-md-pull-9 {
    right: 75%;
  }

  .col-md-pull-8 {
    right: 66.66666667%;
  }

  .col-md-pull-7 {
    right: 58.33333333%;
  }

  .col-md-pull-6 {
    right: 50%;
  }

  .col-md-pull-5 {
    right: 41.66666667%;
  }

  .col-md-pull-4 {
    right: 33.33333333%;
  }

  .col-md-pull-3 {
    right: 25%;
  }

  .col-md-pull-2 {
    right: 16.66666667%;
  }

  .col-md-pull-1 {
    right: 8.33333333%;
  }

  .col-md-pull-0 {
    right: auto;
  }

  .col-md-push-12 {
    left: 100%;
  }

  .col-md-push-11 {
    left: 91.66666667%;
  }

  .col-md-push-10 {
    left: 83.33333333%;
  }

  .col-md-push-9 {
    left: 75%;
  }

  .col-md-push-8 {
    left: 66.66666667%;
  }

  .col-md-push-7 {
    left: 58.33333333%;
  }

  .col-md-push-6 {
    left: 50%;
  }

  .col-md-push-5 {
    left: 41.66666667%;
  }

  .col-md-push-4 {
    left: 33.33333333%;
  }

  .col-md-push-3 {
    left: 25%;
  }

  .col-md-push-2 {
    left: 16.66666667%;
  }

  .col-md-push-1 {
    left: 8.33333333%;
  }

  .col-md-push-0 {
    left: auto;
  }

  .col-md-offset-12 {
    margin-left: 100%;
  }

  .col-md-offset-11 {
    margin-left: 91.66666667%;
  }

  .col-md-offset-10 {
    margin-left: 83.33333333%;
  }

  .col-md-offset-9 {
    margin-left: 75%;
  }

  .col-md-offset-8 {
    margin-left: 66.66666667%;
  }

  .col-md-offset-7 {
    margin-left: 58.33333333%;
  }

  .col-md-offset-6 {
    margin-left: 50%;
  }

  .col-md-offset-5 {
    margin-left: 41.66666667%;
  }

  .col-md-offset-4 {
    margin-left: 33.33333333%;
  }

  .col-md-offset-3 {
    margin-left: 25%;
  }

  .col-md-offset-2 {
    margin-left: 16.66666667%;
  }

  .col-md-offset-1 {
    margin-left: 8.33333333%;
  }

  .col-md-offset-0 {
    margin-left: 0%;
  }
}

div,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
li,
form,
label,
input,
textarea,
button,
img,
span,
dl,
dt,
dd,
th,
pre {
  margin: 0;
  padding: 0;
  outline: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.clearfix:before,
.clearfix:after,
.dl-horizontal dd:before,
.dl-horizontal dd:after,
.container:before,
.container:after,
.container-fluid:before,
.container-fluid:after,
.row:before,
.row:after {
  content: " ";
  display: table;
}

.clearfix:after,
.dl-horizontal dd:after,
.container:after,
.container-fluid:after,
.row:after {
  clear: both;
}


.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.hidden {
  display: none !important;
}

.affix {
  position: fixed;
}

.th_padding,
.left_padding {
  padding-left: 0;
  padding-right: 0;
}



.th_padding_left {
  padding-left: 0;
}

.th_padding_right {
  padding-right: 0;
}

.th_margintop {
  margin-top: 15px;
}

/* ÃƒÂ§Ã‚Â½Ã‚Â®ÃƒÂ©Ã‚Â¡Ã‚Â¶ */
.thgotop {
  position: fixed;
  left: 50%;
  margin-left: 615px;
  bottom: 100px;
  z-index: 99;
}

.thgotop ul li {
  width: 40px;
  height: 40px;
  background: #fff;
  margin-top: 10px;
  box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #fff;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: pointer;
}

.ditop {
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  transition: 0.3s ease-out;
  position: relative;
}

.ditop i {
  display: inline-block;
  margin-top: 5px;
  font-size: 20px;
  color: #000;
}

.ditop span {
  display: none;
  font-size: 12px;
  width: 30px;
  margin: 0 auto;
  margin-top: 3px;
}

.ditop:hover {
  background-color: #3297fc;
  border: 1px solid #3297fc;
  color: #fff;
}

.ditop:hover i {
  display: none;
}

.ditop:hover span {
  display: block;
}

.ditopcon {
  position: absolute;
  width: 160px;
  bottom: -50px;
  left: -163px;
  box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #fff;
  border-radius: 4px;
  box-sizing: border-box;
  transition: 0.3s ease-out;
  display: none;
  padding: 10px;
  background-color: #fff;
}

.ditop-top {
  margin-top: 10px;
}

.ditop-qq-img {
  width: 35px;
  height: 35px;
  margin: 0 auto;
}

.ditop-qq-btn {
  overflow: hidden;
  margin: 10px 0px 0px 0px;
}

.ditop-qq-btn img {
  margin-bottom: 8px;
}

.ditop-qq-height {
  height: 35px;
}

.ditop-time {
  color: #999;
  font-size: 12px;
  line-height: 25px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.ditop-tel {
  color: #3297fc;
  font-size: 18px;
  line-height: 30px;
}

.ditop-email {
  color: #FF5151;
  font-size: 12px;
  line-height: 25px;
}



.ditop:hover .ditopcon {
  display: block;
  color: #999;
  font-size: 14px;
}

.topthewm {
  width: 100px;
  height: 100px;
  border: 1px solid #f0f0f0;
  margin: 0 auto;
  margin-top: 10px;
  box-sizing: border-box;
  padding: 5px;
  border-radius: 5px;
}

.th_header {
  height: 70px;
  background: #fff;
  width: 100%;
  box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.08);
  position: fixed;
  z-index: 100;
  top: 0;
  opacity: 1;
}

.pheaderpad {
  height: 90px;
}

.th_nav li {
  float: left;
  white-space: nowrap;
  position: relative;
}

.th_nav li a {
  position: relative;
  line-height: 70px;
  height: 70px;
  padding: 0 14px;
  font-size: 16px;
  transition: 0.3s ease-out;
  display: inline-block;
}

.th_nav li a i {
  float: right;
  margin-left: 5px;
}

.th_nav li a:after {
  position: absolute;
  content: '';
  left: 50%;
  height: 3px;
  bottom: 0;
  width: 0;
  transition: all .5s;
  background: #3297fc;
}

.th_nav li a:hover:after {
  text-decoration: none;
  width: 100%;
  left: 0;
}
.th_nav li.act a{
  color: #3297fc;
}
.th_nav li.act a:after{
  left: 0;
  width: 100%;
}

.th_nav li ul {
  background-color: #f7f7f7;
  display: none;
}

.th_nav>li:hover>ul {
  display: block;
}

.th_nav li ul li {
  float: none;
}

.th_nav li ul li a {
  float: none;
  height: 40px;
  line-height: 40px;
  width: 100%;
  display: block;
  text-align: center;
}

/* san ji nav */
.th_nav li ul li {
  position: relative;
}

.th_nav li ul li ul {
  background-color: #eaeaea;
  display: none;
  position: absolute;
  top: 0px;
  left: 100px;
}

.th_nav li ul li:hover ul {
  display: block;
}

.ztmer{ display: none;}

.searchpanel {
  margin-top: 15px;
}

.searchpanel input[name='keyword'] {
  font-size: 12px;
  width: 78% !important;
  background: #fcfcfc;
  height: 33px;
  line-height: 33px;
  text-indent: 10px;
  border: 0;
  border: 1px solid #ddd;
  border-radius: 5px;
  transition: all .35s ease 0s;
}

.searchpanel input[name='submit'] {
  border-radius: 2px;
  transition: all .35s ease 0s;
  line-height: 35px;
  height: 35px;
  width: 20%;
  padding: 0 5px;
  border-radius: 5px;
  background: #cde9fd;
  border: 1px solid #cde9fd;
  cursor: pointer;
  transition: all .35s ease 0s;
  color: #6a7a86;
}

.searchpanel input[name='submit']:hover {
  background-color: #3297fc;
  color: #fff;
}

.searchpanel input[name='keyword']:focus {
  border: 1px solid #3297fc;
}

.searchpanel input[name='submit']:focus {
  border: 1px solid #cde9fd;
}



.thhotnews {
  width: 100%;
  background: #fff;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
  padding: 15px;
  box-sizing: border-box;
  height: 335px;
}

.thhotnews .iconhot {
  display: block;
  background: #3297fc;
  background: linear-gradient(-45deg, #32b6ff, #4276ff);
  background: -webkit-gradient(-45deg, linear, left, right, #32b6ff), to(#4276ff);
  background: -moz-linear-gradient(-45deg, left, #32b6ff, #4276ff);
  background: -webkit-linear-gradient(-45deg, left, #32b6ff, #4276ff);
  background: -o-linear-gradient(-45deg, left, #32b6ff, #4276ff);
  font-size: 15px;
  color: #fff;
  width: 110px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 15px;
  font-weight: normal;
}

.thhotnews .iconhot i {
  margin-right: 5px;
}

.thhotnews_con {
  margin-top: 15px;
  overflow: hidden;
}

.thhotnews_con dl {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dotted #f0f0f0;
}

.thhotnews_con dl dt a {
  color: #434A54;
  font-weight: normal;
  display: block;
  height: 25px;
  line-height: 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  white-space: nowrap;
  font-size: 20px;
  transition: 0.3s ease-out;
}

.icon_yin {
  color: #cde9fd;
}

.thhotnews_con dl dt a:hover {
  color: #3297fc;
}

.thhotnews_con dl dd {
  height: 44px;
  line-height: 22px;
  overflow: hidden;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.thjingxuan {
  width: 100%;
  background: #fff;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  padding: 15px;
  overflow: hidden;
}

.thjingxuan_title {
  line-height: 30px;
  /* padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0; */
  font-size: 20px;
  color: #202020;
}

.thjingxuan_title i {
  border-radius: 5px;
  display: block;
  float: left;
  margin-top: 9px;
  width: 4px;
  height: 15px;
  margin-right: 10px;
  background: #3297fc;
}

.thliorder1 {
  padding-bottom: 15px;
  border: 1px solid #f0f0f0;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  margin: 5px;
  padding: 10px;
  border-radius: 1px;
  transition: 0.3s ease-out;
  overflow: hidden;
}

.thliorder1:hover {
  background-color: #f2f5f7;
}

.thjingxuan_sec {
  margin-top: 15px;
;
  overflow: hidden;
}

.thliorder1_title {
  overflow: hidden;
  height: 22px;
  margin-top: 5px;
}

.thliorder1_title span {
  float: left;
}

.thtime {
  float: right;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #999;
}

.thliorder1_title span a {
  display: inline-block;
  color: #fff;
  width: 70px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  text-align: center;
  border-radius: 10px;
  overflow: hidden;
}

.thjingxuan_sec div:nth-child(1) .thliorder1_title a {
  background: #ff8f76;
}

.thjingxuan_sec div:nth-child(2) .thliorder1_title a {
  background: #3297fc;
}

.thjingxuan_sec div:nth-child(3) .thliorder1_title a {
  background: #000;
}

.thjingxuan_sec div:nth-child(4) .thliorder1_title a {
  background: #8623e2;
}

.thjingxuan_sec div:nth-child(5) .thliorder1_title a {
  background: #9ad430;
}

.thjingxuan_sec div:nth-child(6) .thliorder1_title a {
  background: #385977;
}

.daodu {
  margin-top: 10px;
}

.daodu a {
  font-size: 16px;
  height: 25px;
  line-height: 25px;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: 0.3s ease-out;
  color: #464646;
  font-weight: bold;
}

.daodu a:hover {
  color: #3297fc;
}

.daoducon {
  color: #999;
  height: 44px;
  line-height: 22px;
  overflow: hidden;
  font-size: 12px;
  margin-top: 6px;
}

.th-img {
  width: 100%;
  object-fit: cover;
}

.th-ad1 {
  height: 100px;

}
.thnews-img-height {
  height: 130px;
}

.thinfo {
  color: #999;
  height: 22px;
  line-height: 22px;
  overflow: hidden;
  font-size: 12px;
  margin-top: 6px;
}

.gundongimg {
  border: 1px solid #fff;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.gundongimg a img {
  transition: all 0.3s;
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;

}

.gundongimg a:hover img {
  transform: scale(1.1);
  -moz-transform: scale(1.1);
  -webkit-transform: scale(1.1);
  -o-transform: scale(1.1);
  -ms-transform: scale(1.1);
}

.gundong-img-height {
  height: 185px;
}

.thnews-img {
  float: left;
  width: 30%;
  box-sizing: border-box;
  position: relative;
  border: 1px solid #fff;
  overflow: hidden;
}

.thnews-img a img {
  transition: all 0.3s;
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;

}

.thnews-img a:hover img {
  transform: scale(1.1);
  -moz-transform: scale(1.1);
  -webkit-transform: scale(1.1);
  -o-transform: scale(1.1);
  -ms-transform: scale(1.1);
}

.thnews-img span {
  position: absolute;
  top: 5px;
  left: 5px;
  background: #0a0a0a;
  background: linear-gradient(-45deg, #0a0a0a, #394a75);
  background: -webkit-gradient(-45deg, linear, left, right, #0a0a0a), to(#394a75);
  background: -moz-linear-gradient(-45deg, left, #0a0a0a, #394a75);
  background: -webkit-linear-gradient(-45deg, left, #0a0a0a, #394a75);
  background: -o-linear-gradient(-45deg, left, #0a0a0a, #394a75);
  font-size: 12px;
  color: #fff;
  height: 30px;
  line-height: 30px;
  text-align: center;
  display: inline-block;
  padding: 0 10px;
  border-radius: 0 15px 0px 15px;
}


.thnews-con {
  box-sizing: border-box;
  float: right;
  width: 69%;
}


.news-con-tit {
  overflow: hidden;
  width: 100%;
  margin-top: 5px;
}

.news-con-tit a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  height: 25px;
  font-size: 18px;
  text-overflow: ellipsis;
  transition: 0.3s ease-out;
}

.newsummary {
  color: #999;
  font-size: 12px;
  overflow: hidden;
  margin-bottom: 5px;
  height: 44px;
  line-height: 22px;
  margin-top: 10px;
}

.style-night {
  background-color: #444;
}

.baitian {
  display: none;
}

.isnight .yewan {
  display: none;
}

.isnight .baitian {
  display: block;
}


.yewan i,
.baitian i {
  font-size: 22px;
}

.thleftcon {
  width: 100%;

  background: #fff;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

/* ÃƒÂ¤Ã‚Â»Ã¢â‚¬Â¹ÃƒÂ§Ã‚Â»Ã‚ÂÃƒÂ¨Ã†â€™Ã…â€™ÃƒÂ¦Ã¢â€žÂ¢Ã‚Â¯ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ */
.thleftcon-1 {
  width: 100%;
  position: relative;
}

.jsimg-height {
  height: 100px;
}

.jsimg-toux {
  height: 110px;
  width: 110px;
  border: 5px solid #fff;
  position: absolute;
  left: 30%;
  top: 35px;
  border-radius: 80px;
}

.thjs_infor {
  width: 150px;
  margin: 0 auto;
  text-align: center;
  margin-top: 50px;
}

.thjs_beizhu {
  color: #999;
  height: 22px;
  line-height: 22px;
  overflow: hidden;
  font-size: 12px;
  margin-top: 2px;
}

.thleftcon-2 {
  padding: 0 10px;
}

.thleftcon-2 dl dd {
  line-height: 22px;
  font-size: 13px;
  color: #71767a;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  margin-top:10px;
}

.thleftcon-2 dl dd i {
  margin-right: 5px;
}

.aut_count {
  overflow: hidden;
  margin-top: 10px;
}

.aut_count ul li {
  float: left;
  width: 33.333%;
  border-right: 1px #efefef solid;
  padding: 8px 0;
  font-weight: 300;
  text-align: center;
}

.aut_count ul li:last-child {
  border-right: 1px solid transparent;
}

.aut_count ul li span {
  display: block;
  font-size: 14px;
  color: #999;
}

.aut_count ul li strong {
  font-weight: bold;
}

.thleftbt {
  height: 50px;
  line-height: 50px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  box-sizing: border-box;
  font-size: 18px;
}

.thleftbt::before {
  content: "";
  display: block;
  position: absolute;
  left: -2px;
  top: 35%;
  width: 4px;
  height: 30%;
  background-color: #3297fc;
  border-radius: 5px;
}

.thleftbt span {
  margin-left: 15px;
}

.th-5 {
  padding: 10px 0px;
  background: #fff;
}

.th-5 .date {
  float: right;
  color: #bbb;
}

.th-5 li {
  line-height: 36px;
  height: 36px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0px 15px;
}

.th-5 li a {
  transition: 0.3s ease-out;
}

.th-5 li i {
  display: inline-block;
  width: 18px;
  height: 18px;
  line-height: 18px;
  margin-right: 10px;
  background-color: #eee;
  text-align: center;
  font-style: normal;
  color: #888;
  border-radius: 0 15px 10px 10px;
}

.th-5 li:nth-child(1) i,
.th-5 li:nth-child(2) i,
.th-5 li:nth-child(3) i {
  background-color: #3297fc;
  color: #fff;
}

.th-6 {
  padding: 15px 15px;
  background: #fff;
  overflow: hidden;
}

.th-6 li {
  float: left;
  width: 46%;
  margin-right: 10px;
  margin-top: 10px;
}

.th-6 li:nth-child(2),
.th-6 li:nth-child(4),
.th-6 li:nth-child(6) {
  margin-right: 0px;
}

.th-6 li:nth-child(1),
.th-6 li:nth-child(2) {
  margin-top: 0px;
}

.th-6 li a {
  display: inline-block;
  position: relative;
  border-radius: 2px;
}

.zhuti-img-height {
  height: 100px;
}

.zhuantiinfo {
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  left: 0;
  bottom: -36px;
  color: #fff;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.77);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 1)));
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
  padding: 0 10px;
  transition: all 0.3s;
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  display: block;
  height: 36px;
  line-height: 18px;
}

.th-6 li a:hover .zhuantiinfo {
  bottom: 0px;
}

.th-7,.th-tags {
  background: #fff;
  overflow: hidden;
  padding: 15px 15px 10px 15px;
}

.th-7 li {
  float: left;
  margin-right: 6px;
  width: 30%;
  text-align: center;
}

.th-7 li a {
  display: inline-block;
  border-radius: 2px;
  width: 100%;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 2px;
  padding: 0px 5px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  background-color: #f2f5f7;
  font-size: 12px;
  color: #999;
  transition: 0.3s ease-out;
}
.th-7 li a:hover,.th-tags li a:hover {
  color: #3297fc;
}

.th-tags li a {
  display: inline-block;
  border-radius: 2px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 2px;
  padding: 0px 5px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  background-color: #f2f5f7;
  font-size: 12px;
  color: #999;
  transition: 0.3s ease-out;
}



.line-left {
  padding-left: 15px;
}

.th-8 {
  background: #fff;
  overflow: hidden;
  padding: 15px 15px 10px 15px;
}

.th-8 li {
  float: left;
  margin-right: 15px;
  line-height: 25px;
}

.th-8 li a {
  color: #999;
  transition: 0.3s ease-out;
}

.th-8 li a:hover {
  color: #3297fc;
}

.linesq {
  font-size: 12px;
  color: #999;
  float: right;
  margin-right: 15px;
}

.footer2 {
  background: #292929;
  padding: 10px 0;
  text-align: center;
  overflow: hidden;
  color: #9c9c9c;
  font-size: 12px;
}

.footer2 a {
  color: #9c9c9c;
  transition: 0.3s ease-out;
}

.footer2 a:hover {
  color: #3297fc;
}

/* banner */
.thbanner {
  height: 335px;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
}

.swiper-slide {
  width: 100%;
  text-align: center;
  font-size: 18px;
  background: #fff;
  float: left;
}

.swiper-slide a {
  position: relative;
  display: inline-block;
  width: 100%;
}

.thslide2 {
  padding: 10px;
  position: relative;
}

.thslide2-right {
  font-size: 14px;
  color: #999;
  position: absolute;
  top: 0px;
  right: 15px;
}

.thslide2-right a {
  font-size: 14px;
  color: #999;
  transition: 0.3s ease-out;
}

.th-button-next {
  font-size: 14px;
  color: #999;
  position: absolute;
  top: 0px;
  right: 40px;
  cursor: pointer;
}

.th-button-prev {
  font-size: 14px;
  color: #999;
  position: absolute;
  top: 0px;
  right: 15px;
  cursor: pointer;
}

.th-button-next i,
.th-button-prev i {
  font-size: 20px;
  font-weight: bold;
}

.th-button-next:hover,
.th-button-prev:hover {
  color: #3297fc;
}

.thslide2-right a:hover {
  color: #3297fc;
}

.thslidelist {
  overflow: hidden;
}

.thslidetitle {
  height: 20px;
  line-height: 20px;
  font-size: 14px;
}

.silde_a_border {
  border: 1px solid #fff;
  display: block;
  overflow: hidden;
}

.swiper-button-next,
.swiper-button-prev {
  color: #fff;
  opacity: 0.5;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  color: #f2f5f7;
  opacity: 1;

}

/* ÃƒÂ§Ã‚Â²Ã‚Â¾ÃƒÂ¥Ã‚Â½Ã‚Â©ÃƒÂ¦Ã…Â½Ã‚Â¨ÃƒÂ¨Ã‚ÂÃ‚Â */
.swiper-container1 {
  height: 300px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  z-index: 1;
}

.thbn-title {
  position: absolute;
  width: 100%;
  bottom: 0px;
  left: 0;
  background-color: #000;
  color: #fff;
  font-size: 14px;
  opacity: 0.5;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  padding: 0 10px;
}

.thbn-title span {
  display: block;
  width: 85%;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: 0.3s ease-out;
  overflow: hidden;
  text-align: left;
}

.thbanner-img-height {
  height: 335px;
}

.footer1 {
  width: 100%;
  background: #333;
  padding-top: 30px;
  padding-bottom: 20px;
  color: #ccc;
  font-size: 12px;
  margin-top: 15px;
}

.footcon1 dt {
  font-size: 14px;
  margin-bottom: 15px;
  color: #f2f5f7;
}

.footcon1 dd {
  line-height: 20px;
  color: #8a8a8a;
  height: 103px;
  overflow: hidden;
}

.footcon1 dd.footlianx {
  height: 25px;
  line-height: 25px;
}

.footcon1 dd.footlianx span {
  color: #3297fc;
  font-size: 14px;
}

.footcon1 dd i {
  margin-right: 10px;
}

.footcon2 dt {
  font-size: 14px;
  margin-bottom: 15px;
  color: #f2f5f7;
  text-align: center;
}

.footcon2 dd {
  line-height: 20px;
  height: 20px;
  color: #8a8a8a;
  overflow: hidden;
  text-align: center;
}

.footcon2 dd a {
  line-height: 20px;
  height: 20px;
  color: #8a8a8a;
  transition: 0.3s ease-out;
}

.footcon2 dd a:hover {
  color: #3297fc;
}

.footewm {
  width: 103px;
  height: 103px;
  border: 1px solid #585858;
  margin: 0 auto;
  margin-top: 10px;
  box-sizing: border-box;
  padding: 5px;
  border-radius: 5px;
}

.wap_headerclick {
  display: none;
}


.categorylist a {
  font-size: 14px;
  color: #999;
  transition: 0.3s ease-out;
}

.categorylist a:hover {
  color: #3297fc;
}

.categorylist span {
  font-size: 14px;
  color: #999;
  margin-left: 5px
}


.detail-title {
  color: #333;
  font-size: 20px;
  text-align: center;
}

.detail-icon {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
  text-align: center;
}

.detail-con {
  font-size: 16px;
  color: #666;
  margin-top: 10px;
  line-height: 30px;
  padding: 0px 20px;
}

.detail-con p{
  margin: 25px 0px;
}

.detail-con p img {
  max-width: 100%;
}

.detail-con img {
  max-width: 100%;
}

.meta span {
  margin-right: 10px;
}


.detail_ad {
  height: 60px;
  margin-top: 10px;
}

.detail_ad1 {
  float: left;
  height: 60px;
  width: 49%;
  position: relative;
}

.detail_ad2 {
  float: right;
  position: relative;
  height: 60px;
  width: 49%;
}

.detail_ad1 span,
.detail_ad2 span {
  display: inline-block;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #fff;
  padding: 0 10px;
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.3);
}

.detail-zhaiyao {
  width: 100%;
  height: 60px;
  line-height: 22px;
  display: inline-block;
  padding: 10px 15px;
  font-size: 12px;
  color: #9ca0ad;
  background: #f6f7fa;
  border: 1px dashed #e3e5ec;
  overflow: hidden;
  margin-top: 10px;
}

.umCopyright {
  line-height: 22px;
  display: inline-block;
  padding: 10px 15px;
  font-size: 14px;
  color: #9ca0ad;
  background: #f6f7fa;
  border: 1px dashed #e3e5ec;
  width: 100%;
  margin: 25px 0 0;
}

.detail-arr {
  margin-top: 20px;
  overflow: hidden;
}

.detail-arr-left,
.detail-arr-right {
  color: #bbb;
  line-height: 30px;
}

.detail-arr-left a,
.detail-arr-right a {
  display: inline-block;
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 14px;
  color: #999;
}

.detail-arr-left a:hover,
.detail-arr-right a:hover {
  color: #3297fc;
}

.detail-tags {
  color: #bbb;
  margin-top: 20px;
}

.pagebar {
  text-align: center;
  margin-top: 15px;
}

.pagebar a:hover {
  color: #7cbeff;
}

.pagebar a,.pagebar b {
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 16px;
  margin: 0px 5px;
  border: 1px solid #f0f0f5;
  color: #666;
  padding: 0px 8px;
  border-radius: 2px;
  box-sizing: border-box;
}

.pagebar a:hover {
  background: #7cbeff;
  color: #fff;
  border: 1px solid #7cbeff;
}

.pagebar b {
  background: #7cbeff;
  border: 1px solid #7cbeff;
  color: #fff;
  font-weight: normal;
}

/* pingkun */
ul.msg {
  width: 100%;
  margin: 10px 0 20px 0;
  padding: 5px 0px;
  text-align: left;
  list-style-position: outside;
  table-layout: fixed;
  word-wrap: break-word;
}

.tbname {
  height: 35px;
  line-height: 35px;
  font-size: 16px;
  background: #f6f7fa;
  padding-left: 20px;
  border-left: 4px solid #080808
}

li.msgname {
  padding: 0 0 10px 0px;
  font-size: 14px;
  vertical-align: middle;
  color: #9ca0ad;
  height: 45px;
}

li.msgname a {
  color: #9ca0ad;
}


li.msgurl {
  text-align: right;
  padding: 2px 10px 2px 10px;
  margin: 0;
  font-size: 1em;
}

.commentname {
  float: left;
}


.th-mp1,
.th-mp2 {
  margin: 0px;
}

.th-mp1 {
  height: 40px;
  line-height: 40px;
}

.th-mp2 {
  font-size: 14px;
  color: #777;
}

li.msggravatar {
  display: block;
  float: left;
  margin-right: 5px;
}

li.msgarticle {
  list-style-position: outside;
  padding: 10px;
  margin: 0;
  border-bottom: 1px solid #eaeaea;
}

li.msgtime {
  padding: 5px 0 0 0;
  font-weight: normal;
  font-size: 0.8em;
}

img.avatar {
  float: left;
  margin-right: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, .1);
  border: 2px solid #fff;
}

ul.msg ul.msg {
  padding: 10px 0 0 0;
  margin: 20px 0 0 0;
  border-top: 1px solid #eaeaea;
  border-bottom: none;
}

ul.msg ul.msg li.msgarticle {
  padding-bottom: 0px;
  border-bottom: 0px solid #eaeaea;
}

ul.msg ul.msg li.msgname {
  padding: 0px;
}

#ctf_content {
  position: relative;
  z-index: 1;
  padding: 5px;
  height: 100px;
  min-height: 14px;
  margin: 0;
  resize: none;
  outline: 0;
  width: 100%;
  min-width: 100%;
  border: 1px solid rgba(227, 229, 236, .4);
  background: #f8f8fc;
  border-radius: 0rem;
  font-size: 14px;
  line-height: 1.6;
}

.th-ulmsg {
  overflow: hidden;
  margin-bottom: 10px;
;
}

.th-ulmsg li {
  float: left;
  margin-right: 10px;
}

input.text {
  border-radius: 0;
  transition: all .35s ease 0s;
  line-height: 38px;
  height: 38px;
  width: 100%;
  padding: 0 10px;
  border: 1px solid rgba(227, 229, 236, .4);
  background: #f8f8fc;
}

input.text:focus,
#txaArticle:focus {
  border: 1px solid #7cbeff;
}

.comment {
  font-size: 16px;
  line-height: 25px;
  color: #333;
}

#divCommentPost {
  margin-top: 10px;
  border: 1px solid #f6f7fa;
  padding: 10px;
}
.comment_list{
  margin-top: 10px;
  border: 1px solid #f6f7fa;
  padding: 10px;
}
#load_more{
  border: 1px solid #cde9fd;
  background-color: #cde9fd;
  padding: 5px 0;
  border-radius:5px;
  width: 100%;
  display: inline-block;
  text-align: center;
  color: #6a7a86;
}

#huifu, #cancel_reply_comment {
  background: #fff;
  padding: 0px 5px;
  border: 1px solid #e5e5e5;
  background-color: #ececec;
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  float: right;
}

#cancel_reply_comment:hover, #huifu:hover {
  border: 1px solid #7cbeff;
  background: #7cbeff;
  color: #fff;
}

.button {
  display: inline-block;
  height: 38px;
  line-height: 38px;
  padding: 0 20px;
  background-color: #7cbeff;
  color: #fff;
  white-space: nowrap;
  text-align: center;
  font-size: 14px;
  border: 0;
  border-radius: 2px;
  cursor: pointer;
  opacity: .9;
  filter: alpha(opacity=90);
  vertical-align: middle;
  -webkit-user-select: none;
  -ms-user-select: none;
  -moz-user-select: none;
}

/* end */


.leftad-img-height {
  height: 140px;
}

.thwenzhang {
  border-top: 1px solid #f0f0f0;
  background-color: #f8f8f8;
}

.pc_margintop {
  margin-top: 10px;
}

.listMore {
  position: absolute;
  top: 0px;
  right: 10px;
}

.listMore a {
  font-size: 14px;
  color: #999;
  transition: 0.3s ease-out;
}

.listMore a:hover {
  font-size: 14px;
  color: #3297fc;
}

/* 首页 幻灯片上方广告位 */
.index_ad {
  background-color: #fff;
  height: 60px;
  margin-top: 15px;
  position: relative;
}

.index_ad span {
  display: inline-block;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #fff;
  padding: 0 10px;
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.3);
}

.th_index_ad {
  height: 60px;
}


@media only screen and (min-width: 320px) and (max-width: 992px) {

  .wap_headerlogo {
    position: relative;
  }

  .wap_headerclick {
    position: absolute;
    top: 5px;
    right: 10px;
    display: block;
  }

  .wap_headerclick span {
    font-size: 35px;
    color: #999;
    transition: 0.3s ease-out;
  }

  .wap_headerclick span:hover {
    color: #3297fc;
  }

  .wap_display {
    display: none;
  }

  .icon-guanbi1 {
    display: none;
  }

  .isnavicon .icon-guanbi1 {
    display: block;
  }

  .isnavicon .icon-caidan1 {
    display: none;
  }

  .wap_headernav {
    background-color: #fff;
    margin-top: 5px;
    padding: 10px 0px;
  }
  .wap_headersearch {
    background-color: #fff;
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
  }

  .searchpanel {
    margin-top: 5px;
    padding: 0px 40px;
  }

  .line-left {
    padding-left: 0px;
  }

  .th_padding {
    padding-left: 0px;
    padding-right: 0px;
  }

  .left_padding {
    padding-left: 15px;
    padding-right: 15px;
  }

  .th_padding_left {
    padding-left: 15px;
  }

  .th_padding_right {
    padding-right: 15px;
  }

  .gundong-img-height {
    height: 120px;
  }

  .newsummary {
    margin-top: 5px;
  }

  .thnews-img-height {
    height: 110px;
  }

  .thnews-con {
    width: 59%;
  }

  .thnews-img {
    width: 40%;
  }

  .tel_margin_top,
  html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
  }

  body {
    margin: 0;
  }

  article,
  footer,
  header,
  main,
  menu,
  nav,
  section,
  summary {
    display: block;
  }

  ul,
  li {
    list-style: none;
  }

  img {
    border: 0;
  }

  button,
  input,
  optgroup,
  select,
  textarea {
    color: inherit;
    font: inherit;
    margin: 0;
  }

  button {
    overflow: visible;
  }

  button {
    text-transform: none;
  }

  * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }

  *:before,
  *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }

  html {
    font-size: 10px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  body {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.42857143;
    color: #333333;
    background-color: #f2f5f7;
  }

  input,
  button,
  select,
  textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  a {
    color: #333;
    text-decoration: none;
  }

  a:hover,
  a:focus {
    color: #3297fc;
    text-decoration: none;
  }

  a:focus {
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
  }

  figure {
    margin: 0;
  }

  img {
    vertical-align: middle;
  }


  h4,
  .h4 {
    font-size: 18px;
  }

  h5,
  .h5 {
    font-size: 14px;
  }

  h6,
  .h6 {
    font-size: 12px;
  }

  p {
    margin: 0 0 10px;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  .text-justify {
    text-align: justify;
  }

  .text-muted {
    color: #777777;
  }

  .text-primary {
    color: #337ab7;
  }

  a.text-primary:hover,
  a.text-primary:focus {
    color: #286090;
  }

  .text-success {
    color: #3c763d;
  }

  a.text-success:hover,
  a.text-success:focus {
    color: #2b542c;
  }

  .text-info {
    color: #31708f;
  }

  a.text-info:hover,
  a.text-info:focus {
    color: #245269;
  }

  .text-warning {
    color: #8a6d3b;
  }

  a.text-warning:hover,
  a.text-warning:focus {
    color: #66512c;
  }

  .text-danger {
    color: #a94442;
  }

  a.text-danger:hover,
  a.text-danger:focus {
    color: #843534;
  }

  .bg-primary {
    color: #fff;
    background-color: #337ab7;
  }

  a.bg-primary:hover,
  a.bg-primary:focus {
    background-color: #286090;
  }

  .bg-success {
    background-color: #dff0d8;
  }

  a.bg-success:hover,
  a.bg-success:focus {
    background-color: #c1e2b3;
  }

  .bg-info {
    background-color: #d9edf7;
  }

  a.bg-info:hover,
  a.bg-info:focus {
    background-color: #afd9ee;
  }

  .bg-warning {
    background-color: #fcf8e3;
  }

  a.bg-warning:hover,
  a.bg-warning:focus {
    background-color: #f7ecb5;
  }

  .bg-danger {
    background-color: #f2dede;
  }

  a.bg-danger:hover,
  a.bg-danger:focus {
    background-color: #e4b9b9;
  }

  ul,
  ol {
    margin-top: 0;
    margin-bottom: 10px;
  }

  ul ul,
  ol ul,
  ul ol,
  ol ol {
    margin-bottom: 0;
  }


  dl {
    margin-top: 0;
    margin-bottom: 20px;
  }

  dt,
  dd {
    line-height: 1.42857143;
  }

  dt {
    font-weight: bold;
  }

  dd {
    margin-left: 0;
  }

  .container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px;
  }

  @media (min-width: 768px) {
    .container {
      width: 750px;
    }
  }

  @media (min-width: 992px) {
    .container {
      width: 970px;
    }
  }

  @media (min-width: 1200px) {
    .container {
      width: 1180px;
    }
  }



  .row {
    margin-left: -15px;
    margin-right: -15px;
  }

  .col-xs-1,
  .col-sm-1,
  .col-md-1,
  .col-lg-1,
  .col-xs-2,
  .col-sm-2,
  .col-md-2,
  .col-lg-2,
  .col-xs-3,
  .col-sm-3,
  .col-md-3,
  .col-lg-3,
  .col-xs-4,
  .col-sm-4,
  .col-md-4,
  .col-lg-4,
  .col-xs-5,
  .col-sm-5,
  .col-md-5,
  .col-lg-5,
  .col-xs-6,
  .col-sm-6,
  .col-md-6,
  .col-lg-6,
  .col-xs-7,
  .col-sm-7,
  .col-md-7,
  .col-lg-7,
  .col-xs-8,
  .col-sm-8,
  .col-md-8,
  .col-lg-8,
  .col-xs-9,
  .col-sm-9,
  .col-md-9,
  .col-lg-9,
  .col-xs-10,
  .col-sm-10,
  .col-md-10,
  .col-lg-10,
  .col-xs-11,
  .col-sm-11,
  .col-md-11,
  .col-lg-11,
  .col-xs-12,
  .col-sm-12,
  .col-md-12,
  .col-lg-12 {
    position: relative;
    min-height: 1px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .col-xs-1,
  .col-xs-2,
  .col-xs-3,
  .col-xs-4,
  .col-xs-5,
  .col-xs-6,
  .col-xs-7,
  .col-xs-8,
  .col-xs-9,
  .col-xs-10,
  .col-xs-11,
  .col-xs-12 {
    float: left;
  }

  .col-xs-12 {
    width: 100%;
  }

  .col-xs-11 {
    width: 91.66666667%;
  }

  .col-xs-10 {
    width: 83.33333333%;
  }

  .col-xs-9 {
    width: 75%;
  }

  .col-xs-8 {
    width: 66.66666667%;
  }

  .col-xs-7 {
    width: 58.33333333%;
  }

  .col-xs-6 {
    width: 50%;
  }

  .col-xs-5 {
    width: 41.66666667%;
  }

  .col-xs-4 {
    width: 33.33333333%;
  }

  .col-xs-3 {
    width: 25%;
  }

  .col-xs-2 {
    width: 16.66666667%;
  }

  .col-xs-1 {
    width: 8.33333333%;
  }

  .col-xs-pull-12 {
    right: 100%;
  }

  .col-xs-pull-11 {
    right: 91.66666667%;
  }

  .col-xs-pull-10 {
    right: 83.33333333%;
  }

  .col-xs-pull-9 {
    right: 75%;
  }

  .col-xs-pull-8 {
    right: 66.66666667%;
  }

  .col-xs-pull-7 {
    right: 58.33333333%;
  }

  .col-xs-pull-6 {
    right: 50%;
  }

  .col-xs-pull-5 {
    right: 41.66666667%;
  }

  .col-xs-pull-4 {
    right: 33.33333333%;
  }

  .col-xs-pull-3 {
    right: 25%;
  }

  .col-xs-pull-2 {
    right: 16.66666667%;
  }

  .col-xs-pull-1 {
    right: 8.33333333%;
  }

  .col-xs-pull-0 {
    right: auto;
  }

  .col-xs-push-12 {
    left: 100%;
  }

  .col-xs-push-11 {
    left: 91.66666667%;
  }

  .col-xs-push-10 {
    left: 83.33333333%;
  }

  .col-xs-push-9 {
    left: 75%;
  }

  .col-xs-push-8 {
    left: 66.66666667%;
  }

  .col-xs-push-7 {
    left: 58.33333333%;
  }

  .col-xs-push-6 {
    left: 50%;
  }

  .col-xs-push-5 {
    left: 41.66666667%;
  }

  .col-xs-push-4 {
    left: 33.33333333%;
  }

  .col-xs-push-3 {
    left: 25%;
  }

  .col-xs-push-2 {
    left: 16.66666667%;
  }

  .col-xs-push-1 {
    left: 8.33333333%;
  }

  .col-xs-push-0 {
    left: auto;
  }

  .col-xs-offset-12 {
    margin-left: 100%;
  }

  .col-xs-offset-11 {
    margin-left: 91.66666667%;
  }

  .col-xs-offset-10 {
    margin-left: 83.33333333%;
  }

  .col-xs-offset-9 {
    margin-left: 75%;
  }

  .col-xs-offset-8 {
    margin-left: 66.66666667%;
  }

  .col-xs-offset-7 {
    margin-left: 58.33333333%;
  }

  .col-xs-offset-6 {
    margin-left: 50%;
  }

  .col-xs-offset-5 {
    margin-left: 41.66666667%;
  }

  .col-xs-offset-4 {
    margin-left: 33.33333333%;
  }

  .col-xs-offset-3 {
    margin-left: 25%;
  }

  .col-xs-offset-2 {
    margin-left: 16.66666667%;
  }

  .col-xs-offset-1 {
    margin-left: 8.33333333%;
  }

  .col-xs-offset-0 {
    margin-left: 0%;
  }

  @media (min-width: 992px) {

    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12 {
      float: left;
    }

    .col-md-12 {
      width: 100%;
    }

    .col-md-11 {
      width: 91.66666667%;
    }

    .col-md-10 {
      width: 83.33333333%;
    }

    .col-md-9 {
      width: 75%;
    }

    .col-md-8 {
      width: 66.66666667%;
    }

    .col-md-7 {
      width: 58.33333333%;
    }

    .col-md-6 {
      width: 50%;
    }

    .col-md-5 {
      width: 41.66666667%;
    }

    .col-md-4 {
      width: 33.33333333%;
    }

    .col-md-3 {
      width: 25%;
    }

    .col-md-2 {
      width: 16.66666667%;
    }

    .col-md-1 {
      width: 8.33333333%;
    }

    .col-md-pull-12 {
      right: 100%;
    }

    .col-md-pull-11 {
      right: 91.66666667%;
    }

    .col-md-pull-10 {
      right: 83.33333333%;
    }

    .col-md-pull-9 {
      right: 75%;
    }

    .col-md-pull-8 {
      right: 66.66666667%;
    }

    .col-md-pull-7 {
      right: 58.33333333%;
    }

    .col-md-pull-6 {
      right: 50%;
    }

    .col-md-pull-5 {
      right: 41.66666667%;
    }

    .col-md-pull-4 {
      right: 33.33333333%;
    }

    .col-md-pull-3 {
      right: 25%;
    }

    .col-md-pull-2 {
      right: 16.66666667%;
    }

    .col-md-pull-1 {
      right: 8.33333333%;
    }

    .col-md-pull-0 {
      right: auto;
    }

    .col-md-push-12 {
      left: 100%;
    }

    .col-md-push-11 {
      left: 91.66666667%;
    }

    .col-md-push-10 {
      left: 83.33333333%;
    }

    .col-md-push-9 {
      left: 75%;
    }

    .col-md-push-8 {
      left: 66.66666667%;
    }

    .col-md-push-7 {
      left: 58.33333333%;
    }

    .col-md-push-6 {
      left: 50%;
    }

    .col-md-push-5 {
      left: 41.66666667%;
    }

    .col-md-push-4 {
      left: 33.33333333%;
    }

    .col-md-push-3 {
      left: 25%;
    }

    .col-md-push-2 {
      left: 16.66666667%;
    }

    .col-md-push-1 {
      left: 8.33333333%;
    }

    .col-md-push-0 {
      left: auto;
    }

    .col-md-offset-12 {
      margin-left: 100%;
    }

    .col-md-offset-11 {
      margin-left: 91.66666667%;
    }

    .col-md-offset-10 {
      margin-left: 83.33333333%;
    }

    .col-md-offset-9 {
      margin-left: 75%;
    }

    .col-md-offset-8 {
      margin-left: 66.66666667%;
    }

    .col-md-offset-7 {
      margin-left: 58.33333333%;
    }

    .col-md-offset-6 {
      margin-left: 50%;
    }

    .col-md-offset-5 {
      margin-left: 41.66666667%;
    }

    .col-md-offset-4 {
      margin-left: 33.33333333%;
    }

    .col-md-offset-3 {
      margin-left: 25%;
    }

    .col-md-offset-2 {
      margin-left: 16.66666667%;
    }

    .col-md-offset-1 {
      margin-left: 8.33333333%;
    }

    .col-md-offset-0 {
      margin-left: 0%;
    }
  }

  div,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  ul,
  li,
  form,
  label,
  input,
  textarea,
  button,
  img,
  span,
  dl,
  dt,
  dd,
  th,
  pre {
    margin: 0;
    padding: 0;
    outline: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
  }

  .clearfix:before,
  .clearfix:after,
  .dl-horizontal dd:before,
  .dl-horizontal dd:after,
  .container:before,
  .container:after,
  .container-fluid:before,
  .container-fluid:after,
  .row:before,
  .row:after {
    content: " ";
    display: table;
  }

  .clearfix:after,
  .dl-horizontal dd:after,
  .container:after,
  .container-fluid:after,
  .row:after {
    clear: both;
  }


  .text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0;
  }

  .hidden {
    display: none !important;
  }

  .affix {
    position: fixed;
  }

  .th_padding,
  .left_padding {
    padding-left: 0;
    padding-right: 0;
  }



  .th_padding_left {
    padding-left: 0;
  }

  .th_padding_right {
    padding-right: 0;
  }

  .th_margintop {
    margin-top: 15px;
  }

  .thgotop {
    position: fixed;
    left: 50%;
    margin-left: 615px;
    bottom: 100px;
    z-index: 99;
  }

  .thgotop ul li {
    width: 40px;
    height: 40px;
    background: #fff;
    margin-top: 10px;
    box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #fff;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .ditop {
    cursor: pointer;
    text-align: center;
    vertical-align: middle;
    transition: 0.3s ease-out;
    position: relative;
  }

  .ditop i {
    display: inline-block;
    margin-top: 5px;
    font-size: 20px;
    color: #000;
  }

  .ditop span {
    display: none;
    font-size: 12px;
    width: 30px;
    margin: 0 auto;
    margin-top: 3px;
  }

  .ditop:hover {
    background-color: #3297fc;
    border: 1px solid #3297fc;
    color: #fff;
  }

  .ditop:hover i {
    display: none;
  }

  .ditop:hover span {
    display: block;
  }

  .ditopcon {
    position: absolute;
    width: 160px;
    bottom: -50px;
    left: -163px;
    box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #fff;
    border-radius: 4px;
    box-sizing: border-box;
    transition: 0.3s ease-out;
    display: none;
    padding: 10px;
    background-color: #fff;
  }

  .ditop-top {
    margin-top: 10px;
  }

  .ditop-qq-img {
    width: 35px;
    height: 35px;
    margin: 0 auto;
  }

  .ditop-qq-btn {
    overflow: hidden;
    margin: 10px 0px 0px 0px;
  }

  .ditop-qq-btn img {
    margin-bottom: 8px;
  }

  .ditop-qq-height {
    height: 35px;
  }

  .ditop-time {
    color: #999;
    font-size: 12px;
    line-height: 25px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
  }

  .ditop-tel {
    color: #3297fc;
    font-size: 18px;
    line-height: 30px;
  }

  .ditop-email {
    color: #FF5151;
    font-size: 12px;
    line-height: 25px;
  }

  .ditop:hover .ditopcon {
    display: block;
    color: #999;
    font-size: 14px;
  }

  .topthewm {
    width: 100px;
    height: 100px;
    border: 1px solid #f0f0f0;
    margin: 0 auto;
    margin-top: 10px;
    box-sizing: border-box;
    padding: 5px;
    border-radius: 5px;
  }

  .thhotnews {
    width: 100%;
    background: #fff;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
    padding: 15px;
    box-sizing: border-box;
    height: 335px;
  }

  .thhotnews .iconhot {
    display: block;
    background: #3297fc;
    background: linear-gradient(-45deg, #32b6ff, #4276ff);
    background: -webkit-gradient(-45deg, linear, left, right, #32b6ff), to(#4276ff);
    background: -moz-linear-gradient(-45deg, left, #32b6ff, #4276ff);
    background: -webkit-linear-gradient(-45deg, left, #32b6ff, #4276ff);
    background: -o-linear-gradient(-45deg, left, #32b6ff, #4276ff);
    font-size: 15px;
    color: #fff;
    width: 110px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 15px;
    font-weight: normal;
  }

  .thhotnews .iconhot i {
    margin-right: 5px;
  }

  .thhotnews_con {
    margin-top: 15px;
    overflow: hidden;
  }

  .thhotnews_con dl {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px dotted #f0f0f0;
  }

  .thhotnews_con dl dt a {
    color: #434A54;
    font-weight: normal;
    display: block;
    height: 25px;
    line-height: 25px;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    white-space: nowrap;
    font-size: 20px;
    transition: 0.3s ease-out;
  }

  .icon_yin {
    color: #cde9fd;
  }

  .thhotnews_con dl dt a:hover {
    color: #3297fc;
  }

  .thhotnews_con dl dd {
    height: 44px;
    line-height: 22px;
    overflow: hidden;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }

  .thjingxuan {
    width: 100%;
    background: #fff;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    padding: 15px;
    overflow: hidden;
  }

  .thjingxuan_title {
    line-height: 30px;
    font-size: 20px;
    color: #202020;
  }

  .thjingxuan_title i {
    border-radius: 5px;
    display: block;
    float: left;
    margin-top: 9px;
    width: 4px;
    height: 15px;
    margin-right: 10px;
    background: #3297fc;
  }

  .thliorder1 {
    padding-bottom: 15px;
    border: 1px solid #f0f0f0;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    margin: 5px;
    padding: 10px;
    border-radius: 1px;
    transition: 0.3s ease-out;
    overflow: hidden;
  }

  .thliorder1:hover {
    background-color: #f2f5f7;
  }

  .thjingxuan_sec {
    margin-top: 15px;
  ;
    overflow: hidden;
  }

  .thliorder1_title {
    overflow: hidden;
    height: 22px;
    margin-top: 5px;
  }

  .thliorder1_title span {
    float: left;
  }

  .thtime {
    float: right;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    color: #999;
  }

  .thliorder1_title span a {
    display: inline-block;
    color: #fff;
    width: 70px;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    text-align: center;
    border-radius: 10px;
    overflow: hidden;
  }

  .thjingxuan_sec div:nth-child(1) .thliorder1_title a {
    background: #ff8f76;
  }

  .thjingxuan_sec div:nth-child(2) .thliorder1_title a {
    background: #3297fc;
  }

  .thjingxuan_sec div:nth-child(3) .thliorder1_title a {
    background: #000;
  }

  .thjingxuan_sec div:nth-child(4) .thliorder1_title a {
    background: #8623e2;
  }

  .thjingxuan_sec div:nth-child(5) .thliorder1_title a {
    background: #9ad430;
  }

  .thjingxuan_sec div:nth-child(6) .thliorder1_title a {
    background: #385977;
  }

  .daodu {
    margin-top: 10px;
  }

  .daodu a {
    font-size: 16px;
    height: 25px;
    line-height: 25px;
    overflow: hidden;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: 0.3s ease-out;
    color: #464646;
    font-weight: bold;
  }

  .daodu a:hover {
    color: #3297fc;
  }

  .daoducon {
    color: #999;
    height: 44px;
    line-height: 22px;
    overflow: hidden;
    font-size: 12px;
    margin-top: 6px;
  }

  .th-img {
    width: 100%;
    object-fit: cover;
  }
  .gundong-img-height {
    height: 165px;
  }
  .th-ad1 {
    height: 100px;
  }
  .thnews-img-height {
    height: 130px;
  }
  .thinfo {
    color: #999;
    height: 22px;
    line-height: 22px;
    overflow: hidden;
    font-size: 12px;
    margin-top: 6px;
  }
  .thnews-img {
    float: left;
    width: 30%;
    box-sizing: border-box;
    position: relative;
    border: 1px solid #fff;
    overflow: hidden;
  }

  .thnews-img a img {
    transition: all 0.3s;
    -moz-transition: all 0.3s;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;

  }

  .thnews-img a:hover img {
    transform: scale(1.1);
    -moz-transform: scale(1.1);
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    -ms-transform: scale(1.1);
  }

  .thnews-img span {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #0a0a0a;
    background: linear-gradient(-45deg, #0a0a0a, #394a75);
    background: -webkit-gradient(-45deg, linear, left, right, #0a0a0a), to(#394a75);
    background: -moz-linear-gradient(-45deg, left, #0a0a0a, #394a75);
    background: -webkit-linear-gradient(-45deg, left, #0a0a0a, #394a75);
    background: -o-linear-gradient(-45deg, left, #0a0a0a, #394a75);
    font-size: 12px;
    color: #fff;
    height: 30px;
    line-height: 30px;
    text-align: center;
    display: inline-block;
    padding: 0 10px;
    border-radius: 0 15px 0px 15px;
  }


  .thnews-con {
    box-sizing: border-box;
    float: right;
    width: 69%;
  }


  .news-con-tit {
    overflow: hidden;
    width: 100%;
    margin-top: 5px;
  }

  .news-con-tit a {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    height: 25px;
    font-size: 18px;
    text-overflow: ellipsis;
    transition: 0.3s ease-out;
  }

  .newsummary {
    color: #999;
    font-size: 12px;
    overflow: hidden;
    margin-bottom: 5px;
    height: 44px;
    line-height: 22px;
    margin-top: 10px;
  }

  .style-night {
    background-color: #444;
  }

  .baitian {
    display: none;
  }

  .isnight .yewan {
    display: none;
  }

  .isnight .baitian {
    display: block;
  }


  .yewan i,
  .baitian i {
    font-size: 22px;
  }

  .thleftcon {
    width: 100%;

    background: #fff;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
  }

  /* ÃƒÂ¤Ã‚Â»Ã¢â‚¬Â¹ÃƒÂ§Ã‚Â»Ã‚ÂÃƒÂ¨Ã†â€™Ã…â€™ÃƒÂ¦Ã¢â€žÂ¢Ã‚Â¯ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ */
  .thleftcon-1 {
    width: 100%;
    position: relative;
  }

  .jsimg-height {
    height: 100px;
  }

  .jsimg-toux {
    height: 110px;
    width: 110px;
    border: 5px solid #fff;
    position: absolute;
    left: 35%;
    top: 35px;
  }

  .thjs_beizhu {
    color: #999;
    height: 22px;
    line-height: 22px;
    overflow: hidden;
    font-size: 12px;
    margin-top: 2px;
  }

  .thleftcon-2 {
    padding: 0 25px;
  }

  .thleftcon-2 dl dd {
    line-height: 22px;
    font-size: 13px;
    color: #71767a;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    margin-top:10px;
  }

  .thleftcon-2 dl dd i {
    margin-right: 5px;
  }

  .aut_count {
    overflow: hidden;
    margin-top: 10px;
  }

  .aut_count ul li {
    float: left;
    width: 33.333%;
    border-right: 1px #efefef solid;
    padding: 8px 0;
    font-weight: 300;
    text-align: center;
  }

  .aut_count ul li:last-child {
    border-right: 1px solid transparent;
  }

  .aut_count ul li span {
    display: block;
    font-size: 14px;
    color: #999;
  }

  .aut_count ul li strong {
    font-weight: bold;
  }

  .thleftbt {
    height: 50px;
    line-height: 50px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    box-sizing: border-box;
    font-size: 18px;
  }

  .thleftbt::before {
    content: "";
    display: block;
    position: absolute;
    left: -2px;
    top: 35%;
    width: 4px;
    height: 30%;
    background-color: #3297fc;
    border-radius: 5px;
  }

  .thleftbt span {
    margin-left: 15px;
  }

  .th-5 {
    padding: 10px 0px;
    background: #fff;
  }

  .th-5 .date {
    float: right;
    color: #bbb;
  }

  .th-5 li {
    line-height: 36px;
    height: 36px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0px 15px;
  }

  .th-5 li a {
    transition: 0.3s ease-out;
  }

  .th-5 li i {
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    margin-right: 10px;
    background-color: #eee;
    text-align: center;
    font-style: normal;
    color: #888;
    border-radius: 0 15px 10px 10px;
  }

  .th-5 li:nth-child(1) i,
  .th-5 li:nth-child(2) i,
  .th-5 li:nth-child(3) i {
    background-color: #3297fc;
    color: #fff;
  }

  .th-6 {
    padding: 15px 15px;
    background: #fff;
    overflow: hidden;
  }

  .th-6 li {
    float: left;
    width: 47%;
    margin-right: 10px;
    margin-top: 10px;
  }

  .th-6 li:nth-child(2),
  .th-6 li:nth-child(4),
  .th-6 li:nth-child(6) {
    margin-right: 0px;
  }

  .th-6 li:nth-child(1),
  .th-6 li:nth-child(2) {
    margin-top: 0px;
  }

  .th-6 li a {
    display: inline-block;
    position: relative;
    border-radius: 2px;
  }
  .zhuti-img-height {
    height: 100px;
  }

  .detail-related{
    background-color: #f8f8f8;

  }


  .zhuantiinfo {
    height: 100%;
    display: block;
    position: absolute;
    left: 0;
    width: 100%;
    bottom: -36px;
    color: #fff;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.77);
    background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 1)));
    background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
    background: -moz-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
    padding: 0 10px;
    transition: all 0.3s;
    -moz-transition: all 0.3s;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    display: block;
    height: 36px;
    line-height: 18px;
  }

  .th-6 li a:hover .zhuantiinfo {
    bottom: 0px;
  }

  .th-7 {
    background: #fff;
    overflow: hidden;
    padding: 15px 15px 10px 15px;
  }

  .th-7 li {
    float: left;
    margin-right: 6px;
    width: 30%;
    text-align: center;
  }

  .th-7 li a {
    display: inline-block;
    border-radius: 2px;
    width: 100%;
    height: 30px;
    line-height: 30px;
    box-sizing: border-box;
    border-radius: 2px;
    padding: 0px 5px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    background-color: #f2f5f7;
    font-size: 12px;
    color: #999;
    transition: 0.3s ease-out;
  }

  .th-7 li a:hover {
    color: #3297fc;
  }

  .line-left {
    padding-left: 15px;
  }

  .th-8 {
    background: #fff;
    overflow: hidden;
    padding: 15px 15px 10px 15px;
  }

  .th-8 li {
    float: left;
    margin-right: 15px;
    line-height: 25px;
  }

  .th-8 li a {
    color: #999;
    transition: 0.3s ease-out;
  }

  .th-8 li a:hover {
    color: #3297fc;
  }

  .linesq {
    font-size: 12px;
    color: #999;
    float: right;
    margin-right: 15px;
  }

  .footer2 {
    background: #292929;
    padding: 10px 0;
    text-align: center;
    overflow: hidden;
    color: #9c9c9c;
    font-size: 12px;
  }

  /* banner */
  .thbanner {
    height: 335px;
    width: 100%;
    box-sizing: border-box;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.08);
  }

  .swiper-slide {
    width: 100%;
    text-align: center;
    font-size: 18px;
    background: #fff;
    float: left;
  }

  .swiper-slide a {
    position: relative;
    display: inline-block;
    width: 100%;
  }

  .thslide2 {
    padding: 10px;
    position: relative;
  }

  .thslide2-img-height {
    height: 130px;
  }

  .thslide2-right {
    font-size: 14px;
    color: #999;
    position: absolute;
    top: 0px;
    right: 15px;
  }

  .thslide2-right a {
    font-size: 14px;
    color: #999;
    transition: 0.3s ease-out;
  }

  .th-button-next {
    font-size: 14px;
    color: #999;
    position: absolute;
    top: 0px;
    right: 40px;
    cursor: pointer;
  }

  .th-button-prev {
    font-size: 14px;
    color: #999;
    position: absolute;
    top: 0px;
    right: 15px;
    cursor: pointer;
  }

  .th-button-next i,
  .th-button-prev i {
    font-size: 20px;
    font-weight: bold;
  }

  .th-button-next:hover,
  .th-button-prev:hover {
    color: #3297fc;
  }

  .thslide2-right a:hover {
    color: #3297fc;
  }

  .thslidelist {
    height: 160px;
    overflow: hidden;
  }

  .thslidetitle {
    height: 20px;
    line-height: 20px;
    font-size: 14px;
  }

  .silde_a_border {
    border: 1px solid #fff;
    display: block;
    overflow: hidden;
  }

  .swiper-button-next,
  .swiper-button-prev {
    color: #fff;
    opacity: 0.5;
  }

  .swiper-button-next:hover,
  .swiper-button-prev:hover {
    color: #f2f5f7;
    opacity: 1;

  }

  /* ÃƒÂ§Ã‚Â²Ã‚Â¾ÃƒÂ¥Ã‚Â½Ã‚Â©ÃƒÂ¦Ã…Â½Ã‚Â¨ÃƒÂ¨Ã‚ÂÃ‚Â */
  .swiper-container1 {
    height: 300px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: hidden;
    list-style: none;
    padding: 0;
    z-index: 1;
  }

  .thbn-title {
    position: absolute;
    width: 100%;
    bottom: 0px;
    left: 0;
    background-color: #000;
    color: #fff;
    font-size: 14px;
    opacity: 0.5;
    height: 44px;
    line-height: 44px;
    box-sizing: border-box;
    padding: 0 10px;
  }

  .thbn-title span {
    display: block;
    width: 85%;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: 0.3s ease-out;
    overflow: hidden;
    text-align: left;
  }

  .thbanner-img-height {
    height: 335px;
  }

  .footer1 {
    width: 100%;
    background: #333;
    padding-top: 30px;
    padding-bottom: 20px;
    color: #ccc;
    font-size: 12px;
    margin-top: 15px;
  }

  .footcon1 dt {
    font-size: 14px;
    margin-bottom: 15px;
    color: #f2f5f7;
  }

  .footcon1 dd {
    line-height: 20px;
    color: #8a8a8a;
    height: 103px;
    overflow: hidden;
  }

  .footcon1 dd.footlianx {
    height: 25px;
    line-height: 25px;
  }

  .footcon1 dd.footlianx span {
    color: #3297fc;
    font-size: 14px;
  }

  .footcon1 dd i {
    margin-right: 10px;
  }

  .footcon2 dt {
    font-size: 14px;
    margin-bottom: 15px;
    color: #f2f5f7;
    text-align: center;
  }

  .footcon2 dd {
    line-height: 20px;
    height: 20px;
    color: #8a8a8a;
    overflow: hidden;
    text-align: center;
  }

  .footcon2 dd a {
    line-height: 20px;
    height: 20px;
    color: #8a8a8a;
    transition: 0.3s ease-out;
  }

  .footcon2 dd a:hover {
    color: #3297fc;
  }

  .footewm {
    width: 103px;
    height: 103px;
    border: 1px solid #585858;
    margin: 0 auto;
    margin-top: 10px;
    box-sizing: border-box;
    padding: 5px;
    border-radius: 5px;
  }

  .wap_headerclick {
    display: none;
  }


  .categorylist a {
    font-size: 14px;
    color: #999;
    transition: 0.3s ease-out;
  }

  .categorylist a:hover {
    color: #3297fc;
  }

  .categorylist span {
    font-size: 14px;
    color: #999;
    margin-left: 5px
  }


  .detail-title {
    color: #333;
    font-size: 20px;
    text-align: center;
  }

  .detail-icon {
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
    color: #999;
  }

  .detail-con {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
  }

  .meta span {
    margin-right: 10px;
  }

  .detail-zhaiyao {
    width: 100%;
    height: 60px;
    line-height: 22px;
    display: inline-block;
    padding: 10px 15px;
    font-size: 12px;
    color: #9ca0ad;
    background: #f6f7fa;
    border: 1px dashed #e3e5ec;
    overflow: hidden;
    margin-top: 10px;
  }

  .umCopyright {
    line-height: 22px;
    display: inline-block;
    padding: 10px 15px;
    font-size: 14px;
    color: #9ca0ad;
    background: #f6f7fa;
    border: 1px dashed #e3e5ec;
    width: 100%;
    margin: 25px 0 0;
  }

  .detail-arr {
    margin-top: 20px;
    overflow: hidden;
  }

  .detail-arr-left {
    float: left;
    height: 25x;
    line-height: 25px;
  }

  .detail-arr-right {
    float: right;
    height: 25x;
    line-height: 25px;
  }

  .detail-arr-left a,
  .detail-arr-right a {
    display: inline-block;
    padding: 0px 10px;
    border: 1px solid #e3e5ec;
  ;
    background-color: #f6f7fa;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    font-size: 12px;
    color: #999;
  }

  .detail-arr-left a:hover,
  .detail-arr-right a:hover {
    border: 1px solid #3297fc;
  ;
    background: #3297fc;
    color: #fff;
  }


  .pagebar {
    text-align: center;
    margin-top: 15px;
  }

  .pagebar a:hover {
    color: #7cbeff;
  }

  .pagebar span {
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    font-size: 16px;
    margin: 0px 5px;
    border: 1px solid #f0f0f5;
    color: #666;
    padding: 0px 8px;
    border-radius: 2px;
    box-sizing: border-box;
  }

  .pagebar span:hover {
    background: #7cbeff;
    color: #fff;
    border: 1px solid #7cbeff;
  }

  .pagebar span.now-page {
    background: #7cbeff;
    border: 1px solid #7cbeff;
    color: #fff;
  }

  /* pingkun */
  ul.msg {
    width: 100%;
    margin: 10px 0 20px 0;
    padding: 5px 0px;
    text-align: left;
    list-style-position: outside;
    table-layout: fixed;
    word-wrap: break-word;
  }

  .tbname {
    height: 35px;
    line-height: 35px;
    font-size: 16px;
    background: #f6f7fa;
    padding-left: 20px;
    border-left: 4px solid #080808
  }

  li.msgname {
    padding: 0 0 10px 0px;
    font-size: 14px;
    vertical-align: middle;
    color: #9ca0ad;
    height: 45px;
  }

  li.msgname a {
    color: #9ca0ad;
  }


  li.msgurl {
    text-align: right;
    padding: 2px 10px 2px 10px;
    margin: 0;
    font-size: 1em;
  }

  .commentname {
    float: left;
  }


  .th-mp1,
  .th-mp2 {
    margin: 0px;
  }

  .th-mp1 {
    height: 40px;
    line-height: 40px;
  }

  .th-mp2 {
    font-size: 14px;
    color: #777;
  }

  li.msggravatar {
    display: block;
    float: left;
    margin-right: 5px;
  }

  li.msgarticle {
    list-style-position: outside;
    padding: 10px;
    margin: 0;
    border-bottom: 1px solid #eaeaea;
  }

  li.msgtime {
    padding: 5px 0 0 0;
    font-weight: normal;
    font-size: 0.8em;
  }

  img.avatar {
    float: left;
    margin-right: 15px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, .1);
    border: 2px solid #fff;
  }

  ul.msg ul.msg {
    padding: 10px 0 0 0;
    margin: 20px 0 0 0;
    border-top: 1px solid #eaeaea;
    border-bottom: none;
  }

  ul.msg ul.msg li.msgarticle {
    padding-bottom: 0px;
    border-bottom: 0px solid #eaeaea;
  }

  ul.msg ul.msg li.msgname {
    padding: 0px;
  }

  #txaArticle {
    position: relative;
    z-index: 1;
    padding: 5px;
    height: 100px;
    min-height: 14px;
    margin: 0;
    resize: none;
    outline: 0;
    width: 100%;
    min-width: 100%;
    border: 1px solid rgba(227, 229, 236, .4);
    background: #f8f8fc;
    border-radius: 0rem;
    font-size: 14px;
    line-height: 1.6;
  }

  .th-ulmsg {
    overflow: hidden;
    margin-bottom: 10px;
  ;
  }

  .th-ulmsg li {
    float: left;
    margin-right: 10px;
  }

  input.text {
    border-radius: 0;
    transition: all .35s ease 0s;
    line-height: 38px;
    height: 38px;
    width: 100%;
    padding: 0 10px;
    border: 1px solid rgba(227, 229, 236, .4);
    background: #f8f8fc;
  }

  input.text:focus,
  #txaArticle:focus {
    border: 1px solid #7cbeff;
  }

  .comment {
    font-size: 16px;
    line-height: 25px;
    color: #333;
  }

  #divCommentPost {
    margin-top: 10px;
    border: 1px solid #f6f7fa;
    padding: 10px;
  }

  #huifu, #cancel_reply_comment {
    background: #fff;
    padding: 0px 5px;
    border: 1px solid #e5e5e5;
    background-color: #ececec;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    float: right;
  }

  #cancel_reply_comment:hover, #huifu:hover {
    border: 1px solid #7cbeff;
    background: #7cbeff;
    color: #fff;
  }

  .button {
    display: inline-block;
    height: 38px;
    line-height: 38px;
    padding: 0 20px;
    background-color: #7cbeff;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    font-size: 14px;
    border: 0;
    border-radius: 2px;
    cursor: pointer;
    opacity: .9;
    filter: alpha(opacity=90);
    vertical-align: middle;
    -webkit-user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
  }

  /* end */


  .leftad-img-height {
    height: 150px;
  }

  .thwenzhang {
    border-top: 1px solid #f0f0f0;
    background-color: #f8f8f8;
  }

  .pc_margintop {
    margin-top: 10px;
  }

  .listMore {
    position: absolute;
    top: 0px;
    right: 10px;
  }

  .listMore a {
    font-size: 14px;
    color: #999;
    transition: 0.3s ease-out;
  }

  .listMore a:hover {
    font-size: 14px;
    color: #3297fc;
  }

  /* 首页 幻灯片上方广告位 */
  .index_ad {
    background-color: #fff;
    height: 60px;
    margin-top: 15px;
    position: relative;
  }

  .index_ad span {
    display: inline-block;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    color: #fff;
    padding: 0 10px;
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.3);
  }

  .th_index_ad {
    height: 60px;
  }

  @media only screen and (min-width: 320px) and (max-width: 992px) {
    .wap_headerlogo {
      position: relative;
    }

    .wap_headerlogo img {
      width: 180px;
      height: 70px;
    }

    .wap_headerclick {
      position: absolute;
      top: 5px;
      right: 10px;
      display: block;
    }

    .wap_headerclick span {
      font-size: 35px;
      color: #999;
      transition: 0.3s ease-out;
    }

    .wap_headerclick span:hover {
      color: #3297fc;
    }

    .wap_display {
      display: none;
    }

    .icon-guanbi1 {
      display: none;
    }

    .isnavicon .icon-guanbi1 {
      display: block;
    }

    .isnavicon .icon-caidan1 {
      display: none;
    }

    .wap_headernav {
      background-color: #fff;
      margin-top: 5px;
      padding: 10px 0px;
    }


    .wap_headernav ul.th_nav li {
      float: none;
      /* height: 50px;
      line-height: 50px; */
      margin: 0 10px;
      white-space: nowrap;
      position: relative;
      border-bottom: 1px solid #f2f5f7;
    }

    .wap_headernav ul.th_nav li a {
      position: relative;
      line-height: 35px;
      height: 35px;
      width: 100%;
      font-size: 16px;
      transition: 0.3s ease-out;
      display: inline-block;
      text-align: center;
    }

    .wap_headernav ul.th_nav li ul{
      overflow: hidden;
      display: block;
    }
    .wap_headernav ul.th_nav li ul li{
      float: left;
      width: 42%;
      border-bottom: 1px dashed #cde9fd;
    }
    .wap_headernav ul.th_nav li ul li a{
      font-size: 13px;
    }
    .wap_headersearch {
      background-color: #fff;
      padding-bottom: 20px;
      border-bottom: 1px solid #ddd;
    }
    .searchpanel {
      margin-top: 5px;
      padding: 0px 40px;
    }

    .line-left {
      padding-left: 0px;
    }
    .th_padding {
      padding-left: 0px;
      padding-right: 0px;
    }
    .left_padding {
      padding-left: 15px;
      padding-right: 15px;
    }

    .th_padding_left {
      padding-left: 15px;
    }
    .th_padding_right {
      padding-right: 15px;
    }

    .gundong-img-height {
      height: 120px;
    }
    .newsummary {
      margin-top: 5px;
    }
    .thnews-img-height {
      height: 110px;
    }
    .thnews-con {
      width: 59%;
    }

    .thnews-img {
      width: 40%;
    }

    .tel_margin_top,
    .wap_margintop {
      margin-top: 15px;
    }


    .swiper-container {
      height: 280px;
    }

    .swiper-slide a {
      height: 280px;
    }

    .detail-arr,
    .wap_isnone {
      display: none;
    }

    .wap_margintop {
      margin-top: 15px;
    }



    .swiper-container {
      height: 280px;
    }

    .swiper-slide a {
      height: 280px;
    }

    .detail-arr,
    .wap_isnone {
      display: none;
    }

    .detail-con img {
      max-width: 100%;
    }

    .detail-con p img {
      max-width: 100%;
    }
  }


}

#reply_comment_div {
  background: #f8f8fc;
  box-shadow: 0 0 2px #333;
  padding: .3em .8em !important;
  border: 1px solid rgba(227, 229, 236, .4);
  outline: 0;
  display: none;
}

#reply_comment_content{margin-top: 5px;}
a.reply_comment{margin-left: 5px;}

.reply_msgarticle {
  background-color: #e9ecef;
  background-image: url("img/quote.png");
  background-repeat: no-repeat;
  background-position: top right;
  padding: 0.5rem 1rem !important;
}