{inc:header.htm}
{inc:functions.php}
{block:global_cate pagenum="16" dateformat="m-d" showviews="1"}
  <div class="inside-focus" style="background-image: url({$cfg[tpl]}style/images/list.jpg)"> 
   <div class="cate-header"> 
    <div class="container"> 
     <h1>{$cfg_var[name]}</h1> 
     <p>{$cfg_var[intro]}</p> 
    </div> 
   </div> 
  </div> 
  <div class="container"> 
   <div class="col-main"> 
    <ul class="clearfix"> 
	 {loop:$gdata[list] $v} 
     <li class="art"> 
      <div class="art-con" style="position: relative; overflow: hidden;"> 
       <a href="{$v[url]}" title="{$v[title]}" class="art-pic-one"> <img src="{$v[pic]}" alt="{$v[subject]}" /> <span class="mask"></span> </a> 
       <a href="{$cfg_var[url]}" title="{$cfg_var[name]}" class="cat">{$cfg_var[name]}</a> 
       <div class="art-main"> 
        <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b> 
        <p class="col-art-meta clearfix"> <span><i class="iconfont icon-time"></i>{$v[date]}</span> <span><i class="iconfont icon-view"></i>{$v[views]}</span> {if:$v[oprice]>0}<span><i class="iconfont icon-rmb"></i>{$v['oprice']}</span>{/if}</p> 
       </div> 
      </div> 
	  </li> 
	  {/loop}
    </ul> 
    <div class="pages">
     {$gdata[pages]}
    </div> 
   </div> 
  </div>
{/block} 
{inc:footer.htm}
{if:$ziphtml==1} {php} $html_source = ob_get_contents(); ob_clean(); print compressHtml($html_source); ob_end_flush(); {/php} {/if} 