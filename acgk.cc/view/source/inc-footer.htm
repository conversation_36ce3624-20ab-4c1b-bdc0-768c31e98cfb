
  <div class="footer"> 
   <div class="footer-navi"> 
    <div class="container clearfix"> 
     <div class="about"> 
      <h2 class="title">哥特动漫王国</h2> 
      <p>哥特动漫王国</p> 
     </div> 
     <div class="navis"> 

             
     </div> 
    </div> 
   </div> 
   <div class="footer-bottom"> 
    <div class="container"> 
     <ul class="links clearfix"> 
      <li>友情链接：</li> 
     </ul> 
     <div class="footer-copyright">
       Copyright &copy; 2020 
      <a href="{$cfg[weburl]}" rel="home"> {$cfg[webname]}</a>
      <a rel="nofollow" target="_blank" href="http://www.beian.miit.gov.cn">{$cfg[beian]}</a> {$cfg[tongji]}
      <p style="display: block;height: 0;width: 0;overflow: hidden;">{php} echo '页面耗时'.runtime().'秒, 内存占用'.runmem().', 访问数据库'.$_ENV['_sqlnum'].'次'; {/php}</p>
     </div> 
    </div> 
   </div> 
  </div> 
  <!--侧边悬浮--> 
  <div class="rollbar"> 
   <div class="rollbar-item tap-qq" etap="tap-qq">
    <a target="_blank" title="QQ咨询" href="http://wpa.qq.com/msgrd?v=3&amp;uin=&amp;site=qq&amp;menu=yes"><i class="iconfont icon-qq"></i></a>
   </div> 
   <div class="rollbar-item tap-weixin" etap="tap-weixin" data-id="0" title="关注微信">
    <i class="iconfont icon-weixin"></i>
    <img src="{$cfg[tpl]}style/images/weixin.jpg" />
   </div> 
   <div class="rollbar-item to_full" etap="to_full" title="全屏页面">
    <i class="iconfont icon-full"></i>
   </div> 
    <!--<div class="rollbar-item to_top" etap="to_top" title="返回顶部">--> 
   <div class="rollbar-item to_top"  title="返回顶部" id="back-to-top">
    <i class="iconfont icon-shang"></i>
   </div> 
  </div> 
  <div class="m-mask"></div>   
  <script>
    var news_li = $('.col-tit-2').find('li');
    var i = 0;
    news_li.click(function () {
        i = $(this).index();
        $(this).addClass('active').siblings().removeClass('active');
        $('.grids').eq(i).show().siblings('.grids').hide();
    });
 </script>  
 <!--返回顶部-->
 <script type="text/javascript">
    $(document).ready(function() {
        //首先将#back-to-top隐藏
        $("#back-to-top").hide();
        //当滚动条的位置处于距顶部100像素以下时，跳转链接出现，否则消失
        $(function() {
            $(window).scroll(function() {
                if ($(window).scrollTop() > 100) {
                    $("#back-to-top").fadeIn(1500);
                } else {
                    $("#back-to-top").fadeOut(1500);
                }
            });
            //当点击跳转链接后，回到页面顶部位置
            $("#back-to-top").click(function() {
                $('body,html').animate({
                    scrollTop: 0
                },
                1000);
                return false;
            });
        });
    });
 </script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
 </body>
</html>
