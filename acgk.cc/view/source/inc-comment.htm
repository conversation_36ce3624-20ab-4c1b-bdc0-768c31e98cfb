<link rel="stylesheet" type="text/css" href="{$cfg[tpl]}comment/c.css" />
<script type="text/javascript" src="{$cfg[tpl]}comment/c.js"></script>
<div class="showcomment">
<!--评论列表start-->
{block:comment pagenum="2" firstnum="2"}
<div class="com-tit"><h4 class="title-2">评论列表</h4></div>
{if:!empty($data[list])}
<div class="comment_list" id="ctf">
	{loop:$data[list] $v}
	<ul class="msg" id="comment_{$v[commentid]}">
		<li class="msgname">
			<img class="avatar" src="{$v[avatar]}" width="32">&nbsp;
			<span class="commentname">{$v[author]}<small>&nbsp;发布于&nbsp;{$v[date]}&nbsp;<a class="reply_comment" href="javascript:;" onclick="reply_comment({$v[commentid]});">回复该评论</a></small></span>
		</li>
		{if:$v[reply_comment_content]}<li class="reply_msgarticle">回复：{$v[reply_comment_content]}</li>{/if}
		<li class="msgarticle">{$v[content]}</li>
	</ul>
	{/loop}
	<a id="load_more" href="javascript:;" next_url="{$data[next_url]}" isnext="{$data[isnext]}">显示更多评论</a>
</div>
{/if}
{/block}
<!--评论列表end-->
<!--发布评论start-->
<div id="divCommentPost">
	<p><a name="comment" href="javascript:;">发表评论</a></p>
	<form id="ctf_form" action="{$cfg[webdir]}index.php?comment-post-ajax-1.html" method="post">
		<input type="hidden" name="cid" value="{$gdata[cid]}" />
		<input type="hidden" name="id" value="{$gdata[id]}" />
		<input type="hidden" name="reply_commentid" value="0" />
		<div id="reply_comment_div">
			<p><a>回复评论:</a>
				<a rel="nofollow" id="cancel_reply_comment" href="javascript:;" onclick="cancel_reply_comment();"><small>取消回复</small></a>
			</p>
			<div id="reply_comment_content"></div>
		</div>
		{if:$_uid}
		<input type="hidden" name="author" value="{$author}" />
		{else}
		<p style="margin-top:10px"><input type="text" name="author" id="ctf_author" class="text" value="{$author}" placeholder="昵称" size="28" tabindex="1"></p>
		{/if}
		{if:$cfg[open_comment_vcode]}
		<p style="margin-top:10px;margin-left: 0;">
			<span style="padding-left: 0;">
				<input name="vcode" id="ctf_vcode" type="text" class="text" required placeholder="* 请输入数字验证码" autocomplete="off" /> <img id="captchaPic" src="{$cfg[webdir]}index.php?comment-vcode" onclick="this.src='{$cfg[webdir]}index.php?comment-vcode-r-'+Math.random();" alt="验证码" />
			</span>
		</p>
		{/if}
		<p style="margin-top:10px"><textarea name="content" id="ctf_content" maxlength="255" class="text" cols="50" rows="4" tabindex="5" placeholder="文明上网，理性发言" autocomplete="off"></textarea></p>
		<p><input name="sumbit" type="submit" tabindex="6" value="提交" id="ctf_submit" class="button"></p>
	</form>
</div>
<!--发布评论end-->
</div>
<script type="text/javascript" src="{$cfg[webdir]}static/js/pxmu.min.js"></script>
<script type="text/javascript">
//回复某评论
function reply_comment(commentid){
	$("input[name='reply_commentid']").val(commentid);

	var comment = $("#comment_"+commentid+">.msgarticle").html();
	$("#reply_comment_content").html(comment);
	$("#reply_comment_div").show();
}
//取消回复某评论
function cancel_reply_comment() {
	$("input[name='reply_commentid']").val(0);
	$("#reply_comment_div").hide();
}
//加载更多评论
(function(){
	var obj = $("#load_more");
	var next_url = obj.attr("next_url");
	var isnext = obj.attr("isnext");

	var no_more = function() {
		obj.html("没有更多评论了");
		if(typeof load_more != "undefined") obj.off("click", load_more);
	}

	if(isnext < 1) { no_more(); return; }

	var leJosnLock = false;
	var load_more = function() {
		if(!next_url || leJosnLock) return;
		obj.html("玩命加载中...");
		leJosnLock = true;

		$.get(next_url, function(data) {
			try{
				var json = eval("("+data+")");
				next_url = json.next_url;
				$.each(json.list_arr, function(i,item) {
					var s = '<ul class="msg" id="comment_'+item.commentid+'">';
						s += '<li class="msgname"><img class="avatar" src="'+item.avatar+'" width="32">&nbsp;';
						s += '<span class="commentname">'+item.author+'</span><br><small>发布于&nbsp;'+item.date+'&nbsp;<a class="reply_comment" href="javascript:;" onclick="reply_comment('+item.commentid+');">回复该评论</a></small></li>';

						if(item.reply_comment_content){
							s += '<li class="reply_msgarticle">回复：'+item.reply_comment_content+'</div>';
						}

						s += '<li class="msgarticle">'+item.content+'</div>';
						s += '</ul>';
					$(".comment_list>ul:last").after(s);
				});

				obj.html("显示更多评论");
				leJosnLock = false;
				if(json.isnext < 1) no_more();
			}catch(e){
				alert(data);
			}
		});
	}
	obj.click(load_more);
})();
</script>

