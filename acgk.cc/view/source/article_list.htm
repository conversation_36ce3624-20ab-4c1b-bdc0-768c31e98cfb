{inc:header.htm}
{inc:functions.php}
<style type="text/css">
.tupiansa{display:flex;flex-wrap:wrap}.tupiansa img{width:110px;margin-bottom:10px;border-radius:5px;clip-path:inset(0 0 10% 0);height:80px;margin-left:2px;margin-right:2px}@media (max-width:768px){.tupiansa img{width:calc(30%);clip-path:inset(0 0 15% 0)}}
</style>
{block:global_cate pagenum="10" dateformat="m-d" showviews="1"}
  <div class="inside-focus" style="background-image: url({$cfg[tpl]}style/images/list.jpg)"> 
   <div class="cate-header"> 
    <div class="container"> 
     <h1>{$cfg_var[name]}</h1> 
     <p>{$cfg_var[intro]}</p> 
    </div> 
   </div> 
  </div> 
  <div class="container clearfix"> 
   <div class="col-left l" style="background: #fff;"> 
    <ul class="col-left-cate clearfix"> 
     <li class="option clearfix"> <a href="{$cfg_var[url]}" style="color:#F32424;">{$cfg_var[name]}</a> </li> 
    </ul> 
    <ul class="art-list" id="articlelist"> 
	 {loop:$gdata[list] $v} 
     <li class="clearfix" style="position: relative; overflow: hidden;"> 
      <div class="art-list-right"> 
       <h3><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></h3> 
       <div class="art-intro"> 
        {if:$_uid}
                                          <div class="tupiansa">
                                          {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}
                                          </div>
                                          {else}{/if}
       </div> 
       <div class="art-meta"> 
        <span><i class="iconfont icon-time"></i>{$v[date]}</span> 
        <span><i class="iconfont icon-view"></i>{$v[views]}</span> 
        <span><i class="iconfont icon-comm"></i>{$v[comments]}</span> 
       </div> 
      </div> 
	 </li> 
     {/loop} 
    </ul> 
	{if:is_mobile()} 
    <div class="pages">
     {$gdata[pages]}
    </div> {else} 
    <div class="pages">
     <span>共 <font color="red">{$gdata[total]}</font> 篇</span>{$gdata[pages]}
    </div> 
	{/if} 
	{/block} 
   </div> 
   {inc:right.htm}
  </div> 
{inc:footer.htm}
{if:$ziphtml==1} {php} $html_source = ob_get_contents(); ob_clean(); print compressHtml($html_source); ob_end_flush(); {/php} {/if} 