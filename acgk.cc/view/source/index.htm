{inc:header.htm}  
{inc:functions.php}
  <div class="tj-art-wrap"> 
   <div class="container clearfix"> 
    <div class="tj-box-left"> 
     <div class="tj-art-1"> 
	  {block:list_rand mid="2" limit="1" dateformat="m-d"}  
	  {loop:$data[list] $v}
      <div class="tj-art-con" style="position: relative; overflow: hidden;">
       <span class="tipss">头条</span>   
       <a href="{$v[url]}" title="{$v[title]}" class="art-pic-one"> {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}  <span class="overlay"></span> </a>
       <div class="art-main"> 
        <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b> 
        <p class="col-art-meta clearfix"> <span><i class="iconfont icon-folder"></i>{$v[cate_name]}</span> <span><i class="iconfont icon-auth"></i>{$v['author']}</span> <span><i class="iconfont icon-time"></i>{$v[date]}</span> <span><i class="iconfont icon-view"></i>{$v['views']}</span> </p> 
       </div> 
      </div> 
	  {/loop} 
	  {/block}
     </div> 
    </div> 
    <div class="tj-box-right"> 
     <ul class="clearfix"> 
	  {block:list_flag flag="1" limit="4" dateformat="m-d" titlenum="28"} 
	  {loop:$data[list] $v}
      <li class="tj-art-2"> 
       <div class="tj-art-con" style="position: relative; overflow: hidden;"> 
        <span class="tipss">推荐</span>
        <a href="{$v[url]}" title="{$v[title]}" class="art-pic-one"> {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop} <span class="overlay"></span></a>
        <div class="art-main"> 
         <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b> 
         <p class="col-art-meta clearfix">  <span><i class="iconfont icon-auth"></i>{$v['author']}</span> <span><i class="iconfont icon-time"></i>{$v[date]}</span></p> 
        </div> 
       </div> 
	  </li> 
	  {/loop} 
	  {/block}
     </ul> 
    </div> 
   </div> 
  </div> 
  <!--4ad--> 
  <div class="art-wrap"> 
   <div class="container"> 
{block:content_total_by_date mid="2" type="today"}<div class="today-posts" style="background-color: #fff;color: #343a40;border-radius: 5px;text-align: center;font-size: 1.9rem;transition: background-color 0.3s;font-weight: 900;">今日发布：<m style="font-size: 2.8rem;color: red;"> {$data} </m>篇</div>{/block}

	<!--最新发布-->
    <div class="col-tit"> 
     <h2><span>最近更新</span></h2> 
    </div> 
    <div class="col-main"> 
     <ul class="ajax-arts clearfix"> 
	  {block:global_blog mid="2" pagenum="8" dateformat="m-d" showviews="1"} 
	  {loop:$gdata[list] $v}
      <li class="art ajax-art"> 
       <div class="art-con" style="position: relative; overflow: hidden;"> 
        <a href="{$v[url]}" title="{$v[title]}" class="art-pic-one">{loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop} <span class="mask"></span> </a> 
        <a href="{$v['cate_url']}" title="{$v['cate_name']}" class="cat">{$v['cate_name']}</a> 
        <div class="art-main"> 
         <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b> 
         <p class="col-art-meta clearfix"> <span><i class="iconfont icon-time"></i>{$v[date]}</span> <span><i class="iconfont icon-view"></i>{$v['views']}</span></p> 
        </div> 
       </div> 
	   </li> 
       {/loop} 
	   {/block}
     </ul>  
    </div> 
   </div> 
  </div> 

  <!--各个分类文章列表-->
  {block:list cid="1" limit="8" dateformat="m-d" showviews="1"} 
  <div class="art-wrap">
    <div class="container">
        <div class="col-tit">
            <h2><span>{$data[cate_name]}</span></h2>
        </div>
        <div class="col-main">
            <ul class="clearfix">
			    {loop:$data[list] $v}
                 <li class="art">
                    <div class="art-con" style="position: relative; overflow: hidden;">
                        <a href="{$v[url]}" title="{$v[title]}"  class="art-pic-one">
                            			  {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}
                            <span class="mask"></span>
                        </a>
                        <a href="{$data[cate_url]}" title="{$data[cate_name]}" class="cat">{$data[cate_name]}</a>
                        <div class="art-main">
                            <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b>
                            <p class="col-art-meta clearfix">
                                <span><i class="iconfont icon-time"></i>{$v[date]}</span>
                                <span><i class="iconfont icon-view"></i>{$v['views']}</span>
                            </p>
                        </div>
                    </div>
                </li>
				{/loop}
             </ul>
            <div class="more-1"><a href="{$data[cate_url]}" target="_blank">查看更多</a></div>
        </div>
    </div>
  </div>
  {/block}

  <!--各个分类文章列表-->
  {block:list cid="2" limit="8" dateformat="m-d" showviews="1"} 
  <div class="art-wrap">
    <div class="container">
        <div class="col-tit">
            <h2><span>{$data[cate_name]}</span></h2>
        </div>
        <div class="col-main">
            <ul class="clearfix">
			    {loop:$data[list] $v}
                 <li class="art">
                    <div class="art-con" style="position: relative; overflow: hidden;">
						{if:$v[views]>1000}<span class="tipss">热门</span>{else}{/if} 
                        <a href="{$v[url]}" title="{$v[title]}"  class="art-pic-one">
                            {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}
                            <span class="mask"></span>
                        </a>
                        <a href="{$data[cate_url]}" title="{$data[cate_name]}" class="cat">{$data[cate_name]}</a>
                        <div class="art-main">
                            <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b>
                            <p class="col-art-meta clearfix">
                                <span><i class="iconfont icon-time"></i>{$v[date]}</span>
                                <span><i class="iconfont icon-view"></i>{$v['views']}</span>
                            </p>
                        </div>
                    </div>
                </li>
				{/loop}
             </ul>
            <div class="more-1"><a href="{$data[cate_url]}" target="_blank">查看更多</a></div>
        </div>
    </div>
  </div>
  {/block}

  <!--各个分类文章列表-->
  {block:list cid="3" limit="8" dateformat="m-d" showviews="1"} 
  <div class="art-wrap">
    <div class="container">
        <div class="col-tit">
            <h2><span>{$data[cate_name]}</span></h2>
        </div>
        <div class="col-main">
            <ul class="clearfix">
			    {loop:$data[list] $v}
                 <li class="art">
                    <div class="art-con" style="position: relative; overflow: hidden;">
                        {if:$v[views]>1000}<span class="tipss">热门</span>{else}{/if} 
                        <a href="{$v[url]}" title="{$v[title]}"  class="art-pic-one">
                            {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}
                            <span class="mask"></span>
                        </a>
                        <a href="{$data[cate_url]}" title="{$data[cate_name]}" class="cat">{$data[cate_name]}</a>
                        <div class="art-main">
                            <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b>
                            <p class="col-art-meta clearfix">
                                <span><i class="iconfont icon-time"></i>{$v[date]}</span>
                                <span><i class="iconfont icon-view"></i>{$v['views']}</span>
                            </p>
                        </div>
                    </div>
                </li>
				{/loop}
             </ul>
            <div class="more-1"><a href="{$data[cate_url]}" target="_blank">查看更多</a></div>
        </div>
    </div>
  </div>
  {/block}



{inc:footer.htm}
{if:$ziphtml==1} {php} $html_source = ob_get_contents(); ob_clean(); print compressHtml($html_source); ob_end_flush(); {/php} {/if} 