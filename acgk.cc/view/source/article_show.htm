{inc:header.htm}
{inc:functions.php}
{block:global_show show_prev_next="1" dateformat="Y-m-d"}
  <div class="inside-focus" style="background-image: url({$cfg[tpl]}style/images/article.jpg)"> 
   <div class="art-header"> 
    <div class="container"> 
     <h1>{$gdata[title]}</h1> 
     <div class="art-meta"> 
      <span><i class="iconfont icon-auth"></i>{$gdata[author]}</span> 
      <span><i class="iconfont icon-folder"></i>{loop:$cfg_var[place] $v}<a href="{$v[url]}">{$v[name]}</a>{/loop}</span> 
      <span><i class="iconfont icon-time"></i>{$gdata[date]}</span> 
      <span><i class="iconfont icon-view"></i>{$gdata[views]}</span> 
      <span><i class="iconfont icon-comm"></i>{$gdata[comments]}</span> 
     </div> 
    </div> 
   </div> 
  </div> 
	<style type="text/css">
	img{width:100%;height:auto;display:block;clip-path:inset(0 0 25% 0)}
    </style>
  <div class="container clearfix"> 
   <div class="col-left l" style="position: relative; overflow: hidden;"> 
    {if:$gdata[payprice]>0}<a href="#pay"><span class="tipss">付费内容</span></a>{else}{/if}
    <div class="art-wrap-in"> 
     <!--文章内容广告位1--> 
     <div class="content"> 

     {$gdata[content]} 

     <!--图片放大--> 
     <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
     <div id="innerdiv" style="position:absolute;"><img id="bigimg" style="border:5px solid #fff;" src="" /></div>
     </div>
     <!--图片放大-->      
     </div> 
     <div class="art-tags">
     {if:isset($gdata['tag_arr'])} {php}$k=0;{/php} {loop:$gdata[tag_arr] $v} {php}{$k++;}{/php}
     <a href="{$v[url]}" title="{$v[name]}" class="a-tag-{$k}"><span>#{$v[name]}</span></a>
     {/loop} {/if}
     </div>   
     <!--文章内容广告位2--> 
     {if:$gdata[pay]}
     <div class="art-copyright"> 
      <p>1、虚拟资源一经售出概不退款。<br />2、付费资源购买后请尽快下载保存。</p> 
     </div> 
     {else}
     <div class="art-copyright"> 
      <p>1、本站所有资源均为网友分享或网络收集整理而来，仅供学习和研究使用。<br/>2、如有侵犯您的版权，请联系我们指出，核实侵权，本站将立即改正删除。</p> 
     </div>      
     {/if}
    </div> 
	{if:isset($gdata[prev][url]) || isset($gdata[next][url])} 
    <div class="prev-next"> 
     <p class="art-prev"> {if:isset($gdata[prev][url])}<a href="{$gdata[prev][url]}" rel="prev"><span>上一篇</span>{$gdata[prev][title]}</a> {else}<a href="#" rel="prev"><span>上一篇</span>没有了</a>{/if}</p> 
     <p class="art-next"> {if:isset($gdata[next][url])}<a href="{$gdata[next][url]}" rel="next"><span>下一篇</span>{$gdata[next][title]}</a> {else}<a href="#" rel="next"><span>下一篇</span>没有了</a>{/if}</p> 
    </div> 
	{/if} 
	{/block}
	{block:taglike type="1" limit="8"} 
    <div class="related"> 
     <div class="com-tit">
      <h4 class="title-2">相关文章</h4>
     </div> 
     <ul class="related-main clearfix"> 
	 {loop:$data[list] $v}
      <li class="l"> <a href="{$v[url]}" title="{$v[title]}"> 
        <div class="related-img"> 
         <img src="{$v[pic]}" alt="{$v[subject]}" /> 
         <span class="mask"></span> 
        </div> <p>{$v[subject]}</p> </a> </li> 
      {/loop}
     </ul> 
    </div> 
	{/block} 
    <!--comment-->
    {hook:art_comment.htm}
   </div> 
    {inc:sider.htm}
  </div>  
<!--图片放大-->   
<script type="text/javascript">
 $(function(){ 
    $(".pic").click(function(){ 
      var _this = $(this);//将当前的pimg元素作为_this传入函数 
      imgShow("#outerdiv", "#innerdiv", "#bigimg", _this); 
    }); 
  }); 
  function imgShow(outerdiv, innerdiv, bigimg, _this){ 
    var src = _this.attr("src");//获取当前点击的pimg元素中的src属性 
    $(bigimg).attr("src", src);//设置#bigimg元素的src属性 
      /*获取当前点击图片的真实大小，并显示弹出层及大图*/
    $("<img/>").attr("src", src).load(function(){ 
      var windowW = $(window).width();//获取当前窗口宽度 
      var windowH = $(window).height();//获取当前窗口高度 
      var realWidth = this.width;//获取图片真实宽度 
      var realHeight = this.height;//获取图片真实高度 
      var imgWidth, imgHeight; 
      var scale = 1.2;//缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放 
      if(realHeight>windowH*scale) {//判断图片高度 
        imgHeight = windowH*scale;//如大于窗口高度，图片高度进行缩放 
        imgWidth = imgHeight/realHeight*realWidth;//等比例缩放宽度 
        if(imgWidth>windowW*scale) {//如宽度扔大于窗口宽度 
          imgWidth = windowW*scale;//再对宽度进行缩放 
        } 
      } else if(realWidth>windowW*scale) {//如图片高度合适，判断图片宽度 
        imgWidth = windowW*scale;//如大于窗口宽度，图片宽度进行缩放 
              imgHeight = imgWidth/realWidth*realHeight;//等比例缩放高度 
      } else {//如果图片真实高度和宽度都符合要求，高宽不变 
        imgWidth = realWidth; 
        imgHeight = realHeight; 
      } 
          $(bigimg).css("width",imgWidth);//以最终的宽度对图片缩放 
      var w = (windowW-imgWidth)/2;//计算图片与窗口左边距 
      var h = (windowH-imgHeight)/2;//计算图片与窗口上边距 
      $(innerdiv).css({"top":h, "left":w});//设置#innerdiv的top和left属性 
      $(outerdiv).fadeIn("fast");//淡入显示#outerdiv及.pimg 
    }); 
    $(outerdiv).click(function(){//再次点击淡出消失弹出层 
      $(this).fadeOut("fast"); 
    }); 
  }
</script>  
{hook:article_show_after.htm}
<script type="text/javascript" src="{$cfg[webdir]}static/layui/lib/layui/layers-V2.8.js"></script>
<!--图片放大-->  
{inc:footer.htm}
{if:$ziphtml==1} {php} $html_source = ob_get_contents(); ob_clean(); print compressHtml($html_source); ob_end_flush(); {/php} {/if} 