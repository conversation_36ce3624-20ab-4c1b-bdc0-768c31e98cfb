<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>{$cfg[titles]}</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="renderer" content="webkit">
  <link rel="shortcut icon" type="image/x-icon" href= "{$cfg[webdir]}favicon.ico" />
  <link rel="stylesheet" href="{$cfg[tpl]}user/css/frontend.min.css" media="all">
  <link rel="stylesheet" href="{$cfg[tpl]}user/css/user.css" media="all">
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
  <!--[if lt IE 9]>
  <script src="{$cfg[tpl]}user/js/html5shiv.js"></script>
  <script src="{$cfg[tpl]}user/js/respond.min.js"></script>
  <![endif]-->
  <script src="{$cfg[webdir]}static/js/jquery.js" charset="utf-8"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
</head>
<body>
<nav class="navbar navbar-white navbar-fixed-top" role="navigation">
  <div class="container">
    <div class="navbar-header">
      <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#header-navbar">
        <span class="sr-only">{lang:toggle}</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <a class="navbar-brand" href="/">{$cfg[webname]}</a>
    </div>
    <div class="collapse navbar-collapse" id="header-navbar">
      <ul class="nav navbar-nav navbar-right">
        <li><a href="/" title="{$cfg[webname]}">{lang:home}</a></li>
      </ul>
    </div>
  </div>
</nav>

<main class="content">
  <div id="content-container" class="container">
    <div class="user-section login-section">
      <div class="logon-tab clearfix">
        <a class="active" title="{lang:forget_password}" rel="nofollow">{lang:forget_password}</a>{if:$cfg[open_user_login]}<a href="{$login_url}" title="{lang:login}" rel="nofollow">{lang:login}</a>{/if}
      </div>
      <div class="login-main">
        <p style="font-size: 14px;color: red;">邮件发送成功,请查看邮件垃圾箱,有可能在那里</p>
        <p style="font-size: 14px;color: red;">如果邮件发送失败,请联系发卡网在线客服处理！！</p>
        <form id="login-form" class="form-horizontal layui-form" action="index.php?user-forget.html" method="post">
          <input type="hidden" name="FORM_HASH" value="{$form_hash}" />
          <div class="form-group">
            <label for="username" class="col-sm-4 control-label">{lang:username}</label>
            <div class="col-sm-8">
              <input class="form-control" id="username" type="text" name="username" value="" placeholder="{lang:please_input_username}" autocomplete="off">
            </div>
          </div>

          <div class="form-group">
            <label for="email" class="col-sm-4 control-label">{lang:email}</label>
            <div class="col-sm-8">
              <input class="form-control" id="email" type="email" name="email" value="" placeholder="{lang:email}" autocomplete="off">
            </div>
          </div>
          <div class="form-group">
            <label for="vcode" class="col-sm-4 control-label">{lang:vcode}</label>
            <div class="col-sm-4">
              <input class="form-control" id="vcode" type="text" name="vcode" value="" placeholder="{lang:vcode}" autocomplete="off">
            </div>
            <div class="col-sm-4">
              <img src="index.php?user-vcode-name-forgetvcode.html" alt="{lang:vcode}" onclick="this.src='index.php?user-vcode-name-forgetvcode-r-'+Math.random();" id="vcodeimg" style="width: 100%;" />
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label"></label>
            <div class="col-sm-9">
            <button type="submit" class="btn btn-primary btn-lg btn-block" lay-submit lay-filter="form">{lang:send_email}</button>
            </div>
          </div>
          {hook:user_user_forget_after.htm}
        </form>
      </div>
    </div>
  </div>
</main>

<footer class="footer" style="clear:both">
  <p class="copyright">Copyright&nbsp;©&nbsp;{php}echo date('Y');{/php} {$cfg[webname]} All Rights Reserved.</p>
</footer>
<script src="{$cfg[webdir]}static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    form.on('submit(form)', function (data) {
      data = data.field;
      if (data.username == '') {
        layer.msg('{lang:please_input_username}', {icon: 5});
        return false;
      }else if (data.email == '') {
        layer.msg('{lang:email_no_empty}', {icon: 5});
        return false;
      }else if (data.vcode == '') {
        layer.msg('{lang:vcode_no_empty}', {icon: 5});
        return false;
      }else{
        $.post("index.php?user-forget-ajax-1",data,function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location.reload(); }, 1000);
          return false;
        },'json');
        return false;
      }
    });
  });
</script>
</body>
</html>