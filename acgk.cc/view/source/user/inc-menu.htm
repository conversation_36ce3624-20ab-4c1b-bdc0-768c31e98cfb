<style>
    #logout{cursor: pointer;}
  .sidebar-toggle {
            display: block;
            position: fixed;
            right: 20px;
            top: 70px;
            border-radius: 50%;
            background: #007bff; /* 更鲜艳的背景颜色 */
            color: white; /* 字体颜色 */
            font-size: 24px; /* 增大字体大小 */
            padding: 10px;
            line-height: 30px;
            height: 60px; /* 增加按钮高度 */
            width: 60px; /* 增加按钮宽度 */
            text-align: center;
            z-index: 999999;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.5); /* 添加阴影效果 */
            transition: background-color 0.3s, transform 0.3s; /* 添加过渡效果 */
        }
</style>
<div class="col-md-3">
  <div class="sidebar-toggle"><i class="fa fa-bars"></i></div>
  <div id="sidebar-nav" class="sidenav">
    {loop:$_navs $v $k}
    <ul class="list-group">
      {if:!empty($v[href])}
      <li id="{$k}" class="list-group-heading">
        <a href="{$v[href]}" target="{$v[target]}">{$v[title]}</a>
      </li>
      {else}
      <li id="{$k}" class="list-group-heading">{$v[title]}</li>
      {/if}
      {loop:$v[child] $v2}
      <li id="{$v2[id]}" class="list-group-item">
        {if:!empty($v2[href])}
        <a href="{$v2[href]}" target="{$v2[target]}">{if:!empty($v2[icon])}<i class="{$v2[icon]}"></i> {/if}{$v2[title]}</a>
        {else}
        {if:!empty($v2[icon])}<i class="{$v2[icon]}"></i> {/if}{$v2[title]}
        {/if}
      </li>
      {/loop}
    </ul>
    {/loop}
  </div>
</div>
<script type="text/javascript">
  $(document).on("click", ".sidebar-toggle", function () {
    $("body").toggleClass("sidebar-open");
  });
</script>