<!DOCTYPE html>
<html>
 <head> 
  <meta charset="utf-8" /> 
  <meta name="renderer" content="webkit" /> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <meta name="applicable-device" content="pc,mobile" /> 
  <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no,minimum-scale=1,maximum-scale=1" /> 

  <title>{$cfg[titles]}</title>
  <meta property="og:type" content="acticle" />
  <meta property="og:image" content="{$cfg[webroot]}{$gdata[pic]}" />
  <meta property="og:author" content="{$gdata[author]}" />
  <meta property="og:site_name" content="{$cfg[webname]}" />
  <meta property="og:title" content="{$gdata[title]}" />
  <meta property="og:keywords" content="{$cfg[seo_keywords]}" />
  <meta property="og:description" content="{$cfg[seo_description]}" />
  <meta property="og:url" content="{$cfg[webroot]}{$gdata[url]}" />
  <meta property="og:release_date" content="{$gdata[date]}" />


   
  
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <link rel="stylesheet" href="{$cfg[tpl]}style/style.css" /> 
  <link rel="stylesheet" href="{$cfg[tpl]}style/iconfont.css" />
  <script src="{$cfg[tpl]}script/jquery-2.2.4.min.js" type="text/javascript"></script>  
  <script src="{$cfg[tpl]}script/common.js"></script> 
 </head> 
 <body> 
  <div class="header "> 
   <div class="container"> 
    <h1 class="logo"><a href="{$cfg[weburl]}" title="{$cfg[webname]}">{$cfg[webname]}</a></h1> 
     {block:navigate}
     <ul class="navbar clearfix"> 
     <li {if:empty($cfg_var['topcid'])} class="active" {/if}><a href="{$cfg[weburl]}">首页</a></li>
     {loop:$data $v}
     <li {if:$cfg_var['topcid']==$v['cid']} class="active" {/if}> <a href="{$v[url]}" title="{$v[name]}">{$v[name]}</a>
     {if:isset($v[son])}<ul>{loop:$v[son] $v2}<li><a href="{$v2[url]}" title="{$v2[name]}">{$v2[name]}</a></li>{/loop} </ul>{/if}
     </li>
     {/loop}
     <li class="nav-user nav-login">
        {if:$_uid}
        <a href="/my-index.html" class="personal-center">个人中心</a>
    	{else}
        <a href="/user-login.html" class="login-register">登录/注册</a>
    	{/if}</li>
     <li class="search"> <a href="javascript:;" title="搜索"><i class="iconfont icon-search"></i>搜索</a> </li> 
     {if:is_mobile()}
     {else} 
     <li class="nav-btn"> <a href="/index.php?u=comment-draftspublish" title="投稿" target="_blank"><i class="iconfont icon-pen"></i>投稿</a> </li>
     {/if}
     <li class="m-nav-1"><i class="iconfont icon-nav"></i></li>			
     </ul>
     {/block}
    <div class="m-nav"> 
     <i class="iconfont icon-nav"></i> 
    </div> 
    <div class="m-search"> 
     <i class="iconfont icon-search"></i> 
    </div> 
    <div class="sea-wrap"> 
     <div class="container"> 
      <form name="search" method="get" action="{$cfg[webdir]}index.php" target="_blank"> 
	   <input type="hidden" name="u" value="search-index" />
       <input type="text" name="keyword" size="11" placeholder="请输入关键字搜索" /> 
       <button class="sea-btn"><i class="iconfont icon-search"></i></button> 
      </form> 
     </div> 
    </div> 
   </div> 
   <div class="sea-mask"></div> 
  </div> 