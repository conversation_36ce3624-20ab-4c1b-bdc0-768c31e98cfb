{inc:header.htm}
{inc:functions.php}
{block:global_taglist pagenum="12" dateformat="m-d" showviews="1"} 
  <div class="inside-focus" style="background-image: url({$cfg[tpl]}style/images/list.jpg)"> 
   <div class="cate-header"> 
    <div class="container"> 
     <h1>{$tags[name]}</h1> 
     <p>共<b>{$gdata[total]}</b>条与标签“{$tags[name]}”相关内容</p> 
    </div> 
   </div> 
  </div> 
  <div class="container"> 
   <div class="col-main"> 
    <ul class="clearfix">
	 {loop:$gdata[list] $v} 
     <li class="art"> 
      <div class="art-con" style="position: relative; overflow: hidden;"> 
       <a href="{$v[url]}" title="{$v[title]}" class="art-pic-one"> <img src="{$v[pic]}" alt="{$v[subject]}" /> <span class="mask"></span> </a> 
       <a href="{$v['cate_name']}" title="{$v['cate_name']}" class="cat">{$v['cate_name']}</a> 
       <div class="art-main"> 
        <b class="post-tit"><a href="{$v[url]}" title="{$v[title]}">{$v[subject]}</a></b> 
        <p class="col-art-meta clearfix"> <span><i class="iconfont icon-time"></i>{$v[date]}</span> <span><i class="iconfont icon-view"></i>{$v['views']}</span> </p> 
       </div> 
      </div> 
	  </li> 
	  {/loop} 
    </ul> 
	{if:is_mobile()} 
    <div class="pages">
     {$gdata[pages]}
    </div> {else} 
    <div class="pages">
     <span>共 <font color="red">{$gdata[total]}</font> 篇</span>{$gdata[pages]}
    </div> 
	{/if} 
	{/block} 
   </div> 
  </div> 
{inc:footer.htm}
{if:$ziphtml==1} {php} $html_source = ob_get_contents(); ob_clean(); print compressHtml($html_source); ob_end_flush(); {/php} {/if} 