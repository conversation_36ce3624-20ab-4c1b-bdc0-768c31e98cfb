<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
<meta http-equiv="Cache-Control" content="no-transform"/>
<meta http-equiv="Cache-Control" content="no-siteapp"/>
<meta name="applicable-device" content="pc,mobile">
<link rel="shortcut icon" href="{$cfg[tpl]}img/favicon.ico" />
<link rel="icon" sizes="32x32" href="{$cfg[tpl]}img/favicon.png">
<link rel="Bookmark" href="{$cfg[tpl]}img/favicon.png" />
<title>飞雪ACG</title>
<meta name="keywords" content="{$cfg[seo_keywords]}" />
<meta name="description" content="飞雪ACG免费二次元聚集地,欢迎分享！！" />
<link rel="stylesheet" href="{$cfg[tpl]}css/bootstrap-v2.css?1.1">
<link rel="stylesheet" href="{$cfg[tpl]}css/bootstrap-bbs-v2.css?1.2">
<link rel="stylesheet" href="{$cfg[tpl]}css/tag.css?1.1">
{$cfg[tongji]}
</head>
<style type="text/css">
.tupiansa{display:flex;flex-wrap:wrap}.tupiansa img{width:100px;margin-bottom:10px;border-radius:5px;clip-path:inset(0 0 10% 0);height:80px;margin-left:2px;margin-right:2px}@media (max-width:768px){.tupiansa img{width:calc(30%);clip-path:inset(0 0 15% 0)}}
</style>
<body>

	<header class="navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="container">
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="navbar-toggler-icon"></span>
			</button>

			<a class="navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			<a class="navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="icon-search"></i></a></a>
			<div class="collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				{block:navigate}
				<ul class="navbar-nav mr-auto">
					<li{if:$cfg_var['cid'] == $v['cid']} class="nav-item home  active"{else} class="nav-item home"{/if}><a class="nav-link" href="/"><i class="icon-home d-md-none"></i>首页</a></li>	
					{loop:$data $v}
					<li{if:$cfg_var['cid'] == $v['cid']} class="nav-item  active"{else} class="nav-item"{/if}>
						<a class="nav-link" href="{$v[url]}"><i class="icon-circle-o d-md-none"></i>{$v[name]}</a>
					</li>
					{/loop}
                  	<li class="nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	{if:$_uid}
                        <a href="/my-index.html" class="nav-link">个人中心</a>
                    {else}
                        <a href="/user-login.html" class="nav-link">登录/注册</a>
                    {/if}
                  	</li>
				</ul>{/block}
				<!-- 右侧：用户 -->
				<ul class="navbar-nav">
					<li class="nav-item"><a class="nav-link" href="/search/"><i class="icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="container">
<div class="row">
	<div class="col-lg-9 main">
		<div class="card card-threadlist ">
			<div class="card-header">
				<div class="nav nav-tabs card-header-tabs">
					<div class="nav-item">
                      	{block:content_total_by_date mid="2" type="today"}<div class="today-posts" style="background-color: #fff;color: #343a40;border-radius: 5px;text-align: center;font-size: 1.9rem;transition: background-color 0.3s;font-weight: 900;">今日发布：<m style="font-size: 2.8rem;color: red;"> {$data} </m>篇</div>{/block}
						<div class="nav-link active">最新</div>
                      {block:ads id="101"}{$data[content]}{/block}
					</div>
				</div>
			</div>
			{block:ads id="001"}{$data[content]}{/block}
			<div class="card-body">
				<ul class="list-unstyled threadlist mb-0">
				{block:list_flag flag="1" limit="5" dateformat="Y-m-d" showcate="1" showviews="1" life="60"}
{php}
$curr_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
{/php}
{if:$curr_page == 1}
{loop:$data[list] $v}
					<li class="media thread tap top_3" style="background-color: #0b0c0d;" data-href="{$v[url]}">
						<div class="media-body">
							<div class="subject break-all">
								<i class="icon-top-3"></i>
								<a href="{$v[url]}" target="_blank"><span style="color: red;font-weight: bold">{$v[title]}</span></a>
							</div>
							<div class="d-flex justify-content-between small mt-1">
								<div>
									<span class="haya-post-info-username ">
									<a style="color: #212529;text-decoration: none;background-color: #edb91d;display: inline-block;padding: 0.25em 0.4em;font-weight: 500;line-height: 1;text-align: center;white-space: nowrap;vertical-align: baseline;padding-right: 0.6em;padding-left: 0.6em;border-radius: 10rem;" href="{$v[cate_url]}" ><span class="board-bg" style="border-radius: 2px;background-color: #F21120;width: 0.64rem;height: 0.64rem;display: inline-block;margin-right: 5px;"></span>{$v[cate_name]}</a>
									<span class="koox-g"> • </span>
									<span class="username text-grey mr-1">{$v[author]}</span>
									<span class="date text-grey"  data-date="{$v[date]}">{$v[date]}</span>
									</span>
								</div>
								<div class="text-muted small">
									<span class="eye comment-o ml-2 hidden-sm d-none"><i class="jan-icon-fire-1"></i>{$v[views]}</span>
									<span class="likes comment-o ml-2"><i class="icon icon-thumbs-o-up" aria-label="点赞"></i>{$v[likes]}</span>
								</div>
							</div>
						</div>
					</li>
					<div class="jan-hr"></div>
					{/loop}
{/if}
{/block}
{block:global_blog mid="2" cid="0" showcate="1" showviews="1" pagenum="12" dateformat="Y-m-d" pageoffset="3"}
{loop:$gdata[list] $v}
{php}
switch ($v['cid']){
    case 1:
        $color = '#0df226';
        break;
    case 2:
        $color = '#53BEF1';
        break;
    case 3:
        $color = '#F21120';
        break;
    default:
        $color = '#53BEF1';
}
{/php}
					<li class="media thread tap" data-href="{$v[url]}">
						<div class="media-body">
							<div class="subject break-all">
							<a href="{$v[url]}"  target="_blank">{$v[title]}</a>
							</div>
                              {if:$_uid}
                              <div class="tupiansa">
                              {loop:$v[piclist] $src}
                              <img src="{$src}" alt="{$v[title]}">
                              {/loop}
                              </div>
                              {else}{/if}
							<div class="d-flex justify-content-between small mt-1">
								<div>
									<span class="haya-post-info-username ">
									<a style="color: #212529;text-decoration: none;background-color: #edb91d;display: inline-block;padding: 0.25em 0.4em;font-weight: 500;line-height: 1;text-align: center;white-space: nowrap;vertical-align: baseline;padding-right: 0.6em;padding-left: 0.6em;border-radius: 10rem;" href="{$v[cate_url]}"  target="_blank"><span class="board-bg" style="border-radius: 2px;background-color: {$color};width: 0.64rem;height: 0.64rem;display: inline-block;margin-right: 5px;"></span>{$v[cate_name]}</a>
									<span class="koox-g"> • </span>
									<span class="username text-grey mr-1">{$v[author]}</span>
									<span class="date text-grey" data-date="{$v[date]}">{$v[date]}</span>
									</span>
								</div>
								<div class="text-muted small">
									<span class="eye comment-o ml-2 hidden-sm d-none"><i class="jan-icon-fire-1"></i>{$v[views]}</span>
									<span class="likes comment-o ml-2"><i class="icon icon-thumbs-o-up" aria-label="点赞"></i>{$v[likes]}</span>
								</div>
							</div>
						</div>
					</li>
					<div class="jan-hr"></div>
					{/loop}
					{/block}
				</ul>
			</div>
		</div>
				
		<div class="pagenav ajax-pag">{$gdata[pages]}</div>
<style>
.pagenav.ajax-pag {text-align:center;margin:5px 0px 5px;}
.pagenav.ajax-pag a {padding:15px;}
</style>
		
	</div>
	<div class="col-lg-3 d-none d-lg-block aside">

		<div class="card card-site-info">
			<div class="m-3">
				<div class="small line-height-3">
				{$cfg[seo_description]}
				</div>
			</div>
				<div class="card-footer p-2">
				{block:data_total mid="2" showviews="1"}
				<table class="w-100 small">
					<tr align="center">
						<td>
							<span class="text-muted">文章数</span><br>
							<b>{$data[content]}</b>
						</td>
						<td>
							<span class="text-muted">标签数</span><br>
							<b>{$data[tag]}</b>
						</td>
						<td>
							<span class="text-muted">阅读数</span><br>
							<b>{$data[views]}</b>
						</td>
					</tr>
				</table>{/block}
			</div>
			
		</div>

<!--<div class="form-group">
  <form action="{$cfg[webdir]}index.php" id="search_form" name="search" method="get">
      <div class="input-group">
        <input type="hidden" name="u" value="search-index" />
        <input type="hidden" name="mid" value="2" />
        <input type="text" class="form-control" placeholder="关键词" name="keyword">
        <div class="input-group-append">
          <button class="btn btn-primary" type="submit">搜索</button>
        </div>
      </div>
  </form>
</div>-->

<!-- 主页右侧 -->
<div class="card">
		  <div class="card-header">热门推荐</div>
			<div class="card-body user-recent">
				{block:list_rand mid="2" limit="8" dateformat="Y-m-d" showviews="1" titlenum="24" life="120"}
				<ul class="small break-all">
					{loop:$data[list] $v}
					<li class="line-height-2">
						<a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[title]}</a>
					</li>
					{/loop}
				</ul>{/block}
			</div>
</div>

<div class="card">
	<div class="card-header">热门标签</div>
<div id="taghot">
	{block:taglist_rand mid="2" limit="20"}
	<ul>
		{loop:$data[list] $v}
		<li><a title="{$v[name]}" href="{$v[url]}">{$v[name]}</a></li>
		{/loop} 				  
	</ul>{/block}
</div>
</div>

		{block:links}
		{if:$data}
		<div class="card friendlink">
			<div class="card-header">友情链接</div>
			<div class="card-body small">
				<ul>
					{loop:$data $v}
					<li class="mb-1 small line-height-2">
						<a href="{$v[url]}" target="_blank">{$v[name]}</a>
					</li>
					{/loop}
				</ul>
			</div>
		</div>
		{/if}
		{/block}
	</div>
</div>	
		</div>
	</main>

	<footer class="text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="container">
		<div class="row">
			<div class="col">
			{$cfg[copyright]}
			</div>
			<!--<div class="col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<script src="{$cfg[tpl]}js/jquery-3.1.0.js?1.0"></script>
<script src="{$cfg[tpl]}js/bootstrap.js?1.0"></script>
<link rel="stylesheet" href="{$cfg[tpl]}css/scroll.css">
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
<a id="scroll_to_top" href="javascript:void(0);"  class="mui-rightlist"  title="返回顶部" style="display: none;"><i class="icon-angle-double-up"></i></a>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const todayFormatted = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');

    // 获取所有包含 data-date 的元素
    const dateElements = document.querySelectorAll('.date[data-date]');

    dateElements.forEach(function(element) {
        // 获取元素中的发布时间
        const publishDate = element.getAttribute('data-date');
        
        // 如果发布时间和今天的日期相同，修改字体颜色为红色
        if (publishDate === todayFormatted) {
            element.style.setProperty('color', 'red', 'important');
            element.style.fontWeight = 'bold'; // 可选，设置粗体
        }
    });
});
 </script>
 <script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>