<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
<meta http-equiv="Cache-Control" content="no-transform"/>
<meta http-equiv="Cache-Control" content="no-siteapp"/>
<meta name="applicable-device" content="pc,mobile">
<link rel="shortcut icon" href="{$cfg[tpl]}img/favicon.ico" />
<link rel="icon" sizes="32x32" href="{$cfg[tpl]}img/favicon.png">
<link rel="Bookmark" href="{$cfg[tpl]}img/favicon.png" />
<title>标签库 - 飞雪ACG</title>
<meta name="keywords" content="{$cfg[seo_keywords]}">
<meta name="description" content="飞雪ACG">
<link rel="stylesheet" href="{$cfg[tpl]}css/bootstrap-v2.css?1.0">
<link rel="stylesheet" href="{$cfg[tpl]}css/bootstrap-bbs-v2.css?1.2">
<link rel="stylesheet" href="{$cfg[tpl]}css/tag.css?1.0">
{$cfg[tongji]}
</head>

<body>

	<header class="navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="container">
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="navbar-toggler-icon"></span>
			</button>

			<a class="navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			<a class="navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="icon-search"></i></a></a>
			<div class="collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				{block:navigate}
				<ul class="navbar-nav mr-auto">
					<li{if:$cfg_var['cid'] == $v['cid']} class="nav-item home  active"{else} class="nav-item home"{/if}><a class="nav-link" href="/"><i class="icon-home d-md-none"></i> 首页</a></li>	
					{loop:$data $v}
					<li{if:$cfg_var['cid'] == $v['cid']} class="nav-item  active"{else} class="nav-item"{/if}>
						<a class="nav-link" href="{$v[url]}"><i class="icon-circle-o d-md-none"></i>{$v[name]}</a>
					</li>
					{/loop}
                  	<li class="nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	{if:$_uid}
                        <a href="/my-index.html" class="nav-link">个人中心</a>
                    {else}
                        <a href="/user-login.html" class="nav-link">登录/注册</a>
                    {/if}
                  	</li>
				</ul>{/block}
				<!-- 右侧：用户 -->
				<ul class="navbar-nav">
					<li class="nav-item"><a class="nav-link" href="/search/"><i class="icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="container">

<style>.mt-3, .my-3 {margin-top: 0.3rem !important;}.card.search { margin-bottom: 1.3rem !important;}</style>

<div class="row">
	<div class="col-lg-10 mx-auto">
		<div class="card">
			<div class="card-body pb-0">
				<form action="{$cfg[webdir]}index.php" id="search_form" name="search" method="get">
					<div class="input-group mb-3">
						<input type="hidden" name="u" value="search-index" />
						<input type="hidden" name="mid" value="2" />
						<input type="text" class="form-control" placeholder="关键词" name="keyword">
						<div class="input-group-append">
							<button class="btn btn-primary" type="submit" id="submit">搜索</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<style> em{color: #c6303e !important;}</style>

<div class="card">
	<div class="card-header">标签大全</div>
<div id="taghot">
	{block:global_tag mid="2" pagenum="50"}
	<ul>
		{loop:$data[list] $v}
		<li><a title="{$v[name]}" href="{$v[url]}">{$v[name]}({$v[count]})</a></li>
		{/loop} 				  
	</ul>{/block}
</div>
</div>

</div>
		
		</div>
	</div>
	</main>

	<footer class="text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="container">
		<div class="row">
			<div class="col">
			{$cfg[copyright]}
			</div>
			<!--<div class="col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<script src="{$cfg[tpl]}js/jquery-3.1.0.js?1.0"></script>
<script src="{$cfg[tpl]}js/bootstrap.js?1.0"></script>
<link rel="stylesheet" href="{$cfg[tpl]}css/scroll.css">
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
<a id="scroll_to_top" href="javascript:void(0);"  class="mui-rightlist"  title="返回顶部" style="display: none;"><i class="icon-angle-double-up"></i></a>
</div>

<script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>