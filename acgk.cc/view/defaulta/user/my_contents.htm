{inc:user/header.htm}
<style>
  .panel-post {position: relative;}
  .embed-responsive img {position: absolute;object-fit: cover;width: 100%;height: 100%;border: 0;}
  .comment-content {padding: 15px;background: #efefef;color: #444;}
  .panel-user h4 {font-weight: normal;font-size: 14px;}
  .float-right{float: right;}
  .pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span{border-bottom-right-radius:3px;border-top-right-radius:3px;}
  .pager li:first-child > a, .pager li:last-child > a, .pager li:first-child > span, .pager li:last-child > span{border-bottom-left-radius:3px;border-top-left-radius:3px;}
</style>
  <main class="content">
    <div id="content-container" class="container">
      <div class="row">
        {inc:user/menu.htm}
        <!--右侧主体部分 start-->
        <div class="col-md-9">
          <div class="panel panel-default">
            <div class="panel-body">
              <div class="panel-post">
                <h2 class="page-header">{lang:my_contents}</h2>
                <div style="position:absolute;bottom:12px;right:0;">
                  {$midselect}
                </div>
              </div>
              {if:empty($cms_arr)}
              <div class="alert alert-warning"><b>{lang:no_data}</b></div>
              {else}
              {loop:$cms_arr $v}
              <div class="row">
                <div class="col-md-3 text-center">
                  <a href="{$v[url]}" title="{$v[title]}" target="_blank" class="img-thumb">
                    <div class="embed-responsive embed-responsive-4by3 img-zoom">
                      <img src="{$v[pic]}" class="embed-responsive-item" alt="{$v[title]}" />
                    </div>
                  </a>
                </div>
                <div class="col-md-9">
                  <h4>
                    <a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[title]}</a>
                  </h4>
                  <p class="comment-content">{$v[intro]}</p>
                  <p class="text-muted">{lang:date}：{$v[date]}<button type="button" mid="{$mid}" cmsid="{$v[id]}" class="del btn btn-danger btn-xs float-right">{lang:delete}</button></p>
                </div>
              </div>
              <hr/>
              {/loop}
              {/if}
              <div class="pager">{$pages}</div>
            </div>
          </div>
        </div>
        <!--右侧主体部分 end-->
      </div>
    </div>
  </main>
{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;

    $("#my-contents").addClass("active");

    $("#mid").change(function () {
      var mid = $(this).val();
      window.location = "index.php?my-contents-mid-"+mid+".html";
    });

    $(".del").click(function () {
      var id = $(this).attr("cmsid");
      var mid = $(this).attr("mid");
      layer.confirm('{lang:delete_confirm}', {
        btn: ['{lang:confirm}','{lang:cancel}'],
        title:'{lang:tips}'
      }, function () {
        $.post("index.php?my-contents-act-del-ajax-1",{id: id, mid: mid},function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location.reload(); }, 1000);
          return false;
        },'json');
      });
    });
  });
</script>
</body>
</html>