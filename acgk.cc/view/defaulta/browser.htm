<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>提示信息 / Information</title>
<style type="text/css">
a {color:#d4557f; font-size: 12px; color: black;}
a:hover {color:#a01845}
.main{width:1000px; height:350px; margin:20px auto; background:url(view/default/img/browser.gif) no-repeat; position:relative}
.pos1{position:absolute; top:300px; left:380px}
.pos2{position:absolute; top:300px; left:570px}
.pos3{position:absolute; top:300px; left:760px}
</style>
</head>
<body>
<div class="main">
	<a class="pos1" href="http:///browser-download-chrome.htm" target="_blank">下载/Download Chrome</a>
	<a class="pos2" href="http:///browser-download-firefox.htm" target="_blank">下载/Download Firefox</a>
	<a class="pos3" href="http:///browser-download-ie.htm" target="_blank">下载/Download IE10+</a>
</div>
<br>
<p style="font-size: 12px; text-align: center; color: #888888;">您的浏览器信息 / Your browser information：<script>document.write(navigator.userAgent);</script></p>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>