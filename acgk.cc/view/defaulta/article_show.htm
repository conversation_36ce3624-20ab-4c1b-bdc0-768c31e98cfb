<!DOCTYPE html>
    <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
    <meta http-equiv="Cache-Control" content="no-transform"/>
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta name="applicable-device" content="pc,mobile">
    <link rel="shortcut icon" href="{$cfg[tpl]}img/favicon.ico" />
    <link rel="icon" sizes="32x32" href="{$cfg[tpl]}img/favicon.png">
    <link rel="Bookmark" href="{$cfg[tpl]}img/favicon.png" />
    <title>{$gdata[title]} - 飞雪ACG</title>
    {if:$gdata[seo_keywords]}
    <meta name="keywords" content="{$gdata[seo_keywords]}" />
    {elseif:$gdata[tags]}
    <meta name="keywords" content="{php}echo implode(',',$gdata['tags']);{/php}" />
    {else}
    <meta name="keywords" content="{$cfg[seo_keywords]}" />
    {/if}
    <meta name="description" content="{$cfg[seo_description]}">
    <meta property="og:locale" content="zh_CN"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{$gdata[absolute_url]}"/>
    <meta property="og:width" content="800"/>
    <meta property="og:author" content="{$cfg[webname]}">
    <meta property="og:update_time" content="{php} echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']);{/php}">
    <meta property="og:published_time" content="{php}echo date('Y-m-d H:i:s');{/php}">
    <meta property="og:title" content="{$gdata[title]} - {$cfg[webname]}"/>
    <meta property="og:keywords" content="{$gdata[title]}"/>
    <meta property="og:description" content="{$cfg[seo_description]}"/>
    <link rel="stylesheet" href="{$cfg[tpl]}css/bootstrap-v2.css?1.0">
    <link rel="stylesheet" href="{$cfg[tpl]}css/bootstrap-bbs-v2.css?1.2">
    <link rel="stylesheet" href="{$cfg[tpl]}css/tag.css?1.0">
{$cfg[tongji]}
       <style>
	img{width:100%;height:auto;display:block;clip-path:inset(0 0 25% 0)}.notice{background:linear-gradient(135deg,#007bff,#00c6ff);color:white;border-radius:10px;padding:20px;text-align:center;margin:20px 0;box-shadow:0 4px 15px rgba(0,123,255,0.5);transition:transform 0.3s}.notice a{color:white;text-decoration:none;font-size:18px;font-weight:bold;display:inline-block;padding:10px 20px;border-radius:5px;background-color:rgba(255,255,255,0.2);transition:background-color 0.3s}.notice a:hover{background-color:rgba(255,255,255,0.4);text-decoration:none}.notice:hover{transform:scale(1.02).message img{max-width:100%}}
    </style>
</head>

<body>

	<header class="navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="container">
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="navbar-toggler-icon"></span>
			</button>

			<a class="navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			
			<a class="navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="icon-search"></i></a></a>
			
			<div class="collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				{block:navigate}
				<ul class="navbar-nav mr-auto">
					<li{if:$cfg_var['cid'] == $v['cid']} class="nav-item home  active"{else} class="nav-item home"{/if}><a class="nav-link" href="/"><i class="icon-home d-md-none"></i> 首页</a></li>	
					{loop:$data $v}
					<li{if:$cfg_var['cid'] == $v['cid']} class="nav-item  active"{else} class="nav-item"{/if}>
						<a class="nav-link" href="{$v[url]}"><i class="icon-circle-o d-md-none"></i>{$v[name]}</a>
					</li>
					{/loop}
                  	<li class="nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	{if:$_uid}
                        <a href="/my-index.html" class="nav-link">个人中心</a>
                    {else}
                        <a href="/user-login.html" class="nav-link">登录/注册</a>
                    {/if}
                  	</li>
				</ul>{/block}
				<!-- 右侧：用户 -->
				<ul class="navbar-nav">
					<li class="nav-item"><a class="nav-link" href="/search/"><i class="icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="container">
<style>.col-lg-9.main .mb-3, .my-3 {margin-bottom: 0.5rem !important;} .card.card-postlist {margin-bottom: 1.3rem;}.bilibili{position:relative;width:100%;height:0;padding-bottom:75%}.bilibili iframe{position:absolute;width:100%;height:100%;left:0;top:0}</style>
<div class="row u_2006">
	<div class="col-lg-9 main">
		<ol class="breadcrumb d-none d-md-flex">
			<li class="breadcrumb-item"><a href="/" aria-label="首页"><i class="icon-home"></i></a></li>
			{loop:$cfg_var[place] $v}
			<li class="breadcrumb-item"><a href="{$v[url]}">{$v[name]}</a></li>
			{/loop}
			<li class="breadcrumb-item active">正文</li>
		</ol>

		<div class="jan card card-thread">
			<div class="card-body">
				<div class="media">
					<div class="media-body">
						{block:global_show show_prev_next="1"}{/block}
						<h1 class="break-all">{$gdata[title]}</h1>
						
						<div class="d-flex justify-content-between small">
							<div>
								<span class="username">
									<a href="#" class="text-muted font-weight-bold">{$gdata[author]}</a>
								</span>
								<span class="date text-grey ml-2"><i class="jan-icon-clock-1"></i>{$gdata[date]}</span>
								<span class="text-grey ml-2"><i class="jan-icon-eye-4"></i>{$gdata[views]}</span>
							</div>
						</div>
					</div>
				</div>
				<hr class="jan-hr-1">
				<div class="message break-all">

				<div id="content" style="padding: 0 0 8px;">
                 <p>如果您是小白,不会解压,分包解压等转区,英文路径请勿下载,没有任何指导</p>
                 <!--<div class="notice">
                  <a href="https://acgk.cc/" target="_blank" rel="noopener noreferrer">
                      本站体验不佳,点我前往新网站,账号密码同步,无需注册即可登录,同步数据
                  </a>
                  </div>-->
                {$gdata[content]}
                <br /><br /><p>{hook:favorites.htm}</p><br /><br /><br /></div>
				{block:ads id="302"}{$data[content]}{/block}
{if:$gdata[tag_arr]}
	<ul id="tag">
		<li>标签:</li>{loop:$gdata[tag_arr] $v}<li><a href="{$v[url]}" title="查看标签为《{$v[name]}》的所有文章"><i class="icon-tag"></i>{$v[name]}</a></li>{/loop}
	</ul>{/if}

</div>

		<div class="plugin d-flex justify-content-center mt-3">
							
		<div class="haya-post-like px-2">
			<span class="btn-group" role="group">
						<a href="javascript:;" id="likes_do" class="btn btn-outline-secondary js-haya-post-like-thread-tip" data-pid="{$gdata[id]}"><i class="icon icon-thumbs-o-up" aria-label="点赞文章"></i>
						<span class="haya-post-like-thread-btn">点赞</span></a>

<script src="{$cfg[tpl]}js/jquery.min.js?1.0"></script>
<script type="text/javascript">
$("#likes_do").click(function () {
    $.getJSON("{$gdata[likes_url]}", function(data){
        if(data.err){
            alert(data.msg);
        }else{
            $("#likes_count").html(data.likes_count);
            alert(data.msg);
        }
    });
});

$(document).ready(function(){
        $('#likes_do').click(function(){
            // 获取当前点赞数量
            var countElement = $(this).siblings('button').find('.haya-post-like-thread-user-count');
            var currentLikes = parseInt(countElement.text());

            // 这里假设点赞成功，直接更新前端数量
            currentLikes++;
            countElement.text(currentLikes);
        });
    });
</script>
				<button class="btn btn-outline-secondary" title="点赞数量" data-tid="{$gdata[id]}">
					<span class="haya-post-like-thread-user-count">{$gdata[likes]}</span>
				</button>
				
			</span>
		</div>
			<button type="button" onclick="clickBut('content')" class="btn btn-outline-secondary js-haya-post-like-thread-tip">一键复制</button>
<script>
function clickBut(id){
var value=document.getElementById(id).innerText;
var temDom = document.createElement('input');
temDom.setAttribute('value', value);
document.body.appendChild(temDom);
temDom.select();
document.execCommand("Copy");
temDom.style.display='none';
alert('复制成功');
document.body.removeChild(temDom);
}
</script>
				</div>
			</div>
		</div>
		
	<div class="jan-re card card-body">
		{php}$taglikedata = 0;{/php}
		{block:taglike type="1" dateformat="Y-m-d" limit="5"}
		{if:$data[list]}
		{php}$taglikedata = 1;{/php}
		<div class="card-title"><b>相关资源</b></div>			
				{loop:$data[list] $v}
				<hr class="jan-hr-2">
				<div class="relate_post">
				    <a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[title]}</a>	
                       <span style="float:right;">{$v[date]}</span>
				</div>
				{/loop}
		{/if}
		{/block}
		{if:$taglikedata == 0}
		{block:list mid="2" limit="10" dateformat="Y-m-d" showviews="1" life="120"}
		<div class="card-title"><b>相关资源</b></div>			
				{loop:$data[list] $v}
				<hr class="jan-hr-2">
				<div class="relate_post">
				    <a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[title]}</a>	
                       <span style="float:right;">{$v[date]}</span>
				</div>
				{/loop}
		{/block}
		{/if}
   </div>
		
		<div><a role="button" class="btn btn-secondary btn-block xn-back col-lg-6 mx-auto mb-3" href="javascript:history.back();">返回</a></div>
	
	</div>
	
	<div class="col-lg-3 d-none d-lg-block aside">

<div class="card card-user-info">
			<div class="m-3 text-center">
					<img class="avatar-5" src="{$cfg[tpl]}img/tx.png" alt="{$gdata[author]}的头像">
				<h5>{$gdata[author]}</h5>
			</div>
</div>

<!--<div class="form-group">
  <form action="{$cfg[webdir]}index.php" id="search_form" name="search" method="get">
      <div class="input-group">
        <input type="hidden" name="u" value="search-index" />
        <input type="hidden" name="mid" value="2" />
        <input type="text" class="form-control" placeholder="关键词" name="keyword">
        <div class="input-group-append">
          <button class="btn btn-primary" type="submit">搜索</button>
        </div>
      </div>
  </form>
</div>-->

<div class="card">
		  <div class="card-header">作者最近文章</div>
			<div class="card-body user-recent">
				{block:list_by_uid mid="2" limit="5" dateformat="Y-m-d"}
				<ul class="small break-all">
					{loop:$data[list] $v}
					<li class="line-height-2">
						<a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[title]}</a>
					</li>
					{/loop}
				</ul>{/block}
			</div>
</div>

</div>

		</div>
	</main>
	
	<footer class="text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="container">
		<div class="row">
			<div class="col">
			{$cfg[copyright]}
			</div>
			<!--<div class="col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<script src="{$cfg[tpl]}js/jquery-3.1.0.js?1.0"></script>
<script src="{$cfg[tpl]}js/bootstrap.js?1.0"></script>
<link rel="stylesheet" href="{$cfg[tpl]}css/scroll.css">
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
	<a id="scroll_to_top" href="javascript:void(0);"  class="mui-rightlist"  title="返回顶部" style="display: none;"><i class="icon-angle-double-up"></i></a>
</div>

<script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>
{hook:article_show_after.htm}
<script type="text/javascript" src="{$cfg[webdir]}static/layui/lib/layui/layers-V2.8.js"></script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>
