<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<title>{$gdata[title]}</title>
<meta name="keywords" content="{$cfg[seo_keywords]}" />
<meta name="description" content="{$cfg[seo_description]}" />
<link rel="stylesheet" href="{$cfg[tpl]}css/common.css">
<link rel="stylesheet" href="{$cfg[tpl]}css/jinyan.css">
<script src="{$cfg[webdir]}static/js/jquery.js"></script>
<meta http-equiv="Cache-Control" content="no-transform" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<meta name="applicable-device" content="pc,mobile" />
</head>
<body>
<header>
<a href="{$cfg[weburl]}" class="aikahao_logo" title="{$cfg[webname]}"></a>
<div class="search_box">
<div class="seek">
<form name="get" class="model_form_con clearfix" method="get" id="search_form" action="{$cfg[webdir]}index.php" target="_blank">
<div class="seek_cn">
<input type="text" id="hsearchkey" name="keyword" autocomplete="off" class="input_txt padding_input" placeholder="请输入关键字">
<input type="hidden" name="u" value="search-index" />
<input type="hidden" name="mid" value="2" />
</div>
</form>
</div>
</div>
<div class="nevigate">
{block:navigate}
<ul class="nevigate_ul">
{loop:$data $v}
<li{if:$cfg_var['topcid'] == $v['cid']} class="active"{/if}><a href="{$v[url]}">{$v[name]}</a></li>{/loop}
</ul>{/block}
</div>
</header>
<div class="detail_content clearfix">
{block:global_show show_prev_next="1" dateformat="Y-m-d"}
<div class="buzou-timg"> 
<img src="{$gdata[pic]}" alt="{$gdata[title]}">
<div class="timgbj"></div> 
<h1>{$gdata[title]}</h1>
</div>
<p class="exp-info">
<span class="i-origin">原创</span>
<span>|</span>
<span class="browser">
<span class="time">{$gdata[date]}</span>
<span>|</span>浏览：{$gdata[views]}</span> 
</p>
<article class="article-body">
<div class="abstract-text">
{$gdata[content]}
{if:isset($gdata['tag_arr'])}
标签：{loop:$gdata[tag_arr] $v}<a class="th_hover_a1" href="{$v[url]}" title="{$v[name]}">{$v[name]}</a> {/loop}
{/if}
</div>
</article>
{/block}

{block:taglike type="1" limit="5" dateformat="Y-m-d"}
<div class="detael_choices" style="border-top: 8px solid #f4f4f4;">
<div class="choices_cont">
<h3><span>推荐阅读</span></h3>
{if:$data[list]}
<ul class="choices_wen">
{loop:$data[list] $v}
<li>
<div class="leftImg">
<img src="{$v[pic]}" alt="{$v[title]}">
</div>
<div class="txtCon_one">
<a href="{$v[url]}">{$v[title]}</a>
<span class="txt_con">{$v[intro]}..</span>
</div>
</li>
{/loop}
</ul>
{/if}
</div>
</div>
{/block}

<div class="detael_choices">
<div class="choices_cont">
<h3><span>猜你喜欢</span></h3>
{block:list_rand limit="5" life="0"}
{if:$data[list]}
<ul class="choices_wen">
{loop:$data[list] $v}
<li>
<div class="leftImg">
<img src="{$v[pic]}" alt="{$v[title]}">
</div>
<div class="txtCon_one">
<a href="{$v[url]}">{$v[title]}</a>
<span class="txt_con">{$v[intro]}..</span>
</div>
</li>{/loop}
</ul>{/if}{/block}
</div>
</div>
</div>
<footer class="content_nt mar_top8">
<div class="footer_nt">
<p><a href="/sitemap.html" target="_blank">网站地图</a>|<a href="/sitemap.xml" target="_blank">RS订阅</a></p>
<p> Copyright 2023 {$cfg[weburl]} <b>【{$cfg[webname]}】</b> 版权所有 | <img src="{$cfg[tpl]}picture/gonganbeian.png"><a rel="nofollow" href="//beian.miit.gov.cn">{$cfg[beian]}</a></p>
<p>声明：本站为非赢利网站，作品与素材版权均归作者所有，如内容侵权与违规请与本站联系，将在三个工作日内处理，互联网违法和不良信息举报邮箱：{$cfg[webmail]}</p>
</div>
</footer>
<script>
(function() {
$("#search_form").submit(function() {
var mid = $(this).find("[name='mid']").val();
var keyword = $(this).find("[name='keyword']").val();
window.location.href = "/search/" + encodeURIComponent(keyword);
return false;
});
})();
</script>
</body>
</html>