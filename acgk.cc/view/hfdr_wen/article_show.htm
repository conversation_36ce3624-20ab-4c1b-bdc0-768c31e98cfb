{block:global_show show_prev_next="1" dateformat="Y-m-d H:i:s"}{/block}
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<title>{$gdata[title]} - {$cfg[webname]}</title>
<meta property="update_time" content="{php} echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']);{/php}">
    <meta property="published_time" content="{php}echo date('Y-m-d H:i:s');{/php}">
    <meta property="og:locale" content="zh_CN"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{$gdata[absolute_url]}"/>
    <meta property="og:width" content="800"/>
    <meta property="og:author" content="{$cfg[webname]}">
    <meta property="og:update_time" content="{php} echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']);{/php}">
    <meta property="og:published_time" content="{php}echo date('Y-m-d H:i:s');{/php}">
    <meta property="og:title" content="{$gdata[title]} - {$cfg[webname]}"/>
    <meta property="og:keywords" content="{$gdata[title]}"/>
    <meta property="og:description" content="{$cfg[seo_description]}"/>
	<meta name="keywords" content="{$cfg[seo_keywords]}" />
	<meta name="description" content="{$cfg[seo_description]}" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="keywords" content="{$cfg[seo_keywords]}" />
<meta name="description" content="{$cfg[seo_description]}" />
<link rel="stylesheet" href="{$cfg[tpl]}css/article.css">
<script src="{$cfg[webdir]}static/js/jquery.js"></script>
<script src="{$cfg[tpl]}js/interactive.js" type="text/javascript"></script>
<meta http-equiv="Cache-Control" content="no-transform" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<meta name="applicable-device" content="pc,mobile" />
<body>
{inc:header.htm}
<div class="current_path">
<div class="p1">
当前位置：<a href="/">首页</a>{loop:$cfg_var[place] $v} > <a href="{$v[url]}">{$v[name]}</a>{/loop} > 正文
</div>
</div>
<div class="detail_content clearfix">
<div class="detail_lf">
<div class="article-body">
<div class="detail_main clearfix">
<h1 class="detail_title">{$gdata[title]}</h1>
<div class="article-intro">
<div  class="detail_avatar" target="_blank"><img src="{$gdata[avatar]}" alt="{$gdata[author]}"/>
<span class="admit admit_organ"></span>
</div>
<span class="detail_txt_lf">{$gdata[author]}</span>
<span class="browse_time">{$gdata[date]}</span>
</div>
</div>
<article class="detail_list_p"  id="c_more">
				{if:$_uid}
                 {$gdata[content]}
              	<br /><br /><p>{hook:favorites.htm}</p><br /><br /><br />
                
                {else}请先 <a href="/user-login.html">登录</a> 或者 <a href="/user-register.html">注册</a> 后在进行查看哦！{/if}
{if:isset($gdata['tag_arr'])}
<div class="article-tags">
{loop:$gdata[tag_arr] $v}
<a href="{$v[url]}" class="article-tag ablock">{$v[name]}</a>
{/loop}
</div>
{/if}
<div class="art-copyright br mb">
<b class="addr">免责申明：</b>本站所发布的文字与图片素材为非商业目的改编或整理，版权归原作者所有，如侵权或涉及违法，请联系我们删除，如需转载请保留原文地址。  
</div>
{if:isset($gdata[prev][url]) || isset($gdata[next][url])}
<div class="m_ssxx">
{if:isset($gdata[prev][url])}
<p><span>上一篇：</span><a href='{$gdata[prev][url]}'>{$gdata[prev][title]}</a></p>
{else}
<p><span>上一篇：</span><a>没有了</a></p>
{/if}
{if:isset($gdata[next][url])}
<p><span>下一篇：</span><a href='{$gdata[next][url]}'>{$gdata[next][title]}</a></p>
{else}
<p><span>下一篇：</span><a>没有了</a></p>
{/if}
</div>
{/if}
</article>
</div>
<div class="column">
<div class="choices">
<div class="choices_cut">
<ul>
<li class="active">推荐阅读</li>
</ul>
</div>
<div class="choices_cont">
<ul class="choices_main">
{php}$taglikedata = 0;{/php}
{block:taglike type="1" limit="5" dateformat="Y-m-d"}
{if:$data[list]}
{php}$taglikedata = 1;{/php}
{loop:$data[list] $v}
<li class="item_content">
<div class="leftImg">
<a rel="nofollow" href="{$v[url]}">{loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}</a>
</div>
<div class="txtCon_one">
<a class="txt_title" href="{$v[url]}">{$v[title]}</a>
<p class="txt_con">{$v[intro]}</p>
<div class="txt_label">
<span class="user_name">{$v[author]}</span>
<div class="label_right">
<em class="dateline">{$v[date]}</em>
<em class="discuss">
<a rel="nofollow" href="{$v[url]}#comment"><span class="reply"></span>评论</a>
</em>
</div>
</div>
</div>
</li>
{/loop}
{/if}
{/block}
{if:$taglikedata == 0}
{block:list_rand limit="5" life="0"}
{loop:$data[list] $v}
<li class="item_content">
<div class="leftImg">
<a rel="nofollow" href="{$v[url]}">{loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}</a>
</div>
<div class="txtCon_one">
<a class="txt_title" href="{$v[url]}">{$v[title]}</a>
<p class="txt_con">{$v[intro]}</p>
<div class="txt_label">
<span class="user_name">{$v[author]}</span>
<div class="label_right">
<em class="dateline">{$v[date]}</em>
<em class="discuss">
<a rel="nofollow" href="{$v[url]}#comment"><span class="reply"></span>评论</a>
</em>
</div>
</div>
</div>
</li>
{/loop}
{/block}
{/if}
</ul>
</div>
</div>
</div>
</div>

<div class="detail_rh">
{block:list_top orderby="views" limit="8"}
<div class="details_right_username">
<h3 class="right-title right_tit"><span>热门文章</span></h3>
<ul class="author_list clearfix">
{php}$rank = 1;{/php}
{loop:$data[list] $v}
<li>
<div class="author_box"> 
<a rel="nofollow" href="{$v[url]}" title="{$v[title]}"> {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}</a> 
</div>
<span class="author_txt"> 
<a href="{$v[url]}" target="_blank" title="{$v[title]}">{$v[title]}</a> 
</span>
</li>
{php}$rank++;{/php}
{/loop}
</ul>
</div>{/block}
{block:taglist mid="2" limit="18" orderby="count"}
<div class="details_right_username">
<h3 class="right-title right_tit"><span>热门标签</span></h3>
<ul class="popular_label clearfix">
{loop:$data[list] $v}
<li><a title="{$v[name]}" href="{$v[url]}">{$v[name]}</a></li>{/loop}
</ul>
</div>{/block}
{block:list_rand limit="8" life="600"}
<div class="details_right_username fixedDiv">
<h3 class="right-title right_tit"><span>猜你喜欢</span></h3>
<ul class="author_list clearfix">
{loop:$data[list] $v}
<li>
<div class="author_box"> 
<a rel="nofollow" href="{$v[url]}" title="{$v[title]}"> {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}</a> 
</div>
<span class="author_txt"> 
<a href="{$v[url]}" target="_blank" title="{$v[title]}">{$v[title]}</a> 
</span>
</li>{/loop}
   </ul>
</div>{/block}
</div>
</div>
{hook:article_show_after.htm}
{inc:footer.htm}