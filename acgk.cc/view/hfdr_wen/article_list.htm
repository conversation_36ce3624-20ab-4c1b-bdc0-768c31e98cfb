<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
<title>{$cfg_var[name]} - {$cfg[webname]}</title>
<meta name="keywords" content="{$cfg[seo_keywords]}" />
<meta name="description" content="{$cfg[seo_description]}" />
<link rel="stylesheet" href="{$cfg[tpl]}css/common.css">
<script src="{$cfg[webdir]}static/js/jquery.js"></script>
<script src="{$cfg[tpl]}js/interactive.js" type="text/javascript"></script>
<meta http-equiv="Cache-Control" content="no-transform" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<meta name="applicable-device" content="pc,mobile" />
</head>
<body>

{inc:header.htm}
<div class="f-title bdfl" style="background-image: url({$cfg_var[pic]});"><div class="container"> <h1><b>{$cfg_var[place][0][name]}</b></h1>
<p>{$cfg_var[intro]}</p></div></div>
<div class="warp clearfix">
{block:global_cate pagenum="15" dateformat="Y-m-d" showviews="1"}
<div class="column">
<div class="choices">
<div class="choices_cont" >
<ul class="choices_main" id="infoModelLf">
{loop:$gdata[list] $v}
<li class="item_content">
<div class="leftImg">
<a rel="nofollow" href="{$v[url]}">{loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}</a>
</div>
<div class="txtCon_one">
<a class="txt_title" href="{$v[url]}">{$v[title]}</a>
<p class="txt_con">{$v[intro]}</p>
<div class="txt_label">
<span class="user_name">{$v[author]}</span>
<div class="label_right">
<em class="dateline">{$v[date]}</em>
<em class="discuss">
<a rel="nofollow" href="{$v[url]}#comment"><span class="reply"></span>评论</a>
</em>
</div>
</div>
</div>
</li>
{/loop}
</ul>
</div>
</div>
{if:$gdata[pages]}
<div class="col-md-12 col-xs-12  th_padding" style="text-align:center; margin:20px 0">
  <div class="list-title pagebar" libiao style="display:inline-block">
    {$gdata[pages]}
  </div>
</div>
{/if}

<style>
/* 基础分页样式 */
.pagebar a, .pagebar strong {
  display: inline-block;
  padding: 8px 12px;
  margin: 0 3px;
  color: #666;
  text-decoration: none;
}

/* 当前页样式 */
.pagebar strong {
  color: #333;
  font-weight: bold;
}

/* 悬停效果 */
.pagebar a:hover {
  color: #000;
}
</style>
</div>
{/block}


<div class="sidebar">
{block:list_top orderby="views" limit="8"}
<div class="details_right_username">
<h3 class="right-title right_tit"><span>热门文章</span></h3>
<ul class="author_list clearfix">
{php}$rank = 1;{/php}
{loop:$data[list] $v}
<li>
<div class="author_box"> 
<a rel="nofollow" href="{$v[url]}" title="{$v[title]}"> {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}</a> 
</div>
<span class="author_txt"> 
<a href="{$v[url]}" target="_blank" title="{$v[title]}">{$v[title]}</a> 
</span>
</li>
{php}$rank++;{/php}
{/loop}
</ul>
</div>{/block}
{block:taglist mid="2" limit="18" orderby="count"}
<div class="details_right_username">
<h3 class="right-title right_tit"><span>热门标签</span></h3>
<ul class="popular_label clearfix">
{loop:$data[list] $v}
<li><a title="{$v[name]}" href="{$v[url]}">{$v[name]}</a></li>{/loop}
</ul>
</div>{/block}
{block:list_rand limit="8" life="600"}
<div class="details_right_username fixedDiv">
<h3 class="right-title right_tit"><span>猜你喜欢</span></h3>
<ul class="author_list clearfix">
{loop:$data[list] $v}
<li>
<div class="author_box"> 
<a rel="nofollow" href="{$v[url]}" title="{$v[title]}"> {loop:$v[piclist] $src}
                                          <img src="{$src}" alt="{$v[title]}">
                                          {/loop}</a> 
</div>
<span class="author_txt"> 
<a href="{$v[url]}" target="_blank" title="{$v[title]}">{$v[title]}</a> 
</span>
</li>{/loop}
   </ul>
</div>{/block}
</div>
</div>
{inc:footer.htm}