<footer class="content_nt mar_top8">
<div class="footer_nt">
<p><a href="/sitemap.html" target="_blank">网站地图</a>|<a href="/sitemap.xml" target="_blank">RS订阅</a></p>
<p> Copyright 2023 {$cfg[weburl]} <b>【{$cfg[webname]}】</b> 版权所有 | <img src="{$cfg[tpl]}picture/gonganbeian.png"><a rel="nofollow" href="//beian.miit.gov.cn">{$cfg[beian]}</a></p>
<p>声明：本站为非赢利网站，作品与素材版权均归作者所有，如内容侵权与违规请与本站联系，将在三个工作日内处理，互联网违法和不良信息举报邮箱：{$cfg[webmail]}</p>
</div>
</footer>
<script src="{$cfg[tpl]}js/common.js" type="text/javascript"></script>
<script>
(function() {
$("#search_form").submit(function() {
var mid = $(this).find("[name='mid']").val();
var keyword = $(this).find("[name='keyword']").val();
window.location.href = "/search/" + encodeURIComponent(keyword);
return false;
});
})();
(function() {
var feedBackUrl = "//wpa.qq.com/msgrd?v=3&uin={$cfg[webqq]}&site=qq&menu=yes";
var strHml = '<div class="feedBackWrap" > <div class="fq"><a href="' + feedBackUrl + '" target="_blank"></a></div> <div class="feedBackWraphd"></div></div>';
$("body").append(strHml);
$("body").on("click", ".feedBackWraphd", function() {
$(document).scrollTop(0);
})
$(window).scroll(function() {
if ($(document).scrollTop() < 100) {
$('.feedBackWraphd').hide();
} else {
$('.feedBackWraphd').show();
}
})
})()
</script>
</body>
</html>