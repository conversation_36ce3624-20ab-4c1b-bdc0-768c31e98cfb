//焦点图
function xFocus(Box) {
    var Menu = $(Box + ' .slider li'); //圆点菜单
    var Con = $(Box + ' .view li'); //大图
    var Text = $(Box + ' .text li'); //图注文字
    var Prev = $(Box + ' .prev'); //上一页
    var Next = $(Box + ' .next'); //下一页
    var Counts = $(Con).size(); //获取li总数
    var nowIndex = 0;
    var timer;
    $(Prev).add(Next).find('em').html(1).end().find('i').html(Counts);
    /* 点击切换 */
    $(Menu).click(function() {
        var i = $(Menu).index(this)
        gotoPage(i);
    });
    /* 打开相应的标签 */
    function gotoPage(i) {
        $(Menu).removeClass("current").eq(i).addClass("current");
        $(Con).fadeOut(200).eq(i).fadeIn(200);
        $(Text).hide().eq(i).fadeIn(200);
        nowIndex = i;
        $(Prev).add(Next).find('em').html(i + 1).end().find('i').html(Counts);
    };
    /* 下一页 */
    $(Next).click(function() {
        gotoR();
    });

    function gotoR() {
        nowIndex++;
        if (nowIndex > Counts - 1) {
            nowIndex = 0;
        }
        gotoPage(nowIndex);
    };
    /* 上一页 */
    $(Prev).click(function() {
        nowIndex--;
        if (nowIndex < 0) {
            nowIndex = Counts - 1
        }
        gotoPage(nowIndex);
    });
    /* 自动轮播 */
    function setAuto() {
        if (Counts > 1) {
            timer = setInterval(gotoR, 5000);
        };
    };
    setAuto();

    $(Box).hover(function() {
            $(Prev).add(Next).show();
            if (timer) {
                clearInterval(timer);
            }
        },
        function() {
            $(Prev).add(Next).hide();
            setAuto();
        });
    /* 鼠标经过按钮展开 */
    $(Prev).add(Next).hover(function() {
        $(this).stop().animate({
            width: '79px'
        }, 200);
    }, function() {
        $(this).stop().animate({
            width: '38px'
        }, 200);
    });
};
xFocus('#focus001');

//选项卡
$(function() {
    var $tab_li = $('.choices_cut li');
    $tab_li.click(function() {
        $(this).addClass('active').siblings().removeClass('active');
        var index = $tab_li.index(this);
        $('.choices_cont .choices_main').eq(index).show().siblings().hide();
        $("#infoModel").show()
    })
});



function sayHi() {
    console.log('防抖成功')
}
$('#btn')
    .off('click')
    .on('click', function() {
        $('.login').xLoginBox({
            func: function() {
                callLoginScript.call(null)
                reloadMsgInfo.call(null)

                location.reload()


            },
            args: [],
            show: true,
        })
    })




$(function() {
    var utils = {
        getCookie: function(name) {
            var cookieName = encodeURIComponent(name) + '=',
                cookieStart = document.cookie.indexOf(cookieName),
                cookieValue = null

            return cookieValue
        },
    }
})

