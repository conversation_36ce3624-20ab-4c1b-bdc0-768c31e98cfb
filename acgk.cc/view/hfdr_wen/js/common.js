var Utils = (function () {
    //防抖
    var debounce = function (fn, wait) {
        var timer = null;
        return function () {
            timer && clearTimeout(timer);
            timer = setTimeout(function () {
                fn.apply(this, arguments)
            }, wait)
        }
    }
    //截取URL ？ 后边的内容 传入key 取得VALUE
    var getUrlParams = function (str) {
        var url = window.location.href;
        url = url.split("?")[1]
        if (url) {
            var reg = new RegExp("(^|&)" + str + "=([^&]*)(&|$)", "i");
            var r = url.match(reg);
            if (r != null) return unescape(r[2]);
        }
    }
    // onKeyUp="Utils.keypress(this,30)"
    var keypress = function (t, num) {
        var txtval = $(t).val().trim().length;
        // var str = parseInt(num-txtval);
        if (txtval < num) {
            $('.leftCharacter em').text(txtval);
        }
        else {
            $('.leftCharacter em').text(num);
            $(t).val($(t).val().substring(0, num))
        }
    }


    return {
        debounce: debounce,
        getUrlParams: getUrlParams,
        keypress: keypress
    }
})()

/*
 * 以$x 为顶级共用变量，以对象的形式存在
 * 
 */
window.$x = {};
(function ($, win) {
    var $ajax = function (opts) {
        var option = $.extend({
            withCredential: true,//允许携带同源的cookie
            beforeSend: function () {  //开始loading
                // $(".js_loading").show();
            },
            complete: function () {
                // $(".js_loading").hide();
            }
        }, opts);
        $.ajax(option);
    };
    win.$x.ajax = $ajax;
})(jQuery, window);

/* 获取cookie */
(function ($, win) {
    var getCookie = function (name) {
        var cookieName = encodeURIComponent(name) + "=",
            cookieStart = document.cookie.indexOf(cookieName),
            cookieValue = null;
        if (cookieStart > -1) {
            var cookieEnd = document.cookie.indexOf(";", cookieStart);
            if (cookieEnd == -1) {
                cookieEnd = document.cookie.length;
            }
            cookieValue = decodeURIComponent(document.cookie.substring(cookieStart + cookieName.length, cookieEnd));
        }
        return cookieValue;
    }
    win.$x.getCookie = getCookie;
})(jQuery, window);



(function ($, win) {
    var Messager = function (opts) {
        var _this = this;
        this.wrap = {
            jqObj: undefined
        };
        this.state = {
            timer: null
        };
        this.mathods = {
            downTiem: function (num, callback) {
                _this.state.timer = null;
                var time = num || 60;
                var time = time + 1;
                clearInterval(_this.state.timer);
                _this.state.timer = setInterval(function () {
                    --time;
                    if (time <= 0) {
                        clearInterval(_this.state.timer);
                        callback(time);
                    };
                }, 1000);
            },
            setBodyH: function (ele) {
                var w = ele.outerWidth();
                var h = ele.outerHeight();
                var bh = 0;
                if (ele.find(".massHead").length > 0) {
                    bh = h - ele.find(".massHead").outerHeight();
                };
                if (ele.find(".massFoot").length > 0) {
                    bh = bh != 0 ? bh - ele.find(".massFoot").outerHeight() : h - ele.find(".massFoot").outerHeight();
                };
                ele.find(".massBody").height(bh);
            },
            position: function (ele) {
                var w = ele.outerWidth();
                var h = ele.outerHeight();
                ele.css({
                    position: "fixed",
                    left: "50%",
                    top: "50%",
                    marginLeft: "-" + (w / 2) + "px",
                    marginTop: "-" + (h / 2) + "px",
                });
            }
        };

        //this.init({});
    };
    Messager.prototype.init = function (opts, success, cancel) {
        var _this = this;
        this.option = $.extend({}, _this.defaults, opts);
        var opts = this.option;
        var mathods = this.mathods;
        //构造结构
        var html = ' <div class="massagerWrp ' + opts.skin + '" style="width:' + opts.width + 'px;height:' + opts.height + 'px">' +
            '<div class="massHead">' +
            '<h5 class="massTitle">' + opts.title + '</h5>' +
            '<div class="close"></div>' +
            '</div>' +
            '<div class="massBody">' + opts.content + ' </div>' +
            ' <div class="massFoot">' +
            '<div class="cancel">' + opts.cancelText + '</div>' +
            '<div class="ok">' + opts.successText + '</div>' +
            '</div>' +
            '</div>';
        var mask = '<div class="mask"></div>';

        _this.wrap.jqObj = $(html);
        _this.wrap.mask = $(mask);
        _this.wrap.cancel = _this.wrap.jqObj.find(".cancel");
        _this.wrap.ok = _this.wrap.jqObj.find(".ok");
        _this.wrap.close = _this.wrap.jqObj.find(".close");
        _this.wrap.massBody = _this.wrap.jqObj.find(".massBody");
        if (/\<[a-zA-Z]+\>/.test(opts.content)) {
            ;
            _this.wrap.massBody.html(opts.content);
        } else {
            _this.wrap.massBody.html("<p class='msgBodyP'>" + opts.content + "</p>");
        }
        if (opts.type == "alert") {
            _this.wrap.cancel.hide();
        };
        if (opts.isFoot) {
            _this.wrap.jqObj.find(".massFoot").hide();
        }
        //var scrolltop = $(document).scrollTop();
        //$(document).scrollTop(0);
        $("body").append(_this.wrap.jqObj);
        //设置body高度
        mathods.setBodyH(_this.wrap.jqObj);
        mathods.position(_this.wrap.jqObj);
        _this.mathods.close = function () {
            /// $("body").css("overflow","auto");
            //$(document).scrollTop(scrolltop);
            clearInterval(_this.state.timer);
            $("body").find(_this.wrap.jqObj).remove();
            if (opts.isMask) {
                $("body").find(_this.wrap.mask).remove();
            };
        };


        _this.wrap.ok.on("click", function () {
            if (!opts.isform) _this.mathods.close();
            success && success(_this.wrap.jqObj);
        });
        _this.wrap.cancel.on("click", function () {
            _this.mathods.close();
            cancel && cancel();
        });
        _this.wrap.close.on("click", function () {
            _this.mathods.close();
        });

        //执行自动关闭
        if (opts.autoClose) {
            mathods.downTiem(Number(opts.domeTime), function () {
                _this.mathods.close();
                success && success(_this.wrap.jqObj);
            });
        };

        if (opts.isMask) { $("body").append(_this.wrap.mask) };

    };
    Messager.prototype.alert = function (opts, success, cancel) {
        var _this = this;
        _this.init($.extend(opts, { type: "alert" }), success, cancel);

    };
    Messager.prototype.config = function (opts, success, cancel) {
        var _this = this;
        _this.init($.extend(opts, { type: "config" }), success, cancel);

    };
    Messager.prototype.defaults = {
        isMask: true,   //蒙版
        width: "504",
        height: "258",
        massage: $("body"),  //插入到body
        content: "",         //内容
        isFoot: false,       //是否有底部按钮区
        title: "",
        iscancel: true,
        autoClose: false,    //是否自动关闭
        domeTime: 3,         //时间
        isform: false,       //
        skin: "",            //类
        successText: "确认",
        cancelText: "取消"
    };

    win.$x.messager = new Messager();

})(jQuery, window);


/**
 *  计算最大字数
 * */

(function ($, win) {
    var MaxLength = function (form) {
        var getBytes = function (str, max, isConversion) {
            var byteLen = 0;
            var newStr = "";
            var returnValue = "";
            for (var i = 0; i < str.length; i++) {
                if (isConversion) {
                    if (str.charCodeAt(i) > 255) {
                        byteLen += 2;
                        if (byteLen > max) { byteLen -= 2; break };
                    } else {
                        byteLen += 1;
                        if (byteLen > max) break;
                    }

                } else {
                    byteLen += 1;
                }
                returnValue += str[i];
            }
            return { "byteLen": byteLen, "val": returnValue };
        }

        var getData = function () {
            var _this = this;
            var isConversion = _this.attr("isConversion") ? true : false; //是否计算汉字转换两个字符
            var maxLength = _this.attr("maxlength");
            var val = _this.val();
            var byteObj = getBytes(val, maxLength, isConversion)
            var curLength = byteObj.byteLen || 0;
            var nVal = byteObj.val || "";

            return { "curLength": curLength, "maxLength": maxLength, "target": _this, "isConversion": isConversion, "val": nVal };
        };
        var show = function (obj) {
            var str = '<div class="txt_number">' + obj.curLength + '/' + obj.maxLength + '</div>';
            if (obj.target.parent().find(".txt_number").length > 0) {
                obj.target.parent().find(".txt_number").remove();
            }
            obj.target.parent().append(str);
        };
        var initshow = function () {
            var jqObj = form.find("input[maxlength],textarea[maxlength]");
            for (var i = 0; i < jqObj.length; i++) {
                var curObj = getData.call(jqObj.eq(i));
                show(curObj);
            };
        };
        var replace = function (obj) {
            obj.target.val(obj.val);
        };
        var init = function () {
            initshow();
            var fn = function () {
                var curObj = getData.call($(this));
                show(curObj);
                replace(curObj);
            }
            form.off("input", "input[maxlength],textarea[maxlength]", fn);
            form.on("input", "input[maxlength],textarea[maxlength]", fn);
        };

        init();

    };
    win.$x.maxLength = MaxLength;
})(jQuery, window);

/** 5月22日 所有页面右侧边 吸顶效果 */
(function ($, win) {

    // var mark = $("<div class='markTitle'></div>");
    // $(".sidebar").append(mark);
    $(function () {
        setTimeout(function () {
            var titles = $(".right_tit");
            var obj = {};

            for (var i = 0; i < titles.length; i++) {
                //因为综合影响力排行榜会在标题加TAB 所以需要正则过滤
                var txt = ''
                var title_txt = $(titles[i]).html();
                var reg = /(.*)<em/im
                var r = title_txt.match(reg)
                txt = r ? r[1] : title_txt

                obj["top" + i] = $(titles[i]).offset().top;
                obj["text" + i] = txt;
            }
            //热门文章 内容高度
            var fixeDivHeight = $(".fixedDiv>.author_list").height();
            // 热门文章 距离顶部距离
            var fixDivToTop = $('.fixedDiv').offset().top;
            var leftH = $(".choices").length ? $(".choices").height() : 0;
            var rightH = $(".sidebar").length ? $(".sidebar").height() : 0
            var isIndex = window.location.pathname === '/' //判断是否是首页
            // console.log(leftH,rightH)
            $(win).on('scroll', Roll(titles, obj))

            function Roll(titles, obj) {
                return function () {
                    // console.log('xxxxxxxx')
                    for (var i = 0; i < titles.length; i++) {
                        // console.log('win',$(win).scrollTop())
                        if ($(win).scrollTop() >= obj["top" + i] && $(win).scrollTop() <= obj["top" + (i + 1)]) {
                            // console.log('第一步')
                            $(".markTitle").html(obj["text" + i]);
                            $(".markTitle").css("height", "60px");
                            if ($(".fixedDiv").hasClass('addFixedTop') || $(".fixedDiv").hasClass('addFixedBottom')) {
                                $(".fixedDiv").removeClass('addFixedTop');
                                $(".fixedDiv").removeClass('addFixedBottom');
                            }
                        }
                        // //滑到热门文章区域
                        else if ($(win).scrollTop() >= fixDivToTop) {
                            // console.log('第二步')
                            $(".markTitle").html("热门文章");
                            // $(".fixedDiv").addClass('addFixed');
                            $(".markTitle").css("height", "60px");
                            // 小屏幕吸底大屏幕吸顶
                            //如果左邊低，就不吸頂也不吸底了
                            //leftH ==0  因为左侧元素不同 没准找不到
                            if (leftH >= rightH || leftH == 0 || isIndex) {
                                if (fixeDivHeight > $(win).height()) {
                                    //  $(".fixedDiv").addClass('addFixedBottom'); 
                                    if (fixDivToTop + fixeDivHeight + 60 <= $(win).height() + $(win).scrollTop()) {
                                        $(".fixedDiv").addClass('addFixedBottom');
                                    }
                                }
                                else {
                                    $(".fixedDiv").addClass('addFixedTop');
                                }
                            }



                        }

                        else if ($(win).scrollTop() <= obj.top0) {
                            // console.log('第三步')
                            $(".markTitle").html("");
                            $(".markTitle").css("height", "0px");
                            if ($(".fixedDiv").hasClass('addFixedTop') || $(".fixedDiv").hasClass('addFixedBottom')) {
                                $(".fixedDiv").removeClass('addFixedTop');
                                $(".fixedDiv").removeClass('addFixedBottom');
                            }
                        }

                    }
                }
            }
            Roll(titles, obj)();
            //点击我的关注 如果关注的少页面突然变短，需要从新计算高度
            $(".choices #btntag li").on("click", function () {
                setTimeout(function () {
                    leftH = $(".choices").length ? $(".choices").height() : 0;
                    // console.log(leftH)
                }, 500)
            })
        }, 700)
    })




})(jQuery, window);

$(function () {
    //导航顶部点击展开
    // $('.nevigate_ul .morebtn').on("click", function (event) {
    //     // console.log(1)
    //     event.stopPropagation();
    //     if ($('.morepop').is(':visible')) {
    //         $('.morebtn').removeClass('active');
    //         $('.morepop').hide();
    //     } else {
    //         $('.morebtn').addClass('active');
    //         $('.morepop').show();
    //     }
    // });
    // $(window).click(function () {
    //     $('.nevigate_ul .morebtn').removeClass('active');
    //     $('.morepop').hide();
    // })

    // 爱卡号二级导航点菜单下拉点击改为hover
    $('.nevigate_ul').on("mouseenter", '.morebtn', function (evt) {
        var $target = $(evt.target);
        var $dropMenu = $('<ol class="morepop" style="display:none;"></ol>');
        $dropMenu.append($('.nevigate .morepop').children().clone());
        $dropMenu.find('li').css({ 'float': 'inherit' });
        $dropMenu.find('li a').css({ 'line-height': 'inherit' });
        $target.append($dropMenu);
        $target.css({ color: '#0088ff' });
        $target.find('.morepop').show();
    });
    $('.nevigate_ul').on("mouseleave", '.morebtn', function (evt) {
        var $target = $('.nevigate_ul .morebtn');
        $target.css({ color: 'inherit' });
        $target.html('...');
    });
})



