<?php defined('ROOT_PATH') || exit; function runtime() { return number_format(microtime(1) - $_ENV['_start_time'], 4); } function runmem() { return MEMORY_LIMIT_ON ? get_byte(memory_get_usage() - $_ENV['_start_memory']) : 'unknown'; } function ip() { if(isset($_SERVER['HTTP_X_FORWARDED_FOR'])) { preg_match('#[\d\.]{7,15}#', $_SERVER['HTTP_X_FORWARDED_FOR'], $mat); if($mat){ $ip = $mat[0]; }else{ return ip_c(); } }elseif(isset($_SERVER['HTTP_CLIENT_IP'])) { $ip = $_SERVER['HTTP_CLIENT_IP']; }elseif(isset($_SERVER['REMOTE_ADDR'])) { $ip = $_SERVER['REMOTE_ADDR']; if($ip == '::1') {$ip='127.0.0.1';} }else{ $ip = '127.0.0.1'; } return long2ip(ip2long($ip)); } function ip_c() { $ip = '127.0.0.1'; if(isset($_SERVER['HTTP_CDN_SRC_IP'])) { $ip = $_SERVER['HTTP_CDN_SRC_IP']; } elseif(isset($_SERVER['HTTP_CLIENTIP'])) { $ip = $_SERVER['HTTP_CLIENTIP']; } elseif(isset($_SERVER['HTTP_CLIENT_IP'])) { $ip = $_SERVER['HTTP_CLIENT_IP']; } elseif(isset($_SERVER['HTTP_X_FORWARDED_FOR'])) { $ip = $_SERVER['HTTP_X_FORWARDED_FOR']; $arr = array_filter(explode(',', $ip)); $ip = trim(end($arr)); } else { $ip = $_SERVER['REMOTE_ADDR']; if($ip == '::1') {$ip='127.0.0.1';} } $longip = ip2long($ip); $longip < 0 AND $longip = sprintf("%u", $longip); return long2ip($longip); } function E($err, $msg, $name = '', $extra = array()) { $arr = array('err'=>$err, 'msg'=>$msg, 'name'=>$name); !empty($extra) && $arr += $extra; exit( _json_encode($arr) ); } function R($k, $var = 'G') { switch($var) { case 'G': $var = &$_GET; break; case 'P': $var = &$_POST; break; case 'C': $var = &$_COOKIE; break; case 'R': $var = isset($_GET[$k]) ? $_GET : (isset($_POST[$k]) ? $_POST : $_COOKIE); break; case 'S': $var = &$_SERVER; break; } return isset($var[$k]) ? $var[$k] : null; } function _SESSION($k, $def = NULL) { global $g_session; return isset($_SESSION[$k]) ? $_SESSION[$k] : (isset($g_session[$k]) ? $g_session[$k] : $def); } function _GLOBALS($k, $def = NULL) { return isset($GLOBALS[$k]) ? $GLOBALS[$k] : $def; } function _ENV($k, $def = NULL) { return isset($_ENV[$k]) ? $_ENV[$k] : $def; } function param_force($val, $htmlspecialchars = TRUE, $addslashes = FALSE) { $get_magic_quotes_gpc = ini_set("magic_quotes_runtime",0) ? true:false; if(is_array($val)) { foreach($val as &$v) { if(is_string($v)) { $addslashes AND !$get_magic_quotes_gpc && $v = addslashes($v); !$addslashes AND $get_magic_quotes_gpc && $v = stripslashes($v); $htmlspecialchars AND $v = htmlspecialchars($v); } else { $v = intval($v); } } } else { if(is_string($val)) { $addslashes AND !$get_magic_quotes_gpc && $val = addslashes($val); !$addslashes AND $get_magic_quotes_gpc && $val = stripslashes($val); $htmlspecialchars AND $val = htmlspecialchars($val); } else { $val = intval($val); } } return $val; } function C($key, $val = null) { if(is_null($val)) return isset($_ENV['_config'][$key]) ? $_ENV['_config'][$key] : $val; return $_ENV['_config'][$key] = $val; } function FW($filename, $data) { $dir = dirname($filename); is_dir($dir) || mkdir($dir, 0755, true); return file_put_contents($filename, $data); } function _setcookie($name, $value='', $expire=0, $path='', $domain='', $secure=false, $httponly=false) { $name = $_ENV['_config']['cookie_pre'].$name; if(!$path) $path = $_ENV['_config']['cookie_path']; if(!$domain) $domain = $_ENV['_config']['cookie_domain']; $_COOKIE[$name] = $value; return setcookie($name, $value, $expire, $path, $domain, $secure, $httponly); } function _addslashes(&$var) { if(is_array($var)) { foreach($var as $k=>&$v) _addslashes($v); }else{ $var = addslashes($var); } } function _stripslashes(&$var) { if(is_array($var)) { foreach($var as $k=>&$v) _stripslashes($v); }else{ $var = stripslashes($var); } } function _htmls(&$var) { if(is_array($var)) { foreach($var as $k=>&$v) _htmls($v); }else{ $var = htmlspecialchars($var); } } function _trim(&$var) { if(is_array($var)) { foreach($var as $k=>&$v) _trim($v); }else{ $var = trim($var); } } function _urlencode($s) { return str_replace('-', '%2D', urlencode($s)); } function _json_encode($data) { return json_encode($data, JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE); } function _json_decode($json) { $json = trim($json, "\xEF\xBB\xBF"); $json = trim($json, "\xFE\xFF"); return json_decode($json, 1); } function _array_multisort(&$data, $c_1, $c_2 = true, $a_1 = 1, $a_2 = 1) { if(!is_array($data)) return $data; $col_1 = $col_2 = array(); foreach($data as $key => $row) { $col_1[$key] = $row[$c_1]; $col_2[$key] = $c_2===true ? $key : $row[$c_2]; } $asc_1 = $a_1 ? SORT_ASC : SORT_DESC; $asc_2 = $a_2 ? SORT_ASC : SORT_DESC; array_multisort($col_1, $asc_1, $col_2, $asc_2, $data); return $data; } function _int(&$c, $k, $v = 0) { if(isset($c[$k])) { $i = intval($c[$k]); return $i ? $i : $v; }else{ return $v; } } function _scandir($dir) { if(function_exists('scandir')) return scandir($dir); $dh = opendir($dir); $arr = array(); while($file = readdir($dh)) { if($file == '.' || $file == '..') continue; $arr[] = $file; } closedir($dh); return $arr; } function _rmdir($dir, $keepdir = 0) { if(!is_dir($dir) || $dir == '/' || $dir == '../') return FALSE; $files = _scandir($dir); foreach($files as $file) { if($file == '.' || $file == '..') continue; $filepath = $dir.'/'.$file; if(!is_dir($filepath)) { try{unlink($filepath);}catch(Exception $e){} }else{ _rmdir($filepath); } } if(!$keepdir) try{rmdir($dir);}catch(Exception $e){} return TRUE; } function _is_writable($file) { try{ if(is_dir($file)) { $tmpfile = $file.'/_test.tmp'; $n = @file_put_contents($tmpfile, 'test'); if($n > 0) { unlink($tmpfile); return TRUE; }else{ return FALSE; } }elseif(is_file($file)) { if(strpos(strtoupper(PHP_OS), 'WIN') !== FALSE) { $fp = @fopen($file, 'a'); @fclose($fp); return (bool)$fp; }else{ return is_writable($file); } } }catch(Exception $e) {} return FALSE; } function _strip_whitespace($content) { $tokens = token_get_all($content); $last = FALSE; $s = ''; for($i = 0, $j = count($tokens); $i < $j; $i++) { if(is_string($tokens[$i])) { $last = FALSE; $s .= $tokens[$i]; }else{ switch($tokens[$i][0]) { case T_COMMENT: case T_DOC_COMMENT: break; case T_WHITESPACE: if(!$last) { $s .= ' '; $last = TRUE; } break; case T_START_HEREDOC: $s .= "<<<FRAMEWORK\n"; break; case T_END_HEREDOC: $s .= "FRAMEWORK;\n"; for($k = $i+1; $k < $j; $k++) { if(is_string($tokens[$k]) && $tokens[$k] == ';') { $i = $k; break; }elseif($tokens[$k][0] == T_CLOSE_TAG) { break; } } break; default: $last = FALSE; $s .= $tokens[$i][1]; } } } return $s; } function _strlen($s) { return mb_strlen($s, 'UTF-8'); } function _substr($s, $start, $len) { return mb_substr($s, $start, $len, 'UTF-8'); } function file_get_contents_try($file, $times = 3) { while($times-- > 0) { $fp = fopen($file, 'rb'); if($fp) { $size = filesize($file); if($size == 0) return ''; $s = fread($fp, $size); fclose($fp); return $s; } else { sleep(1); } } return FALSE; } function file_put_contents_try($file, $s, $times = 3) { while($times-- > 0) { $fp = fopen($file, 'wb'); if($fp AND flock($fp, LOCK_EX)){ $n = fwrite($fp, $s); version_compare(PHP_VERSION, '5.3.2', '>=') AND flock($fp, LOCK_UN); fclose($fp); clearstatcache(); return $n; } else { sleep(1); } } return FALSE; } function random($length, $type = 1, $chars = '0123456789abcdefghijklmnopqrstuvwxyz') { if($type == 1) { $hash = sprintf('%0'.$length.'d', mt_rand(0, pow(10, $length) - 1)); } else { $hash = ''; if($type == 3) $chars .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'; $max = strlen($chars) - 1; for($i = 0; $i < $length; $i++) $hash .= $chars[mt_rand(0, $max)]; } return $hash; } function get_byte($byte) { if($byte < 1024) { return $byte.' Byte'; }elseif($byte < 1048576) { return round($byte/1024, 2).' KB'; }elseif($byte < 1073741824) { return round($byte/1048576, 2).' MB'; }elseif($byte < 1099511627776) { return round($byte/1073741824, 2).' GB'; }else{ return round($byte/1099511627776, 2).' TB'; } } function human_date($dateline, $dateformat = 'Y-m-d H:i:s') { $second = $_ENV['_time'] - $dateline; if($second > 31536000) { return date($dateformat, $dateline); }elseif($second > 2592000) { return floor($second / 2592000).'月前'; }elseif($second > 86400) { return floor($second / 86400).'天前'; }elseif($second > 3600) { return floor($second / 3600).'小时前'; }elseif($second > 60) { return floor($second / 60).'分钟前'; }else{ return $second.'秒前'; } } function safe_str($s, $ext = '') { $ext = preg_quote($ext); $s = preg_replace('#[^\040\w\x{4E00}-\x{9FA5}\x{30A0}-\x{30FF}\x{3040}-\x{309F}\x{1100}-\x{11FF}\x{3130}-\x{318F}\x{AC00}-\x{D7AF}'.$ext.']+#u', '', $s); $s = trim($s); return $s; } function get_dirs($path, $fullpath = false) { $arr = array(); $dh = opendir($path); while($dir = readdir($dh)) { if(preg_match('#\W#', $dir) || !is_dir($path.$dir)) continue; $arr[] = $fullpath ? $path.$dir.'/' : $dir; } sort($arr); return $arr; } function str_replace_once($search, $replace, $content) { $pos = strpos($content, $search); if($pos === false) return $content; return substr_replace($content, $replace, $pos, strlen($search)); } function str_auth($string, $operation = 'DECODE', $key = '', $expiry = 0) { $ckey_length = 4; $key = md5($key != '' ? $key : C('auth_key')); $keya = md5(substr($key, 0, 16)); $keyb = md5(substr($key, 16, 16)); $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length): substr(md5(microtime()), -$ckey_length)) : ''; $cryptkey = $keya.md5($keya.$keyc); $key_length = strlen($cryptkey); $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0).substr(md5($string.$keyb), 0, 16).$string; $string_length = strlen($string); $result = ''; $box = range(0, 255); $rndkey = array(); for($i = 0; $i <= 255; $i++) { $rndkey[$i] = ord($cryptkey[$i % $key_length]); } for($j = $i = 0; $i < 256; $i++) { $j = ($j + $box[$i] + $rndkey[$i]) % 256; $tmp = $box[$i]; $box[$i] = $box[$j]; $box[$j] = $tmp; } for($a = $j = $i = 0; $i < $string_length; $i++) { $a = ($a + 1) % 256; $j = ($j + $box[$a]) % 256; $tmp = $box[$a]; $box[$a] = $box[$j]; $box[$j] = $tmp; $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256])); } if($operation == 'DECODE') { if((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26).$keyb), 0, 16)) { return substr($result, 26); }else{ return ''; } }else{ return $keyc.str_replace('=', '', base64_encode($result)); } } function form_hash() { return substr(md5(substr($_ENV['_time'], 0, -5).$_ENV['_config']['auth_key']), 16); } function form_submit() { return R('FORM_HASH', 'P') == form_hash(); } function fetch_url($url, $timeout = 30) { $opts = array ('http'=>array('method'=>'GET', 'timeout'=>$timeout)); $context = stream_context_create($opts); $html = file_get_contents($url, false, $context); return $html; } function http() { if ( !empty($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) !== 'off') { return 'https://'; } elseif ( isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https' ) { return 'https://'; } elseif ( !empty($_SERVER['HTTP_FRONT_END_HTTPS']) && strtolower($_SERVER['HTTP_FRONT_END_HTTPS']) !== 'off') { return 'https://'; } return 'http://'; } function humansize($num) { static $custom_humansize = NULL; if($custom_humansize === NULL) $custom_humansize = function_exists('custom_humansize'); if($custom_humansize) return custom_humansize($num); if($num > 1073741824) { return number_format($num / 1073741824, 2, '.', '').'G'; } elseif($num > 1048576) { return number_format($num / 1048576, 2, '.', '').'M'; } elseif($num > 1024) { return number_format($num / 1024, 2, '.', '').'K'; } else { return $num.'B'; } } function http_location($url = '', $code = '') { if(empty($url)){ exit('URL is empty.'); } if ($code == '301'){ header('HTTP/1.1 301 Moved Permanently'); }elseif ($code == '302'){ header('HTTP/1.1 302 Moved Permanently'); }elseif($code == '404'){ header('HTTP/1.1 404 Not Found'); } header('Location:'.$url); exit; } function is_mobile($waphost = 1) { if($waphost && (substr($_SERVER['HTTP_HOST'],0,4) == 'wap.' || substr($_SERVER['HTTP_HOST'],0,2) == 'm.')){ return 1; } if ( isset($_SERVER['HTTP_VIA']) && stristr($_SERVER['HTTP_VIA'], "wap") ){ return 1; } elseif ( isset($_SERVER['HTTP_ACCEPT']) && strpos(strtoupper($_SERVER['HTTP_ACCEPT']), "VND.WAP.WML") ){ return 1; } elseif ( isset($_SERVER['HTTP_X_WAP_PROFILE']) && !empty($_SERVER['HTTP_X_WAP_PROFILE']) ) { return 1; } elseif ( isset($_SERVER['HTTP_PROFILE']) && !empty($_SERVER['HTTP_PROFILE']) ) { return 1; } $browser = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''; if ($browser && preg_match('/(blackberry|configuration\/cldc|hp |hp-|htc |htc_|htc-|iemobile|kindle|midp|mmp|motorola|mobile|nokia|opera mini|opera |Googlebot-Mobile|YahooSeeker\/M1A1-R2D2|android|iphone|ipod|mobi|palm|palmos|pocket|portalmmm|ppc;|smartphone|sonyericsson|sqh|spv|symbian|treo|up.browser|up.link|vodafone|windows ce|xda |xda_)/i', $browser)) { return 1; } $mobile_agents = array( 'iphone','ipod','android','samsung','sony','meizu','ericsson','mot','htc','sgh','lg','sharp','sie-', 'philips','panasonic','alcatel','lenovo','blackberry','netfront','symbian','ucweb','windowsce', 'palm','operamini','operamobi','openwave','nexusone','cldc','midp','wap','mobile', ); $is_mobile = 0; foreach($mobile_agents as $agent) { if(stripos($browser, $agent) !== false) { $is_mobile = 1; break; } } return $is_mobile; } function isSpider() { $agent= isset($_SERVER['HTTP_USER_AGENT']) ? strtolower($_SERVER['HTTP_USER_AGENT']) : ''; if (!empty($agent)) { $spiderSite= array( "Baiduspider", "baiduboxapp", "Googlebot", "Sogou Spider", "360Spider", "HaosouSpider", "bing", "yisouspider", "soso" ); foreach($spiderSite as $val) { $str = strtolower($val); if (strpos($agent, $str) !== false) { return $str; } } return false; } else { return false; } } function user_http_referer($no = array(), $weburl = ''){ $referer = R('referer'); empty($referer) AND $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : ''; $referer = str_replace(array('\"', '"', '<', '>', ' ', '*', "\t", "\r", "\n"), '', $referer); if( !preg_match('#^(http|https)://[\w\-=/\.]+/[\w\-=.%\#?]*$#is', $referer) || strpos($referer, 'login') !== FALSE || strpos($referer, 'register') !== FALSE || strpos($referer, 'logout') !== FALSE || strpos($referer, 'forget') !== FALSE || strpos($referer, 'resetpwd') !== FALSE ) { $referer = './'; } if($no){ foreach ($no as $u){ if(strpos($referer, $u) !== FALSE){ $referer = './'; break; } } } if( $referer == './' && !empty($weburl) ){ $referer = $weburl; }elseif( stripos($referer, $weburl) === FALSE ){ $referer = $weburl; } return $referer; } function http_url_path() { $port = R('SERVER_PORT','S'); $host = R('HTTP_HOST','S'); $https = R('HTTPS','S') == null ? 'off' : strtolower(R('HTTPS')); $proto = strtolower(R('HTTP_X_FORWARDED_PROTO','S')); $path = substr($_SERVER['PHP_SELF'], 0, strrpos($_SERVER['PHP_SELF'], '/')); $http = (($port == 443) || $proto == 'https' || ($https && $https != 'off')) ? 'https' : 'http'; return "$http://$host$path/"; } function url($url, $extra = array()) { !isset($_ENV['_config']['lecms_parseurl']) AND $_ENV['_config']['lecms_parseurl'] = 0; $r = $path = $query = ''; if(strpos($url, '/') !== FALSE) { $path = substr($url, 0, strrpos($url, '/') + 1); $query = substr($url, strrpos($url, '/') + 1); } else { $path = '/'; $query = $url; } if($_ENV['_config']['lecms_parseurl'] == 0) { $query = str_replace('_','-',$query); $r = $path . 'index.php?' . $query; } else { $query = str_replace('-','_',$query); $r = $path . $query. C('url_suffix'); } if($extra) { $args = http_build_query($extra); $sep = strpos($r, '?') === FALSE ? '?' : '&'; $r .= $sep.$args; } return $r; } function sql_split($sql, $tablepre) { $sql = str_replace('pre_', $tablepre, $sql); $sql = str_replace("\r", '', $sql); $ret = array(); $num = 0; $queriesarray = explode(";\n", trim($sql)); unset($sql); foreach($queriesarray as $query) { $ret[$num] = isset($ret[$num]) ? $ret[$num] : ''; $queries = explode("\n", trim($query)); foreach($queries as $query) { $ret[$num] .= isset($query[0]) && $query[0] == "#" ? '' : trim(preg_replace('/\#.*/', '', $query)); } $num++; } return $ret; } function auto_intro($intro = '', $content = '', $len = 255) { $s_arr = array('&nbsp;', '&amp;', '&quot;', '&#039;', '&ldquo;', '&rdquo;', '&mdash;', '&lt;', '&gt;', '&middot;', '&hellip;'); $r_arr = array(' ', '&', '"', "'", '“', '”', '—', '<', '>', '·', '…'); if(empty($intro)) { $content = str_replace($s_arr, $r_arr, $content); $intro = preg_replace('/\s{2,}/', ' ', strip_tags($content)); return trim(utf8::cutstr_cn($intro, $len, '')); }else{ $intro = str_replace($s_arr, $r_arr, $intro); return str_replace(array("\r\n", "\r", "\n"), '<br />', strip_tags($intro)); } } function _file_get_contents($url, $timeout=10, $referer='', $post_data=''){ if(function_exists('curl_init')){ $ch = curl_init(); curl_setopt ($ch, CURLOPT_URL, $url); curl_setopt ($ch, CURLOPT_HEADER, 0); curl_setopt ($ch, CURLOPT_TIMEOUT, $timeout); curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1); curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, $timeout); $referer AND curl_setopt ($ch, CURLOPT_REFERER, $referer); curl_setopt ($ch, CURLOPT_FOLLOWLOCATION, 1); if($post_data){ curl_setopt($ch, CURLOPT_POST, 1); curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data); } $http = parse_url($url); if(isset($http['scheme']) && $http['scheme'] == 'https'){ curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); } $content = curl_exec($ch); curl_close($ch); if($content){ return $content; } } $ctx = stream_context_create(array('http'=>array('timeout'=>$timeout))); $content = @file_get_contents($url, 0, $ctx); if($content){ return $content; } return false; } function unicode_encode($str, $encoding = 'UTF-8', $prefix = '&#', $postfix = ';') { $str = iconv($encoding, 'UCS-2', $str); $arrstr = str_split($str, 2); $unistr = ''; for($i = 0, $len = count($arrstr); $i < $len; $i++) { $dec = hexdec(bin2hex($arrstr[$i])); $unistr .= $prefix . $dec . $postfix; } return $unistr; } function plugin_is_enable($plugin_dir = '',$force = 0){ static $plugins = array(); if(!empty($plugins) && !$force){ if( !isset($plugin_arr[$plugin_dir]) ){ return false; }elseif ( empty($plugin_arr[$plugin_dir]['enable']) ){ return false; }else{ return true; } } if(!is_dir(PLUGIN_PATH)) return false; $plugin_arr = is_file(CONFIG_PATH.'plugin.inc.php') ? (array)include(CONFIG_PATH.'plugin.inc.php') : array(); if(empty($plugin_arr)){ return false; } if( !isset($plugin_arr[$plugin_dir]) ){ return false; }elseif ( empty($plugin_arr[$plugin_dir]['enable']) ){ return false; }else{ return true; } } function lang($key, $arr = array()) { $lang = isset($_SERVER['lang']) ? $_SERVER['lang'] : array(); if(!isset($lang[$key])) return 'lang['.$key.']'; $s = $lang[$key]; if(!empty($arr)) { foreach($arr as $k=>$v) { $s = str_replace('{'.$k.'}', $v, $s); } } return $s; } function pages_bootstrap($page = 1, $maxpage = 0, $url = '', $offset = 5, $lang = array('&#171;', '&#187;')){ return paginator::pages_bootstrap($page, $maxpage, $url, $offset, $lang); } function pages($page = 1, $maxpage = 0, $url = '', $offset = 5, $lang = array('&#171;', '&#187;')){ return paginator::pages($page, $maxpage, $url, $offset, $lang); } function encrypt($str = '', $key = '') { $key = $key == '' ? C('auth_key') : $key; $md5str = preg_replace('|[0-9/]+|','',md5($key)); $key = substr($md5str, 0, 2); $texlen = strlen($str); $rand_key = md5($key); $reslutstr = ""; for ($i = 0; $i < $texlen; $i++) { $reslutstr .= $str[$i] ^ $rand_key[$i % 32]; } $reslutstr = trim(base64_encode($reslutstr), "=="); $reslutstr = $key.substr(md5($reslutstr), 0, 3) . $reslutstr; return $reslutstr; } function decrypt($str = '') { $key = substr($str, 0, 2); $str = substr($str, 2); $verity_str = substr($str, 0, 3); $str = substr($str, 3); if ($verity_str != substr(md5($str), 0, 3)) { return false; } $str = base64_decode($str); $texlen = strlen($str); $reslutstr = ""; $rand_key = md5($key); for($i = 0; $i < $texlen; $i++) { $reslutstr .= $str[$i] ^ $rand_key[$i % 32]; } return $reslutstr; } function hashids_encrypt($cid = 0, $id = 0){ $hashids = Hashids::instance(C('auth_key')); return $hashids->encode($cid, $id); } function hashids_decrypt($str = ''){ $hashids = Hashids::instance(C('auth_key')); return $hashids->decode($str); } function view_tpl_exists($tpl = ''){ if(file_exists(VIEW_PATH.$_ENV['_theme'].'/'.$tpl)){ return $tpl; }else{ return false; } } function xn_txt_to_html($s) { $s = htmlspecialchars($s); $s = str_replace(" ", '&nbsp;', $s); $s = str_replace("\t", ' &nbsp; &nbsp; &nbsp; &nbsp;', $s); $s = str_replace("\r\n", "\n", $s); $s = str_replace("\n", '<br>', $s); return $s; } function xn_in_string($s, $str) { if(!$s || !$str) return FALSE; $s = ",$s,"; $str = ",$str,"; return strpos($str, $s) !== FALSE; } function xn_mkdir($dir, $mod = NULL, $recusive = NULL) { $r = !is_dir($dir) ? mkdir($dir, $mod, $recusive) : FALSE; return $r; } function xn_rmdir($dir) { $r = is_dir($dir) ? rmdir($dir) : FALSE; return $r; } function xn_unlink($file) { $r = is_file($file) ? unlink($file) : FALSE; return $r; } function xn_filemtime($file) { return is_file($file) ? filemtime($file) : 0; } function xn_set_dir($id, $dir = './') { $id = sprintf("%09d", $id); $s1 = substr($id, 0, 3); $s2 = substr($id, 3, 3); $dir1 = $dir.$s1; $dir2 = $dir."$s1/$s2"; !is_dir($dir1) && mkdir($dir1, 0777); !is_dir($dir2) && mkdir($dir2, 0777); return "$s1/$s2"; } function xn_get_dir($id) { $id = sprintf("%09d", $id); $s1 = substr($id, 0, 3); $s2 = substr($id, 3, 3); return "$s1/$s2"; } function xn_urlencode($s) { $s = urlencode($s); $s = str_replace('_', '_5f', $s); $s = str_replace('-', '_2d', $s); $s = str_replace('.', '_2e', $s); $s = str_replace('+', '_2b', $s); $s = str_replace('=', '_3d', $s); $s = str_replace('%', '_', $s); return $s; } function xn_urldecode($s) { $s = str_replace('_', '%', $s); $s = urldecode($s); return $s; } function match_img($content = '', $return_img_str = false){ $pattern = "/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png|\.jpeg|\.bmp|\.webp]))[\'|\"].*?[\/]?>/"; preg_match_all($pattern, $content, $match); if(isset($match[1]) && !empty($match[1])){ $imgurl = $match[1]; }else{ $imgurl = array(); } if($return_img_str){ if(isset($match[0]) && !empty($match[0])){ $imgs = $match[0]; }else{ $imgs = array(); } }else{ $imgs = array(); } if($return_img_str){ return array('imgurl'=>$imgurl, 'imgs'=>$imgs); }else{ return $imgurl; } } function grab_remote_img($content = '', $path = '', $targeturl = ''){ set_time_limit(0); $img_array = match_img($content); if(!$img_array){ return $content; } empty($path) AND $path = date('Ymd'); $uploadpath = 'upload/'.$path; $updir = ROOT_PATH.$uploadpath; $weburl = http_url_path(); $urlpath = $weburl.$uploadpath; if(!is_dir($updir) && !mkdir($updir, 0755, true)) { return $content; } foreach($img_array as $value){ $val = $value; if(strpos($value, 'http') === false){ if(!$targeturl) continue; $value = $targeturl.$value; } if(strpos($value, '?')){ $value = explode('?', $value); $value = $value[0]; } if(substr($value, 0, 4) != 'http'){ continue; }elseif (stripos($value, $weburl) !== FALSE){ continue; } $ext = strtolower(trim(substr(strrchr($value, '.'), 1, 10))); if(!in_array(strtolower($ext), array('png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'ico'))){ continue; } $imgname = date('YmdHis').rand(100, 999).'.'.$ext; $filename = $updir.'/'.$imgname; $imgurl = $urlpath.'/'.$imgname; ob_start(); @readfile($value); $data = ob_get_contents(); ob_end_clean(); $data && file_put_contents($filename, $data); if(is_file($filename)){ $content = str_replace($val, $imgurl, $content); } } return $content; } function send_http_status($code){ static $_status = array( 100 => 'Continue', 101 => 'Switching Protocols', 200 => 'OK', 201 => 'Created', 202 => 'Accepted', 203 => 'Non-Authoritative Information', 204 => 'No Content', 205 => 'Reset Content', 206 => 'Partial Content', 300 => 'Multiple Choices', 301 => 'Moved Permanently', 302 => 'Moved Temporarily ', 303 => 'See Other', 304 => 'Not Modified', 305 => 'Use Proxy', 307 => 'Temporary Redirect', 400 => 'Bad Request', 401 => 'Unauthorized', 402 => 'Payment Required', 403 => 'Forbidden', 404 => 'Not Found', 405 => 'Method Not Allowed', 406 => 'Not Acceptable', 407 => 'Proxy Authentication Required', 408 => 'Request Timeout', 409 => 'Conflict', 410 => 'Gone', 411 => 'Length Required', 412 => 'Precondition Failed', 413 => 'Request Entity Too Large', 414 => 'Request-URI Too Long', 415 => 'Unsupported Media Type', 416 => 'Requested Range Not Satisfiable', 417 => 'Expectation Failed', 500 => 'Internal Server Error', 501 => 'Not Implemented', 502 => 'Bad Gateway', 503 => 'Service Unavailable', 504 => 'Gateway Timeout', 505 => 'HTTP Version Not Supported', 509 => 'Bandwidth Limit Exceeded', 550 => 'Can not connect to MySQL server' ); if(isset($_status[$code])) { header('HTTP/1.1 '.$code.' '.$_status[$code]); header('Status:'.$code.' '.$_status[$code]); } } function in_array_case($value = '', $array = array()){ return in_array(strtolower($value), array_map('strtolower', $array)); } function to_guid_string($mix) { if (is_object($mix)) { return spl_object_hash($mix); } elseif (is_resource($mix)) { $mix = get_resource_type($mix) . strval($mix); } else { $mix = serialize($mix); } return md5($mix); } function redirect($url, $time = 0, $msg = '') { $url = str_replace(array("\n", "\r"), '', $url); if (empty($msg)) $msg = "系统将在{$time}秒之后自动跳转到{$url}！"; if (!headers_sent()) { if (0 === $time) { header('Location: ' . $url); } else { header("refresh:{$time};url={$url}"); echo($msg); } exit(); } else { $str = "<meta http-equiv='Refresh' content='{$time};URL={$url}'>"; if ($time != 0) $str .= $msg; exit($str); } } function auto_charset($fContents, $from = 'gbk', $to = 'utf-8') { $from = strtoupper($from) == 'UTF8' ? 'utf-8' : $from; $to = strtoupper($to) == 'UTF8' ? 'utf-8' : $to; if (strtoupper($from) === strtoupper($to) || empty($fContents) || (is_scalar($fContents) && !is_string($fContents))) { return $fContents; } if (is_string($fContents)) { if (function_exists('mb_convert_encoding')) { return mb_convert_encoding($fContents, $to, $from); } elseif (function_exists('iconv')) { return iconv($from, $to, $fContents); } else { return $fContents; } } elseif (is_array($fContents)) { foreach ($fContents as $key => $val) { $_key = auto_charset($key, $from, $to); $fContents[$_key] = auto_charset($val, $from, $to); if ($key != $_key) unset($fContents[$key]); } return $fContents; } else { return $fContents; } }class core{ public static function init_start() { debug::init_start(); self::open_ob_start(); self::init_set(); self::init_misc(); self::init_lang(); self::init_get(); self::init_control(); } public static function open_ob_start() { ob_start(array('core', 'ob_gzip')); } public static function ob_gzip($s) { $gzip = $_ENV['_config']['gzip']; $isfirst = empty($_ENV['_isgzip']); if($gzip) { if(function_exists('ini_get') && ini_get('zlib.output_compression')) { $isfirst && header("Content-Encoding: gzip"); }elseif(function_exists('gzencode') && isset($_SERVER["HTTP_ACCEPT_ENCODING"]) && strpos($_SERVER["HTTP_ACCEPT_ENCODING"], 'gzip') !== FALSE) { $s = gzencode($s, 5); if($isfirst) { header("Content-Encoding: gzip"); } } }elseif($isfirst) { header("Content-Encoding: none"); } $isfirst && $_ENV['_isgzip'] = 1; return $s; } public static function ob_clean() { !empty($_ENV['_isgzip']) && ob_clean(); } public static function ob_end_clean() { !empty($_ENV['_isgzip']) && ob_end_clean(); } public static function init_set() { date_default_timezone_set($_ENV['_config']['zone']); spl_autoload_register(array('core', 'autoload_handler')); if(MAGIC_QUOTES_GPC) { _stripslashes($_GET); _stripslashes($_POST); _stripslashes($_COOKIE); } $_ENV['_sqls'] = array(); $_ENV['_include'] = array(); $_ENV['_time'] = isset($_SERVER['REQUEST_TIME']) ? $_SERVER['REQUEST_TIME'] : time(); $_ENV['_ip'] = ip(); $_ENV['_longip'] = ip2long($_ENV['_ip']); $_ENV['_sqlnum'] = 0; $_ENV['_method'] = $_SERVER['REQUEST_METHOD']; if(!isset($_SERVER['REQUEST_URI'])) { if(isset($_SERVER['HTTP_X_REWRITE_URL'])) { $_SERVER['REQUEST_URI'] = &$_SERVER['HTTP_X_REWRITE_URL']; }else{ $_SERVER['REQUEST_URI'] = ''; $_SERVER['REQUEST_URI'] .= $_SERVER['REQUEST_URI']; $_SERVER['REQUEST_URI'] .= isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : ''; $_SERVER['REQUEST_URI'] .= empty($_SERVER['QUERY_STRING']) ? '' : '?'.$_SERVER['QUERY_STRING']; } } header("Expires: 0"); header("Cache-Control: private, post-check=0, pre-check=0, max-age=0"); header("Pragma: no-cache"); header(base64_decode('WC1Qb3dlcmVkLUJ5OiBYaXVub1BIUCAmIExlY21z')); header('Content-Type: text/html; charset=UTF-8'); } public static function autoload_handler($classname) { $classname = str_replace('\\', DIRECTORY_SEPARATOR, $classname); if(substr($classname, 0, 3) == 'db_') { include FRAMEWORK_PATH.'db/'.$classname.'.class.php'; }elseif(substr($classname, 0, 6) == 'cache_') { include FRAMEWORK_PATH.'cache/'.$classname.'.class.php'; }elseif(is_file(FRAMEWORK_PATH.'ext/'.$classname.'.class.php')) { include FRAMEWORK_PATH.'ext/'.$classname.'.class.php'; }elseif(is_file(FRAMEWORK_PATH.'ext/network/'.$classname.'.php')) { include FRAMEWORK_PATH.'ext/network/'.$classname.'.php'; }else{ if(!defined('VENDOR')){ throw new Exception("类 $classname 不存在"); } } DEBUG && $_ENV['_include'][] = $classname.' 类'; return class_exists($classname, false); } public static function init_get() { if(!empty($_ENV['_config'][APP_NAME.'_parseurl'])) { self::parseurl_control(); }else{ if(isset($_GET['u'])) { $u = $_GET['u']; unset($_GET['u']); }elseif(!empty($_SERVER['PATH_INFO'])) { $u = $_SERVER['PATH_INFO']; }else{ $_GET = array(); $u = $_SERVER["QUERY_STRING"]; } $url_suffix = C('url_suffix'); if($url_suffix) { $suf_len = strlen($url_suffix); if(substr($u, -($suf_len)) == $url_suffix) $u = substr($u, 0, -($suf_len)); } $uarr = explode('-', $u); if(isset($uarr[0])) { $_GET['control'] = $uarr[0]; array_shift($uarr); } if(isset($uarr[0])) { $_GET['action'] = $uarr[0]; array_shift($uarr); } $num = count($uarr); for($i=0; $i<$num; $i+=2){ isset($uarr[$i+1]) && $_GET[$uarr[$i]] = $uarr[$i+1]; } } $_GET['control'] = isset($_GET['control']) && preg_match('/^\w+$/', $_GET['control']) ? strtolower($_GET['control']) : 'index'; $_GET['action'] = isset($_GET['action']) && preg_match('/^\w+$/', $_GET['action']) ? strtolower($_GET['action']) : 'index'; if(in_array($_GET['control'], array('parseurl', 'error404', 'base'))) { $_GET['control'] = 'error404'; $_GET['action'] = 'index'; } } public static function parseurl_control() { $controlname = 'parseurl_control.class.php'; $objfile = RUNTIME_CONTROL.$controlname; if(DEBUG || !is_file($objfile)) { $controlfile = self::get_original_file($controlname, CONTROL_PATH); if(!$controlfile) { $_GET['control'] = 'parseurl'; throw new Exception("访问的 URL 不正确，$controlname 文件不存在"); } self::process_all($controlfile, $objfile, "写入 control 编译文件 $controlname 失败"); } include $objfile; $obj = new parseurl_control(); $obj->index(); } public static function init_misc(){ $misc_filename = 'misc.func.php'; $misc_file_cache = RUNTIME_PATH.$misc_filename; if(DEBUG || !is_file($misc_file_cache)) { $miscfile = self::get_original_file($misc_filename, FRAMEWORK_PATH.'lib/'); if($miscfile) { $s = file_get_contents($miscfile); $s = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $s); if (!FW($misc_file_cache, $s)) { throw new Exception("写入 misc.func.php 编译文件失败"); } } } include $misc_file_cache; } public static function init_lang(){ !isset($lang) AND $lang = array(); $_SERVER['lang'] = array(); if( defined('F_APP_NAME') ){ if( isset($_ENV['_config']['admin_lang']) ){ $admin_lang = $_ENV['_config']['admin_lang']; }else{ $admin_lang = 'zh-cn'; } if( is_file(FRAMEWORK_PATH.'lang/'.$admin_lang.'.php') ){ $langname = $admin_lang.'.php'; $objfile = RUNTIME_PATH.'core_lang/'.$langname; if(DEBUG || !is_file($objfile)) { $langfile = self::get_original_file($langname, FRAMEWORK_PATH.'lang/'); if($langfile){ $s = file_get_contents($langfile); $s = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $s); if (!FW($objfile, $s)) { throw new Exception("写入 core_lang 编译文件 $langname 失败"); } } } $lang = include $objfile; is_array($lang) && $_SERVER['lang'] = array_merge($_SERVER['lang'], $lang); } if( is_file(LANG_PATH.$admin_lang.'_admin.php') ){ $langname = $_ENV['_config']['admin_lang'].'_admin.php'; $objfile = RUNTIME_PATH.'lang/'.$langname; if(DEBUG || !is_file($objfile)) { $langfile = self::get_original_file($langname, LANG_PATH); if($langfile){ $s = file_get_contents($langfile); $s = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $s); if (!FW($objfile, $s)) { throw new Exception("写入 lang 编译文件 $langname 失败"); } } } $lang = include $objfile; is_array($lang) && $_SERVER['lang'] = array_merge($_SERVER['lang'], $lang); } }else{ if( isset($_ENV['_config']['lang']) ){ $home_lang = $_ENV['_config']['lang']; }else{ $home_lang = 'zh-cn'; } if(R('lang','C')){ $home_lang = R('lang','C'); } if( is_file(FRAMEWORK_PATH.'lang/'.$home_lang.'.php') ){ $langname = $home_lang.'.php'; $objfile = RUNTIME_PATH.'core_lang/'.$langname; if(DEBUG || !is_file($objfile)) { $langfile = self::get_original_file($langname, FRAMEWORK_PATH.'lang/'); if($langfile){ $s = file_get_contents($langfile); $s = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $s); if (!FW($objfile, $s)) { throw new Exception("写入 core_lang 编译文件 $langname 失败"); } } } $lang = include $objfile; is_array($lang) && $_SERVER['lang'] = array_merge($_SERVER['lang'], $lang); } if( is_file(LANG_PATH.$home_lang.'.php') ){ $langname = $home_lang.'.php'; $objfile = RUNTIME_PATH.'lang/'.$langname; if(DEBUG || !is_file($objfile)) { $langfile = self::get_original_file($langname, LANG_PATH); if($langfile){ $s = file_get_contents($langfile); $s = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $s); if (!FW($objfile, $s)) { throw new Exception("写入 lang 编译文件 $langname 失败"); } } } $lang = include $objfile; is_array($lang) && $_SERVER['lang'] = array_merge($_SERVER['lang'], $lang); } } } public static function init_control() { $control = &$_GET['control']; $action = &$_GET['action']; $controlname = "{$control}_control.class.php"; $objfile = RUNTIME_CONTROL.$controlname; if(DEBUG || !is_file($objfile)) { $controlfile = self::get_original_file($controlname, CONTROL_PATH); if($controlfile) { self::process_all($controlfile, $objfile, "写入 control 编译文件 $controlname 失败"); }elseif(DEBUG > 0) { throw new Exception("访问的 URL 不正确，$controlname 文件不存在"); }else{ self::error404(); } } include $objfile; $class_name = $control.'_control'; $obj = new $class_name(); $obj->$action(); } public static function error404() { log::write('404错误，访问的 URL 不存在', 'php_error404.php'); $errorname = 'error404_control.class.php'; $objfile = RUNTIME_CONTROL.$errorname; if(DEBUG || !is_file($objfile)) { $errorfile = self::get_original_file($errorname, CONTROL_PATH); if(!$errorfile) { throw new Exception("控制器加载失败，$errorname 文件不存在"); } self::process_all($errorfile, $objfile, "写入 control 编译文件 $errorname 失败"); } include $objfile; $obj = new error404_control(); $obj->index(); exit(); } public static function process_all($readfile, $writefile, $errorstr) { $s = file_get_contents($readfile); $s = self::process_extends($s); $s = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $s); if(!FW($writefile, $s)) { throw new Exception($errorstr); } } public static function process_extends($s) { if(preg_match('#class\s+\w+\s+extends\s+(\w+)\s*\{#', $s, $m)) { if($m[1] != 'control') { $controlname = $m[1].'.class.php'; $realfile = CONTROL_PATH.$controlname; if(is_file($realfile)) { $objfile = RUNTIME_CONTROL.$controlname; self::process_all($realfile, $objfile, "写入继承的类的编译文件 $controlname 失败"); $s = str_replace_once($m[0], 'include RUNTIME_CONTROL.\''.$controlname."'; ".$m[0], $s); }else{ throw new Exception("您继承的类文件 $controlname 不存在"); } } } return $s; } public static function model($model) { $modelname = $model.'_model.class.php'; if(isset($_ENV['_models'][$modelname])) { return $_ENV['_models'][$modelname]; } $objfile = RUNTIME_MODEL.$modelname; if(DEBUG || !is_file($objfile)) { $modelfile = core::get_original_file($modelname, MODEL_PATH); if(!$modelfile) { if( defined('CURRENT_APP_NAME') ){ $modelfile = core::get_original_file($modelname, ROOT_PATH.CURRENT_APP_NAME.'/model/'); if(!$modelfile) { throw new Exception(CURRENT_APP_NAME."模型 $modelname 文件不存在"); } }else{ throw new Exception("模型 $modelname 文件不存在"); } } $s = file_get_contents($modelfile); $s = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $s); if(!FW($objfile, $s)) { throw new Exception("写入 model 编译文件 $modelname 失败"); } } include $objfile; $mod = new $model(); $_ENV['_models'][$modelname] = $mod; return $mod; } public static function get_original_file($filename, $path) { if(empty($_ENV['_config']['plugin_disable'])) { $plugins = self::get_plugins(); if(isset($plugins['enable']) && is_array($plugins['enable'])) { $plugin_enable = array_keys($plugins['enable']); foreach($plugin_enable as $p) { if(is_file(PLUGIN_PATH.$p.'/'.APP_NAME.'/'.$filename)) { return PLUGIN_PATH.$p.'/'.APP_NAME.'/'.$filename; } if(is_file(PLUGIN_PATH.$p.'/'.$filename)) { return PLUGIN_PATH.$p.'/'.$filename; } if(is_file(PLUGIN_PATH.$p.'/control/'.$filename)) { return PLUGIN_PATH.$p.'/control/'.$filename; } if(is_file(PLUGIN_PATH.$p.'/model/'.$filename)) { return PLUGIN_PATH.$p.'/model/'.$filename; } if(is_file(PLUGIN_PATH.$p.'/block/'.$filename)) { return PLUGIN_PATH.$p.'/block/'.$filename; } } } } if(is_file($path.$filename)) { return $path.$filename; } return FALSE; } public static function get_plugins($force = 0) { static $plugins = array(); if(!empty($plugins) && !$force) return $plugins; if(!is_dir(PLUGIN_PATH)) return array(); $plugin_dirs = get_dirs(PLUGIN_PATH); $plugin_arr = is_file(CONFIG_PATH.'plugin.inc.php') ? (array)include(CONFIG_PATH.'plugin.inc.php') : array(); foreach($plugin_dirs as $dir) { $cfg = is_file(PLUGIN_PATH.$dir.'/conf.php') ? (array)include(PLUGIN_PATH.$dir.'/conf.php') : array(); if( isset($_ENV['_config']['admin_lang']) && is_file(PLUGIN_PATH.$dir.'/conf_'.$_ENV['_config']['admin_lang'].'.php')){ $cfg = (array)include(PLUGIN_PATH.$dir.'/conf_'.$_ENV['_config']['admin_lang'].'.php'); } $cfg['rank'] = isset($cfg['rank']) ? (int)$cfg['rank'] : 100; if(empty($plugin_arr[$dir])) { $plugins['not_install'][$dir] = $cfg; }elseif(empty($plugin_arr[$dir]['enable'])) { $plugins['disable'][$dir] = $cfg; }else{ $plugins['enable'][$dir] = $cfg; } } _array_multisort($plugins['enable'], 'rank'); _array_multisort($plugins['disable'], 'rank'); _array_multisort($plugins['not_install'], 'rank'); return $plugins; } public static function process_hook($matches) { $str = "\n"; if(!is_dir(PLUGIN_PATH) || !empty($_ENV['_config']['plugin_disable'])) return $str; $plugins = core::get_plugins(); if(empty($plugins['enable'])) return $str; $plugin_enable = array_keys($plugins['enable']); foreach($plugin_enable as $p) { $file = PLUGIN_PATH.$p.'/'.$matches[1]; if( is_file($file) ){ $s = file_get_contents($file); $str .= self::clear_code($s); } $file = PLUGIN_PATH.$p.'/hook/'.$matches[1]; if( is_file($file) ){ $s = file_get_contents($file); $str .= self::clear_code($s); } } return $str; } public static function clear_code($s) { $s = trim($s); if(substr($s, 0, 11) == '<?php exit;') $s = substr($s, 11); if(substr($s, 0, 5) == '<?php') $s = substr($s, 5); $s = ltrim($s); if(substr($s, 0, 29) == 'defined(\'ROOT_PATH\') || exit;') $s = substr($s, 29); if(substr($s, -2, 2) == '?>') $s = substr($s, 0, -2); return $s; } } class debug{ public static function init_start() { if(DEBUG) { error_reporting(E_ALL | E_STRICT); register_shutdown_function(array('debug', 'shutdown_handler')); }else{ error_reporting(0); } function_exists('ini_set') && ini_set('display_errors', DEBUG ? 'On' : 'Off'); set_error_handler(array('debug', 'error_handler')); set_exception_handler(array('debug', 'exception_handler')); } public static function error_handler($errno, $errstr, $errfile, $errline) { if(!empty($_ENV['_exception'])) return; defined('E_DEPRECATED') || define('E_DEPRECATED', 8192); defined('E_USER_DEPRECATED') || define('E_USER_DEPRECATED', 16384); $error_type = array( E_ERROR => '运行错误', E_WARNING => '运行警告', E_PARSE => '语法错误', E_NOTICE => '运行通知', E_CORE_ERROR => '初始错误', E_CORE_WARNING => '初始警告', E_COMPILE_ERROR => '编译错误', E_COMPILE_WARNING => '编译警告', E_USER_ERROR => '用户定义的错误', E_USER_WARNING => '用户定义的警告', E_USER_NOTICE => '用户定义的通知', E_STRICT => '代码标准建议', E_RECOVERABLE_ERROR => '致命错误', E_DEPRECATED => '代码警告', E_USER_DEPRECATED => '用户定义的代码警告', ); $errno_str = isset($error_type[$errno]) ? $error_type[$errno] : '未知错误'; $s = "[$errno_str] : $errstr"; if(DEBUG) { throw new Exception($s); }else{ if(in_array($errno, array(E_NOTICE, E_USER_NOTICE, E_DEPRECATED))) { log::write($s); }else{ if(!defined('ADMIN_PATH')){ self::error_die('[程序错误: 请开启调试模式查看详细信息]'); } throw new Exception($s); } } } public static function error_die($msg){ header("HTTP/1.1 404 Not Found"); $css = 'body{margin: 10px;background: #f2f2f2;}@media screen and (min-width:768px) {.layui-container {width:720px}}@media screen and (min-width:992px) {.layui-container {width:960px}}@media screen and (min-width:1200px) {.layui-container {width:1150px}}
.layui-container {position: relative;margin: 0 auto;box-sizing: border-box;margin-top:15%;}
.layui-card {margin-bottom: 15px;border-radius: 5px;background-color: #fff;box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);}
.layui-card-header {position: relative;height: 42px;line-height: 42px;padding: 0 15px;border-bottom: 1px solid #f8f8f8;color: #333;border-radius: 2px 2px 0 0;font-size: 14px;}
.layui-card-body {position: relative;padding: 10px 15px;line-height: 24px;}'; $div = '<div class="layui-container"><div class="layui-card"><div class="layui-card-header"><b>出错啦！</b></div><div class="layui-card-body">'.$msg.'</div></div></div>'; $html = '<!DOCTYPE html><html><head><title>出错啦</title><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><style>'.$css.'</style></head><body>'.$div.'</body></html>'; exit($html); } public static function exception_handler($e) { DEBUG && $_ENV['_exception'] = 1; $trace = $e->getTrace(); if(!empty($trace) && $trace[0]['function'] == 'error_handler' && $trace[0]['class'] == 'debug') { $message = $e->getMessage(); $file = $trace[0]['args'][2]; $line = $trace[0]['args'][3]; }else{ $message = '[程序异常] : '.$e->getMessage(); $file = $e->getFile(); $line = $e->getLine(); } $message = self::to_message($message); log::write("$message File: $file [$line]"); if(!defined('ADMIN_PATH') && !DEBUG){ self::error_die('[程序异常: 请查看日志或者开启调试模式查看详细信息]'); } try{ core::ob_clean(); if(R('ajax', 'R')) { if(DEBUG) { $error = "$message File: $file [$line]<br><br>".str_replace("\n", '<br>', $e->getTraceAsString()); }else{ $len = strlen($_SERVER['DOCUMENT_ROOT']); $file = substr($file, $len); $error = "$message File: $file [$line]"; } echo json_encode(array('error' => $error)); }else{ if(DEBUG) { self::exception($message, $file, $line, $e->getTraceAsString()); }else{ $len = strlen($_SERVER['DOCUMENT_ROOT']); $file = substr($file, $len); self::sys_error($message, $file, $line); } } }catch(Exception $e) { echo get_class($e)." thrown within the exception handler. Message: ".$e->getMessage()." on line ".$e->getLine(); } } public static function exception($message, $file, $line, $tracestr) { include FRAMEWORK_PATH.'tpl/exception.php'; } public static function arr2str($arr, $type = 2, $html = TRUE) { $s = ''; $i = 0; foreach($arr as $k => $v) { switch ($type) { case 0: $k = ''; break; case 1: $k = "#$k "; break; default: $k = "#$k => "; } $i++; $c = $i%2 == 0 ? ' class="even"' : ''; $html && is_string($v) && $v = htmlspecialchars($v); if(is_array($v) || is_object($v)) { $v = gettype($v); } $s .= "<li$c>$k$v</li>"; } return $s; } public static function shutdown_handler() { if(empty($_ENV['_exception'])) { if($e = error_get_last()) { core::ob_clean(); $message = $e['message']; $file = $e['file']; $line = $e['line']; if(R('ajax', 'R')) { if(!DEBUG) { $len = strlen($_SERVER['DOCUMENT_ROOT']); $file = substr($file, $len); } $error = "[致命错误] : $message File: $file [$line]"; echo json_encode(array('error' => $error)); }else{ self::sys_error('[致命错误] : '.$message, $file, $line); } } } } public static function sys_error($message, $file, $line) { include FRAMEWORK_PATH.'tpl/sys_error.php'; } public static function get_code($file, $line) { $arr = file($file); $arr2 = array_slice($arr, max(0, $line - 5), 10, true); $s = '<table cellspacing="0" width="100%">'; foreach ($arr2 as $i => &$v) { $i++; $v = htmlspecialchars($v); $v = str_replace(' ', '&nbsp;', $v); $v = str_replace('	', '&nbsp;&nbsp;&nbsp;&nbsp;', $v); $s .= '<tr'.($i == $line ? ' style="background:#faa;"' : '').'><td width="40">#'.$i."</td><td>$v</td>"; } $s .= '</table>'; return $s; } public static function to_message($s) { $s = strip_tags($s); if(strpos($s, 'mysql_connect') !== false || strpos($s, 'mysqli_connect') !== false || stripos($s, 'pdo') !== false) { $s .= ' [连接数据库出错！配置信息见l'.'e'.'cms/config/config.inc.php]'; } return $s; } public static function debug_info() { include FRAMEWORK_PATH.'tpl/sys_trace.php'; } } class log { public static function write($s, $file = 'php_error.php') { $file_key = substr($file, 0, -4); if( isset($_ENV['_config'][$file_key]) && (int)$_ENV['_config'][$file_key] ){ $time = isset($_ENV['_time']) ? date('Y-m-d H:i:s', (int)$_ENV['_time']) : date('Y-m-d H:i:s'); $ip = $_ENV['_ip']; $url = self::to_str($_SERVER['REQUEST_URI']); $s = self::to_str($s); self::write_log('<?php exit;?>'."$time  $ip  $url  $s\r\n", $file); } return TRUE; } public static function to_str($s) { return str_replace(array("\r\n", "\r", "\n", "\t"), ' ', $s); } public static function write_log($s, $file) { $logfile = LOG_PATH.$file; try{ $fp = fopen($logfile, 'ab+'); if(!$fp) { throw new Exception("写入日志失败，可能文件 $file 不可写或磁盘已满。"); } fwrite($fp, $s); fclose($fp); }catch(Exception $e) {} return TRUE; } public static function trace($s) { if(!DEBUG) return; empty($_ENV['_trace']) && $_ENV['_trace'] = ''; $_ENV['_trace'] .= $s.' - '.number_format(microtime(1) - $_ENV['_start_time'], 4)."\r\n"; } public static function trace_save($file = 'php_trace.php') { if(empty($_ENV['_trace'])) return; $s = "<?php exit;?>\r\n========================================================================\r\n"; $s .= $_SERVER['REQUEST_URI']."\r\nPOST:".print_r($_POST, 1)."\r\nSQL:".print_r($_ENV['_sqls'], 1)."\r\n"; $s .= $_ENV['_trace']."\r\n\r\n"; self::write_log($s, $file); } public static function le_log($s, $file = 'error') { if(DEBUG == 0 && strpos($file, 'error') === FALSE) return; $time = $_ENV['_time']; $ip = $_ENV['_ip']; $day = date('Ym', $time); $mtime = date('Y-m-d H:i:s'); $url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : ''; $logpath = LOG_PATH.'/'.$day; !is_dir($logpath) AND mkdir($logpath, 0777, true); if(is_array($s)){$s = serialize($s);} $s = str_replace(array("\r\n", "\n", "\t"), ' ', $s); $s = "<?php exit;?>\t$mtime\t$ip\t$url\t$s\r\n"; @error_log($s, 3, $logpath."/$file.php"); } }  class model{ public $table; public $pri = array(); public $maxid; static $dbs = array(); static $caches = array(); private $unique = array(); function __get($var) { switch ($var) { case 'db': return $this->db = $this->load_db(); case 'cache': return $this->cache = $this->load_cache(); case 'db_conf': return $this->db_conf = &$_ENV['_config']['db']; case 'cache_conf': return $this->cache_conf = &$_ENV['_config']['cache']; default: return $this->$var = core::model($var); } } function __call($method, $args) { throw new Exception("方法 $method 不存在"); } public function load_db() { if(isset($this->db_conf['type'])){ $type = $this->db_conf['type']; }else{ return false; } if(isset($this->db_conf['master'])) { $m = $this->db_conf['master']; $id = $type.'-'.$m['host'].'-'.$m['user'].'-'.$m['password'].'-'.$m['name'].'-'.$m['tablepre']; }else{ $id = $type; } if(isset(self::$dbs[$id])) { return self::$dbs[$id]; }else{ $db = 'db_'.$type; self::$dbs[$id] = new $db($this->db_conf); return self::$dbs[$id]; } } public function load_cache() { if(isset($this->cache_conf['type'])){ $type = $this->cache_conf['type']; }else{ return false; } if(isset($this->cache_conf[$type])) { $c = $this->cache_conf[$type]; $id = $type.'-'.$c['host'].'-'.$c['port']; }else{ $id = $type; } if(isset(self::$caches[$id])) { return self::$caches[$id]; }else{ $cache = 'cache_'.$type; self::$caches[$id] = new $cache($this->cache_conf); return self::$caches[$id]; } } public function create($data) { if(empty($this->maxid)) { $key = $this->pri2key($data); return $this->cache_db_set($key, $data); }else{ $data[$this->maxid] = $this->maxid('+1'); $key = $this->pri2key($data); $this->count('+1'); if($this->cache_db_set($key, $data)) { return $data[$this->maxid]; }else{ $this->maxid('-1'); $this->count('-1'); return FALSE; } } } public function set($key, $data, $life = 0) { $key = $this->arr2key($key); $this->unique[$key] = $data; return $this->cache_db_set($key, $data, $life); } public function read($arg1, $arg2 = FALSE, $arg3 = FALSE, $arg4 = FALSE) { $arr = ($arg2 !== FALSE) ? $this->arg2arr($arg1, $arg2, $arg3, $arg4) : (array)$arg1; return $this->get($arr); } public function get($arr) { $key = $this->arr2key($arr); if(!isset($this->unique[$key])) { $this->unique[$key] = $this->cache_db_get($key); } return $this->unique[$key]; } public function mget($arr) { $data = array(); foreach($arr as $k=>&$key) { $key = $this->arr2key($key); if(isset($this->unique[$key])) { $data[$key] = $this->unique[$key]; unset($arr[$k]); }else{ $this->unique[$key] = $data[$key] = NULL; } } $data2 = $this->cache_db_multi_get($arr); return array_merge($data, $data2); } public function update($data, $life = 0) { $key = $this->pri2key($data); $this->unique[$key] = $data; return $this->cache_db_update($key, $data, $life); } public function delete($arg1, $arg2 = FALSE, $arg3 = FALSE, $arg4 = FALSE) { $arr = ($arg2 !== FALSE) ? $this->arg2arr($arg1, $arg2, $arg3, $arg4) : (array)$arg1; return $this->del($arr); } public function del($arr) { $key = $this->arr2key($arr); $ret = $this->cache_db_delete($key); if($ret) { unset($this->unique[$key]); $this->count('-1'); } return $ret; } public function truncate() { return $this->cache_db_truncate(); } public function maxid($val = FALSE) { return $this->cache_db_maxid($val); } public function count($val = FALSE) { return $this->cache_db_count($val); } public function get_field(){ return $this->db->get_field($this->table); } public function find_fetch($where = array(), $order = array(), $start = 0, $limit = 0, $life = 0) { return $this->cache_db_find_fetch($this->table, $this->pri, $where, $order, $start, $limit, $life); } public function find_fetch_key($where = array(), $order = array(), $start = 0, $limit = 0, $life = 0) { return $this->cache_db_find_fetch_key($this->table, $this->pri, $where, $order, $start, $limit, $life); } public function find_update($where, $data, $order = array(), $limit = 0, $lowprority = FALSE) { $this->unique = array(); if($this->cache_conf['enable']) { $n = $this->find_count($where); if($n > 2000) { $this->cache->truncate($this->table); }else{ $keys = $this->find_fetch_key($where); foreach($keys as $key) { $this->cache->delete($key); } } } return $this->db->find_update($this->table, $where, $data, $order, $limit, $lowprority); } public function update_views($key, $n = 1, $field = 'views') { $key = $this->arr2key($key); return $this->cache_db_update_views($key, $n, $field); } public function find_delete($where, $order = array(), $limit = 0, $lowprority = FALSE) { $this->unique = array(); if($this->cache_conf['enable']) { $n = $this->find_count($where); if($n > 2000) { $this->cache->truncate($this->table); }else{ $keys = $this->find_fetch_key($where); foreach($keys as $key) { $this->cache_db_delete($key); } } } $num = $this->db->find_delete($this->table, $where, $order, $limit, $lowprority); if(!empty($this->maxid) && $num > 0) { $this->count('-'.$num); } return $num; } public function find_maxid() { return isset($this->maxid) ? $this->db->find_maxid($this->table.'-'.$this->maxid) : 0; } public function find_count($where = array()) { return $this->db->find_count($this->table, $where); } public function index_create($index) { return $this->db->index_create($this->table, $index); } public function index_drop($index) { return $this->db->index_drop($this->table, $index); } public function pri2key($arr) { $s = $this->table; foreach($this->pri as $v) { $s .= "-$v-".$arr[$v]; } return $s; } public function arr2key($arr) { $arr = (array)$arr; $s = $this->table; foreach($this->pri as $k=>$v) { if(!isset($arr[$k])) { $err = array(); foreach($this->pri as $pk=>$pv) { $var = isset($arr[$pk]) ? $arr[$pk] : 'null'; $err[] = "'$pv => $var"; } throw new Exception('非法键名数组: array('.implode(', ', $err).');'); } $s .= "-$v-".$arr[$k]; } return $s; } public function arg2arr($arg1, $arg2, $arg3 = FALSE, $arg4 = FALSE) { $arr = (array)$arg1; array_push($arr, $arg2); $arg3 !== FALSE && array_push($arr, $arg3); $arg4 !== FALSE && array_push($arr, $arg4); return $arr; } public function cache_db_get($key) { if($this->cache_conf['enable']) { $data = $this->cache->get($key); if(empty($data)) { $data = $this->db->get($key); $this->cache->set($key, $data); } return $data; }else{ return $this->db->get($key); } } public function cache_db_multi_get($keys) { if($this->cache_conf['enable']) { $data = $this->cache->multi_get($keys); if(empty($data)) { $data = $this->db->multi_get($keys); foreach((array)$data as $k=>$v) { $this->cache->set($k, $v); } }else{ foreach($data as $k=>&$v) { if($v === FALSE) { $v = $this->db->get($k); $this->cache->set($k, $v); } } } return $data; }else{ return $this->db->multi_get($keys); } } public function cache_db_set($key, $data, $life = 0) { $this->cache_conf['enable'] && $this->cache->set($key, $data, $life); return $this->db->set($key, $data); } public function cache_db_update($key, $data, $life = 0) { $this->cache_conf['enable'] && $this->cache->update($key, $data, $life); return $this->db->update($key, $data); } public function cache_db_update_views($key, $n = 1, $field = 'views') { $this->cache_conf['enable'] && $this->cache->delete($key); return $this->db->update_views($key, $n, $field); } public function cache_db_delete($key) { $this->cache_conf['enable'] && $this->cache->delete($key); return $this->db->delete($key); } public function cache_db_truncate() { $this->cache_conf['enable'] && $this->cache->truncate($this->table); return $this->db->truncate($this->table); } public function cache_db_maxid($val = FALSE) { $key = $this->table.'-'.$this->maxid; if($this->cache_conf['enable']) { if($val === FALSE) { $maxid = $this->cache->maxid($key, $val); if(empty($maxid)) { $maxid = $this->db->maxid($key, $val); $this->cache->maxid($key, $maxid); } return $maxid; }else{ $maxid = $this->db->maxid($key, $val); return $this->cache->maxid($key, $maxid); } }else{ return $this->db->maxid($key, $val); } } public function cache_db_count($val = FALSE) { $key = $this->table; if($this->cache_conf['enable']) { if($val === FALSE) { $rows = $this->cache->count($key, $val); if(empty($rows)) { $rows = $this->db->count($key, $val); $this->cache->count($key, $rows); } return $rows; }else{ $rows = $this->db->count($key, $val); return $this->cache->count($key, $rows); } }else{ return $this->db->count($key, $val); } } public function cache_db_find_fetch($table, $pri, $where = array(), $order = array(), $start = 0, $limit = 0, $life = 0) { if($this->db_conf['type'] == 'mongodb') { return $this->db->find_fetch($table, $pri, $where, $order, $start, $limit); }else{ $keys = $this->cache_db_find_fetch_key($table, $pri, $where, $order, $start, $limit, $life); return $this->cache_db_multi_get($keys); } } public function cache_db_find_fetch_key($table, $pri, $where = array(), $order = array(), $start = 0, $limit = 0, $life = 0) { if($this->cache_conf['enable'] && $this->cache_conf['l2_cache'] === 1) { $key = $table.'_'.md5(serialize(array($pri, $where, $order, $start, $limit))); $keys = $this->cache->l2_cache_get($key); if(empty($keys)) { $keys = $this->db->find_fetch_key($table, $pri, $where, $order, $start, $limit); $this->cache->l2_cache_set($key, $keys, $life); } }else{ $keys = $this->db->find_fetch_key($table, $pri, $where, $order, $start, $limit); } return $keys; } } class view{ private $vars = array(); private $head_arr = array(); public function __construct() { $_ENV['_theme'] = 'default'; $_ENV['_view_diy'] = FALSE; } public function assign($k, &$v) { $this->vars[$k] = &$v; } public function assign_value($k, $v) { $this->vars[$k] = $v; } public function display($filename = null) { $_ENV['_tplname'] = is_null($filename) ? $_GET['control'].'_'.$_GET['action'].'.htm' : $filename; extract($this->vars, EXTR_SKIP); $tplfile = $this->get_tplfile($_ENV['_tplname']); if( is_file($tplfile) ){ include $tplfile; }else{ if( !DEBUG ){ echo lang('tpl_file_not_exists', array('tplfile'=>$_ENV['_theme'].'/'.$filename)); exit(); } } } private function get_tplfile($filename) { $view_dir = APP_NAME.($_ENV['_view_diy'] ? '_view_diy' : '_view').'/'; $php_file = RUNTIME_PATH.$view_dir.$_ENV['_theme'].','.$filename.'.php'; if(!is_file($php_file) || DEBUG) { $tpl_file = core::get_original_file($filename, VIEW_PATH.$_ENV['_theme'].'/'); if(!$tpl_file && DEBUG) { $msg = lang('tpl_file_not_exists', array('tplfile'=>$_ENV['_theme'].'/'.$filename)); throw new Exception($msg); } if($tpl_file && FW($php_file, $this->tpl_process($tpl_file)) === false && DEBUG) { $msg = lang('write_tpl_file_failed', array('tplfile'=>$filename)); throw new Exception($msg); } } return $php_file; } private function tpl_process($tpl_file) { $reg_arr = '[a-zA-Z_]\w*(?:\[\w+\]|\[\'\w+\'\]|\[\"\w+\"\]|\[\$[a-zA-Z_]\w*\])*'; $s = file_get_contents($tpl_file); $s = preg_replace_callback('#\{inc\:([\w|\/\.]+)\}#', array($this, 'process_inc'), $s); $s = preg_replace_callback('#\{hook\:([\w\.]+)\}#', array('core', 'process_hook'), $s); $s = preg_replace('#(?:\<\?.*?\?\>|\<\?.*)#s', '', $s); $s = preg_replace('#\{php\}(.*?)\{\/php\}#s', '<?php \\1 ?>', $s); $s = preg_replace_callback('#\{block\:([a-zA-Z_]\w*)\040?([^\n\}]*?)\}(.*?){\/block}#s', array($this, 'process_block'), $s); while(preg_match('#\{loop\:\$'.$reg_arr.'(?:\040\$[a-zA-Z_]\w*){1,2}\}.*?\{\/loop\}#s', $s)) $s = preg_replace_callback('#\{loop\:(\$'.$reg_arr.'(?:\040\$[a-zA-Z_]\w*){1,2})\}(.*?)\{\/loop\}#s', array($this, 'process_loop'), $s); while(preg_match('#\{if\:[^\n\}]+\}.*?\{\/if\}#s', $s)) $s = preg_replace_callback('#\{if\:([^\n\}]+)\}(.*?)\{\/if\}#s', array($this, 'process_if'), $s); $s = preg_replace('#\{\@([^\}]+)\}#', '<?php echo(\\1); ?>', $s); $s = preg_replace_callback('#\{(\$'.$reg_arr.')\}#', array($this, 'process_vars'), $s); $s = preg_replace_callback('#\{lang\:([\w\.]+)\}#', array($this, 'process_lang'), $s); if(defined('CODE_COMPRESS') && CODE_COMPRESS == 1 && DEBUG == 0) { $s = str_replace(array("\r\n", "\n", "\t"), '', $s); $s = preg_replace("/\s(?=\s)/","\\1",$s); $s = str_replace("> <","><",$s); } if(isset($_ENV['_view_original']) && !empty($_ENV['_view_original']) ){ $s = preg_replace_callback('/<(.*?) class=["\'](.*?)["\']>/i',array($this, 'process_view_original'), $s); } $head_str = empty($this->head_arr) ? '' : implode("\r\n", $this->head_arr); $s = "<?php defined('APP_NAME') || exit('Access Denied'); $head_str\r\n?>$s"; $s = str_replace('?><?php ', '', $s); return $s; } private function process_view_original($matches){ if(isset($matches[1]) && isset($matches[2])){ $str1 = $matches[2]; $num = isset($_ENV['_view_original']['css_class_length']) ? (int)$_ENV['_view_original']['css_class_length'] : 6; $fen = isset($_ENV['_view_original']['css_class_separator']) ? $_ENV['_view_original']['css_class_separator'] : '-'; $tou = isset($_ENV['_view_original']['css_class_prefix']) ? $_ENV['_view_original']['css_class_prefix'].$fen : 'l'.'ecms'.$fen; $auth_key = $_ENV['_config']['auth_key']; if( isset($_ENV['_view_original']['css_class_type']) && intval($_ENV['_view_original']['css_class_type']) == 1 ){ $m = substr(base64_encode(md5($str1.$auth_key)), 2, $num); }else{ $m = substr(md5($str1.$auth_key), 2, $num); } return '<'.$matches[1].' class="'.$tou.$m.' '.$str1.'">'; } } private function process_inc($matches) { if( strpos($matches[1], '/') == false ){ $filename = 'inc-'.$matches[1]; }else{ $arr = explode('/', $matches[1]); $filename = $arr[0].'/inc-'.$arr[1]; } $tpl_file = core::get_original_file($filename, VIEW_PATH.$_ENV['_theme'].'/'); if(!$tpl_file) { if( DEBUG ){ $msg = lang('tpl_file_not_exists', array('tplfile'=>$_ENV['_theme'].'/'.$filename)); throw new Exception($msg); }else{ return ''; } } return file_get_contents($tpl_file); } private function process_lang($matches){ global $lang; empty($lang) && $lang = isset($_SERVER['lang']) ? $_SERVER['lang'] : array(); if(isset($matches[1]) && $matches[1]){ $language = lang($matches[1]); }else{ $language = ''; } return "<?php echo '".$language."'; ?>"; } private function process_block($matches) { $func = $matches[1]; $config = $matches[2]; $s = $matches[3]; $lib_file = core::get_original_file('block_'.$func.'.lib.php', BLOCK_PATH); if(!is_file($lib_file)) return ''; $lib_str = file_get_contents($lib_file); $lib_str = preg_replace_callback('#\t*\/\/\s*hook\s+([\w\.]+)[\r\n]#', array('core', 'process_hook'), $lib_str); if(!DEBUG) $lib_str = _strip_whitespace($lib_str); $lib_str = core::clear_code($lib_str); if(!function_exists('block_'.$func)){ $this->head_arr['block_'.$func] = $lib_str; } $s = $this->rep_double($s); $config = $this->rep_double($config); $config_arr = array(); preg_match_all('#([a-zA-Z_]\w*)="(.*?)" #', $config.' ', $m); foreach($m[2] as $k=>$v) { if(isset($v)) $config_arr[strtolower($m[1][$k])] = addslashes($v); } unset($m); $func_str = 'block_'.$func.'('.var_export($config_arr, 1).');'; $before = $after = ''; if(substr($func, 0, 7) == 'global_') { $this->head_arr[$func] = '$gdata = '.$func_str; }else{ $before .= '<?php $data = '.$func_str.' ?>'; $after .= '<?php unset($data); ?>'; } if($_ENV['_view_diy']) { $this->block_id++; $before .= '<span block_diy="before" block_id="'.$this->block_id.'"></span>'; $after .= '<span block_diy="after" block_id="'.$this->block_id.'"></span>'; } return $before.$s.$after; } private function process_loop($matches) { $args = explode(' ', $this->rep_double($matches[1])); $s = $this->rep_double($matches[2]); $arr = $this->rep_vars($args[0]); $v = empty($args[1]) ? '$v' : $args[1]; $k = empty($args[2]) ? '' : $args[2].'=>'; return "<?php if(isset($arr) && is_array($arr)) { foreach($arr as $k&$v) { ?>$s<?php }} ?>"; } private function process_if($matches) { $expr = $this->rep_double($matches[1]); $expr = $this->rep_vars($expr); $s = preg_replace_callback('#\{elseif\:([^\n\}]+)\}#', array($this, 'rep_elseif'), $this->rep_double($matches[2])); $s = str_replace('{else}', '<?php }else{ ?>', $s); return "<?php if ($expr) { ?>$s<?php } ?>"; } private function rep_elseif($matches) { $expr = $this->rep_double($matches[1]); $expr = $this->rep_vars($expr); return "<?php }elseif($expr) { ?>"; } private function process_vars($matches) { $vars = $this->rep_double($matches[1]); $vars = $this->rep_vars($vars); return "<?php echo(isset($vars) ? $vars : ''); ?>"; } private function rep_double($s) { return str_replace('\"', '"', $s); } private function rep_vars($s) { $s = preg_replace('#\[(\w+)\]#', "['\\1']", $s); $s = preg_replace('#\[\"(\w+)\"\]#', "['\\1']", $s); $s = preg_replace('#\[\'(\d+)\'\]#', '[\\1]', $s); return $s; } } class control{ public function __get($var) { if($var == 'view') { return $this->view = new view(); }elseif($var == 'db') { $db = 'db_'.$_ENV['_config']['db']['type']; return $this->db = new $db($_ENV['_config']['db']); }else{ return $this->$var = core::model($var); } } public function assign($k, &$v) { $this->view->assign($k, $v); } public function assign_value($k, $v) { $this->view->assign_value($k, $v); } public function display($filename = null) { $this->view->display($filename); } public function message($status, $message, $jumpurl = '', $delay = 2, $sys_message_file = '') { if(R('ajax','R')) { echo json_encode(array('status'=>$status, 'message'=>$message, 'jumpurl'=>$jumpurl, 'delay'=>$delay)); }else{ if(empty($jumpurl)) { $jumpurl = empty($_SERVER['HTTP_REFERER']) ? '' : $_SERVER['HTTP_REFERER']; } if($sys_message_file && is_file($sys_message_file)){ include $sys_message_file; }else{ include FRAMEWORK_PATH.'tpl/sys_message.php'; } } exit; } public function __call($method, $args) { if(DEBUG) { throw new Exception('控制器没有找到：'.get_class($this).'->'.$method.'('.(empty($args) ? '' : var_export($args, 1)).')'); }else{ core::error404(); } } } interface db_interface { public function get($key); public function multi_get($keys); public function set($key, $data); public function update($key, $data); public function delete($key); public function maxid($key, $val = FALSE); public function count($key, $val = FALSE); public function truncate($table); public function version(); public function find_fetch($table, $pri, $where = array(), $order = array(), $start = 0, $limit = 0); public function find_fetch_key($table, $pri, $where = array(), $order = array(), $start = 0, $limit = 0); public function find_update($table, $where, $data, $order = array(), $limit = 0, $lowprority = FALSE); public function find_delete($table, $where, $order = array(), $limit = 0, $lowprority = FALSE); public function find_maxid($key); public function find_count($table, $where = array()); public function index_create($table, $index); public function index_drop($table, $index); public function get_field($table); public function exist_table($table); public function table_drop($table); public function table_create($table, $cols); public function delete_db(); } defined('FRAMEWORK_PATH') || exit; class db_pdo_mysql implements db_interface { public $tablepre; public $conf = array(); public function __construct(&$conf) { $this->conf = &$conf; $this->tablepre = $conf['master']['tablepre']; } public function __get($var) { if($var == 'wlink') { $cfg = $this->conf['master']; empty($cfg['engine']) && $cfg['engine'] = 'MyISAM'; $this->wlink = $this->connect($cfg['host'], $cfg['port'], $cfg['user'], $cfg['password'], $cfg['name'], $cfg['charset'], $cfg['engine']); return $this->wlink; }elseif($var == 'rlink') { if(!isset($this->conf['slaves']) || empty($this->conf['slaves'])) { $this->rlink = $this->wlink; return $this->rlink; } $n = rand(0, count($this->conf['slaves']) - 1); $cfg = $this->conf['slaves'][$n]; empty($cfg['engine']) && $cfg['engine'] = 'MyISAM'; $this->rlink = $this->connect($cfg['host'], $cfg['port'], $cfg['user'], $cfg['password'], $cfg['name'], $cfg['charset'], $cfg['engine']); return $this->rlink; }elseif($var == 'xlink') { if(empty($this->conf['arbiter'])) { $this->xlink = $this->wlink; return $this->xlink; } $cfg = $this->conf['arbiter']; empty($cfg['engine']) && $cfg['engine'] = 'MyISAM'; $this->xlink = $this->connect($cfg['host'], $cfg['port'], $cfg['user'], $cfg['password'], $cfg['name'], $cfg['charset'], $cfg['engine']); return $this->xlink; } } public function get($key) { list($table, $keyarr, $keystr) = $this->key2arr($key); empty($link) && $link = $this->rlink; $sql = "SELECT * FROM {$this->tablepre}$table WHERE $keystr LIMIT 1"; $result = $this->query($sql); if(is_object($result)){ $r = $result->setFetchMode(PDO::FETCH_ASSOC); if($r){ return $result->fetch(); }else{ return array(); } } else { $error = $link->errorInfo(); throw new Exception("Errno: $error[0], Errstr: $error[2]"); } } public function multi_get($keys) { $sql = ''; $ret = array(); foreach($keys as $k) { $ret[$k] = array(); list($table, $keyarr, $keystr) = $this->key2arr($k); $sql .= "$keystr OR "; } $sql = substr($sql, 0, -4); if($sql) { $query = $this->query("SELECT * FROM {$this->tablepre}$table WHERE $sql"); if(!is_object($query)){ if( defined('DEBUG') && DEBUG ){ $error = $this->rlink->errorInfo(); throw new Exception("Errno: $error[0], Errstr: $error[2]"); } return array(); } $r = $query->setFetchMode(PDO::FETCH_ASSOC); if( !$r ){ return array(); } $row = $query->fetchAll(); foreach($row as $data) { $keyname = $table; foreach($keyarr as $k=>$v) { $keyname .= "-$k-".$data[$k]; } $ret[$keyname] = $data; } } return $ret; } public function set($key, $data) { if(!is_array($data)) return FALSE; list($table, $keyarr) = $this->key2arr($key); $data += $keyarr; $s = $this->arr2sql($data); $exists = $this->get($key); if(empty($exists)) { return $this->query("INSERT INTO {$this->tablepre}$table SET $s", $this->wlink); } else { return $this->update($key, $data); } } public function update($key, $data) { list($table, $keyarr, $keystr) = $this->key2arr($key); $s = $this->arr2sql($data); return $this->query("UPDATE {$this->tablepre}$table SET $s WHERE $keystr LIMIT 1", $this->wlink); } public function delete($key) { list($table, $keyarr, $keystr) = $this->key2arr($key); return $this->query("DELETE FROM {$this->tablepre}$table WHERE $keystr LIMIT 1", $this->wlink); } public function maxid($key, $val = FALSE) { list($table, $col) = explode('-', $key); $maxid = $this->table_maxid($key); if($val === FALSE) { return $maxid; }elseif(is_string($val)) { $val = max(0, $maxid + intval($val)); } $this->query("UPDATE {$this->tablepre}framework_maxid SET maxid='$val' WHERE name='$table' LIMIT 1", $this->xlink); return $val; } public function table_maxid($key) { list($table, $maxid) = explode('-', $key); $arr = $this->fetch_first("SELECT MAX($maxid) AS num FROM {$this->tablepre}$table"); return !empty($arr) ? intval($arr['num']) : $arr; } public function count($table, $val = FALSE) { $count = $this->table_count($table); if($val === FALSE) { return $count; }elseif(is_string($val)) { if($val[0] == '+') { $val = $count + intval($val); }elseif($val[0] == '-') { $val = max(0, $count + intval($val)); } } $this->query("UPDATE {$this->tablepre}framework_count SET count='$val' WHERE name='$table' LIMIT 1", $this->xlink); return $val; } public function table_count($table) { $count = 0; try { $arr = $this->fetch_first("SELECT COUNT(*) AS num FROM {$this->tablepre}$table", $this->xlink); $count = isset($arr['num']) ? intval($arr['num']) : 0; } catch (Exception $e) { throw new Exception('table_count 错误'); } return $count; } public function truncate($table) { try { $this->query("TRUNCATE {$this->tablepre}$table"); return TRUE; } catch(Exception $e) { return FALSE; } } public function get_field($table){ $ret = array(); $cfg = $this->conf['master']; $sql = "SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE table_name = '".$this->tablepre.$table."' AND table_schema='".$cfg['name']."'"; $query = $this->rlink->query($sql); if(!is_object($query)) { if( defined('DEBUG') && DEBUG ){ $error = $this->rlink->errorInfo(); throw new Exception("Errno: $error[0], Errstr: $error[2]"); } return array(); } $r = $query->setFetchMode(PDO::FETCH_ASSOC); if( !$r ){ return array(); } $row = $query->fetchAll(); foreach($row as $data) { $ret[] = $data['COLUMN_NAME']; } return $ret; } public function exist_table($table){ $db_name = $this->conf['master']['name']; $sql = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE table_schema='".$db_name."' and table_name='".$this->tablepre.$table."'"; $query = $this->rlink->query($sql); if(!is_object($query)) { if( defined('DEBUG') && DEBUG ){ $error = $this->rlink->errorInfo(); throw new Exception("Errno: $error[0], Errstr: $error[2]"); } return false; } $r = $query->setFetchMode(PDO::FETCH_ASSOC); if( !$r ){ return false; } $row = $query->fetchAll(); if( $row ){ return true; }else{ return false; } } public function find_fetch($table, $pri, $where = array(), $order = array(), $start = 0, $limit = 0) { $key_arr = $this->find_fetch_key($table, $pri, $where, $order, $start, $limit); if(empty($key_arr)) return array(); return $this->multi_get($key_arr); } public function find_fetch_key($table, $pri, $where = array(), $order = array(), $start = 0, $limit = 0) { $pris = implode(',', $pri); $s = "SELECT $pris FROM {$this->tablepre}$table"; $s .= $this->arr2where($where); if(!empty($order)) { $s .= ' ORDER BY '; $comma = ''; foreach($order as $k=>$v) { $s .= $comma."$k ".($v == 1 ? ' ASC ' : ' DESC '); $comma = ','; } } $s .= ($limit ? " LIMIT $start,$limit" : ''); $sql = $s; $ret = array(); $query = $this->rlink->query($sql); if(!is_object($query)) { if( defined('DEBUG') && DEBUG ){ $error = $this->rlink->errorInfo(); throw new Exception("Errno: $error[0], Errstr: $error[2]"); } return array(); } $r = $query->setFetchMode(PDO::FETCH_ASSOC); if( !$r ){ return array(); } $row = $query->fetchAll(); foreach($row as $data) { $keystr = ''; foreach($pri as $k) { $keystr .= "-$k-".$data[$k]; } $ret[] = $table.$keystr; } return $ret; } public function find_update($table, $where, $data, $order = array(), $limit = 0, $lowprority = FALSE) { $where = $this->arr2where($where); $data = $this->arr2sql($data); $lpy = $lowprority ? 'LOW_PRIORITY' : ''; $s = ''; if($order) { $s .= ' ORDER BY '; $comma = ''; foreach($order as $k=>$v) { $s .= $comma."$k ".($v == 1 ? ' ASC ' : ' DESC '); $comma = ','; } } if($limit){ $s .= " LIMIT {$limit}"; } return $this->exec("UPDATE $lpy {$this->tablepre}$table SET $data $where $s", $this->wlink); } public function update_views($key, $n = 1, $field = 'views'){ list($table, $keyarr, $keystr) = $this->key2arr($key); $s = "{$field}={$field}+{$n}"; return $this->query("UPDATE LOW_PRIORITY {$this->tablepre}$table SET $s WHERE $keystr LIMIT 1", $this->wlink); } public function find_delete($table, $where, $order = array(), $limit = 0, $lowprority = FALSE) { $where = $this->arr2where($where); $lpy = $lowprority ? 'LOW_PRIORITY' : ''; $s = ''; if($order) { $s .= ' ORDER BY '; $comma = ''; foreach($order as $k=>$v) { $s .= $comma."$k ".($v == 1 ? ' ASC ' : ' DESC '); $comma = ','; } } if($limit){ $s .= " LIMIT {$limit}"; } return $this->exec("DELETE $lpy FROM {$this->tablepre}$table $where $s", $this->wlink); } public function find_maxid($key) { list($table, $maxid) = explode('-', $key); $arr = $this->fetch_first("SELECT MAX($maxid) AS num FROM {$this->tablepre}$table"); return isset($arr['num']) ? intval($arr['num']) : 0; } public function find_count($table, $where = array()) { $where = $this->arr2where($where); $arr = $this->fetch_first("SELECT COUNT(*) AS num FROM {$this->tablepre}$table $where"); return isset($arr['num']) ? intval($arr['num']) : 0; } public function find_count_col($key,$where = array()) { $where = $this->arr2where($where); list($table, $filed) = explode('-', $key); $res = 0; $query = $this->fetch_all("SELECT IFNULL(sum($filed),0) AS money FROM {$this->tablepre}$table $where"); if($query) { $res = $query[0]['money']; } return $res; } public function index_create($table, $index) { $keys = implode(',', array_keys($index)); $keyname = implode('_', array_keys($index)); return $this->query("ALTER TABLE {$this->tablepre}$table ADD INDEX $keyname($keys)", $this->wlink); } public function index_drop($table, $index) { $keys = implode(',', array_keys($index)); $keyname = implode('_', array_keys($index)); return $this->query("ALTER TABLE {$this->tablepre}$table DROP INDEX $keyname", $this->wlink); } public function table_create($table, $cols, $engineer = '') { empty($engineer) && $engineer = 'MyISAM'; $sql = "CREATE TABLE IF NOT EXISTS {$this->tablepre}$table (\n"; $sep = ''; foreach($cols as $col) { if(strpos($col[1], 'int') !== FALSE) { $sql .= "$sep$col[0] $col[1] NOT NULL DEFAULT '0'"; } else { $sql .= "$sep$col[0] $col[1] NOT NULL DEFAULT ''"; } $sep = ",\n"; } $sql .= ") ENGINE=$engineer DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;"; return $this->query($sql, $this->wlink); } public function table_drop($table) { $sql = "DROP TABLE IF EXISTS {$this->tablepre}$table"; return $this->query($sql, $this->wlink); } public function delete_db(){ $sql = "DROP DATABASE IF EXISTS ".$this->conf['master']['name']; return $this->query($sql, $this->wlink); } public function table_column_add($table, $colname, $colattr) { $default = strpos($colattr, 'int') !== FALSE ? "'0'" : "''"; $sql = "ALTER TABLE {$this->tablepre}$table ADD COLUMN $colname $colattr NOT NULL DEFAULT $default;"; try {$this->query($sql, $this->wlink);} catch (Exception $e) {}; return TRUE; } public function table_column_drop($table, $colname) { $sql = "ALTER TABLE {$this->tablepre}$table DROP COLUMN $colname;"; try {$this->query($sql, $this->wlink);} catch (Exception $e) {}; return TRUE; } public function connect($host, $port, $user, $pass, $name, $charset = 'utf8', $engine = '') { if(strpos($host, ':') !== FALSE) { list($host, $port) = explode(':', $host); } else { $port = !empty($port) ? $port : 3306; } try{ $attr = array( PDO::ATTR_TIMEOUT => 5, ); $link = new PDO("mysql:host=$host;port=$port;dbname=$name;charset=$charset", $user, $pass, $attr); } catch (Exception $e) { throw new Exception('连接数据库服务器失败:'.mb_convert_encoding($e->getMessage(),'UTF-8','GBK')); } if($charset){ $link->query('SET NAMES '.$charset.', sql_mode=""'); }else{ $link->query('SET sql_mode=""'); } return $link; } public function query($sql, $link = NULL, $isthrow = TRUE) { if(!$this->rlink) return FALSE; $link = $this->link = $this->rlink; try { if(defined('DEBUG') && DEBUG && isset($_ENV['_sqls']) && count($_ENV['_sqls']) < 1000) { $start = microtime(1); $query = $link->query($sql); $runtime = number_format(microtime(1) - $start, 4); $_ENV['_sqls'][] = ' <font color="red">[time:'.$runtime.'s]</font> '.htmlspecialchars(stripslashes($sql)); }else{ $query = $link->query($sql); } } catch (Exception $e) { $s = $link->errorInfo(); $s = str_replace($this->tablepre, '***', $s); throw new Exception('Pdo_MySQL Query Error:'.$sql.' '.(isset($s[2]) ? "Errstr: $s[2]" : '')); } $_ENV['_sqlnum']++; return $query; } public function exec($sql) { if(!$this->wlink ) return FALSE; $link = $this->link = $this->wlink; $n = 0; try { if(defined('DEBUG') && DEBUG && isset($_ENV['_sqls']) && count($_ENV['_sqls']) < 1000) { $start = microtime(1); $n = $link->exec($sql); $runtime = number_format(microtime(1) - $start, 4); $_ENV['_sqls'][] = ' <font color="red">[time:'.$runtime.'s]</font> '.htmlspecialchars(stripslashes($sql)); }else{ $n = $link->exec($sql); } } catch (Exception $e){ $s = $link->errorInfo(); $s = str_replace($this->tablepre, '***', $s); throw new Exception('Pdo_MySQL Query Error:'.$sql.' '.(isset($s[2]) ? "Errstr: $s[2]" : '')); return FALSE; } $_ENV['_sqlnum']++; if($n !== FALSE) { $pre = strtoupper(substr(trim($sql), 0, 7)); if($pre == 'INSERT ' || $pre == 'REPLACE') { return $this->last_insert_id(); } } else { $s = $link->errorInfo(); $s = str_replace($this->tablepre, '***', $s); throw new Exception('Pdo_MySQL Query Error:'.$sql.' '.(isset($s[2]) ? "Errstr: $s[2]" : '')); } return $n; } public function last_insert_id() { return $this->wlink->lastinsertid(); } public function fetch_first($sql, $link = NULL) { empty($link) && $link = $this->rlink; $result = $this->query($sql, $link); if(is_object($result)) { $r = $result->setFetchMode(PDO::FETCH_ASSOC); if( !$r ){ return array(); } return $result->fetch(); } else { $error = $link->errorInfo(); throw new Exception("Errno: $error[0], Errstr: $error[2]"); } } public function fetch_all($sql, $link = NULL) { empty($link) && $link = $this->rlink; $result = $this->query($sql, $link); if(is_object($result)) { $r = $result->setFetchMode(PDO::FETCH_ASSOC); if( !$r ){ return array(); } $datalist = $result->fetchAll(); return $datalist; } else { $error = $link->errorInfo(); throw new Exception("Errno: $error[0], Errstr: $error[2]"); } } public function version() { return 'pdo_mysql'; } public function __destruct() { if(is_resource($this->wlink)) { $this->wlink = NULL; } if(is_resource($this->rlink) && is_resource($this->wlink) && $this->rlink != $this->wlink) { $this->rlink = NULL; } } public function arr2where($arr) { $s = ''; if(!empty($arr)) { foreach($arr as $key=>$val) { if(is_array($val)) { foreach($val as $k=>$v) { if(is_array($v)) { if($k === 'IN' && $v) { $s .= ' ('; foreach($v as $i) { $i = is_null($i) ? '' : addslashes($i); $s .= "$key='$i' OR "; } $s = substr($s, 0, -4).') AND '; } }else{ $v = is_null($v) ? '' : addslashes($v); if($k === 'LIKE') { $s .= "$key LIKE '%$v%' AND "; }elseif($k === 'FIND_IN_SET') { $s .= "FIND_IN_SET ('$v', $key) AND "; }else{ $s .= "$key$k'$v' AND "; } } } }else{ $val = is_null($val) ? '' : addslashes($val); $s .= "$key='$val' AND "; } } $s && $s = ' WHERE '.substr($s, 0, -5); } return $s; } private function arr2sql($arr) { $s = ''; foreach($arr as $k=>$v) { $v = is_null($v) ? '' : addslashes($v); $s .= "$k='$v',"; } return rtrim($s, ','); } private function key2arr($key) { $arr = explode('-', $key); if(empty($arr[0])) { throw new Exception('table name is empty.'); } $table = $arr[0]; if($table == 'only_alias'){ $keyval = substr($key, 17); $keystr = "alias='".$keyval."'"; $keyarr['alias'] = $keyval; return array($table, $keyarr, $keystr); } $keyarr = array(); $keystr = ''; $len = count($arr); for($i = 1; $i < $len; $i = $i + 2) { if(isset($arr[$i + 1])) { $v = $arr[$i + 1]; $keyarr[$arr[$i]] = is_int($v) ? intval($v) : $v; $v = is_null($v) ? '' : addslashes($v); $keystr .= ($keystr ? ' AND ' : '').$arr[$i]."='".$v."'"; } else { $keyarr[$arr[$i]] = NULL; } } if(empty($keystr)) { throw new Exception('keystr name is empty.'); } return array($table, $keyarr, $keystr); } } interface cache_interface { public function get($key); public function multi_get($keys); public function set($key, $data, $life = 0); public function update($key, $data, $life = 0); public function delete($key); public function maxid($table, $val = FALSE); public function count($table, $val = FALSE); public function truncate($pre = ''); public function l2_cache_get($l2_key); public function l2_cache_set($l2_key, $keys, $life = 0); } defined('FRAMEWORK_PATH') || exit; class cache_memcache implements cache_interface{ private $conf; private $is_getmulti = FALSE; public $pre; public function __construct(&$conf) { $this->conf = &$conf; $this->pre = $conf['pre']; } public function __get($var) { if(!isset($this->conf['memcache'])){ throw new Exception('Can not fount memcache config. Please check config.inc.php'); } $c = $this->conf['memcache']; if($var == 'memcache') { if(extension_loaded('Memcache')) { $this->memcache = new Memcache; }else{ throw new Exception('Memcache Extension not loaded.'); } if(!$this->memcache) { throw new Exception('PHP.ini Error: Memcache extension not loaded.'); } if($this->memcache->connect($c['host'], $c['port'])) { if(!empty($c['multi'])) { $this->is_getmulti = method_exists($this->memcache, 'getMulti'); } return $this->memcache; }else{ throw new Exception('Can not connect to Memcache host.'); } } } public function get($key) { return $this->memcache->get($this->pre.$key); } public function multi_get($keys) { $data = array(); if($this->is_getmulti) { $m_keys = array(); foreach ($keys as $i=>$k) { $m_keys[$i] = $this->pre.$k; } $m_data = $this->memcache->getMulti($m_keys); foreach($keys as $k) { if(empty($m_data[$this->pre.$k])) { $data[$k] = FALSE; }else{ $data[$k] = $m_data[$this->pre.$k]; } } }else{ foreach($keys as $k) { $arr = $this->memcache->get($this->pre.$k); if(empty($arr)) { $data[$k] = FALSE; }else{ $data[$k] = $arr; } } } return $data; } public function set($key, $data, $life = 0) { if($this->conf['l2_cache'] === 1) { $this->memcache->delete($this->pre.'_l2_cache_time'); } return $this->memcache->set($this->pre.$key, $data, 0, $life); } public function update($key, $data, $life = 0) { $arr = $this->get($key); if($arr !== FALSE) { is_array($arr) && is_array($data) && $arr = array_merge($arr, $data); return $this->set($key, $arr, $life); } return FALSE; } public function delete($key) { if($this->conf['l2_cache'] === 1) { $this->memcache->delete($this->pre.'_l2_cache_time'); } return $this->memcache->delete($this->pre.$key); } public function maxid($table, $val = FALSE) { $key = $table.'-Auto_increment'; if($val === FALSE) { return intval($this->get($key)); }else{ $this->set($key, $val); return $val; } } public function count($table, $val = FALSE) { $key = $table.'-Rows'; if($val === FALSE) { return intval($this->get($key)); }else{ $this->set($key, $val); return $val; } } public function truncate($pre = '') { return $this->memcache->flush(); } public function l2_cache_get($l2_key) { $l2_cache_time = $this->get('_l2_cache_time'); $l2_key_time = $this->get($l2_key.'_time'); if($l2_cache_time && $l2_cache_time === $l2_key_time) { return $this->get($l2_key); } return FALSE; } public function l2_cache_set($l2_key, $keys, $life = 0) { $l2_cache_time = $this->get('_l2_cache_time'); if(empty($l2_cache_time)) { $l2_cache_time = microtime(1); $this->memcache->set($this->pre.'_l2_cache_time', $l2_cache_time, 0, 0); } $this->memcache->set($this->pre.$l2_key.'_time', $l2_cache_time, 0, $life); return $this->memcache->set($this->pre.$l2_key, $keys, 0, $life); } }  interface Network__Interface { public function abort(); public function getAllResponseHeaders(); public function getResponseHeader($bstrHeader); public function open($bstrMethod, $bstrUrl, $varAsync = true, $bstrUser = '', $bstrPassword = ''); public function send($varBody = ''); public function setRequestHeader($bstrHeader, $bstrValue); public function enableGzip(); public function setMaxRedirs($n = 0); public function addBinary($name, $entity); public function addText($name, $entity); public function setTimeOuts($resolveTimeout, $connectTimeout, $sendTimeout, $receiveTimeout); } 