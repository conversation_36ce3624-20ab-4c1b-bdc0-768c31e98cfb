<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2021-10-11
 * Time: 9:05
 * Description:火车头免登录发布接口
 */
defined('ROOT_PATH') or exit;

class drafts_locoy_control extends control {
    public $setting = array();
    public $pwd = '';
    public $mid = 2;
    public $table = 'article';

    function __construct() {
        $this->pwd = trim(R('pwd'));
        $this->setting = $this->kv->get('le_drafts_setting');
        if ($this->setting['locoy_pwd'] && $this->setting['locoy_pwd'] != $this->pwd) {
            core::error404();
        }
        $this->mid = max(2, R('mid'));
        $this->table = $this->models->get_table($this->mid);
        empty($this->table) && core::error404();
    }

    //火车头获取分类
    public function category() {
        // 获取分类下拉框
        $cidhtml = $this->category->get_cidhtml_by_mid($this->mid, 0, '选择分类');
        exit($cidhtml);
    }
  //火车头获取分类
    public function categorya() {
        // 获取分类下拉框
        $cidhtml = $this->category->get_cidhtml_by_mid($this->mid, 1, '选择分类');
        exit($cidhtml);
    }
  //火车头获取分类
    public function categoryb() {
        // 获取分类下拉框
        $cidhtml = $this->category->get_cidhtml_by_mid($this->mid, 2, '选择分类');
        exit($cidhtml);
    }
  //火车头获取分类
    public function categoryc() {
        // 获取分类下拉框
        $cidhtml = $this->category->get_cidhtml_by_mid($this->mid, 3, '选择分类');
        exit($cidhtml);
    }

    //火车头发布到草稿箱
    public function dopost() {
        $uid = 0;
        $uids = $this->setting['locoy_uid'];
        if (is_numeric($uids)) {
            $user = $this->user->get($uids);
        } else {
            $uid_arr = explode(',', $uids);
            $uid_arr = array_filter($uid_arr, 'strlen');    //去掉空值
            if (count($uid_arr) > 1) {
                shuffle($uid_arr);
                $uid = $uid_arr[0];
            } else {
                $uid_arr = explode('~', $uids);
                if (isset($uid_arr[1]) && $uid_arr[1]) {
                    $uid = rand($uid_arr[0], $uid_arr[1]);
                }
            }
            $user = $this->user->get($uid);
        }

        if (empty($user)) {
            exit('UID ' . $uid . '，用户信息不存在！');
        } else {
            $uid = max(1, $user['uid']);
        }

        $cid = intval(R('cid', 'P'));
        $title = trim(strip_tags(R('title', 'P')));
        $alias = trim(R('alias', 'P'));
        $tags = trim(R('tags', 'P'));
        $intro = trim(R('intro', 'P'));
        $pic = trim(R('pic', 'P'));
        $author = trim(R('author', 'P'));
        if (empty($author) || $author == '[db:作者]') {
            $author = empty($user['author']) ? $user['username'] : $user['author'];
        }
        $source = trim(R('source', 'P'));
        $views = intval(R('views', 'P'));
        $dateline = trim(R('dateline', 'P'));
        $seo_title = trim(strip_tags(R('seo_title', 'P')));
        $seo_keywords = trim(strip_tags(R('seo_keywords', 'P')));
        $seo_description = trim(strip_tags(R('seo_description', 'P')));
        $jumpurl = trim(R('jumpurl', 'P'));
        $flags = trim(R('flags', 'P'));
        $flags == 0 && $flags = '';
        $contentstr = trim(R('content', 'P'));
        if (strlen($contentstr) < 5) {
            exit('您的内容字数太少了哦');
        }

        // 修改默认值为1
        $iscomment = intval(R('iscomment', 'P'));
        if ($iscomment == '[db:禁止评论]') {
            $iscomment = 1; // 将默认值改为1
        }

        // 处理其他默认值
        $title == '[db:标题]' && $title = '';
        $alias == '[db:别名]' && $alias = '';
        $views == '[db:浏览次数]' && $views = rand(50, 999);
        $dateline == '[db:发布时间]' && $dateline = time();
        $contentstr == '[db:内容]' && $contentstr = '';
        $intro == '[db:摘要]' && $intro = '';
        $pic == '[db:缩略图]' && $pic = '';
        $source == '[db:来源]' && $source = '';
        $tags == '[db:标签]' && $tags = '';
        // 这里将 iscomment 的默认值设置为 1
        $iscomment == '[db:禁止评论]' && $iscomment = 1; // 确保默认值为1
        $seo_title == '[db:SEO标题]' && $seo_title = '';
        $seo_keywords == '[db:SEO关键词]' && $seo_keywords = '';
        $seo_description == '[db:SEO描述]' && $seo_description = '';
        $jumpurl == '[db:跳转URL]' && $jumpurl = '';
        $flags == '[db:属性]' && $flags = '';

        

        if (!in_array($iscomment, array(0, 1))) {
            $iscomment = 1; // 确保 iscomment 只为 0 或 1
        }
        empty($cid) && exit('您的分类ID忘了填哦');
        empty($title) && exit('您的标题忘了填哦');

        $categorys = $this->category->read($cid);
        if (empty($categorys)) exit('分类ID信息不存在');
        if ($categorys['type'] == 1) exit('频道分类不能发布数据');
        $mid = $categorys['mid'];
        if ($mid != $this->mid) {
            exit('分类ID所属模型和你要发布的模型不一致哦');
        }

        // 检测别名是否能用
        if ($alias && $err_msg = $this->only_alias->check_alias($alias)) {
            $alias = '';
        } elseif ($alias && $this->drafts->find_fetch_key(array('alias' => $alias))) {
            $alias = '';
        }
        // 如果摘要为空，自动生成摘要
        $intro = auto_intro($intro, $contentstr);

        // 写入内容表
        $post = array(
            'mid' => $this->mid,
            'cid' => $cid,
            'title' => $title,
            'alias' => $alias,
            'tags' => $tags,
            'intro' => $intro,
            'pic' => $pic,
            'uid' => $uid,
            'author' => $author,
            'source' => $source,
            'dateline' => $dateline ? $dateline : $_ENV['_time'],
            'lasttime' => $_ENV['_time'],
            'ip' => ip2long($_ENV['_ip']),
            'views' => $views,
            'iscomment' => $iscomment,
            'flags' => $flags,
            'seo_title' => $seo_title,
            'seo_keywords' => $seo_keywords,
            'seo_description' => $seo_description,
            'jumpurl' => $jumpurl,
            'content' => $contentstr,
            'orderby' => rand(1, 9999999),
        );
        

        $id = $this->drafts->create($post);
        if (!$id) {
            exit('写入草稿表出错');
        }

        exit('发布成功');
    }

    //定时任务发布到正式内容
    public function docorn(){
        $endstr = '';
        $limit = (int)R('limit','G');
        empty($limit) && $limit = $this->setting['corn_limit'];

        if(!empty($this->setting['in_hour'])){
            $hour_arr = explode(',',$this->setting['in_hour']);
            $hour = date('G');
            if( !in_array($hour,$hour_arr) ){
                exit('不在指定时间发布范围！');
            }
        }

        $orderway = $this->setting['orderway'];
        switch ($orderway){
            case 0:
                $orderby = array('id'=>1);
                break;
            case 1:
                $orderby = array('id'=>-1);
                break;
            case 2: //随机取 orderby字段升序
                $orderby = array('orderby'=>1);
                break;
            default:
                $orderby = array('id'=>1);
        }

        if(isset($this->setting['high_release']) && !empty($this->setting['high_release'])){    //高级发布
            $high_release = explode(',',$this->setting['high_release']);
            $cms_arr = array();

            $all_category = $this->category->get_category_db();
            foreach ($high_release as $vl){
                $son_arr = explode('|', $vl);
                if(count($son_arr) == 2){
                    $cid_temp = (int)$son_arr[0];
                    $limit_temp = (int)$son_arr[1];

                    if(!isset($all_category[$cid_temp])){
                        continue;
                    }elseif ($all_category[$cid_temp]['mid'] != $this->mid){
                        continue;
                    }

                    $list_arr = $this->drafts->find_fetch(array('cid'=>$cid_temp), $orderby, 0, $limit_temp);
                    if($list_arr){
                        $cms_arr += $list_arr;
                    }
                }
            }
            if( empty($cms_arr) ){
                exit('无符合高级设置的草稿数据');
            }
            $high_release = 1;
        }else{
            if(strpos($limit,',', 1) !== false ){
                $limit_arr = explode(',',$limit);
                if(count($limit_arr) != 2){
                    exit('limit set error.');
                }else{
                    $limit = rand($limit_arr[0], $limit_arr[1]);
                }
            }else{
                $limit = (int)$limit;
            }

            $where = array('mid'=>$this->mid);
            $cms_arr = $this->drafts->find_fetch($where, $orderby, 0, $limit);
            if( empty($cms_arr) ){
                exit('无草稿数据');
            }
            $high_release = 0;
        }

        $succ_num = $err_num = 0;
        foreach ($cms_arr as $v){
            $res = $this->drafts->release($this->table, $v['id'], $this->setting, 0, $high_release);
            if($res['err']){
                $err_num++;
                $endstr .= "【{$res['msg']}】";
            }else{
                $succ_num++;
            }
        }
        
        $msg = '发布完成，成功 '.$succ_num.' 条，失败 '.$err_num.' 条'.$endstr;
        exit($msg);
    }

    public function docorntime(){
        $endstr = '';
        $limit = 100; //

        $where = array('mid'=>$this->mid, 'dateline'=>array('<'=>$_ENV['_time']));
        $orderby = array('dateline' => 1); // 修改为根据 dateline 升序排列

        $cms_arr = $this->drafts->find_fetch($where, $orderby, 0, $limit);
        if(empty($cms_arr)){
            exit('暂无符合条件的草稿数据');
        }

        $succ_num = $err_num = 0;
        foreach ($cms_arr as $v){
            $res = $this->drafts->release($this->table, $v['id'], $this->setting, 1);
            if($res['err']){
                $err_num++;
                $endstr .= "【{$res['msg']}】";
            }else{
                $succ_num++;
            }
        }

        $msg = '发布完成，成功 '.$succ_num.' 条，失败 '.$err_num.' 条'.$endstr;
        exit($msg);
    }
    
}
