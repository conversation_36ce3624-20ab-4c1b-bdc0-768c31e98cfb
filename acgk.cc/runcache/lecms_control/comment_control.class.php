<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-09-24
 * Time: 9:05
 * Description:前台评论相关控制器
 */
defined('ROOT_PATH') or exit;

include RUNTIME_CONTROL.'base_control.class.php'; class comment_control extends base_control{

	public function index() {



		$_GET['cid'] = (int)R('cid');
		$_GET['id'] = (int)R('id');
		$this->_var = $this->category->get_cache($_GET['cid']);

		//分类不存在
		if(empty($this->_var)){
            core::error404();
        }
		//模型ID给到GET，方便block里面自动获取
        $_GET['mid'] = (int)$this->_var['mid'];

		if($this->_var['mid'] == 1){
            // 读取单页分类内容
            $cms_page = $this->cms_page->get($_GET['cid']);
            if( empty($cms_page) ){
                core::error404();
            }
            $_show = $this->_var;
            $_show['title'] = $this->_var['name'];
            $_show['intro'] = utf8::cutstr_cn($cms_page['content'], 200);
            


            // SEO 相关
            if( empty($_show['seo_title']) ){
                $this->_cfg['titles'] = $_show['name'].'-'.$this->_cfg['webname'];
            }else{
                $this->_cfg['titles'] = $_show['seo_title'].'-'.$this->_cfg['webname'];
            }
            if( empty($_show['seo_keywords']) ){
                $this->_cfg['seo_keywords'] = $_show['name'].','.$this->_cfg['webname'];
            }else{
                $this->_cfg['seo_keywords'] = $_show['seo_keywords'].','.$this->_cfg['webname'];
            }
            $this->_cfg['seo_description'] = empty($_show['seo_description']) ? $this->_cfg['webname'].'：'.$_show['name']: $_show['seo_description'];
        }else{
            // 初始模型表名
            $this->cms_content->table = 'cms_'.$this->_var['table'];

            // 读取内容
            $_show = $this->cms_content->get($_GET['id']);
            if(empty($_show['cid']) || $_show['cid'] != $_GET['cid']) core::error404();

            


            // SEO 相关
            if( empty($_show['seo_title']) ){
                if( empty($this->_var['seo_title']) ){
                    $this->_cfg['titles'] = $_show['title'].'-'.$this->_var['name'].'-'.$this->_cfg['webname'];
                }else{
                    $this->_cfg['titles'] = $_show['title'].'-'.$this->_var['seo_title'].'-'.$this->_cfg['webname'];
                }
            }else{
                $this->_cfg['titles'] = $_show['seo_title'].'-'.$this->_cfg['webname'];
            }
            if( empty($_show['seo_keywords']) ){
                if( empty($this->_var['seo_keywords']) ){
                    $this->_cfg['seo_keywords'] = $this->_cfg['webname'].','.$this->_var['name'].','.$_show['title'];
                }else{
                    $this->_cfg['seo_keywords'] = $this->_cfg['webname'].','.$this->_var['seo_keywords'].','.$_show['title'];
                }
            }else{
                $this->_cfg['seo_keywords'] = $_show['seo_keywords'].','.$this->_var['name'].','.$this->_cfg['webname'];
            }
            $this->_cfg['seo_description'] = empty($_show['seo_description']) ? $this->_cfg['webname'].'：'.$_show['intro']: $_show['seo_description'];
        }

        $page = (int)R('page','G');
        if( $page > 1 ){
            $this->_cfg['titles']  .= '-'.lang('page_current', array('page'=>$page));
        }

        


		$this->assign('cfg', $this->_cfg);
		$this->assign('cfg_var', $this->_var);

		$GLOBALS['run'] = &$this;
		$GLOBALS['_show'] = &$_show;
        $tpl = 'comment.htm';




		$_ENV['_theme'] = &$this->_cfg['theme'];
		$this->display($tpl);
	}

	// 发表评论
	public function post() {


        if( !empty($_POST) ) {
            $cid = (int)R('cid', 'P');
            $id = (int)R('id', 'P');
            $content = htmlspecialchars(trim(R('content', 'P')));
            $author = htmlspecialchars(trim(R('author', 'P')));
            $ip = ip2long(ip());

            if (empty($cid)) $this->message(0, lang('data_error'));
            empty($author) && $author = $this->_author;
            _strlen($author) > 20 && $this->message(0, lang('comment_author_than_20'));


            empty($content) && $this->message(0, lang('comment_content_no_empty'));
            _strlen($content) > 255 && $this->message(0, lang('comment_content_than_255'));

            // 关闭全站评论
            empty($this->_cfg['open_comment']) && $this->message(0, lang('comments_closed'));

            //未开启游客评论
            if (empty($this->_cfg['open_no_login_comment']) && empty($this->_uid)) {
                $this->message(0, lang('please_login'));
            }

            //开启评论验证码
            if (isset($this->_cfg['open_comment_vcode']) && !empty($this->_cfg['open_comment_vcode'])) {
                $vcode = htmlspecialchars(trim(R('vcode', 'P')));
                empty($vcode) && $this->message(0, lang('vcode_no_empty'));
                strtoupper($vcode) != _SESSION('vcode') && $this->message(0, lang('vcode_error'));
            }

            $cates = $this->category->get_cache($cid);

            if (empty($cates)) {
                $this->message(0, lang('data_error'));
            } elseif ($cates['mid'] == 1) {   //单页
                $id = $cid;
                $cms_data = array();
            } else {  //内容页
                if (empty($id)) $this->message(0, lang('data_error'));

                $this->cms_content->table = 'cms_' . $cates['table'];
                $cms_data = $this->cms_content->get($id);
                if (empty($cms_data)) {
                    $this->message(0, lang('data_no_exists'));
                }

                if (isset($cms_data['iscomment']) && $cms_data['iscomment']) {
                    $this->message(0, lang('content_comments_closed'));
                }
            }

            $comment_data = array(
                'mid' => $cates['mid'],
                'id' => $id,
                'uid' => $this->_uid,
                'author' => $author,
                'content' => $content,
                'ip' => $ip,
                'dateline' => $_ENV['_time'],
                'reply_commentid' => (int)R('reply_commentid', 'P')
            );
            
$maxcommentid = $this->cms_content_comment_draft->create($comment_data);
if($maxcommentid) {
    // 发送 Telegram 通知
    $bot_token = '**********************************************';
    $chat_id = '7966090073';
    
    $message = "新评论待审核:\n";
    $message .= "用户: " . $comment_data['author'] . "\n";
    $message .= "内容: " . $comment_data['content'] . "\n";
    $message .= "IP: " . long2ip($comment_data['ip']) . "\n";
    $message .= "时间: " . date('Y-m-d H:i:s', $comment_data['dateline']);
    
    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    $params = [
        'chat_id' => $chat_id,
        'text' => $message,
        'parse_mode' => 'HTML'
    ];
    
    // 发送请求到 Telegram API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $result = curl_exec($ch);
    curl_close($ch);
    
    $this->message(1, '发表评论成功，等待管理员审核！');
} else {
    $this->message(0, '写入评论表出错！');
}

            $maxid = $this->cms_content_comment->create($comment_data);
            if (!$maxid) {
                $this->message(0, lang('commented_failed'));
            }

            //内容评论，更新评论数和写入评论排序表 （分类评论用的少，不写入评论排序表）
            if ($cms_data) {
                $cms_data['comments'] += 1;

                //更新内容表的评论数
                if (!$this->cms_content->update($cms_data)) {
                    $this->message(0, lang('commented_failed'));
                }

                //更新评论排序表的评论数和最后评论时间
                $ret = $this->cms_content_comment_sort->set(array($cates['mid'], $id), array(
                    'cid' => $cid,
                    'comments' => $cms_data['comments'],
                    'lastdate' => $_ENV['_time'],
                ));
                if (!$ret) {
                    $this->message(0, lang('commented_failed'));
                }
            }

            


            $this->message(1, lang('commented_successfully'));
        }else{
            exit;
        }
	}

	// 获取评论JSON（内容评论+分类评论【留言板】）
	public function json() {
		$cid = (int)R('cid');
		$id = (int)R('id');
		$commentid = (int)R('commentid');
        $floor = max(1, (int)R('floor'));

		$orderway = isset($_GET['orderway']) && $_GET['orderway'] == 1 ? 1 : -1;
		$pagenum = empty($_GET['pagenum']) ? 20 : max(1, (int)$_GET['pagenum']);
		$dateformat = empty($_GET['dateformat']) ? 'Y-m-d H:i:s' : decrypt($_GET['dateformat']);
		$humandate = isset($_GET['humandate']) ? ($_GET['humandate'] == 1 ? 1 : 0) : 1;

		if(empty($cid) || empty($id) || empty($commentid)) $this->message(0, lang('data_error'));

		$cates = $this->category->get_cache($cid);
        if(empty($cates)){
            $this->message(0, lang('data_error'));
        }

		// 获取评论列表
		$key = $orderway == 1 ? '>' : '<';
		$where = array('mid' => $cates['mid'], 'id' => $id, 'commentid' => array($key => $commentid));
		$ret = array();
		$ret['list_arr'] = $this->cms_content_comment->find_fetch($where, array('commentid' => $orderway), 0, $pagenum);
		foreach($ret['list_arr'] as &$v) {
			$this->cms_content_comment->format($v, $dateformat, $humandate);
            $v['floor'] = $floor++;
		}

		if($ret['list_arr']){
            $end_arr = end($ret['list_arr']);
            $commentid = $end_arr['commentid'];
            $orderway = max(0, $orderway);
            $dateformat = base64_encode($dateformat);
            $ret['next_url'] = $this->_cfg['weburl']."index.php?comment-json-cid-$cid-id-$id-commentid-$commentid-orderway-$orderway-pagenum-$pagenum-dateformat-".encrypt($dateformat)."-humandate-$humandate-floor-{$floor}-ajax-1";
            $ret['isnext'] = count($ret['list_arr']) < $pagenum ? 0 : 1;
        }else{
            $ret['list_arr'] = array();
            $ret['next_url'] = '';
            $ret['isnext'] = 0;
        }
        $ret['comment_url'] = $this->cms_content->comment_url($cid, $id);

		echo json_encode($ret);
		exit;
	}

	//生成验证码
    public function vcode(){
        

        $vcode = new vcode();
        $name = R('name','G');
        $width = isset($_GET['width']) ? (int)$_GET['width'] : 0;
        $height = isset($_GET['height']) ? (int)$_GET['height'] : 0;
        

        return $vcode->get_vcode($name, $width, $height);
    }


//导入TXT文件到草稿箱
function import_tags()
{
    set_time_limit(0);
    $start_time = microtime(1);
    $limit = (int) R('limit', 'G');
    $setting = $this->kv->get('import_tags_setting');
    $import_model = $setting['import_model'];
    $txt_dir = APP_PATH . $setting['txt_dir'];
    if (!is_dir($txt_dir)) {
        exit('txt文件夹路径不是一个文件夹哦！');
    }
    $maxcount = 50;
    //每次执行50条
    if ($limit) {
        $maxcount = $limit;
    }
    $dh = opendir($txt_dir);
    $txtfile_arr = array();
    while ($txtfile = readdir($dh)) {
        if ($txtfile == '.' || $txtfile == '..') {
            continue;
        }
        $ext = preg_replace('/\\W/', '', strtolower(substr(strrchr($txtfile, '.'), 1, 10)));
        if ($ext == 'txt') {
            $txtfile_arr[] = $txtfile;
            if (count($txtfile_arr) == $maxcount) {
                break;
            }
        }
    }
    closedir($dh);
    if (empty($txtfile_arr)) {
        exit($txt_dir . ' 无txt文件或者已全部导入！');
    }
    $succ = 0;
    if ($import_model == 1) {
        foreach ($txtfile_arr as $txt) {
            $txtfile_path = $txt_dir . '/' . $txt;
            if (is_file($txtfile_path)) {
                $title = substr($txt, 0, -4);
                $name = _file_get_contents($txtfile_path);
                //转为UTF-8
                $encoding = mb_detect_encoding($name, array('GB2312', 'GBK', 'UTF-16', 'UCS-2', 'UTF-8', 'BIG5', 'ASCII'));
                if ($encoding != 'UTF-8') {
                    $name = mb_convert_encoding($name, 'UTF-8', 'GB2312');
                }
                //$names = explode(PHP_EOL, $name);
                $names = explode("\r\n", $name);
                empty($name) && E(1, '标签名不得为空');
                $i = 0;
                foreach ($names as $key => $name) {
                    $i++;
                    $name = $name;
                    $data = array('name' => $name);
                    // 初始模型表名
                    $mid = max(2, R('mid', 'R'));
                    $modles = $this->models->get($mid);
                    if (empty($modles)) {
                        exit('mid值错误！');
                    }
                    $table = $modles['tablename'];
                    $this->cms_content->table = 'cms_' . $table;
                    $this->cms_content_tag->table = 'cms_' . $table . '_tag';
                    $this->cms_content_tag_data->table = 'cms_' . $table . '_tag_data';
                    if ($this->cms_content_tag->find_fetch(array('name' => $name), array(), 0, 1)) {
                        E(1, '导入标签已经存在啦');
                    }
                    $tag_data = $this->cms_content_tag->create($data);
                }
                if ($tag_data['err']) {
                    exit($tag_data['msg']);
                } else {
                    unlink($txtfile_path);
                    //成功后删除txt文件
                    $succ++;
                }
            }
        }
    }
    if ($limit) {
        exit("成功导入{$succ}个文件！");
    } else {
        echo '成功导入' . $succ . '文件<br>';
        echo number_format(microtime(1) - $start_time, 4);
        echo '<script>setTimeout(function(){ location.reload() }, 2000);</script>';
        exit;
    }
}
}
