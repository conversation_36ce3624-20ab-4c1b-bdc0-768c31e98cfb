<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-09-24
 * Time: 9:05
 * Description:前台分类控制器
 */
defined('ROOT_PATH') or exit;

include RUNTIME_CONTROL.'base_control.class.php'; class cate_control extends base_control{

    //分类列表
    public function index(){
        


        $_GET['cid'] = (int)R('cid');
        $this->_var = $this->category->get_cache($_GET['cid']);
        empty($this->_var) && core::error404();

        $this->category->format($this->_var);

        $_GET['mid'] = $this->_var['mid'];
        $_GET['type'] = $this->_var['type'];
        
        


        // SEO 相关
        $this->_cfg['titles'] = (empty($this->_var['seo_title']) ? $this->_var['name'] : $this->_var['seo_title']).'-'.$this->_cfg['webname'];
        $this->_cfg['seo_keywords'] = (empty($this->_var['seo_keywords']) ? $this->_var['name'] : $this->_var['seo_keywords']).','.$this->_cfg['webname'];
        !empty($this->_var['seo_description']) && $this->_cfg['seo_description'] =  $this->_var['seo_description'];

        $page = (int)R('page','G');
        if( $page > 1 ){
            $this->_cfg['titles']  .= '-'.lang('page_current', array('page'=>$page));
        }
        


        $this->assign('cfg', $this->_cfg);
        $this->assign('cfg_var', $this->_var);

        $GLOBALS['run'] = &$this;
        $tpl = $this->_var['cate_tpl'];

        

        $_ENV['_theme'] = &$this->_cfg['theme'];
        $this->display($tpl);
    }

    

}