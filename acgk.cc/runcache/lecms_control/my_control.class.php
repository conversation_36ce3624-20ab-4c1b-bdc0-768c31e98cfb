<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-10-09
 * Time: 15:21
 * Description: 个人中心控制器
 */

defined('ROOT_PATH') or exit;

include RUNTIME_CONTROL.'base_control.class.php'; class my_control extends base_control {
    public $_navs = array();	// 个人中心导航

	function __construct() {
        parent::__construct();

        


        if( !isset($this->_cfg['open_user']) || empty($this->_cfg['open_user']) ){//未开启用户功能
            $this->message(0, lang('open_user_0'), $this->_cfg['weburl']);
        }elseif ( empty($this->_uid) ){
            $this->message(0, lang('please_login'), $this->urls->user_url('login','user'));
        }

        // 检查用户组权限
        $err = $this->check_user_group();
        if(!$err){
            // 初始化导航数组
            $this->init_navigation();

            $this->assign('_navs', $this->_navs);
        }

        
// 在my控制器构造函数后加载sj_rainbow_pay相关模型
defined('ROOT_PATH') or exit;

// 确保sj_rainbow_pay模型正确加载
try {
    $this->sj_rainbow_pay_config = core::model('sj_rainbow_pay_config');
    $this->sj_rainbow_pay_orders = core::model('sj_rainbow_pay_orders');
    $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');
} catch (Exception $e) {
    // 如果模型加载失败，记录错误但不中断执行
    error_log('sj_rainbow_pay前端模型加载失败: ' . $e->getMessage());
}
	}

    // 检查用户组权限
    protected function check_user_group() {
        

        if($this->_group['groupid'] == 6){
            $this->user->user_token_logout(0);
            $this->message(0, lang('please_waiting_verification'), $this->_cfg['weburl']);
        }elseif ($this->_group['groupid'] == 7){
            $this->user->user_token_logout(0);
            $this->message(0, lang('dis_login'), $this->_cfg['weburl']);
        }
        

        return '';
    }

    // 初始化导航数组
    public function init_navigation() {
        


        $this->_navs['my'] = array(
            'title' => lang('my_center'),
            'icon' => 'fa fa-user-circle',
            'href' => $this->urls->user_url('index','my'),
            'target' => '_self',
            'child' =>array(
                array('id'=>'my-index', 'title' => lang('my_index'), 'href' => $this->urls->user_url('index','my'), 'icon' => 'fa fa-user-circle fa-fw', 'target' => '_self', 'class' => ''),
                array('id'=>'my-profile', 'title' => lang('my_profile'), 'href' => $this->urls->user_url('profile','my'), 'icon' => 'fa fa-user-o fa-fw', 'target' => '_self', 'class' => ''),
                array('id'=>'my-password', 'title' => lang('my_password'), 'href' => $this->urls->user_url('password','my'), 'icon' => 'fa fa-key fa-fw', 'target' => '_self', 'class' => ''),
            ),
        );

        
$this->_navs['my']['child'][]=array(
    'id'=>'my-vip',
    'title'=>'开VIP或充金币',
    'href'=>'/my-vip.html',
    'icon'=>'fa fa-hand-o-up fa-fw',
    'target'=>'_self',
    'class'=>''
);
$this->_navs['my']['child'][]=array(
    'id'=>'my-kami',
    'title'=>'卡密使用记录',
    'href'=>'/my-kami.html',
    'icon'=>'fa fa-history fa-fw',
    'target'=>'_self',
    'class'=>''
);$this->_navs['my']['child'][]=array(
    'id'=>'my-buy',
    'title'=>'我购买的内容',
    'href'=>'/my-buy.html',
    'icon'=>'fa fa-cart-plus fa-fw',
    'target'=>'_self',
    'class'=>''
);// 只在my控制器中添加导航菜单
if (isset($this->_navs) && isset($this->_navs['my'])) {
    $this->_navs['my']['child'][] = array(
        'id' => 'my-recharge',
        'title' => '在线充值',
        'href' => '/my-recharge.html',
        'icon' => 'fa fa-credit-card fa-fw',
        'target' => '_self',
        'class' => ''
    );
    $this->_navs['my']['child'][] = array(
        'id' => 'my-orders',
        'title' => '充值记录',
        'href' => '/my-orders.html',
        'icon' => 'fa fa-list-alt fa-fw',
        'target' => '_self',
        'class' => ''
    );
    // 账户状态已集成到用户中心首页，不再需要单独菜单
}

        $this->_navs['content'] = array(
            'title' => lang('contents_mng'),
            'icon' => 'fa fa-list-alt',
            'href' => $this->urls->user_url('contents','my'),
            'target' => '_self',
            'child' =>array(
                array('id'=>'my-contents', 'title' => lang('my_contents'), 'href' => $this->urls->user_url('contents','my'), 'icon' => 'fa fa-newspaper-o fa-fw', 'target' => '_self', 'class' => ''),
                array('id'=>'my-comments', 'title' => lang('my_comments'), 'href' => $this->urls->user_url('comments','my'), 'icon' => 'fa fa-comments fa-fw', 'target' => '_self', 'class' => ''),
            ),
        );

        
//用户中心投稿菜单
$le_drafts_setting = $this->kv->get('le_drafts_setting');
if(isset($le_drafts_setting['open_tougao']) && !empty($le_drafts_setting['open_tougao'])){
    $this->_navs['content']['child'][] = array('id'=>'my-drafts', 'title' => '我的投稿', 'href' => $this->cms_content->user_url('drafts','my'), 'icon' => 'fa fa-book fa-fw', 'target' => '_self', 'class' => '');
}//个人中心 我的收藏菜单
$this->_navs['content']['child'][] = array('id'=>'my-favorites', 'title' => lang('my_favorites'), 'href' => $this->cms_content->user_url('favorites','my'), 'icon' => 'fa fa-star fa-fw', 'target' => '_self', 'class' => '');

        $this->_navs['logout'] = array(
            'title' => lang('logout'),
            'icon' => 'fa fa-sign-out',
            'href' => $this->urls->user_url('logout','my'),
            'target' => '_self',
            'child' => array(),
        );

        

    }

    //----------------------------------------------------以下是用户中心的一些基本操作

    // 个人中心主页
    public function index(){
        
$this->_user['vip_times']=$this->_user['vip_times']>0?date('Y-m-d H:i:s',$this->_user['vip_times']):'';

        $this->_cfg['titles'] = lang('my_center').'_'.$this->_cfg['webname'];
        $this->_var['topcid'] = -1;

        $this->assign('cfg', $this->_cfg);
        $this->assign('cfg_var', $this->_var);

        $GLOBALS['run'] = &$this;
        $_ENV['_theme'] = &$this->_cfg['theme'];
        $this->display('user/my_index.htm');
    }

    //编辑资料
    public function profile(){
	    if( empty($_POST) ){
            


            $this->_cfg['titles'] = lang('my_profile').'_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);

            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];

	        $this->display('user/my_profile.htm');
        }else{
            $post = array(
                'uid'=>$this->_uid,
                'author'=>htmlspecialchars(trim(R('author', 'P'))),
                'email'=>htmlspecialchars(trim(R('email', 'P'))),
                'mobile'=>htmlspecialchars(trim(R('mobile', 'P'))),
                'homepage'=>htmlspecialchars(trim(R('homepage', 'P'))),
                'intro'=>htmlspecialchars(trim(R('intro', 'P'))),
            );
            

            if( $this->user->update($post) ){
                

                $this->message(1, lang('edit_successfully'));
            }else{
                $this->message(0, lang('edit_failed'));
            }
        }
    }

    //修改密码
    public function password(){
        if( empty($_POST) ){
            


            $this->_cfg['titles'] = lang('my_password').'_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $login_url = $this->urls->user_url('login', 'user');
            $this->assign('login_url', $login_url);

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);

            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];

            $this->display('user/my_password.htm');
        }else{
            $oldpwd = htmlspecialchars(trim(R('oldpwd', 'P')));
            $newpwd = htmlspecialchars(trim(R('newpwd', 'P')));
            $renewpwd = htmlspecialchars(trim(R('renewpwd', 'P')));

            


            if( empty($oldpwd) ){
                $this->message(0, lang('old_pwd_no_empty'));
            }elseif ( empty($newpwd) ){
                $this->message(0, lang('new_pwd_no_empty'));
            }elseif ( $newpwd != $renewpwd ){
                $this->message(0, lang('new_pwd_inconsistent'));
            }elseif ( $err = $this->user->check_password($newpwd) ){
                $this->message(0, $err);
            }elseif( !$this->user->verify_password($oldpwd, $this->_user['salt'], $this->_user['password']) ) {
                $this->message(0, lang('old_pwd_error'));
            }

            


            $data['uid'] = $this->_uid;
            $data['password'] = $this->user->safe_password($newpwd, $this->_user['salt']);

            


            if( $this->user->update($data) ){
                //清除登录信息，重新登录
                $this->user->user_token_logout(0);

                

                $this->message(1, lang('edit_successfully'));
            }else{
                $this->message(0, lang('edit_failed'));
            }
        }
    }

    //我的内容列表
    public function contents(){
        if( empty($_POST) ) {
            


            $mid = max(2, (int)R('mid'));

            $select = $this->models->get_models_html($mid, 'class="form-control"');

            $table = $this->models->get_table($mid);
            $this->cms_content->table = 'cms_'.$table;

            $where['uid'] = $this->_uid;
            


            // 初始分页
            $pagenum = 10;
            $total = $this->cms_content->find_count($where);
            $maxpage = max(1, ceil($total/$pagenum));
            $page = min($maxpage, max(1, intval(R('page'))));
            $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('contents', 'my', true, array('mid'=>$mid))); //这里使用bootstrap风格
            $this->assign('pages', $pages);
            $this->assign('total', $total);

            $cms_arr = $this->cms_content->list_arr($where, 'id', -1, ($page-1)*$pagenum, $pagenum, $total);
            foreach($cms_arr as &$v) {
                $this->cms_content->format($v, $mid);
            }

            


            $this->assign('cms_arr', $cms_arr);
            $this->assign('midselect', $select);
            $this->assign('mid', $mid);

            $this->_cfg['titles'] = lang('my_contents').'_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);

            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];

            $this->display('user/my_contents.htm');
        }else{
            $act = R('act','R');

            if($act == 'del'){
                $id = (int)R('id','P');
                $mid = max(2, (int)R('mid','P'));
                $models = $this->_cfg['table_arr'];
                $table = isset($models[$mid]) ? $models[$mid] : '';

                if( empty($id) || empty($mid) || empty($table) ){
                    $this->message(0, lang('data_error'));
                }else{
                    $this->cms_content->table = 'cms_'.$table;
                    $data = $this->cms_content->get($id);

                    if( empty($data) || $data['uid'] != $this->_uid){
                        $this->message(0, lang('data_error'));
                    }else{
                        $err = $this->cms_content->xdelete($table, $id, $data['cid']);
                        if( $err ){
                            $this->message(0, lang('delete_failed'));
                        }else{
                            


                            $this->message(1, lang('delete_successfully'));
                        }
                    }
                }
            }

            

        }
    }

    //我的评论列表
    public function comments(){
        if( empty($_POST) ){
            


            $models = $this->_cfg['table_arr'];

            $where['uid'] = $this->_uid;
            


            // 初始分页
            $pagenum = 10;
            $total = $this->cms_content_comment->find_count($where);
            $maxpage = max(1, ceil($total/$pagenum));
            $page = min($maxpage, max(1, intval(R('page'))));
            $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('comments', 'my', true)); //这里使用bootstrap风格
            $this->assign('pages', $pages);
            $this->assign('total', $total);

            // 获取评论列表和评论内容列表
            $cms_arr = array();
            $comment_arr = $this->cms_content_comment->list_arr($where, 'commentid', -1, ($page-1)*$pagenum, $pagenum, $total);
            foreach($comment_arr as $k=>&$v) {
                $this->cms_content_comment->format($v, 'Y-m-d H:i', 0);

                $id = $v['id'];
                $mid = $v['mid'];
                $table = isset($models[$mid]) ? $models[$mid] : '';
                if( !empty($table)  ){
                    if( !isset($cms_arr[$id]) ){
                        $this->cms_content->table = 'cms_'.$table;
                        $data = $this->cms_content->get($id);
                        if($data){
                            $this->cms_content->format($data, $mid);
                            $cms_arr[$id] = $data;
                        }else{
                            $this->cms_content_comment->delete($v['commentid']);    //内容不存在的，则评论也删除
                        }
                    }
                }else{
                    $this->cms_content_comment->delete($v['commentid']);    //模型不存在的，则评论也删除
                }
            }

            


            $this->assign('comment_arr', $comment_arr);
            $this->assign('cms_arr', $cms_arr);

            $this->_cfg['titles'] = lang('my_comments').'_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);

            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];

            $this->display('user/my_comments.htm');
        }else{
            $act = R('act','R');

            if($act == 'del'){
                $commentid = (int)R('commentid','P');
                if( empty($commentid) ){
                    $this->message(0, lang('data_error'));
                }else{
                    $data = $this->cms_content_comment->get($commentid);
                    if( empty($data) || $data['uid'] != $this->_uid){
                        $this->message(0, lang('data_error'));
                    }else{
                        $mid = $data['mid'];
                        $models = $this->_cfg['table_arr'];
                        $table = isset($models[$mid]) ? $models[$mid] : '';
                        $cms_data = array();
                        if( $table ){
                            $this->cms_content->table = 'cms_'.$table;
                            $cms_data = $this->cms_content->get($data['id']);
                        }
                        if( $this->cms_content_comment->delete($commentid) ){
                            if($cms_data['comments'] > 0){
                                $cms_data['comments']--;
                                $this->cms_content->update($cms_data);
                            }
                            


                            $this->message(1, lang('delete_successfully'));
                        }else{
                            $this->message(0, lang('delete_failed'));
                        }
                    }
                }
            }

            

        }
    }

    //上传用户头像
    public function upload_avatar(){
        $cfg = $this->runtime->xget();

        $updir = 'upload/avatar/'.substr(sprintf("%09d", $this->_uid), 0, 3).'/';
        $config = array(
            'maxSize'=>$cfg['up_img_max_size'],
            'allowExt'=>$cfg['up_img_ext'],
            'upDir'=>ROOT_PATH,
        );
        //指定存放路径和文件名
        $extra = array(
            'dir'=>$updir,
            'fileNewName'=>$this->_uid.'.png'
        );

        


        $up = new upload($config, 'upfile', false, $extra);
        $info = $up->getFileInfo();

        if($info['state'] == 'SUCCESS') {
            


            $path = $info['path'];   //相对路径

            if($cfg['user_avatar_width'] && $cfg['user_avatar_height']){    //等比例缩放裁剪头像
                image::thumb(ROOT_PATH.$path, ROOT_PATH.$path, $cfg['user_avatar_width'], $cfg['user_avatar_height'], 4, 100);
            }

            //更新新的头像文件到数据表
            $this->user->update( array('uid'=>$this->_uid, 'avatar'=>$path) );

            //layui上传图片成功 返回数据
            $data = array(
                'err'=>0,
                'msg'=>lang('upload_successfully'),
                'data'=>array(
                    'src'=> $path ,
                    'path' => $path,
                    'title'=>substr($info['name'],0, -(strlen($info['ext'])+1)), //不含后缀名
                )
            );
            echo json_encode($data);
        }else{
            $res = array('err'=>1, 'msg'=>$info['state']);
            echo json_encode($res);
        }
        exit();
    }

    // 退出登录
    public function logout(){
        

        $this->user->user_token_logout(0);

        $this->message(1, lang('logout_successfully'), $this->_cfg['weburl']);
    }


//草稿箱 - 我的投稿（列表、添加、编辑、删除）
function drafts(){
    $mid = 2;   //文章
    $this->assign('mid', $mid);

    $le_drafts_setting = $this->kv->get('le_drafts_setting');

    $act = isset($_GET['act']) ? trim($_GET['act']) : 'list';
    if($act == 'del'){
        $id = R('id','P');
        if( empty($id) ){
            $this->message(0, '参数错误！');
        }else{
            $data = $this->drafts->get($id);
            if( empty($data) || $data['uid'] != $this->_uid){
                $this->message(0, '内容不存在！');
            }else{
                $err = $this->drafts->xdelete($id);
                if($err){
                    $this->message(0, '删除失败，请重试！');
                }else{
                    $this->message(1, '删除成功！');
                }
            }
        }
    }elseif ($act == 'add'){
        if(!isset($le_drafts_setting['open_tougao']) && empty($le_drafts_setting['open_tougao'])){
            $this->message(0, '已关闭投稿功能！');
        }
        $disable_uid_arr = isset($le_drafts_setting['disable_uid']) ? explode(',', $le_drafts_setting['disable_uid']) : array();
        if( in_array($this->_uid, $disable_uid_arr) ){
            $this->message(0, '您已被禁止使用投稿功能！');
        }
        if(isset($le_drafts_setting['tougao_limit']) && !empty($le_drafts_setting['tougao_limit'])){
            $starttime = mktime(0,0,0,date('m'),date('d'),date('Y'));
            $where = array('uid'=>$this->_uid,'dateline'=>array('>'=>$starttime));
            $total = $this->drafts->find_count($where);
            if( $total >= (int)$le_drafts_setting['tougao_limit'] ){
                $this->message(0, '今日投稿已达上限，不能再投稿啦！');
            }
        }

        if( !empty($_POST) ){
            $models = $this->_cfg['table_arr'];
            $table = isset($models[$mid]) ? $models[$mid] : 'article';

            //日期形式的转为时间戳
            if(isset($_POST['dateline']) && strpos($_POST['dateline'], '-') !== false){
                $_POST['dateline'] = strtotime($_POST['dateline']);
            }

            $_POST['mid'] = $mid;
            $res = $this->drafts->xadd($_POST, $this->_user, $table);
            if( $res['err'] ){
                E(1, $res['msg']);
            }
            E(0, '投稿成功');
        }else{
            $data = array();
            $this->assign('data', $data);

            // 获取分类下拉框
            $category_arr = $this->category->get_category();

            $cidhtml = '<select name="cid" id="cid" class="form-control">';
            if(empty($category_arr)) {
                $cidhtml .= '<option value="0">没有分类</option>';
            }else{
                $cidhtml .= '<option value="0">选择分类</option>';
                foreach($category_arr as $curr_mid => $arr) {
                    if($mid != $curr_mid) continue;

                    foreach($arr as $v) {
                        if($v['contribute'] == 0){
                            continue;
                        }
                        $disabled = $v['type'] == 1 ? ' disabled="disabled"' : '';
                        $cidhtml .= '<option value="'.$v['cid'].'"'.$disabled.'>';
                        $cidhtml .= str_repeat("　", $v['pre']-1);
                        $cidhtml .= '|─'.$v['name'].($v['type'] == 1 ? '[频道]' : '').'</option>';
                    }
                }
            }
            $cidhtml .= '</select>';
            $this->assign('cidhtml', $cidhtml);

            $edit_cid_id = '-mid-'.$mid.'-nodb-1';
            $this->assign('edit_cid_id', $edit_cid_id);

            $this->assign('act', $act);
            $actName = '发布';
            $this->assign('actName', $actName);

            $my_drafts_url = $this->cms_content->user_url('drafts','my');
            $this->assign('my_drafts_url', $my_drafts_url);

            $this->assign_value('min', date('Y-m-d H:i:s'));

            $this->_cfg['titles'] = '发布投稿_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);
            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];

            $this->display('my_drafts_set.htm');
        }
    }elseif ($act == 'edit'){
        if( !empty($_POST) ){
            $models = $this->_cfg['table_arr'];
            $table = isset($models[$mid]) ? $models[$mid] : 'article';

            $_POST['mid'] = $mid;
            $res = $this->drafts->xedit($_POST, $this->_user, $table);
            if( $res['err'] ){
                E(1, $res['msg']);
            }
            E(0, '编辑成功');
        }else{
            $id = (int)R('id','G');
            $data = $this->drafts->get($id);
            if(empty($data) || $data['mid'] != $mid || $data['uid'] != $this->_uid){
                $this->message(0, '内容不存在！');
            }
            $this->assign('data', $data);

            // 获取分类下拉框
            $category_arr = $this->category->get_category();

            $cidhtml = '<select name="cid" id="cid" class="form-control">';
            if(empty($category_arr)) {
                $cidhtml .= '<option value="0">没有分类</option>';
            }else{
                $cidhtml .= '<option value="0">选择分类</option>';
                foreach($category_arr as $curr_mid => $arr) {
                    if($mid != $curr_mid) continue;

                    foreach($arr as $v) {
                        if($v['contribute'] == 0){
                            continue;
                        }
                        $disabled = $v['type'] == 1 ? ' disabled="disabled"' : '';
                        $cidhtml .= '<option value="'.$v['cid'].'"'.($v['type'] == 0 && $v['cid'] == $data['cid'] ? ' selected="selected"' : '').$disabled.'>';
                        $cidhtml .= str_repeat("　", $v['pre']-1);
                        $cidhtml .= '|─'.$v['name'].($v['type'] == 1 ? '[频道]' : '').'</option>';
                    }
                }
            }
            $cidhtml .= '</select>';
            $this->assign('cidhtml', $cidhtml);

            $edit_cid_id = '-mid-'.$mid.'-nodb-1';
            $this->assign('edit_cid_id', $edit_cid_id);

            $this->assign('act', $act);
            $actName = '编辑';
            $this->assign('actName', $actName);

            $my_drafts_url = $this->cms_content->user_url('drafts','my');
            $this->assign('my_drafts_url', $my_drafts_url);

            $this->_cfg['titles'] = '编辑投稿_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);
            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];
            $this->display('my_drafts_set.htm');
        }
    }elseif ($act == 'upload_pic'){ //上传缩略图
        $mid = max(2, (int)R('mid','R'));

        $data = $this->drafts->upload_pic($mid);
        echo json_encode($data);
        exit();
    }elseif ($act == 'list'){
        $where['mid'] = $mid;
        $where['uid'] = $this->_uid;

        // 初始分页
        $pagenum = 10;
        $total = $this->drafts->find_count($where);
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, intval(R('page'))));
        $pages = paginator::pages_bootstrap($page, $maxpage, 'index.php?my-drafts-act-list-mid-'.$mid.'-page-{page}'); //这里使用bootstrap风格
        $this->assign('pages', $pages);
        $this->assign('total', $total);

        $cms_arr = $this->drafts->list_arr($where, 'id', -1, ($page-1)*$pagenum, $pagenum, $total);
        foreach($cms_arr as &$v) {
            $this->cms_content->format($v, $mid);
            unset($v['url']);
        }
        $this->assign('cms_arr', $cms_arr);

        $this->_cfg['titles'] = '我的投稿_'.$this->_cfg['webname'];
        $this->_var['topcid'] = -1;

        $this->assign('cfg', $this->_cfg);
        $this->assign('cfg_var', $this->_var);
        $GLOBALS['run'] = &$this;
        $_ENV['_theme'] = &$this->_cfg['theme'];
        $this->display('my_drafts.htm');
    }
}//umeditor编辑器 用户中心 上传图片
public function up_image() {
    $type = R('type');
    $mid = max(1, (int)R('mid','R'));
    $cid = (int)R('cid');
    $id = (int)R('id');
    $cfg = $this->_cfg;

    if( empty($this->_uid) ){
        exit('未登录不能上传哦！');
    }

    if($mid == 1) {
        // 单页上传的图片量小，所以不保存到数据库。
        $updir = 'upload/page/';
        $config = array(
            'maxSize'=>$cfg['up_img_max_size'],
            'allowExt'=>$cfg['up_img_ext'],
            'upDir'=>ROOT_PATH.$updir,
        );
        $up = new upload($config, 'upfile');
        $info = $up->getFileInfo();
    }else{
        // 非单页模型
        $table = $this->models->get_table($mid);
        $updir = 'upload/'.$table.'/';
        $config = array(
            'maxSize'=>$cfg['up_img_max_size'],
            'allowExt'=>$cfg['up_img_ext'],
            'upDir'=>ROOT_PATH.$updir,
        );
        if( isset($_GET['nodb']) ){ //不写入附件表
            $up = new upload($config, 'upfile');
            $info = $up->getFileInfo();
        }else{
            $this->cms_content_attach->table = 'cms_'.$table.'_attach';
            $info = $this->cms_content_attach->uploads($config, $this->_user['uid'], $cid, $id);
        }
    }
    $path = $updir.$info['path'];

    // 是否添加水印
    if($info['state'] == 'SUCCESS' && !empty($cfg['watermark_pos'])) {
        image::watermark(ROOT_PATH.$path, ROOT_PATH.'static/img/watermark.png', null, $cfg['watermark_pos'], $cfg['watermark_pct']);
    }

    if($type == 'ajax') {
        echo '{"path":"'.$path.'","state":"'.$info['state'].'"}';
    }else{
        $editorid = preg_replace('/\W/', '', R('editorid'));
        echo "<script>parent.UM.getEditor('".$editorid."').getWidgetCallback('image')('".$path."','".$info['state']."')</script>";
    }
    exit;
}//收藏插件 个人中心 我的收藏
public function favorites(){
    if( empty($_POST) ) {
        $models = $this->_cfg['table_arr'];

        $where['uid'] = $this->_uid;
        // 初始分页
        $pagenum = 10;
        $total = $this->favorites->find_count($where);
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, intval(R('page'))));
        $pages = paginator::pages_bootstrap($page, $maxpage, 'index.php?my-favorites-page-{page}'); //这里使用bootstrap风格
        $this->assign('pages', $pages);
        $this->assign('total', $total);

        $cms_arr = array();
        $favorites_arr = $this->favorites->list_arr($where, 'fid', -1, ($page-1)*$pagenum, $pagenum, $total);
        foreach($favorites_arr as $k=>&$v) {
            $v['date'] = isset($v['dateline']) ? date('Y-m-d H:i:s', $v['dateline']) : date('Y-m-d H:i:s');

            $id = $v['id'];
            $mid = $v['mid'];
            $table = isset($models[$mid]) ? $models[$mid] : '';
            if( !empty($table)  ){
                if( !isset($cms_arr[$id]) ){
                    $this->cms_content->table = 'cms_'.$table;
                    $data = $this->cms_content->get($id);
                    if($data){
                        $this->cms_content->format($data, $mid);
                        $cms_arr[$id] = $data;
                    }else{
                        $this->favorites->delete($v['fid']);    //内容不存在的，则收藏也删除
                    }
                }
            }else{
                $this->favorites->delete($v['fid']);    //模型不存在的，则收藏也删除
            }
        }

        $this->assign('favorites_arr', $favorites_arr);
        $this->assign('cms_arr', $cms_arr);

        $this->_cfg['titles'] = lang('my_favorites').'_'.$this->_cfg['webname'];
        $this->_var['topcid'] = -1;

        $this->assign('cfg', $this->_cfg);
        $this->assign('cfg_var', $this->_var);

        $GLOBALS['run'] = &$this;
        $_ENV['_theme'] = &$this->_cfg['theme'];

        $this->display('my_favorites.htm');
    }else{
        $act = R('act','R');
        if($act == 'del'){
            $fid = (int)R('fid','P');
            if( empty($fid) ){
                $this->message(0, lang('data_error'));
            }else{
                $data = $this->favorites->get($fid);
                if( empty($data) || $data['uid'] != $this->_uid){
                    $this->message(0, lang('data_no_exists'));
                }else{
                    $err = $this->favorites->xdelete($fid, $this->_uid);
                    if($err){
                        $this->message(0, $err);
                    }else{
                        $this->message(1, lang('delete_successfully'));
                    }
                }
            }
        }
    }
}function vip(){
     if(!empty($_POST)){
         $kami=trim(R('kami','P'));
         $data1=$this->kami->find_fetch(array('pwd'=>$kami), array(), 0, 1);
         $data = $data1 ? current($data1) : array();
         if(empty($data)){
             E(1,'卡密错误或已被使用！');
         }
         if($data['status']==1){
             E(1,'该卡密已被使用！');
         }
        $msg='';
         $updateUserData=array(
             'uid'=>$this->_user['uid'],
         );
         if($data['type']==1){
             $msg.="{$data['value']}金币 ";
             $updateUserData['golds']=$this->_user['golds']+$data['value'];
         }elseif($data['type']==2){
             $msg.="{$data['value']}天Vip ";
             if($this->_user['groupid']==11 && $data['value']>0){
                 $updateUserData['groupid']=12;
                 $updateUserData['vip_times']=time()+$data['value']*86400;
             }elseif($this->_user['groupid']==12){
                 $updateUserData['vip_times']=$this->_user['vip_times']+$data['value']*86400;
             }
         }
         $updateKamiData=array(
             'id'=>$data['id'],
             'uid'=>$this->_user['uid'],
             'update_time'=>time(),
             'status'=>1,
         );
        if(!$this->user->update($updateUserData) || !$this->kami->update($updateKamiData)) {
            E(1, '卡密使用失败！');
        }
        E(0, '卡密使用成功，已为您增加：'.$msg);
     }
    $this->_cfg['titles'] = '升级会员';
    $this->_var['topcid'] = -1;
    $this->assign('cfg', $this->_cfg);
    $this->assign('cfg_var', $this->_var);
    $GLOBALS['run'] = &$this;
    $_ENV['_theme'] = &$this->_cfg['theme'];
    $this->display('templet/my_vip.htm');
}
function  kami()
{
    $this->_cfg['titles'] = '我使用的卡密';
    $where['uid'] = $this->_uid;
    // 初始分页
    $pagenum = 10;
    $total = $this->kami->find_count($where);
    $maxpage = max(1, ceil($total/$pagenum));
    $page = min($maxpage, max(1, intval(R('page'))));
    $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('kami', 'my', true)); //这里使用bootstrap风格
    $this->assign('pages', $pages);
    $this->assign('total', $total);
    $data = $this->kami->list_arr2($where, 'update_time', -1, ($page-1)*$pagenum, $pagenum, $total);
    foreach ($data as $k=>$v){
        $msg='';
        if($v['type']==1){
            $msg.="金币+{$v['value']} ";
        }elseif ($v['type']==2){
            $msg.="Vip+{$v['value']}天 ";
        }
        $data[$k]['msg']=$msg;
    }
    $this->assign('data', $data);
    $this->assign('cfg', $this->_cfg);
    $this->assign('cfg_var', $this->_var);
    $GLOBALS['run'] = &$this;
    $_ENV['_theme'] = &$this->_cfg['theme'];
    $this->display('templet/my_kami.htm');
}function  buy()
{
    $this->_cfg['titles'] = '我购买的内容';
    $mid = max(2, (int)R('mid'));
    $table = $this->models->get_table($mid);
    $this->cms_content->table = 'cms_'.$table;
    $where['uid'] = $this->_uid;
    // 初始分页
    $pagenum = 10;
    $total = $this->user_buy->find_count($where);
    $maxpage = max(1, ceil($total/$pagenum));
    $page = min($maxpage, max(1, intval(R('page'))));
    $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('kami', 'my', true)); //这里使用bootstrap风格
    $this->assign('pages', $pages);
    $this->assign('total', $total);
    $data = $this->user_buy->list_arr($where, 'create_time', -1, ($page-1)*$pagenum, $pagenum, $total);
    foreach ($data as $k=>$v){
        $item=$this->cms_content->get($v['aid']);
        $data[$k]['create_time']=empty($v['create_time'])?'-':date('Y-m-d H:i:s',$v['create_time']);
        $data[$k]['title']=$item['title'];
        $data[$k]['url']=$this->cms_content->content_url($item, $mid);
    }
    $this->assign('data', $data);
    $this->assign('cfg', $this->_cfg);
    $this->assign('cfg_var', $this->_var);
    $GLOBALS['run'] = &$this;
    $_ENV['_theme'] = &$this->_cfg['theme'];
    $this->display('templet/my_buy.htm');
}
function userinfo()
{
    if($_POST){
        if(empty($_POST['id']) || !is_numeric($_POST['id'])){
            E(1, '参数错误');
        }
        $arc=$this->cms_article->get($_POST['id']);
        $golds=$this->_user['golds']-$arc['golds'];
        if($golds<0){
            E(1, '您的金币余额不足，请<a href="/my-vip.html" target="_blank" style="color: #0a5ba6">充值</a>后再试！');
        }
        if($arc['golds']<=0){
            E(1, '该内容不支持金币购买！');
        }
        //写入购买记录
        $data = array(
            'aid' => $_POST['id'],
            'uid' => $this->_user['uid'],
            'fee' => $arc['golds'],
            'pay_type' =>2,
            'create_time'=>time()
        );
        $id = $this->user_buy->create($data);
        if(!$id) {
            E(1, '购买失败！');
        }
        $updateData=array(
            'uid'=>$this->_user['uid'],
            'golds'=>$golds
        );
        $this->user->update($updateData);
        //更新余额
        E(0, '购买成功，你账户的金币余额为：'.$golds);
    }
    $data=array(
        'golds'=>$this->_user['golds'],
        'credits'=>$this->_user['credits'],
    );
    E(0, 'ok','',['data'=>$data]);
}// 在线充值页面 - 对应URL: /my-recharge.html
function recharge()
{
    $this->_cfg['titles'] = '在线充值';
    $this->_var['topcid'] = -1;

    // 获取套餐列表
    $gold_packages = $this->sj_rainbow_pay_packages->get_frontend_packages(1);
    $vip_packages = $this->sj_rainbow_pay_packages->get_frontend_packages(2);

    // 计算VIP信息
    $vip_days = 0;
    $vip_expire_time = '';
    if ($this->_user['groupid'] == 12 && $this->_user['vip_times'] > time()) {
        $vip_days = ceil(($this->_user['vip_times'] - time()) / 86400);
        $vip_expire_time = date('Y-m-d H:i:s', $this->_user['vip_times']);
    }

    // 设置模板变量
    $this->assign('gold_packages', $gold_packages);
    $this->assign('vip_packages', $vip_packages);
    $this->assign('vip_days', $vip_days);
    $this->assign('vip_expire_time', $vip_expire_time);
    $this->assign('cfg', $this->_cfg);
    $this->assign('_cfg', $this->_cfg);
    $this->assign('_user', $this->_user);
    $this->assign('my_url', $this->_my_url);
    $this->display('templet/my_rainbow_pay.htm');
}

// 创建订单 - 对应URL: /my-create.html
function create()
{
    if (empty($_POST)) {
        E(1, '请求方式错误！');
    }

    if (!empty($_POST)) {
        $package_id = intval(R('package_id', 'P'));
        $pay_type = trim(R('pay_type', 'P'));

        if ($package_id <= 0) {
            E(1, '请选择充值套餐！');
        }

        if (!in_array($pay_type, array('alipay', 'wxpay'))) {
            E(1, '请选择支付方式！');
        }

        // 创建订单
        $result = $this->sj_rainbow_pay_orders->create_order($this->_uid, $package_id, $pay_type);
        if ($result['err']) {
            E(1, $result['msg']);
        }
        
        $order = $result['data'];

        // 获取支付配置
        $config = $this->sj_rainbow_pay_config->get_all_config();
        
        if ($pay_type == 'alipay') {
            $api_url = $config['alipay_api_url'];
            $pid = $config['alipay_pid'];
            $key = $config['alipay_key'];
        } else { // wxpay
            $api_url = $config['wechat_api_url'];
            $pid = $config['wechat_pid'];
            $key = $config['wechat_key'];
        }
        
        if (empty($api_url) || empty($pid) || empty($key)) {
            E(1, '支付配置不完整，请联系管理员！');
        }
        
        // 获取用户IP
        $client_ip = $this->get_client_ip();

        // 构建支付参数 - 使用VPS专用回调（与后台显示地址保持一致）
        $params = array(
            'pid' => $pid,
            'type' => $pay_type,
            'out_trade_no' => $order['order_no'],
            'notify_url' => $this->_cfg['weburl'] . 'lecms/plugin/sj_rainbow_pay/vps_notify.php',
            'return_url' => $this->_cfg['weburl'] . 'my-payreturn.html',
            'name' => $config['site_name'] . ' - ' . ($order['type'] == 1 ? $order['value'] . '金币' : $order['value'] . '天VIP'),
            'money' => $order['amount'],
            'clientip' => $client_ip,
            'device' => $this->get_device_type(),
            'param' => ''
        );

        // 生成签名
        $sign = $this->generate_sign($params, $key);
        $params['sign'] = $sign;
        $params['sign_type'] = 'MD5';

        // 只使用表单提交方式，避免重复创建订单
        $submit_url = rtrim($api_url, '/') . '/submit.php';

        // 构建支付页面URL
        $payment_url = $submit_url . '?' . http_build_query($params);

        // 检测设备类型，为移动端提供更好的支付体验
        $is_mobile = $this->is_mobile_device();
        $device_type = $this->get_device_type();

        // 返回支付页面URL和设备信息
        $pay_data = array(
            'order_no' => $order['order_no'],
            'trade_no' => $order['order_no'], // 使用订单号作为trade_no
            'pay_type' => 'redirect',  // 标识为重定向支付
            'qrcode' => $payment_url,   // 支付页面URL
            'is_mobile' => $is_mobile,  // 是否为移动设备
            'device_type' => $device_type, // 设备类型
            'payment_method' => $is_mobile ? 'redirect' : 'newwindow' // 支付方式
        );

        E(0, '订单创建成功！', '', $pay_data);
    }
}

// 支付返回页面 - 对应URL: /my-payreturn.html
function payreturn()
{
    $this->_cfg['titles'] = '支付结果';
    $this->_var['topcid'] = -1;

    $order_no = R('out_trade_no');
    $trade_status = R('trade_status');

    $order = array();
    $status_text = '支付状态未知';
    $auto_close = false;

    if ($order_no) {
        $order = $this->sj_rainbow_pay_orders->get_by_order_no($order_no);
        if (!empty($order) && $order['uid'] == $this->_uid) {
            if ($order['status'] == 1) {
                $status_text = '支付成功';
                $auto_close = true;
            } elseif ($order['status'] == 0) {
                $status_text = '支付中，请稍候...';
            } else {
                $status_text = '支付失败';
            }
        }
    }

    // 如果是支付成功且trade_status为TRADE_SUCCESS，也标记为自动关闭
    if ($trade_status == 'TRADE_SUCCESS') {
        $status_text = '支付成功';
        $auto_close = true;
    }

    // 设置模板变量
    $this->assign('order', $order);
    $this->assign('status_text', $status_text);
    $this->assign('auto_close', $auto_close);
    $this->assign('cfg', $this->_cfg);
    $this->assign('_cfg', $this->_cfg);
    $this->assign('_user', $this->_user);
    $this->assign('my_url', $this->_my_url);
    $this->display('templet/my_rainbow_pay_return.htm');
}

// 充值记录 - 对应URL: /my-orders.html
function orders()
{
    $this->_cfg['titles'] = '充值记录';
    $this->_var['topcid'] = -1;
    
    $where = array('uid' => $this->_uid);

    // 初始分页
    $pagenum = 10;
    $total = $this->sj_rainbow_pay_orders->find_count($where);
    $maxpage = max(1, ceil($total / $pagenum));
    $page = min($maxpage, max(1, intval(R('page'))));
    $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('orders', 'my', true));

    $this->assign('pages', $pages);
    $this->assign('total', $total);

    $data = $this->sj_rainbow_pay_orders->list_arr($where, 'create_time', -1, ($page - 1) * $pagenum, $pagenum, $total);
    
    // 设置模板变量
    $this->assign('data', $data);
    $this->assign('cfg', $this->_cfg);
    $this->assign('_cfg', $this->_cfg);
    $this->assign('_user', $this->_user);
    $this->assign('my_url', $this->_my_url);
    $this->display('templet/my_rainbow_pay_orders.htm');
}

// 账户状态功能已集成到用户中心首页，此函数已移除

// 检查订单状态（Ajax） - 对应URL: /my-check.html
function check()
{
    try {
        $order_no = trim(R('order_no', 'P'));
        if (empty($order_no)) {
            E(1, '订单号不能为空！');
        }

        $order = $this->sj_rainbow_pay_orders->get_by_order_no($order_no);
        if (empty($order)) {
            E(1, '订单不存在！');
        }

        if ($order['uid'] != $this->_uid) {
            E(1, '无权限查看此订单！');
        }

        $status_text = array(
            0 => '未支付',
            1 => '已支付',
            2 => '已取消'
        );

        $status = intval($order['status']);
        $response_data = array(
            'status' => $status,
            'status_text' => isset($status_text[$status]) ? $status_text[$status] : '未知状态',
            'pay_time' => $order['pay_time'] ? date('Y-m-d H:i:s', $order['pay_time']) : '',
            'order_no' => $order['order_no'],
            'amount' => $order['amount'],
            'type' => $order['type']
        );

        // 返回数据格式与前端JavaScript期望一致
        E(0, '查询成功', '', array('data' => $response_data));

    } catch (Exception $e) {
        E(1, '查询失败：' . $e->getMessage());
    }
}

// 生成签名 - 按照彩虹易支付官方文档
function generate_sign($params, $key)
{
    // 1. 按参数名ASCII码从小到大排序，sign、sign_type、和空值不参与签名
    ksort($params);
    $string = '';
    foreach ($params as $k => $v) {
        if ($v !== '' && $v !== null && $k != 'sign' && $k != 'sign_type') {
            $string .= $k . '=' . $v . '&';
        }
    }
    $string = rtrim($string, '&');

    // 2. 拼接商户密钥并MD5加密（小写）
    return md5($string . $key);
}

// 获取客户端IP
function get_client_ip()
{
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ips[0]);
    } elseif (isset($_SERVER['HTTP_X_REAL_IP']) && !empty($_SERVER['HTTP_X_REAL_IP'])) {
        return $_SERVER['HTTP_X_REAL_IP'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }
    return '127.0.0.1';
}

// 获取设备类型 - 优化移动端支付体验
function get_device_type()
{
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

    if (strpos($user_agent, 'MicroMessenger') !== false) {
        return 'wechat'; // 微信内浏览器
    } elseif (strpos($user_agent, 'AlipayClient') !== false) {
        return 'alipay'; // 支付宝客户端
    } elseif (strpos($user_agent, 'QQ/') !== false) {
        return 'qq'; // 手机QQ内浏览器
    } elseif (strpos($user_agent, 'Mobile') !== false || strpos($user_agent, 'Android') !== false || strpos($user_agent, 'iPhone') !== false || strpos($user_agent, 'iPad') !== false) {
        return 'mobile'; // 手机/平板浏览器
    } else {
        return 'pc'; // 电脑浏览器
    }
}

// 检测是否为移动设备
function is_mobile_device()
{
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    return preg_match('/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $user_agent);
}

// 发送POST请求
function post_request($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'LECMS Rainbow Pay Plugin');

    $response = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        return false;
    }

    return $response;
}
}
