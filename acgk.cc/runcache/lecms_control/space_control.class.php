<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-11-29
 * Time: 9:05
 * Description:前台用户主页控制器
 */
defined('ROOT_PATH') or exit;

include RUNTIME_CONTROL.'base_control.class.php'; class space_control extends base_control{

    //用户主页详情
    public function index(){
        


        $_GET['uid'] = (int)R('uid');
        $this->_var = $this->user->get($_GET['uid']);
        empty($this->_var) && core::error404();

        $this->user->format($this->_var);
        empty($this->_var['author']) && $this->_var['author'] = $this->_var['username'];
        $this->_var['topcid'] = -1;
        $_show = $this->_var;

        


        // SEO 相关
        

        $this->_cfg['titles'] = $this->_var['author'].'-'.$this->_cfg['webname'];
		$page = (int)R('page','G');
        if( $page > 1 ){
            $this->_cfg['titles']  .= '-'.lang('page_current', array('page'=>$page));
        }
        $this->_cfg['seo_keywords'] = $this->_var['author'].','.$this->_cfg['webname'];
        $this->_cfg['seo_description'] = empty($this->_var['intro']) ? $this->_cfg['webname'] : $this->_var['intro'];
        


        $this->assign('cfg', $this->_cfg);
        $this->assign('cfg_var', $this->_var);

        $GLOBALS['run'] = &$this;
        $GLOBALS['_show'] = &$_show;
        $tpl = 'space.htm';

        

        $_ENV['_theme'] = &$this->_cfg['theme'];
        $this->display($tpl);
    }

    

}