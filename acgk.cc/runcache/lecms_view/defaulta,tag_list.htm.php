<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_list_flag($conf) { global $run; $flag = _int($conf, 'flag', 0); $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = _int($conf, 'mid', 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list_flag'); $cache_key = $life ? md5('list_flag'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($flag == 0){ return array('list'=> array()); } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array('flag' => $flag); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('flag' => $flag, 'cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('flag' => $flag, 'cid' => $cid); } } if($table == 'page'){ return array(); } $run->cms_content_flag->table = 'cms_'.$table.'_flag'; $key_arr = $run->cms_content_flag->list_arr($where, 'id', $orderway, $start, $limit, $limit, $extra); $keys = array(); foreach($key_arr as $v) { $keys[] = $v['id']; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_global_taglist($conf) { global $run, $tags, $mid, $table; $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_taglist'); $cache_key = $life ? md5('global_taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if(empty($tags)){ return false; } $tagid = $tags['tagid']; $total = $tags['count']; $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); $pages = paginator::$page_function($page, $maxpage, $run->cms_content->tag_url($mid, $tags, TRUE), $pageoffset); $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; $tag_arr = $run->cms_content_tag_data->list_arr($tagid, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); $keys = array(); foreach($tag_arr as $v) { $keys[] = $v['id']; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]) ? $views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
$gdata = block_global_taglist(array (
  'pagenum' => '12',
  'dateformat' => 'Y-m-d',
  'showcate' => '1',
  'showviews' => '1',
  'pageoffset' => '3',
));
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglist_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table.'_tag'; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total = $run->cms_content_tag->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.tagid FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(tagid) FROM {$table_full})) AS tagid) AS t2 WHERE t1.tagid >= t2.tagid LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['tagid'], $keys)){ $keys[] = $arr['tagid']; $i++; } } $list_arr = $run->cms_content_tag->mget($keys); }else{ $keys = array(); $sql = "SELECT tagid FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['tagid']; } $list_arr = $run->cms_content_tag->mget($keys); } $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
<meta http-equiv="Cache-Control" content="no-transform"/>
<meta http-equiv="Cache-Control" content="no-siteapp"/>
<meta name="applicable-device" content="pc,mobile">
<link rel="shortcut icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.ico" />
<link rel="icon" sizes="32x32" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png">
<link rel="Bookmark" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png" />
<title><?php echo(isset($cfg_var['name']) ? $cfg_var['name'] : ''); ?> - 飞雪ACG</title>
<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
<meta name="description" content="飞雪ACG免费二次元聚集地,欢迎分享！！" />
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-v2.css?1.0">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-bbs-v2.css?1.2">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/tag.css?1.0">
<?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?>
</head>

<body>

	<header class="acgb-fec5ef navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="acgb-e849bd container">
			<button class="acgb-3ed93f navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="acgb-98211e navbar-toggler-icon"></span>
			</button>

			<a class="acgb-901cd3 navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			<a class="acgb-57f644 navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="acgb-529e13 icon-search"></i></a></a>
			<div class="acgb-cac1f4 collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				<?php $data = block_navigate(array (
)); ?>
				<ul class="acgb-dc5293 navbar-nav mr-auto">
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="acgb-d4e0a6 nav-item home  active"<?php }else{ ?> class="nav-item home"<?php } ?>><a class="nav-link" href="/"><i class="acgb-0169a5 icon-home d-md-none"></i> 首页</a></li>	
					<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="nav-item  active"<?php }else{ ?> class="nav-item"<?php } ?>>
						<a class="acgb-4df27e nav-link" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><i class="acgb-b7603e icon-circle-o d-md-none"></i><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
					</li>
					<?php }} ?>
                  	<li class="acgb-980980 nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	<?php if ($_uid) { ?>
                        <a href="/my-index.html" class="acgb-88f4ee nav-link">个人中心</a>
                    <?php }else{ ?>
                        <a href="/user-login.html" class="acgb-88f4ee nav-link">登录/注册</a>
                    <?php } ?>
                  	</li>
				</ul><?php unset($data); ?>
				<!-- 右侧：用户 -->
				<ul class="acgb-a21420 navbar-nav">
					<li class="acgb-2d9907 nav-item"><a class="acgb-454400 nav-link" href="/search/"><i class="acgb-529e13 icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="acgb-e849bd container">
<div class="acgb-a2c59a row">
	<div class="acgb-7a1c9a col-lg-9 main">
<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
		<ol class="acgb-c1db89 breadcrumb d-none d-md-flex">
			<li class="acgb-8150fc breadcrumb-item"><a href="/"><i class="acgb-d0ec7d icon-home" aria-hidden="true"></i></a></li>
			<li class="acgb-ebe4a7 breadcrumb-item active"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
			<?php }} ?>
		</ol>
		<div class="acgb-772608 card card-threadlist ">
			<div class="acgb-abe027 card-header">
				<div class="acgb-b7facd nav nav-tabs card-header-tabs">
					<div class="acgb-2d9907 nav-item">
						<div class="acgb-e8c5cf nav-link active">“<?php echo(isset($tags['name']) ? $tags['name'] : ''); ?>”相关内容</div>
					</div>
				</div>
			</div>
			<div class="acgb-fd351d card-body">
				
				<ul class="acgb-86a005 list-unstyled threadlist mb-0">
				<?php $data = block_list_flag(array (
  'flag' => '1',
  'limit' => '5',
  'dateformat' => 'Y-m-d',
  'showviews' => '1',
  'intronum' => '120',
  'life' => '60',
)); ?>
<?php 
$curr_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
 ?>
<?php if ($curr_page == 1) { ?>
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
					<li class="acgb-d4c7db media thread tap top_3" data-href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>">
						<div class="acgb-b131e6 media-body">
							<div class="acgb-9f213a subject break-all">
								<i class="acgb-971201 icon-top-3"></i>
								<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"><span style="color:#f26c4f;"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></span></a>
							</div>
							<div class="acgb-f523d5 d-flex justify-content-between small mt-1">
								<div>								
									<span class="acgb-2ccd24 haya-post-info-username ">
									<a  style="color:#748594;" href="<?php echo(isset($cfg_var['url']) ? $cfg_var['url'] : ''); ?>" ><span class="acgb-c45245 board-bg" style="border-radius: 2px;background-color: #f1c84c;width: 0.64rem;height: 0.64rem;display: inline-block;margin-right: 5px;"></span><?php echo(isset($cfg_var['name']) ? $cfg_var['name'] : ''); ?></a>
									<span class="acgb-f18a94 koox-g"> • </span>
									<span class="acgb-ecd13b username text-grey mr-1"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
									<span class="acgb-28213e date text-grey"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
									</span>													 
								</div>
								<div class="acgb-67caf1 text-muted small">
									<span class="acgb-6eea17 eye comment-o ml-2 hidden-sm d-none"><i class="acgb-6f23e8 jan-icon-fire-1"></i><?php echo(isset($v['views']) ? $v['views'] : ''); ?></span>
									<span class="acgb-8ab4e8 likes comment-o ml-2"><i class="acgb-39fd7e icon icon-thumbs-o-up" aria-label="点赞"></i><?php echo(isset($v['likes']) ? $v['likes'] : ''); ?></span>
								</div>
							</div>
						</div>
					</li>
					<div class="acgb-cd7489 jan-hr"></div>
					<?php }} ?>
<?php } ?>
<?php unset($data); ?>

<?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
					<li class="acgb-279085 media thread tap" data-href="thread-39257.htm">
						<div class="acgb-b131e6 media-body">
							<div class="acgb-9f213a subject break-all">
							<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>										
							</div>
							<div class="acgb-f523d5 d-flex justify-content-between small mt-1">
								<div>								
									<span class="acgb-2ccd24 haya-post-info-username ">
									<a  style="color:#748594;" href="<?php echo(isset($cfg_var['url']) ? $cfg_var['url'] : ''); ?>" ><span class="acgb-c45245 board-bg" style="border-radius: 2px;background-color: #f1c84c;width: 0.64rem;height: 0.64rem;display: inline-block;margin-right: 5px;"></span><?php echo(isset($cfg_var['name']) ? $cfg_var['name'] : ''); ?></a>
									<span class="acgb-f18a94 koox-g"> • </span>
									<span class="acgb-ecd13b username text-grey mr-1"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
									<span class="acgb-28213e date text-grey"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
									</span>													 
								</div>
								<div class="acgb-67caf1 text-muted small">
									<span class="acgb-6eea17 eye comment-o ml-2 hidden-sm d-none"><i class="acgb-6f23e8 jan-icon-fire-1"></i><?php echo(isset($v['views']) ? $v['views'] : ''); ?></span>
									<span class="acgb-8ab4e8 likes comment-o ml-2"><i class="acgb-39fd7e icon icon-thumbs-o-up" aria-label="点赞"></i><?php echo(isset($v['likes']) ? $v['likes'] : ''); ?></span>
								</div>
							</div>
						</div>
					</li>
					<div class="acgb-cd7489 jan-hr"></div>
					<?php }} ?>
												
				</ul>
			</div>
		</div>
				
		<div class="acgb-14968a pagenav ajax-pag"><?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?></div>
<style>
.pagenav.ajax-pag {text-align:center;margin:5px 0px 5px;}
.pagenav.ajax-pag a {padding:15px;}
</style>
		
	</div>
	
	<div class="acgb-f7af45 col-lg-3 d-none d-lg-block aside">

<div class="acgb-038466 card card-forum-info">
			<div class="acgb-67234e card-body text-center">
				<img class="acgb-835fd7 logo-5 mb-2" src="<?php echo(isset($cfg_var['pic']) ? $cfg_var['pic'] : ''); ?>">
				<h1><?php echo(isset($cfg_var['name']) ? $cfg_var['name'] : ''); ?></h1>
				<div class="acgb-1eb49c text-left line-height-2"><?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?></div>
			</div>
			<div class="acgb-8bbe84 card-footer p-2">
			</div>
</div>

<!--<div class="acgb-58ec43 form-group">
  <form action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form" name="search" method="get">
      <div class="acgb-d52830 input-group">
        <input type="hidden" name="u" value="search-index" />
        <input type="hidden" name="mid" value="2" />
        <input type="text" class="acgb-b051c8 form-control" placeholder="关键词" name="keyword">
        <div class="acgb-885483 input-group-append">
          <button class="acgb-51afda btn btn-primary" type="submit">搜索</button>
        </div>
      </div>
  </form>
</div>-->

<!-- 主页右侧 -->
<div class="acgb-7a073b card">
		  <div class="acgb-abe027 card-header">热门资源</div>
			<div class="acgb-25494a card-body user-recent">
				<?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '8',
  'dateformat' => 'Y-m-d',
  'showviews' => '1',
  'titlenum' => '24',
  'life' => '120',
)); ?>
				<ul class="acgb-7b6b71 small break-all">
					<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
					<li class="acgb-e80888 line-height-2">
						<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
					</li>
					<?php }} ?>
				</ul><?php unset($data); ?>
			</div>
</div>

<div class="acgb-7a073b card">
	<div class="acgb-abe027 card-header">热门标签</div>
<div id="taghot">
	<?php $data = block_taglist_rand(array (
  'mid' => '2',
  'limit' => '20',
)); ?>
	<ul>
		<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
		<li><a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
		<?php }} ?> 				  
	</ul><?php unset($data); ?>
</div>
</div>	
		<?php $data = block_links(array (
)); ?>
		<?php if ($data) { ?>
		<div class="acgb-b642a8 card friendlink">
			<div class="acgb-abe027 card-header">友情链接</div>
			<div class="acgb-53e7fe card-body small">
				<ul>
					<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
					<li class="acgb-e1cb2b mb-1 small line-height-2">
						<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
					</li>
					<?php }} ?>
				</ul>
			</div>
		</div>
		<?php } ?>
		<?php unset($data); ?>
	</div>
</div>	
		</div>
	</main>

	<footer class="acgb-a9e8f9 text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="acgb-e849bd container">
		<div class="acgb-a2c59a row">
			<div class="acgb-735abb col">
			<?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?>
			</div>
			<!--<div class="acgb-c0f5b0 col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-3.1.0.js?1.0"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/bootstrap.js?1.0"></script>
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
<a id="scroll_to_top" href="javascript:void(0);"  class="acgb-982394 mui-rightlist"  title="返回顶部" style="display: none;"><i class="acgb-4dd66e icon-angle-double-up"></i></a>
</div>

<script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>