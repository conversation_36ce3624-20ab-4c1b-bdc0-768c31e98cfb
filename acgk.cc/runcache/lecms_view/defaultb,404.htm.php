<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_content_total_by_date($conf) { global $run; $mid = _int($conf, 'mid', 2); $type = isset($conf['type']) ? $conf['type'] : 'all'; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return 0; } $cache_key = $life ? md5('content_total_by_date'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return (int)$cache_data; } } $where = array(); $run->cms_content->table = 'cms_'.$table; $total = 0; switch($type) { case 'all': $where = array(); break; case 'today': $starttime = mktime(0,0,0,date('m'),date('d'),date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'yesterday': $starttime = mktime(0,0,0,date('m'),date('d')-1,date('Y')); $endtime = mktime(0,0,0,date('m'),date('d'),date('Y'))-1; $where = array('dateline'=>array('>'=>$starttime, '<='=>$endtime)); break; case 'week': $starttime = mktime(0, 0, 0, date('m'), (date('d') - (date('w')>0 ? date('w') : 7) + 1), date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'month': $starttime = mktime(0,0,0,date('m'),1,date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'year': $starttime = strtotime(date('Y',time())."-1"."-1"); $where = array('dateline'=>array('>'=>$starttime)); break; } if($where){ $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>快萌ACG - 南+社区</title>
<meta name="keyword" content="快萌ACG,南+社区,快萌论坛,游戏,百合,acg,和谐,同人,C94,韩漫,游戏,百合,萝莉,魔法少女,初音,东方,伪娘,末途,校园奴隶契约,驱灵师,小黄游,HS2,游戏合集包,漫画合集包,写真,套图">
<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>">
<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1"/>
<meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width">
<meta name="apple-mobile-web-app-title" content="">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="green">
<meta name="format-detection" content="telphone=no, email=no">
<meta name="HandheldFriendly" content="true">
<meta name="screen-orientation" content="portrait">
<meta name="x5-orientation" content="portrait">
<meta name="full-screen" content="yes">
<meta name="x5-fullscreen" content="true">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="renderer" content="webkit">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/font_3913661_iikaqjykdll.js"></script>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font_3913661_iikaqjykdll.css" rel="stylesheet">
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font-awesome.min.css" rel="stylesheet">
<style type="text/css">
.iconfont {
    font-size: inherit;
}
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
.th_padding, .left_padding {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 992px) {
  .col-md-12 {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  @media only screen and (min-width: 320px) and (max-width: 992px) {
    .th_padding {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  .pagebar {
    text-align: center;
    margin-top: 15px;
  }
 }
.pagebar {
  text-align: center;
  margin-top: 15px;
}
  
  .pagebar a, .pagebar b {
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 16px;
  margin: 0px 5px;
  border: 1px solid #f0f0f5;
  color: #666;
  padding: 0px 8px;
  border-radius: 2px;
  box-sizing: border-box;
}
  .pagebar b {
  background: #7cbeff;
  border: 1px solid #7cbeff;
  color: #fff;
  font-weight: normal;
}
</style>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/style.css?2024" rel="stylesheet">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/jquery.min.js"></script>
<div class="ui progress" style="position: fixed; top: 0; left: 0; z-index: 999; width: 100vw;">
  <div id="page-reading-percent" class="percent" style="width: 0;"></div>
</div>
<script>
    $(function (){
         $(document).scroll(function (){
             let height = $(this).height() - $(window).height();
             let top = $(this).scrollTop();
             let percent = top / height * 100;
             $("#page-reading-percent").width(percent.toFixed(2) + "%");
        });
    });
     function scrollToTop(){
        $("html, body").animate({ scrollTop:0 }, 500);
    }
     function showModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("show");
        setTimeout(() => {
            modalPageDiv.addClass("open");
        }, 50);
    }
     function hideModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("close");
        setTimeout(() => {
            modalPageDiv.removeClass("show");
            modalPageDiv.removeClass("open");
            modalPageDiv.removeClass("close");
        }, 500);
    }
</script>
<div class="ui modal-page right" id="modal-page"> 
   <div class="modal-background" onclick="hideModal()"></div>
   <div class="modal-container padding">
    <div class="ui flex-item padding">
      <div class="center"></div>
      <div class="end">
        <div class="ui button circle outline" onclick="hideModal()"><i class="iconfont icon-close"></i></div>
      </div>
    </div>
     <div class="panel-block">
      <div class="title">导航菜单</div>
      <div class="ui tree linear-split" style="padding: 20px 0;"> <?php $data = block_navigate(array (
)); ?>
        <ul>
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold"><a href="/" class="padding-half">首页</a></div>
            </div>
          </li>
          <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="padding-half"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></div>
            </div>
          </li>
          <?php }} ?>
          <li>
            <div class="ui flex-item item">
              <div class="start justify-center">
                <div class="ui button clear waiting tiny circle"><i class="fa fa-"></i></div>
              </div>
              <div class="center justify-center text-bold">
               <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="padding-half">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="padding-half">登录/注册</a>
                <?php } ?></div>
            </div>
          </li>
        </ul>
        <?php unset($data); ?> </div>
    </div>
  </div>
</div>
<header>
  <div class="container auto-margin ui flex-item"> <a class="start web-logo" href="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); ?>"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"></a> <a class="start web-name padding-left" href="/">快萌ACG</a>
<div class="search center padding-left justify-center">
      <div class="ui input radius">
        <input type="text" placeholder="前往acgk.cc搜索,本站待修复">
        <div class="">
          <div class="ui button clear" ><i class="iconfont icon-sousuo"></i></div>
        </div>
      </div>
    </div>
    <?php $data = block_navigate(array (
)); ?>
    <div class="end justify-center">
      <nav> <a class="item" href="/">首页</a> <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> <a class="item" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?> 
      <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="item">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="item">登录/注册</a>
      <?php } ?>
      </nav>
    </div>
    <?php unset($data); ?>
    <div class="end ext-menu justify-center">
      <div class="ui button circle" onclick="showModal()"><i class="fa fa-list"></i></div>
    </div>
  </div>
</header>

<style>
	.error .clip .shadow {height:180px;}
	.error .clip:nth-of-type(2) .shadow {width:130px;}
	.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:250px;}
	.error .digit {width:150px;height:150px;line-height:150px;font-size:120px;font-weight:bold;}
	.error h2 {font-size:32px;}
	.error .msg {top:-190px;left:30%;width:80px;height:80px;line-height:80px;font-size:32px;}
	.error span.triangle {top:70%;right:0%;border-left:20px solid #535353;border-top:15px solid transparent;border-bottom:15px solid transparent;}
	.error .container-error-404 {top: 50%;margin-top: 250px;position:relative;height:250px;padding-top:40px;}
	.error .container-error-404 .clip {display:inline-block;transform:skew(-45deg);}
	.error .clip .shadow {overflow:hidden;}
	.error .clip:nth-of-type(2) .shadow {overflow:hidden;position:relative;box-shadow:inset 20px 0px 20px -15px rgba(150,150,150,0.8),20px 0px 20px -15px rgba(150,150,150,0.8);}
	.error .clip:nth-of-type(3) .shadow:after,.error .clip:nth-of-type(1) .shadow:after {content:"";position:absolute;right:-8px;bottom:0px;z-index:9999;height:100%;width:10px;background:linear-gradient(90deg,transparent,rgba(173,173,173,0.8),transparent);border-radius:50%;}
	.error .clip:nth-of-type(3) .shadow:after {left:-8px;}
	.error .digit {position:relative;top:8%;color:white;background:#1E9FFF;border-radius:50%;display:inline-block;transform:skew(45deg);}
	.error .clip:nth-of-type(2) .digit {left:-10%;}
	.error .clip:nth-of-type(1) .digit {right:-20%;}
	.error .clip:nth-of-type(3) .digit {left:-20%;}
	.error h2 {font-size:24px;color:#A2A2A2;font-weight:bold;padding-bottom:20px;}
	.error .tohome {font-size:16px;color:#07B3F9;}
	.error .msg {position:relative;z-index:9999;display:block;background:#535353;color:#A2A2A2;border-radius:50%;font-style:italic;}
	.error .triangle {position:absolute;z-index:999;transform:rotate(45deg);content:"";width:0;height:0;}
	@media(max-width:767px) {.error .clip .shadow {height:100px;}
		.error .clip:nth-of-type(2) .shadow {width:80px;}
		.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:100px;}
		.error .digit {width:80px;height:80px;line-height:80px;font-size:52px;}
		.error h2 {font-size:18px;}
		.error .msg {top:-110px;left:15%;width:40px;height:40px;line-height:40px;font-size:18px;}
		.error span.triangle {top:70%;right:-3%;border-left:10px solid #535353;border-top:8px solid transparent;border-bottom:8px solid transparent;}
		.error .container-error-404 {height:150px;}
	}
</style>
<main class="main">
	<div class="error">
		<div class="container-floud">
			<div style="text-align: center">
				<div class="container-error-404">
					<div class="clip">
						<div class="shadow">
							<span class="digit thirdDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit secondDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit firstDigit"></span>
						</div>
					</div>
					<div class="msg">OH!
						<span class="triangle"></span>
					</div>
				</div>
				<h2 class="h1">很抱歉，你访问的页面找不到了</h2>
              	<?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'today',
)); ?><div class="today-posts" style="background-color: #fff;color: #343a40;border-radius: 5px;text-align: center;font-size: 1.9rem;transition: background-color 0.3s;font-weight: 900;">今日发布：<m style="font-size: 2.8rem;color: red;"> <?php echo(isset($data) ? $data : ''); ?> </m>篇</div><?php unset($data); ?>
			</div>
		</div>
	</div>
</main>

<footer>
  <div class="container auto-margin ui flex-item">
    <div class="center">
      <?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?> <a rel="nofollow" href="https://beian.miit.gov.cn"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a> <?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?></div>
  </div>
</footer>
<!-- 随航组件 -->
<div class="fixed-menu">
  <div class="item"> 
    <!-- 此处的二维码为自动生成 -->
    <div class="hover-display url-qrcode" id="pageUrlQrcode"></div>
    <i class="fa fa-qrcode"></i> </div>
  <div class="item">
    <div class="hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="QQ"> </div>
    <i class="fa fa-qq"></i> </div>
  <div class="item">
    <div class="hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="微信"> </div>
    <i class="fa fa-weixin"></i> </div>
  <div class="item" onclick="scrollToTop()"><i class="fa fa-arrow-up"></i></div>
</div>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/qrcode.js"></script> 
<script>
    const qrcode = new QRCode("pageUrlQrcode", {
        text: window.location.href,
        width: 150,
        height: 150,
        correctLevel : QRCode.CorrectLevel.H
    });
</script>
<footer style="background-color: #f1f1f1;padding: 1px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body></html>
<script type="text/javascript">
	function randomNum() {
		return Math.floor(Math.random() * 9) + 1;
	}

	var loop1, loop2, loop3, time = 30, i = 0, number;
	loop3 = setInterval(function () {
		if (i > 40) {
			clearInterval(loop3);
			document.querySelector('.thirdDigit').textContent = 4;
		} else {
			document.querySelector('.thirdDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop2 = setInterval(function () {
		if (i > 80) {
			clearInterval(loop2);
			document.querySelector('.secondDigit').textContent = 0;
		} else {
			document.querySelector('.secondDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop1 = setInterval(function () {
		if (i > 100) {
			clearInterval(loop1);
			document.querySelector('.firstDigit').textContent = 4;
		} else {
			document.querySelector('.firstDigit').textContent = randomNum();
			i++;
		}
	}, time);
</script>
<script type="text/javascript">
	function randomNum() {
		return Math.floor(Math.random() * 9) + 1;
	}

	var loop1, loop2, loop3, time = 30, i = 0, number;
	loop3 = setInterval(function () {
		if (i > 40) {
			clearInterval(loop3);
			document.querySelector('.thirdDigit').textContent = 4;
		} else {
			document.querySelector('.thirdDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop2 = setInterval(function () {
		if (i > 80) {
			clearInterval(loop2);
			document.querySelector('.secondDigit').textContent = 0;
		} else {
			document.querySelector('.secondDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop1 = setInterval(function () {
		if (i > 100) {
			clearInterval(loop1);
			document.querySelector('.firstDigit').textContent = 4;
		} else {
			document.querySelector('.firstDigit').textContent = randomNum();
			i++;
		}
	}, time);

	// 新增倒计时功能
	var countdown = 3; // 倒计时3秒
	var countdownElement = document.createElement('div');
	countdownElement.style.fontSize = '24px';
	countdownElement.style.textAlign = 'center';
	countdownElement.style.marginTop = '20px';
	document.querySelector('.error').appendChild(countdownElement);

	var countdownInterval = setInterval(function() {
		countdownElement.textContent = "将在 " + countdown + " 秒后返回首页...";
		countdown--;
		if (countdown < 0) {
			clearInterval(countdownInterval);
			window.location.href = '/'; // 返回首页
		}
	}, 1000);
</script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>
