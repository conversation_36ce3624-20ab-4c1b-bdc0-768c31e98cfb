<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_content_total_by_date($conf) { global $run; $mid = _int($conf, 'mid', 2); $type = isset($conf['type']) ? $conf['type'] : 'all'; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return 0; } $cache_key = $life ? md5('content_total_by_date'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return (int)$cache_data; } } $where = array(); $run->cms_content->table = 'cms_'.$table; $total = 0; switch($type) { case 'all': $where = array(); break; case 'today': $starttime = mktime(0,0,0,date('m'),date('d'),date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'yesterday': $starttime = mktime(0,0,0,date('m'),date('d')-1,date('Y')); $endtime = mktime(0,0,0,date('m'),date('d'),date('Y'))-1; $where = array('dateline'=>array('>'=>$starttime, '<='=>$endtime)); break; case 'week': $starttime = mktime(0, 0, 0, date('m'), (date('d') - (date('w')>0 ? date('w') : 7) + 1), date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'month': $starttime = mktime(0,0,0,date('m'),1,date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'year': $starttime = strtotime(date('Y',time())."-1"."-1"); $where = array('dateline'=>array('>'=>$starttime)); break; } if($where){ $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_data_total($conf) { global $run; $mid = _int($conf, 'mid', 2); $source = empty($conf['source']) ? '' : $conf['source']; $showviews = _int($conf, 'showviews', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $allow_source = array('content','comment','tag','views','category'); if($source && !in_array($source, $allow_source)){ return array(); } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return array(); } $cache_key = $life ? md5('data_total'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $total = array(); switch ($source){ case 'content': $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); break; case 'comment': $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); break; case 'tag': $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); break; case 'views': $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } break; case 'category': $total['category'] = $run->category->find_count(array('mid'=>$mid)); break; default: $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); $total['category'] = $run->category->find_count(array('mid'=>$mid)); if($showviews){ $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_list_flag($conf) { global $run; $flag = _int($conf, 'flag', 0); $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = _int($conf, 'mid', 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list_flag'); $cache_key = $life ? md5('list_flag'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($flag == 0){ return array('list'=> array()); } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array('flag' => $flag); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('flag' => $flag, 'cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('flag' => $flag, 'cid' => $cid); } } if($table == 'page'){ return array(); } $run->cms_content_flag->table = 'cms_'.$table.'_flag'; $key_arr = $run->cms_content_flag->list_arr($where, 'id', $orderway, $start, $limit, $limit, $extra); $keys = array(); foreach($key_arr as $v) { $keys[] = $v['id']; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $cids = empty($conf['cids']) ? '' : $conf['cids']; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list'); $cache_key = $life ? md5('list'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if ($cids){ $cid_arr = explode(',', $cids); $where = array('cid' => array("IN" => $cid_arr)); $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; }else{ if($cid == 0) { $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); if(empty($cate_arr)) return; $cate_name = $cate_arr['name']; $cate_url = $run->category->category_url($cate_arr); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('cate_name'=> $cate_name, 'cate_url'=> $cate_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<html lang="zh-CN">
 <head> 
  <meta charset="UTF-8" /> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <meta name="viewport" content="width=device-width, initial-scale=1" /> 
  <link href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>favicon.ico" rel="icon" /> 
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
   
   
  <?php $control = isset($_GET['control'])?strtolower($_GET['control']):'';$action = isset($_GET['action'])?strtolower($_GET['action']):''; ?>
  <?php if ($control=='show' && $action == 'index') { ?>
  <meta property="og:type" content="acticle" />
  <meta property="og:image" content="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>" />
  <meta property="og:author" content="<?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?>" />
  <meta property="og:site_name" content="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" />
  <meta property="og:title" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>" />
  <meta property="og:keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
  <meta property="og:description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
  <meta property="og:url" content="<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>" />
  <meta property="og:release_date" content="<?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?>" />	
  <?php } ?>  
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/sweetalert2.min.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>font-awesome/css/font-awesome.min.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/external.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/default.css" media="all" /> 
  <?php if ($control=='show' && $action == 'index') { ?>
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/jquery.fancybox.min.css" type="text/css" media="all" />
  <?php } ?>
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-2.2.4.min.js"></script> 
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/sweetalert2.all.min.js"></script> 
  <?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/readmore.min.js"></script>
  <?php } ?>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/login.js"></script>
  <style>.nice-color,a:hover{color:#4a86e8}.site-header,.home-filter--content{background-color:#4a86e8}.button,input[type="submit"],button[type="submit"],.navigation .nav-previous a,.navigation .nav-next a{background-color:#4a86e8}.owl .owl-prev,.owl .owl-next,.term-bar{background-color:#4a86e8}.on{color:#4a86e8}.filter--content .filter-item a.on i{background:#4a86e895}.off-canvas .logo{background:#fff}<?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>.entry-media .placeholder{padding-bottom:70%!important;}<?php } ?>.navbar li ul{min-width:190px;}.lanse{text-indent:0em !important;font-size:16px;font-weight:normal;color:#FFF;margin:10px 0;padding:5px 10px;background-color:#ed2d38;display:none;}.lanseseo{text-indent:0em !important;font-size:16px;font-weight:normal;color:#FFF;margin:10px 0;padding:5px 10px;background-color:#ed2d38;display:inline-block;}.article-content .post-album li img{width:auto!important;height:auto!important;}.navbar li ul{min-width:100% !important;}.u-text-format h2{font-size:20px!important;border-left:5px solid #4a86e8!important;padding-left:15px!important;font-weight:bold!important;}p{margin:0 0 30px;}</style> 
 </head>
 <?php if ($control=='index' && $action == 'index') { ?>
 <body class="gtlol-525381 index home blog modular-title-2 paged-next"> 
 <?php }elseif($control=='cate' && $action == 'index') { ?>
 <body class="gtlol-dd0427 category paged-next ">
 <?php }elseif($control=='show' && $action == 'index') { ?>
 <body class="gtlol-dc6fdc article single single-post sidebar-right ">
 <?php }elseif($control=='tag' && $action == 'index') { ?>
 <body class="gtlol-cab977 tag paged-next ">
 <?php }elseif($control=='search' && $action == 'index') { ?>
 <body class="gtlol-7efff4 search archive searchplus paged-next "> 
 <?php }else{ ?>
 <body class="gtlol-2b7b9b page page sidebar-right " style="transform: none;">
 <?php } ?>
  <div class="gtlol-70d612 site"> 
    <header class="gtlol-2d944f site-header">
    <div class="gtlol-e849bd container"> 
     <div class="gtlol-d4dab2 navbar"> 
      <div class="gtlol-cfa30a logo-wrapper"> 
       <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"> <img class="logo regular tap-logo"  alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> </a> 
      </div> 
      <div class="gtlol-ee9753 sep"></div> 
      <nav class="gtlol-cb627e main-menu hidden-xs hidden-sm hidden-md"> 
       <ul id="menu-menu-1" class="gtlol-223b2f nav-list u-plain-list"> 
        <nav class="gtlol-cb627e main-menu hidden-xs hidden-sm hidden-md"> 
         <ul id="menu-menu-1" class="gtlol-223b2f nav-list u-plain-list"> 
		  <?php $data = block_navigate(array (
)); ?> 
          <li> <a href="/">首页</a></li> 
          <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> 
          <li class="gtlol-f56ab3 <?php if (isset($v['son'])) { ?>menu-item-has-children<?php } ?> <?php if ($cfg_var['topcid'] == $v['cid']) { ?> current-menu-item<?php } ?>"> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
		   <?php if (isset($v['son'])) { ?>
           <ul> 
            <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li> <a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?>
           </ul> 
		   <?php } ?> 
		  </li> 
		  <?php }} ?>
          <?php unset($data); ?>
         </ul>
        </nav> 
       </ul> 
      </nav> 
      <div class="gtlol-3913c5 actions">  
       <div class="gtlol-d4764e login-btn navbar-button main_nav"><!--main_nav弹窗登录注册-->
        <?php if (empty($_uid)) { ?><a href="/user-login.html" title="会员登录"><i class="gtlol-3ad843 mdi mdi-account"></i> 登录/注册</a><?php }else{ ?><a href="/my-index.html" title="用户中心"><i class="gtlol-3ad843 mdi mdi-account"></i> 用户中心</a><?php } ?>
       </div> 
	    <!--点击弹窗--><div class="gtlol-277bd8 note-open navbar-button"><i class="gtlol-c26a81 fa fa-bell-o"></i></div>
       <div class="gtlol-0f7766 burger"></div> 
      </div> 
     </div> 
    </div> 
   </header> 
   <div class="gtlol-9777aa header-gap"></div> 
   <div class="gtlol-ebdf6d site-content"> 
    <div class="gtlol-2748c8 content-area"> 
     <main class="gtlol-6c82a9 site-main"> 
      <div class="gtlol-254a1d section pt-0 pb-0"> 
       <div class="gtlol-a2c59a row"> 
        <div class="gtlol-af60f8 home-filter--content lazyload" data-bg="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/shape.png"> 
         <div class="gtlol-e849bd container"> 
          <h3 class="gtlol-2b50f0 focusbox-title">哥特动漫王国-哥特萝莉社</h3> 
          <p class="gtlol-7bea6f focusbox-desc">哥特动漫王国又叫哥特萝莉社是一个温馨的资源交流平台，欢迎各位萌新大佬入住</p> 
          <form id="search_form" class="gtlol-1353f8 mb-0" autocomplete="off" method="get"  target="_blank"> 
           <div class="gtlol-fc4248 form-box search-properties"> 
            <div class="gtlol-a2c59a row"> 
             <div class="gtlol-0a8932 col-xs-12 col-sm-6 col-md-9"> 
              <div class="gtlol-315f11 form-group mb-0"> 
			   <input type="hidden" name="u" value="search-index" /> 
			   <input type="hidden" name="mid" value="2" />
               <input type="text" class="home_search_input" name="keyword" placeholder="输入关键词搜索..." /> 
              </div> 
             </div> 
             <div class="gtlol-fdcd20 col-xs-12 col-sm-6 col-md-3"> 
              <input type="submit" value="搜索" class="btn btn--block" /> 
             </div> 
            </div> 
            <div class="gtlol-45dd4d home-search-results"></div> 
           </div> 
          </form> 
         </div> 
         <div class="gtlol-dc7f9b containercount"> 
           <div class="gtlol-8d597f jizhisucai-tj"> 
            <span class="gtlol-65c118 type_icont_1"><i class="gtlol-c26a81 fa fa-bell-o"></i> 平台统计 </span> 
            <ul>  
             <?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'today',
)); ?><li><span>今日发布：</span><em class="gtlol-651873 jizhisucai-sz"><?php echo(isset($data) ? $data : ''); ?></em></li><?php unset($data); ?>
			 <?php $data = block_data_total(array (
  'mid' => '2',
  'showviews' => '1',
)); ?>
             <li><span>话题总数：</span><em class="gtlol-651873 jizhisucai-sz"><?php echo(isset($data['tag']) ? $data['tag'] : ''); ?></em></li>			 
             <li><span>累计访问：</span><em class="gtlol-651873 jizhisucai-sz"><?php echo(isset($data['views']) ? $data['views'] : ''); ?></em></li> 
             <li><span>资源总数：</span><em class="gtlol-651873 jizhisucai-sz"><?php echo(isset($data['content']) ? $data['content'] : ''); ?></em></li> 
			 <?php unset($data); ?>
            </ul>
           </div>
         </div>  
        </div> 
       </div> 
      </div> 
	  <?php if (is_mobile()) { ?>
      <div class="gtlol-5559a7 section bgcolor-fff"> 
       <div class="gtlol-e849bd container"> 
        <div class="gtlol-9575e7 module category-boxes owl owl-loaded owl-drag"> 
        <div class="gtlol-93daee owl-stage" style="transform: translate3d(0px, 0px, 0px); transition: all 0s ease 0s; width: 1200px;">	   
        <div class="gtlol-a2c59a row"> 
          <div class="gtlol-cde63c col-lg-12"> 
          <div class="gtlol-2748c8 content-area">
           <main class="gtlol-6c82a9 site-main">  
            <div class="gtlol-94d39d row posts-wrapper"> 
             <div class="gtlol-062a5d owl-item active" style="width: 390px; margin-right: 30px;">
              <div class="gtlol-cf470c category-box"> 
               <div class="gtlol-18ae86 entry-thumbnails"> 
                <div class="gtlol-d60d64 big thumbnail"> 
                 <small>推荐</small> 
                 <img class=" lazyloaded" data-src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/svip.png" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/svip.png" /> 
                </div> 
              </div> 
               <a class="gtlol-de51c4 u-permalink" href="/" title="哥特动漫王国"></a> 
              </div>
             </div>			 
            </div> 
            </main> 
          </div> 
          </div> 
          </div> 
         </div> 
        </div> 		
       </div> 
      </div> 	  
	  <?php }else{ ?>
      <div class="gtlol-5559a7 section bgcolor-fff"> 
       <div class="gtlol-e849bd container"> 
        <div class="gtlol-9575e7 module category-boxes owl owl-loaded owl-drag"> 
        <div class="gtlol-93daee owl-stage" style="transform: translate3d(0px, 0px, 0px); transition: all 0s ease 0s; width: 1200px;">	   
        <div class="gtlol-a2c59a row"> 
          <div class="gtlol-cde63c col-lg-12"> 
          <div class="gtlol-2748c8 content-area">
           <?php $data = block_list_flag(array (
  'flag' => '1',
  'limit' => '4',
  'showcate' => '1',
)); ?>
           <main class="gtlol-6c82a9 site-main">  
            <div class="gtlol-94d39d row posts-wrapper"> 
			 <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
             <div class="gtlol-cee591 owl-item active" style="width: 270px; margin-right: 30px;">
              <div class="gtlol-cf470c category-box"> 
               <div class="gtlol-18ae86 entry-thumbnails"> 
                <div class="gtlol-d60d64 big thumbnail"> 
                 <h3 class="gtlol-56fa66 entry-title post"><?php echo(isset($v['indexflagtitle']) ? $v['indexflagtitle'] : ''); ?></h3> 
                 <small><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></small> 
                 <img class="gtlol-0945be  lazyloaded" data-src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>"  src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                </div> 
              </div> 
               <a class="gtlol-d87841 u-permalink" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"></a> 
              </div>
             </div>			 
			 <?php }} ?>
            </div> 
            </main> 
		   <?php unset($data); ?>
          </div> 
          </div> 
          </div> 
         </div> 
        </div> 		
       </div> 
      </div> 
	  <?php } ?> 
      <div class="gtlol-f4b0e4 section"> 
       <div class="gtlol-e849bd container"> 
	    <!--广告-->
        <div class="gtlol-a574e6 site-ads ads-list-header">
          <div class="gtlol-858aac wvshow wrap listbtshow"> 
           <div class="gtlol-949854 sjwu"> 
            <div style="color:#4B0082; border:#D3312D 2px dotted; padding:1px; margin-top:1px; cursor:pointer;"> 
             <marquee behavior="alternate" onmouseover="this.stop()" onmouseout="this.start()">
              <a href="/vip/"> 
			  <b id="nr">哥特动漫王国<span style="color:#fd1717;">又叫哥特萝莉社是</span><span style="color:#003399;">一个温馨的资源交流平台。</span><span style="color:#FF9900;">欢迎各位萌新大佬入住，</span><span style="color:#006600;">全站所有资源任意下载。</span>
			  </b>
			  </a>
             </marquee>
            </div>
           </div> 
          </div>
        </div> 	   
	    <!--广告-->
        <div class="gtlol-a2c59a row"> 
         <div class="gtlol-cde63c col-lg-12"> 
          <div class="gtlol-2748c8 content-area">
           <?php $data = block_list(array (
  'cid' => '0',
  'mid' => '2',
  'limit' => '12',
  'dateformat' => 'm-d',
  'intronum' => '160',
  'showcate' => '1',
  'showviews' => '1',
)); ?>
           <main class="gtlol-6c82a9 site-main"> 
            <div class="gtlol-6b1159 section-title"> 
             <h1>最近更新</h1> 
             <p>是一个有意思的ACG动漫综合资源网</p> 
            </div> 
            <div class="gtlol-94d39d row posts-wrapper"> 
			 <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
             <div class="gtlol-8832c3 col-6 col-sm-6 col-md-4 col-lg-3"> 
              <article class="gtlol-7fee8d post post-grid type-post hentry hasintro" data-aos="zoom-in"> 
               <div class="gtlol-65f3f7 entry-media"> 
                <div class="gtlol-62e057 placeholder" style="padding-bottom: 66.666666666667%;"> 
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img class="gtlol-c205d0  lazyloaded" data-src="<?php echo(isset($src) ? $src : ''); ?>" src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?>  </a> 
                </div> 
			   <?php if ($v['payprice']>0) { ?><div class="gtlol-f21954 entry-tipsindex">￥<?php echo(isset($v['payprice']) ? $v['payprice'] : ''); ?></div><?php }else{ } ?> 
			   <?php if ($v['cid']==1) { ?><div class="gtlol-f21954 entry-tipsindex">免费分享</div><?php } ?>
               </div> 
               <div class="gtlol-8054f6 entry-wrapper"> 
                <header class="gtlol-009a7a entry-header"> 
                 <h2 class="gtlol-9bc1a8 entry-title"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" rel="bookmark"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2> 
                 <div class="gtlol-b6501c entry-meta"> 
                  <?php if (isset($v['tag_arr'])) { ?><span class="gtlol-d18513 meta-category"> <?php if(isset($v['tag_arr']) && is_array($v['tag_arr'])) { foreach($v['tag_arr'] as &$v2) { ?><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="_blank" rel="category"><i class="gtlol-355d1b dot"></i><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a><?php }} ?></span><?php } ?> 
                 </div> 
                </header> 
                <div class="gtlol-e05834 entry-excerpt u-text-format"><?php echo(isset($v['intro']) ? $v['intro'] : ''); ?></div> 
                <div class="gtlol-0013fc entry-footer"> 
				 <a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><span class="gtlol-d3894f price"><i class="gtlol-52cf09 fa fa-user-circle-o"></i> <?php echo(isset($v['author']) ? $v['author'] : ''); ?></span></a>
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><time datetime="2021-08-01"><i class="gtlol-3b90a0 fa fa-clock-o"></i> <?php echo(isset($v['date']) ? $v['date'] : ''); ?></time></a> 
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><span><i class="gtlol-3a691c fa fa-eye"></i> <?php echo(isset($v['views']) ? $v['views'] : ''); ?></span></a> 
                </div> 
               </div> 
              </article> 
             </div> 
			 <?php }} ?>
            </div> 
           </main> 
		   <?php unset($data); ?>
          </div> 
         </div> 
        </div> 
       </div> 
      </div> 
	  <?php $data = block_list(array (
  'cid' => '1',
  'mid' => '2',
  'limit' => '12',
  'dateformat' => 'm-d',
  'intronum' => '160',
  'showcate' => '1',
  'showviews' => '1',
)); ?>
      <div class="gtlol-f4b0e4 section"> 
       <div class="gtlol-e849bd container"> 
	    <!--广告2-->
  
	    <!--广告2-->		   
        <div class="gtlol-6b1159 section-title"> 
         <h1><a href="<?php echo(isset($data['cate_url']) ? $data['cate_url'] : ''); ?>" title="<?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?>" target="_blank"><?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?></a></h1> 
         <p><?php echo(isset($data['cate_intro']) ? $data['cate_intro'] : ''); ?></p> 
        </div> 
        <div class="gtlol-1f7f21 row cat-posts-wrapper"> 
             <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
             <div class="gtlol-8832c3 col-6 col-sm-6 col-md-4 col-lg-3"> 
              <article class="gtlol-7fee8d post post-grid type-post hentry hasintro" data-aos="zoom-in"> 
               <div class="gtlol-65f3f7 entry-media"> 
                <div class="gtlol-62e057 placeholder" style="padding-bottom: 66.666666666667%;"> 
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img class="gtlol-c205d0  lazyloaded" data-src="<?php echo(isset($src) ? $src : ''); ?>" src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?>  </a> 
                </div> 
				<div class="gtlol-f21954 entry-tipsindex">免费分享</div>
               </div> 
               <div class="gtlol-8054f6 entry-wrapper"> 
                <header class="gtlol-009a7a entry-header"> 
                 <h2 class="gtlol-9bc1a8 entry-title"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" rel="bookmark"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2> 
                 <div class="gtlol-b6501c entry-meta"> 
                  <?php if (isset($v['tag_arr'])) { ?><span class="gtlol-d18513 meta-category"> <?php if(isset($v['tag_arr']) && is_array($v['tag_arr'])) { foreach($v['tag_arr'] as &$v2) { ?><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="_blank" rel="category"><i class="gtlol-355d1b dot"></i><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a><?php }} ?></span><?php } ?> 
                 </div> 
                </header> 
                <div class="gtlol-e05834 entry-excerpt u-text-format"><?php echo(isset($v['intro']) ? $v['intro'] : ''); ?></div> 
                <div class="gtlol-0013fc entry-footer"> 
				 <a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><span class="gtlol-d3894f price"><i class="gtlol-52cf09 fa fa-user-circle-o"></i> <?php echo(isset($v['author']) ? $v['author'] : ''); ?></span></a>
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><time datetime="2021-08-01"><i class="gtlol-3b90a0 fa fa-clock-o"></i> <?php echo(isset($v['date']) ? $v['date'] : ''); ?></time></a> 
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><span><i class="gtlol-3a691c fa fa-eye"></i> <?php echo(isset($v['views']) ? $v['views'] : ''); ?></span></a> 
                </div> 
               </div> 
              </article> 
             </div>  
		     <?php }} ?> 
        </div> 
        <div class="gtlol-b122f5 infinite-scroll-action"> 
         <div class="gtlol-62c98c infinite-scroll-button button link">
          <a href="<?php echo(isset($data['cate_url']) ? $data['cate_url'] : ''); ?>" title="<?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?>" target="_blank">查看更多<?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?></a>
         </div> 
        </div> 
       </div> 
      </div> 
	  <?php unset($data); ?>
	  <?php $data = block_list(array (
  'cids' => '2',
  'mid' => '2',
  'limit' => '12',
  'dateformat' => 'm-d',
  'intronum' => '160',
  'showcate' => '1',
  'showviews' => '1',
)); ?>
      <div class="gtlol-f4b0e4 section"> 
       <div class="gtlol-e849bd container"> 
        <div class="gtlol-6b1159 section-title"> 
         <h1><a href="<?php echo(isset($data['cate_url']) ? $data['cate_url'] : ''); ?>" title="<?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?>" target="_blank">动漫</a></h1> 
         <p>次元猫,二次元 ,二次元美女图 ,二次元头像 , 二次元美女</p> 
        </div> 
        <div class="gtlol-1f7f21 row cat-posts-wrapper"> 
            <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
             <div class="gtlol-8832c3 col-6 col-sm-6 col-md-4 col-lg-3"> 
              <article class="gtlol-7fee8d post post-grid type-post hentry hasintro" data-aos="zoom-in"> 
               <div class="gtlol-65f3f7 entry-media"> 
                <div class="gtlol-62e057 placeholder" style="padding-bottom: 66.666666666667%;"> 
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img class="gtlol-c205d0  lazyloaded" data-src="<?php echo(isset($src) ? $src : ''); ?>" src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?>  </a> 
                </div> 
				<?php if ($v['payprice']>190) { ?><div class="gtlol-9731d7 entry-tipss">免费分享</div><?php }elseif($v['payprice']>90) { ?><div class="gtlol-f9af97 entry-tipssm">免费分享</div><?php }else{ ?><div class="gtlol-9fdf5c entry-tipssl">免费分享</div><?php } ?> 
               </div> 
               <div class="gtlol-8054f6 entry-wrapper"> 
                <header class="gtlol-009a7a entry-header"> 
                 <h2 class="gtlol-9bc1a8 entry-title"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" rel="bookmark"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2> 
                 <div class="gtlol-b6501c entry-meta"> 
                  <?php if (isset($v['tag_arr'])) { ?><span class="gtlol-d18513 meta-category"> <?php if(isset($v['tag_arr']) && is_array($v['tag_arr'])) { foreach($v['tag_arr'] as &$v2) { ?><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="_blank" rel="category"><i class="gtlol-355d1b dot"></i><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a><?php }} ?></span><?php } ?> 
                 </div> 
                </header> 
                <div class="gtlol-e05834 entry-excerpt u-text-format"><?php echo(isset($v['intro']) ? $v['intro'] : ''); ?></div> 
                <div class="gtlol-0013fc entry-footer"> 
				 <a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><span class="gtlol-d3894f price"><i class="gtlol-52cf09 fa fa-user-circle-o"></i> <?php echo(isset($v['author']) ? $v['author'] : ''); ?></span></a>
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><time datetime="2021-08-01"><i class="gtlol-3b90a0 fa fa-clock-o"></i> <?php echo(isset($v['date']) ? $v['date'] : ''); ?></time></a> 
                 <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><span><i class="gtlol-3a691c fa fa-eye"></i> <?php echo(isset($v['views']) ? $v['views'] : ''); ?></span></a> 
                </div> 
				<div class="gtlol-cd19a5 pricebtn">
				 <?php if ($v['payprice']>0) { ?><a class="gtlol-e0ebce price" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#pay" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php }else{ ?><a class="gtlol-6ba6c5 price" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php } ?><strong><i class="gtlol-e00b30 fa fa-rmb"></i> <?php if ($v['payprice']>0) { echo(isset($v['payprice']) ? $v['payprice'] : ''); }else{ ?>免费<?php } ?></strong></a> 
                 <?php if (empty($v['source'])) { ?>
				 <a class="gtlol-ca2473 buy" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_ablank"><i class="gtlol-ab9774 fa fa-share"></i> 查看详情 
				 <?php }else{ ?>
				 <a class="gtlol-5a6462 buy" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#download" target="_ablank"><i class="gtlol-5d2080 fa fa-download"></i> 前往下载
				 <?php } ?>
				 </a>
                 <?php if (!is_mobile()) { ?><a class="gtlol-b6f104 deom" href="<?php echo(isset($v['source']) ? $v['source'] : ''); ?>" target="_ablank"><i class="gtlol-8f36ea fa fa-desktop"></i> 演示地址</a><?php } ?>
				</div>				
               </div> 
              </article> 
             </div>  
		     <?php }} ?> 
        </div> 
        <div class="gtlol-b122f5 infinite-scroll-action"> 
         <div class="gtlol-62c98c infinite-scroll-button button link">
          <a href="/vip" title="免费分享" target="_blank">免费分享</a>
         </div> 
        </div> 
       </div> 
      </div> 
	  <?php unset($data); ?>
	  <!--双列文字通栏-->
      <div class="gtlol-f4b0e4 section"> 
       <div class="gtlol-e849bd container"> 
        <div class="gtlol-a2c59a row"> 
		 <?php $data = block_list(array (
  'cid' => '1',
  'mid' => '2',
  'limit' => '9',
  'dateformat' => 'm-d',
  'intronum' => '160',
  'showcate' => '1',
  'showviews' => '1',
)); ?>
         <div class="gtlol-e7dae2 col-12 col-sm-6"> 
          <div class="gtlol-b30254 uposts"> 
           <div class="gtlol-276d5d codesign-list lazyload visible" data-bg="<?php echo(isset($cfg_var['pic']) ? $cfg_var['pic'] : ''); ?> "> 
            <h4 class="gtlol-ad553c codeisgn-h4"><a href="<?php echo(isset($data['cate_url']) ? $data['cate_url'] : ''); ?>" title="<?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?>" target="_blank"><?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?></a></h4> 
            <span class="gtlol-229335 codesign-esc"><p><?php echo(isset($data['cate_intro']) ? $data['cate_intro'] : ''); ?></p></span> 
            <div class="gtlol-799382 codesign-cover"></div> 
           </div> 
		   <?php $left=0; ?>
		   <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
		   <?php {$left++;} ?>	
           <div class="gtlol-707d8d hentry"> 
            <h2 class="gtlol-4209da title"><span class="gtlol-0e1f10 post-num num-<?php echo(isset($left) ? $left : ''); ?>"><?php echo(isset($left) ? $left : ''); ?></span><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2> 
            <div class="gtlol-683ac4 meta">
             <span>热度(<?php echo(isset($v['views']) ? $v['views'] : ''); ?>)</span>
            </div> 
           </div> 
		   <?php }} ?> 
          </div> 
         </div> 
		 <?php unset($data); ?>
		 <?php $data = block_list(array (
  'cid' => '2',
  'mid' => '2',
  'limit' => '9',
  'dateformat' => 'm-d',
  'intronum' => '160',
  'showcate' => '1',
  'showviews' => '1',
)); ?>
         <div class="gtlol-e7dae2 col-12 col-sm-6"> 
          <div class="gtlol-b30254 uposts"> 
           <div class="gtlol-276d5d codesign-list lazyload visible" data-bg="<?php echo(isset($cfg_var['pic']) ? $cfg_var['pic'] : ''); ?> "> 
            <h4 class="gtlol-ad553c codeisgn-h4"><a href="<?php echo(isset($data['cate_url']) ? $data['cate_url'] : ''); ?>" title="<?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?>" target="_blank"><?php echo(isset($data['cate_name']) ? $data['cate_name'] : ''); ?></a></h4> 
            <span class="gtlol-229335 codesign-esc"><p><?php echo(isset($data['cate_intro']) ? $data['cate_intro'] : ''); ?></p></span> 
            <div class="gtlol-799382 codesign-cover"></div> 
           </div> 
		   <?php $right=0; ?>
		   <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
		   <?php {$right++;} ?>	
           <div class="gtlol-707d8d hentry"> 
            <h2 class="gtlol-4209da title"><span class="gtlol-736b62 post-num num-<?php echo(isset($right) ? $right : ''); ?>"><?php echo(isset($right) ? $right : ''); ?></span><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2> 
            <div class="gtlol-683ac4 meta">
             <span>热度(<?php echo(isset($v['views']) ? $v['views'] : ''); ?>)</span>
            </div> 
           </div> 
		   <?php }} ?> 
          </div> 
         </div> 
		 <?php unset($data); ?> 
        </div> 
	    <!--广告-->
        <div class="gtlol-a574e6 site-ads ads-list-header">
          <a class="gtlol-582908 adplan" data-id="15" style="display:block;width:100%;margin-bottom:10px;" rel="nofollow" target="_blank" href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=Accessoft&amp;menu=yes" title="联系qq：<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/vip.jpg" width="1180" height="200" /></a>
        </div> 	   
	    <!--广告-->		
       </div> 
      </div> 
      <div class="gtlol-7edd17 section services bgcolor-fff"> 
       <div class="gtlol-e849bd container"> 
        <div class="gtlol-a2c59a row"> 
         <div class="gtlol-62fedf col-lg-3 col-sm-6"> 
          <div class="gtlol-fe4b39 service-single"> 
           <span><i class="gtlol-9716e6 fa fa-trophy"></i></span> 
           <h4>精品资源</h4> 
           <p>本站所有精品资源，均为用户收集，或付费收集！</p> 
          </div> 
         </div> 
         <div class="gtlol-62fedf col-lg-3 col-sm-6"> 
          <div class="gtlol-fe4b39 service-single"> 
           <span><i class="gtlol-ae3c3b fa fa-diamond"></i></span> 
           <h4>免费下载</h4> 
           <p>用户可任意免费下载使用所有资源和评论，投稿！</p> 
          </div> 
         </div> 
         <div class="gtlol-62fedf col-lg-3 col-sm-6"> 
          <div class="gtlol-fe4b39 service-single"> 
           <span><i class="gtlol-3a1712 fa fa-product-hunt"></i></span> 
           <h4>哥特动漫王国</h4> 
           <p>哥特萝莉社是一个温馨的资源交流平台！</p> 
          </div> 
         </div> 
         <div class="gtlol-62fedf col-lg-3 col-sm-6"> 
          <div class="gtlol-fe4b39 service-single"> 
           <span><i class="gtlol-d72625 fa fa-users"></i></span> 
           <h4>售后支持</h4> 
           <p>早上09:00-12:00，下午14:00-18:00，工作时间内，保证解决所有客户遇到问题！</p> 
          </div> 
         </div> 
        </div> 
       </div> 
      </div> 
     </main> 
    </div> 
   </div> 
   <!--登录留言 开始-->   
   <div class="gtlol-da3271 cd-user-modal"> 
    <div class="gtlol-5309de cd-user-modal-container"> 
    <ul class="gtlol-db0ea6 cd-switcher"> 
	 <li><a href="#login">会员登录</a></li> 
     <li><a href="#message">在线留言</a></li> 
    </ul> 
    <!-- 登录表单 --> 
    <div id="cd-login"> 
	<?php if (empty($_uid)) { ?>
    <form  class="gtlol-b892cc cd-form" id="login-form" action="<?php echo(isset($login_url) ? $login_url : ''); ?>" method="post">
    <input type="hidden" name="FORM_HASH" value="<?php echo(isset($form_hash) ? $form_hash : ''); ?>" />
    <p class="gtlol-5c46da fieldset"><input class="gtlol-541b1b full-width has-padding has-border" id="username" type="text" name="username" value="" placeholder="* 请输入 用户名" autocomplete="off"></p>
    <p class="gtlol-5c46da fieldset"><input class="gtlol-d0854f full-width has-padding has-border" id="password" type="password" name="password" value="" placeholder="* 请输入 密码" autocomplete="off"></p>
    <?php if ($cfg['open_user_login_vcode']) { ?>
    <p class="gtlol-5c46da fieldset"><input class="gtlol-5e4755 full-width3 has-padding has-border" id="vcode" type="text" name="vcode" value="" placeholder="* 请输入 验证码" autocomplete="off">&nbsp;&nbsp;<img src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?user-vcode-name-loginvcode" alt="验证码" onclick="this.src='index.php?user-vcode-loginvcode-r-'+Math.random();" id="vcodeimg" style="width: 40%;" />
    </p>
    <?php } ?>
    <p class="gtlol-5c46da fieldset"><button type="submit" class="gtlol-20ec98 full-width2"  lay-submit lay-filter="form">登 录</button>
      <?php if ($cfg['open_user_register']) { ?><a href="/user-register.html" target="_blank" title="用户注册" class="gtlol-e62455 btn btn-default btn-lg btn-block mt-3 no-border">还没有账号？点击注册</a><?php } ?>
	  <?php if ($cfg['open_user_reset_password']) { ?><a href="/user-forget.html" target="_blank" class="gtlol-e62455 btn btn-default btn-lg btn-block mt-3 no-border">忘记密码?</a><?php } ?>
    </p>
    

    </form>
    <?php }else{ ?>
     <div class="gtlol-29ff95 cd-form"> 
      <p class="gtlol-5c46da fieldset">即将进入会员中心，请稍等......</p> 
     </div>
	<?php } ?>
    </div>  	
     <!-- 留言提交 --> 
    <div id="cd-message"> 
     <form class="gtlol-cd1410 cd-form" id="ctf_message_form" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-domessage-ajax-1.html" method="post"> 
      <p class="gtlol-5c46da fieldset"><input type="text" class="full-width has-padding has-border" name="message[title]" value="" placeholder="* 请输入 留言标题" required="required" /></p> 
      <p class="gtlol-5c46da fieldset"><input type="text" class="full-width has-padding has-border" name="message[author]" value="" placeholder="* 请输入 您的称呼" required="required" /></p> 
      <p class="gtlol-5c46da fieldset"><input type="text" class="full-width has-padding has-border" name="message[contact]" value="" placeholder="* 请输入 联系电话" required="required" /></p> 
      <p class="gtlol-5c46da fieldset"><textarea type="text"  class="gtlol-15d79f full-width has-padding has-border" name="message[content]" placeholder="* 请输入 留言内容，" required="required"></textarea></p> 
      <p class="gtlol-5c46da fieldset"><input class="full-width3 has-padding has-border" name="message[vcode]" placeholder="* 请输入 验证码" type="text" required="required" />&nbsp;&nbsp;<img id="captchaPic" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode-name-message" onclick="this.src='<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode-name-message-r-'+Math.random();" alt="验证码" style="width: 40%;" /></p> 
      <p class="gtlol-5c46da fieldset"><input class="full-width2" type="submit" id="ctf_submit" tabindex="6" value="提交" /></p> 
      <p id="ctf_message_tips"></p> 
     </form> 
    </div> 
    <a href="#0" class="gtlol-b7f3dd cd-close-form">关闭</a> 
    </div> 
  </div>
  <!-- 留言提交 --> 
  <script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/pxmu.min.js"></script>
  <script>
    // 留言提交
    window.ctf_message_form_one = false;
    $("#ctf_message_form").submit(function() {
        if (window.ctf_message_form_one) return false;
        window.ctf_message_form_one = true;
        var content = $("textarea[name='message[content]']").val();
        var title = $("input[name='message[title]']").val();
        var contact = $("input[name='message[contact]']").val();
        var author = $("input[name='message[author]']").val();
        var vcode = $("input[name='message[vcode]']").val();
        setTimeout(function(){
            window.ctf_message_form_one = false;
        }, 2000);
        if( content == '' || title == '' || contact == '' || author == '' || vcode == '' ){
            pxmu.fail('各项信息都不能为空哦！');
        }else{
            var _this = $(this);
            $.post(_this.attr("action"), _this.serialize(), function(data){
                try{
                    var json = eval("("+data+")");
                    if(json.kong_status) {
                        pxmu.success({msg: json.message, time: 1000});
                        setTimeout(function(){
                            window.location.reload();
                        }, 2000);
						$("#ctf_message_form").find("input").val("");//递交成功清空input
						$("#ctf_message_form").find("textarea").val("");//递交成功清空textarea
                    }else{
                        pxmu.fail(json.message);
                    }
                }catch(e){
                    alert(data);
                }
            });
        }
        return false;
    });
  </script>
  <!--登录留言 END--> 
<!--边栏客服-->
<link rel="stylesheet" type="text/css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/supermenu.css"/>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/jquery.supermenu.js"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/share.js"></script>
<div class="gtlol-fc4229 hovermenu">
   <div class="gtlol-9a3c9b hovermenu-box">
    <a href="javascript:;" class="gtlol-8e9bdb a-show"><span class="gtlol-4aa605 i"></span></a> 
    <a href="javascript:;" class="gtlol-fc0111 a a-hide"><span class="gtlol-4aa605 i"></span></a> 
	<?php if (($control=='index' || $control=='cate' || $control=='show' || $control=='tag') && $action == 'index') { ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-e679a3 i a-share" title="点击分享"></span> 
     <div class="gtlol-adb0e9 d-share" style="display: none;"> 
      <h3>分享<span class="gtlol-a4dc3a d-close iconfont"></span></h3>
      <div class="gtlol-9d4da6 Hcopyurl">
       <p id="Hcopyurl" onclick="copyurl()">
	    <?php if ($control=='index' && $action == 'index') { ?>
		<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>
		<?php }elseif($control=='show' && $action == 'index') { ?>
		<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>
		<?php }else{ ?>
		<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($cfg_var['url']) ? $cfg_var['url'] : ''); ?>
		<?php } ?>
	   </p> 
       <span onclick="copyurl()">复制链接</span> 
      </div>
       <div class="gtlol-9ebab9 social-share" data-initialized="true" style="text-align: center;"> 
          <a href="#" class="gtlol-bdd198 social-share-icon icon-weibo"></a> 
          <a href="#" class="gtlol-f34d18 social-share-icon icon-qq"></a> 
          <a href="#" class="gtlol-033365 social-share-icon icon-wechat"></a> 
          <a href="#" class="gtlol-40b3be social-share-icon icon-qzone"></a> 
          <a href="#" class="gtlol-bfcafd social-share-icon icon-facebook"></a> 
          <a href="#" class="gtlol-993b8a social-share-icon icon-twitter"></a> 
       </div> 
     </div> 
    </div> 
	<?php } ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-79f668 i a-qq"></span> 
     <div class="gtlol-ae234f d d-qq" style="display: none;">
	  <span class="gtlol-e95b77 arrow"></span>
      <div class="gtlol-009344 hqq">售前咨询<a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="咨询网站客服QQ"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/qq.png" />QQ在线</a></div> 
	  <div class="gtlol-009344 hqq">售后服务<a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="咨询网站客服QQ"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/qq.png" />QQ在线</a></div> 
      <div class="gtlol-8a63d6 worktime">
       上班时间：9：00-22：00
       <br />周六、周日：14：00-22：00
      </div>
     </div>
    </div> 
	<?php if ($control=='show' && $action == 'index'  &&  $gdata['payprice']>0) { ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-0c5c08 i a-buy"></span> 
     <div class="gtlol-01bacd d d-buy" style="display: none;">
      <span class="gtlol-e95b77 arrow"></span> 
      <div class="gtlol-95d97e Hbuy">
       <span class="gtlol-e4e7de hprice"><?php if ($gdata['payprice']>0) { echo(isset($gdata['payprice']) ? $gdata['payprice'] : ''); }else{ ?>免费<?php } ?></span>
       <a href="#pay" title="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"><img src="<?php if ($gdata['haspic']) { echo(isset($gdata['pic']) ? $gdata['pic'] : ''); }else{ echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); } ?>" alt="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>" class="buycover" /></a>
       <a href="#pay" title="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"><h3>购买主题</h3></a>
       <div class="gtlol-bf6e63 htips">
        <?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>
       </div>
      </div>
     </div>
    </div> 	
	<?php }else{ ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-0c5c08 i a-buy"></span> 
     <div class="gtlol-01bacd d d-buy" style="display: none;">
      <span class="gtlol-e95b77 arrow"></span> 
      <div class="gtlol-95d97e Hbuy">
       <span class="gtlol-e4e7de hprice">399</span>
       <a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="购买本站同款主题"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/theme.jpg" alt="购买本站同款主题" class="buycover" /></a>
       <a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="购买本站同款主题"><h3>购买本站同款主题</h3></a>
       <div class="gtlol-bf6e63 htips">
        本站LECMS付费资源主题
       </div>
      </div>
     </div>
    </div> 	
	<?php } ?>
	<?php if ($control=='show' && $action == 'index'  &&  $gdata['payprice']>0) { ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-aba934 i a-down"></span> 
     <div class="gtlol-f0db0c d d-down" style="display: none;">
      <i class="gtlol-e95b77 arrow"></i>
      <h3><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h3>
      <a href="#download" class="gtlol-c1e71e Hdown"><span class="gtlol-d99724 iconfont"></span>点击下载</a>
      <div class="gtlol-bf6e63 htips">
		<?php if (isset($gdata['tag_arr'])) { if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?>
         <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" rel="tag"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 			 
        <?php }} } ?>
      </div>
     </div>
    </div>	
	<?php }else{ ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-aba934 i a-down"></span> 
     <div class="gtlol-f0db0c d d-down" style="display: none;">
      <i class="gtlol-e95b77 arrow"></i>
      <h3>LECMS网站程序 LECMS 3.0 正式版</h3>
      <a target="_blank" rel="nofollow"  href="https://www.lecms.cc/?thread-205.htm" class="gtlol-c1e71e Hdown"><span class="gtlol-d99724 iconfont"></span>点击下载</a>
      <div class="gtlol-bf6e63 htips">
       LECMS是一款高负载、轻量级、可扩展建站程序。
      </div>
     </div>
    </div>
    <?php } ?>	
	<a href="#" class="gtlol-b4b300 a" title="VIP会员"><span class="gtlol-cfc782 i a-vip note-open"></span></a>
    <a href="javascript:;" class="gtlol-e2893c a-top" style="display: block;" title="返回顶部"><span class="gtlol-4aa605 i"></span></a> 
   </div>
</div>   
 <script>
function copyurl(){
const range = document.createRange();
range.selectNode(document.getElementById("Hcopyurl"));
const selection = window.getSelection();
if(selection.rangeCount > 0) selection.removeAllRanges();
selection.addRange(range);
document.execCommand("copy");
alert("复制链接成功，感谢您的关注！");
};
function copytel(){
const range = document.createRange();
range.selectNode(document.getElementById("copytel"));
const selection = window.getSelection();
if(selection.rangeCount > 0) selection.removeAllRanges();
selection.addRange(range);
document.execCommand("copy");
alert("已经复制电话号码！期待您的来电！");
};
</script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/chinese-s.js"></script>     
   <div class="gtlol-dc1f68 module parallax"> 
    <img class="jarallax-img lazyload" data-srcset="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/footbanner.jpg" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="" /> 
    <div class="gtlol-e849bd container"> 
     <h4 class="gtlol-66873e entry-title" data-aos="fade-up">哥特动漫王国-哥特萝莉社</h4> 
     <a target="_blank" class="gtlol-bde8de button" data-aos="fade-right" href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" rel="nofollow" title="全站免费"><i class="gtlol-950342 fa fa-heartbeat"></i> 模板定制</a> 
     <a target="_blank" class="gtlol-aa41a9 button transparent" data-aos="fade-left" href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" rel="nofollow" title="欢迎加入"><i class="gtlol-c8185c fa fa-qq"></i> 联系我们</a> 
    </div> 
   </div>
   <footer class="gtlol-95250f site-footer"> 
    <div class="gtlol-e849bd container"> 
     <div class="gtlol-a9d3b9 footer-widget"> 
      <div class="gtlol-a2c59a row"> 
       <div class="gtlol-6a2773 col-xs-12 col-sm-6 col-md-3 widget--about"> 
        <div class="gtlol-35a7f5 widget--content"> 
         <div class="gtlol-3f43dd footer--logo mb-20"> 
          <img class="tap-logo" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/logofoot.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> 
         </div> 
         <p class="gtlol-ccf69d mb-10"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></p> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="gtlol-268104 col-xs-12 col-sm-3 col-md-2 col-md-offset-1 widget--links"> 
        <div class="gtlol-198027 widget--title"> 
         <h5>本站导航</h5> 
        </div> 
        <div class="gtlol-35a7f5 widget--content"> 
         <ul class="gtlol-566c2b list-unstyled mb-0"> 
          <li><a href="/about">关于我们</a></li> 
          <li><a href="/contact">联系我们</a></li> 
          <li><a href="/aftersale">售后保障</a></li> 
         </ul> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="gtlol-b5b18c col-xs-12 col-sm-3 col-md-2 widget--links"> 
        <div class="gtlol-198027 widget--title"> 
         <h5>更多介绍</h5> 
        </div> 
        <div class="gtlol-35a7f5 widget--content"> 
         <ul class="gtlol-566c2b list-unstyled mb-0"> 
          <li><a href="/process">购买流程</a></li> 
          <li><a href="/clause">交易条款</a></li> 
          <li><a href="/problem">常见问题</a></li> 
         </ul> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="gtlol-8f3c7d col-xs-12 col-sm-12 col-md-4 widget--newsletter"> 
        <div class="gtlol-198027 widget--title"> 
         <h5>快速搜索</h5> 
        </div> 
        <div class="gtlol-35a7f5 widget--content"> 
         <form id="search_form3" class="gtlol-da4d98 newsletter--form mb-30" method="get" target="_blank"> 
		  <input type="hidden" name="u" value="search-index" /> 
          <input type="text" class="full-width has-padding has-border" name="keyword" placeholder="关键词" /> 
          <button type="submit"><i class="gtlol-417359 fa fa-arrow-right"></i></button> 
         </form> 
         <h6><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>提供的大部分资源来于网络收集整理，仅供学习交流之用。请勿非法使用，否则产生的一切后果自行承担，本站概不负责。</h6>
        </div> 
       </div> 
      </div> 
     </div> 
    </div> 
	<?php if ($control=='index' && $action == 'index') { ?>
    <div class="gtlol-389c54 links"> 
     <div class="gtlol-e849bd container"> 
      <ul>
       <li>友情链接：</li>
	   <?php $data = block_links(array (
)); if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
       <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
       <?php }} unset($data); ?> 
      </ul> 
     </div> 
    </div>  
	<?php } ?>
    <div class="gtlol-72cc7c site-info">
	 本站所发布的大部分内容来源于互联网，仅限于小范围内传播学习和文献参考，请在下载后24小时内删除！如果有侵权之处请第一时间联系我们删除，敬请谅解！
     <br />任何人不得对本站资源进行倒卖、行骗、传播。严禁用于商业用途，请遵循相关法律法规，本站一切资源不代表本站立场！
     <br />Copyright&copy;2015-2021 <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" rel="home"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>
     <a href="/sitemap.xml" target="_blank">网站地图</a> 
     <a href="/tags" target="_blank">TAGS</a>
     <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a> 
    </div> 	
   </footer> 
   <!--<div class="gtlol-606b14 rollbar">
   <div class="gtlol-f4bce9 rollbar-item note-open"><i class="gtlol-c26a81 fa fa-bell-o"></i></div><!--点击弹窗-->
   <!--<div class="gtlol-238be5 rollbar-item" etap="to_top"><i class="gtlol-43414a fa fa-angle-up"></i></div><!--返回顶部--> 
   <!--</div>-->  
   <!--移动端logo-->
   <div class="gtlol-f54efd off-canvas"> 
    <div class="gtlol-7fc92c canvas-close">
     <i class="gtlol-963018 mdi mdi-close"></i> 
    </div> 
    <div class="gtlol-cfa30a logo-wrapper"> 
     <a href="/"><img class="logo regular" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/logom.jpg" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> </a> 
    </div> 
    <div class="gtlol-9d2b4c mobile-menu hidden-lg hidden-xl"></div> 
    <aside class="gtlol-37ce8a widget-area"></aside> 
   </div> 
   <!--js-->
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/main.js'></script>
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/plugins.js'></script>
   <script language="javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/server.js"></script>
   <?php if ($control=='show' && $action == 'index') { ?>
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery.fancybox.min.js'></script>
   <script type="application/ld+json">
   {
    "@content": "https://ziyuan.baidu.com/contexts/cambrian.jsonld",
    "@id": "<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>",
    "appid": "",
    "title": "<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>",
    "images": ["<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>"],
    "description": "<?php echo(isset($gdata['intro']) ? $gdata['intro'] : ''); ?>",
    "pubDate": "<?php  echo date('Y-m-d', $gdata['dateline']).'T'.date('H:i:s', $gdata['dateline']); ?>",
    "upDate": "<?php  echo date('Y-m-d', $gdata['lasttime']).'T'.date('H:i:s', $gdata['lasttime']); ?>",
    "lrDate": "<?php  echo date('Y-m-d', $gdata['lasttime']).'T'.date('H:i:s', $gdata['lasttime']); ?>"
   }
   </script>  
   <script>
    $(".entry-content a").each(function(){
    var articleHref = $(this).attr("href").split('/')[2];
    if(articleHref != window.location.host){
    $(this).attr("rel","external nofollow");
    };
    })
   </script>
   <?php } ?>
   </div>
   <?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>
   <script>$(".moretag").readmore({moreLink: '<a href="#" class="gtlol-59b066 more"><i class="gtlol-10dda2 fa fa-plus-square"></i>更多</a>',lessLink: '<a href="#" class="gtlol-59b066 more"><i class="gtlol-6856b1 fa fa-minus-square"></i>收起</a>',speed: 100,collapsedHeight: 30});</script>
   <?php } ?>
   <script>   
	(function(){
		$("#search_form,#search_form2,#search_form3").submit(function(){
			var mid = $(this).find("[name='mid']").val();
			var keyword = $(this).find("[name='keyword']").val();
			window.location.href = "/search/"+encodeURIComponent(keyword)+"/";
			return false;
		});	
	})();  
   </script>    
  </div>
 </body>
</html> 