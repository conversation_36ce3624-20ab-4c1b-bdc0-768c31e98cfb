<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_search($conf) { global $run, $keyword; $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $maxcount = isset($conf['maxcount']) ? (int)$conf['maxcount'] : 100000; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $extra = array('block_name'=>'block_global_search'); $mid = max(2, (int)R('mid')); if( empty($keyword) ) return array('total'=> 0, 'pages'=> '', 'list'=> array()); if(isset($run->_cfg['close_search']) && !empty($run->_cfg['close_search'])){ return array('total'=> 0, 'pages'=> '', 'list'=> array()); } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return array('total'=> 0, 'pages'=> '', 'list'=> array()); } $where = array('title'=>array('LIKE'=>$keyword)); $run->cms_content->table = 'cms_'.$table; if($run->cms_content->count() > $maxcount) return array('total'=> 0, 'pages'=> '', 'list'=> array()); $total = $run->cms_content->find_count($where); $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); $search_url = $run->urls->search_url($mid, $keyword, true); $pages = paginator::$page_function($page, $maxpage, $search_url, $pageoffset); if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); $v['subject'] = str_ireplace($keyword, '<font color="red">'.$keyword.'</font>', $v['subject']); $v['intro'] = str_ireplace($keyword, '<font color="red">'.$keyword.'</font>', $v['intro']); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } return array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); }
$gdata = block_global_search(array (
  'pagenum' => '15',
  'showcate' => '1',
  'showviews' => '1',
  'maxcount' => '99999999999999999',
  'dateformat' => 'Y-m-d',
  'intronum' => '160',
));
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $table = isset($run->_cfg['table_arr'][$mid]) ? $run->_cfg['table_arr'][$mid] : 'article'; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('tagid', 'count', 'orderby')) ? $conf['orderby'] : 'count'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = isset($conf['limit']) ? (int)$conf['limit'] : 10; $cms_limit = _int($conf, 'cms_limit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_taglist'); $cache_key = $life ? md5('taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $run->cms_content_tag->table = 'cms_'.$table.'_tag'; if($cms_limit){ $run->cms_content->table = 'cms_'.$table; $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; } $where = array(); $list_arr = $run->cms_content_tag->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } if($cms_limit){ $tag_data_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$v['tagid']), array('id'=>-1), 0, $cms_limit); $keys = array(); foreach($tag_data_arr as $lv) { $keys[] = $lv['id']; } $cms_arr = $run->cms_content->mget($keys); foreach($cms_arr as &$cv) { $run->cms_content->format($cv, $mid); } $v['cms'] = $cms_arr; unset($cms_arr); } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?><!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></title>
<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/common.css">
<script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/interactive.js" type="text/javascript"></script>
<meta http-equiv="Cache-Control" content="no-transform" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<meta name="applicable-device" content="pc,mobile" />
</head>
<body>
<header>
<div class="hgacg-3c0116 xmedia_nav">
<a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" class="hgacg-d9e418 aikahao_logo" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"></a>
<div class="hgacg-faacab nevigate">
<?php $data = block_navigate(array (
)); ?>
<ul class="hgacg-408375 nevigate_ul">
<li <?php if (empty($cfg_var['topcid'])) { ?>class="active"<?php } ?>><a href="/">首页</a><i class="hgacg-f7d5bd ico_bg"></i></li>
<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
<li <?php if ($cfg_var['topcid'] == $v['cid']) { ?>class="active"<?php } ?>><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><i class="hgacg-f7d5bd ico_bg"></i></li><?php }} ?>
<?php if ($_uid) { ?>
                    <li><a href="/my-index.html" class="hgacg-fbdb65 padding-half">个人中心</a></li>
                <?php }else{ ?>
                    <li><a href="/user-login.html" class="hgacg-fbdb65 padding-half">登录/注册</a></li>
                <?php } ?>
</ul>
<?php unset($data); ?>
</div>
<div class="hgacg-33de5d search_box">
<div class="hgacg-823656 seek">
<form name="get" class="hgacg-599cf9 model_form_con clearfix"  method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form" target="_blank">
<div class="hgacg-191545 seek_cn">
<span class="hgacg-3e2cda seek_ico"></span>
<input type="text" id="hsearchkey" name="keyword" autocomplete="off" class="hgacg-b361f4 input_txt padding_input" placeholder="请输入关键字">
<input type="hidden" name="u" value="search-index" />
<input type="hidden" name="mid" value="2" />
</div>
<button  type="submit" class="hgacg-9ce515 blue_but" id="bdcs-search-form-submit">搜索</button>
</form>
</div>
</div>
</div>
</header>
<div class="hgacg-8eff22 f-title bd43"><div class="hgacg-e849bd container"> <h1><b><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></b></h1> <p>查找与<?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?>的相关内容，24小时内关于<?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?>的最新内容！</p></div></div>
<div class="hgacg-de9a7c warp clearfix">

<div class="hgacg-a5afc3 column">
<div class="hgacg-1631c3 choices">
<div class="choices_cont" >
<ul class="hgacg-57c725 choices_main" id="infoModelLf">
<?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
<li class="hgacg-b92513 item_content">
<div class="hgacg-97c8fa leftImg">
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a>
</div>
<div class="hgacg-10a7d8 txtCon_one">
<a class="hgacg-2e0487 txt_title" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
<p class="hgacg-81e687 txt_con"><?php echo(isset($v['intro']) ? $v['intro'] : ''); ?></p>
<div class="hgacg-a54b8e txt_label">
<span class="hgacg-c27a0c user_name"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
<div class="hgacg-ae5a4f label_right">
<em class="hgacg-4c8fb4 dateline"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></em>
<em class="hgacg-33cc09 discuss">
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#comment"><span class="hgacg-38479c reply"></span>评论</a>
</em>
</div>
</div>
</div>
</li>
<?php }} ?>
</ul>
</div>
</div>
</div>


<div class="hgacg-297768 sidebar">
<?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '8',
  'titlenum' => '24',
)); ?>
<div class="hgacg-9d9afb details_right_username">
<h3 class="hgacg-33fcf9 right-title right_tit"><span>热门文章</span></h3>
<ul class="hgacg-2b647a author_list clearfix">
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li>
<div class="hgacg-4b4a15 author_box"> 
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a> 
</div>
<span class="hgacg-82b3f8 author_txt"> 
<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a> 
</span>
</li>
<?php }} ?>
</ul>
</div>
<?php unset($data); ?>

<?php $data = block_taglist(array (
  'mid' => '2',
  'limit' => '18',
  'orderby' => 'count',
)); ?>
<div class="hgacg-9d9afb details_right_username">
<h3 class="hgacg-33fcf9 right-title right_tit"><span>热门标签</span></h3>
<ul class="hgacg-2d9827 popular_label clearfix">
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li><a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
</ul>
</div>
<?php unset($data); ?>
<?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '8',
  'life' => '600',
)); ?>
<div class="hgacg-4f35e7 details_right_username fixedDiv">
<h3 class="hgacg-33fcf9 right-title right_tit"><span>猜你喜欢</span></h3>
<ul class="hgacg-2b647a author_list clearfix">
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li>
<div class="hgacg-4b4a15 author_box"> 
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a> 
</div>
<span class="hgacg-82b3f8 author_txt"> 
<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a> 
</span>
</li>
<?php }} ?>
 </ul>
</div>
<?php unset($data); ?>
</div>
</div>
<footer class="hgacg-936831 content_nt mar_top8">
<div class="hgacg-b03135 footer_nt">
<p><a href="/sitemap.html" target="_blank">网站地图</a>|<a href="/sitemap.xml" target="_blank">RS订阅</a></p>
<p> Copyright 2023 <?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?> <b>【<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>】</b> 版权所有 | <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>picture/gonganbeian.png"><a rel="nofollow" href="//beian.miit.gov.cn"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a></p>
<p>声明：本站为非赢利网站，作品与素材版权均归作者所有，如内容侵权与违规请与本站联系，将在三个工作日内处理，互联网违法和不良信息举报邮箱：<?php echo(isset($cfg['webmail']) ? $cfg['webmail'] : ''); ?></p>
</div>
</footer>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/common.js" type="text/javascript"></script>
<script>
(function() {
$("#search_form").submit(function() {
var mid = $(this).find("[name='mid']").val();
var keyword = $(this).find("[name='keyword']").val();
window.location.href = "/search/" + encodeURIComponent(keyword);
return false;
});
})();
(function() {
var feedBackUrl = "//wpa.qq.com/msgrd?v=3&uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&site=qq&menu=yes";
var strHml = '<div class="hgacg-eedeca feedBackWrap" > <div class="fq"><a href="' + feedBackUrl + '" target="_blank"></a></div> <div class="hgacg-ad6621 feedBackWraphd"></div></div>';
$("body").append(strHml);
$("body").on("click", ".feedBackWraphd", function() {
$(document).scrollTop(0);
})
$(window).scroll(function() {
if ($(document).scrollTop() < 100) {
$('.feedBackWraphd').hide();
} else {
$('.feedBackWraphd').show();
}
})
})()
</script>
</body>
</html>