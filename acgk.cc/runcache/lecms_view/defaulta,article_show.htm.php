<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_show($conf) { global $run, $_show, $_user; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $show_prev_next = isset($conf['show_prev_next']) && (int)$conf['show_prev_next'] ? true : false; $prev_next_cid = isset($conf['cid']) ? (int)$conf['cid'] : intval($_GET['cid']); $field_format = _int($conf, 'field_format', 0); $pageoffset = _int($conf, 'pageoffset', 5); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_show'); $cache_key = $life ? md5('global_show'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; if($mid == 1) return FALSE; $uid = isset($_user['uid']) ? (int)$_user['uid'] : 0; $run->cms_content_data->table = 'cms_'.$run->_var['table'].'_data'; $run->cms_content->format($_show, $mid, $dateformat, 0, 0, $field_format); $id = &$_show['id']; $_show['comment_url'] = $run->cms_content->comment_url($run->_var['cid'], $id); $_show['views_url'] = $run->_cfg['webdir'].'index.php?views--cid-'.$run->_var['cid'].'-id-'.$id; $data = $run->cms_content_data->get($id); if($data){ if($field_format && plugin_is_enable('models_filed')){ $models_field = $run->models_field->user_defined_field($mid); $run->models_field->field_val_format($models_field, $data, 0); } $_show += $data; $page = max(1,(int)R('page','G')); $_show = $run->cms_content_data->format_content($_show, $page); if( isset($_show['content_page']) && isset($_show['maxpage']) ){ $_show['pages'] = paginator::$page_function($page, $_show['maxpage'], $run->cms_content->content_url($_show, $mid, TRUE), $pageoffset); }else{ $_show['pages'] = false; } }else{ $_show['pages'] = false; } $run->cms_content_views->table = 'cms_'.$run->_var['table'].'_views'; $views_data = $run->cms_content_views->get($id); if($views_data){ if( empty($run->_cfg['close_views']) ){ $_show['views'] = $views_data['views']+1; $run->cms_content_views->update_views($id); }else{ $_show['views'] = $views_data['views']; } }else{ $_show['views'] = 1; empty($run->_cfg['close_views']) && $run->cms_content_views->set($id, array('views'=>1,'cid'=>$_show['cid'])); } if(isset($_show['filenum']) && !empty($_show['filenum'])){ list($attachlist, $imagelist, $filelist) = $run->cms_content_attach->attach_find_by_id($run->_var['table'], $id, array('id'=>$id, 'isimage'=>0)); $_show['filelist'] = $filelist; if($_show['uid'] == $uid && $uid){ $file_delete = true; }else{ $file_delete = false; } $_show['filelist_html'] = $run->cms_content_attach->file_list_html($filelist, $mid, $file_delete); }else{ $_show['filelist'] = array(); $_show['filelist_html'] = ''; } if( isset($run->_cfg['le_content_img_seo']) && !empty($run->_cfg['le_content_img_seo']) ){ $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/"; preg_match_all($pattern, $_show['content'], $match); $find_arr = array('{title}', '{cate_name}', '{webname}', '{count}'); if( isset($match[0]) ){ $img_count = 1; foreach ($match[0] as $k=>$img){ $replace_arr = array("{$_show['title']}", "{$run->_var['name']}", "{$run->_cfg['webname']}", $img_count); $new_alt = str_replace($find_arr, $replace_arr, $run->_cfg['le_content_img_seo']); if( stripos($img, "alt=") != false ){ $img_new = preg_replace('/alt=["\'](.*?)["\']/', 'alt="'.$new_alt.'"', $img); }else{ $img_new = str_replace_once('<img', '<img alt="'.$new_alt.'" ', $img); } if( stripos($img_new, "title=") != false ){ $img_new = preg_replace('/title=["\'](.*?)["\']/', '', $img_new); } if( strpos($img_new, $run->_cfg['webdomain']) === false ){ $img_new = str_replace_once('<img', '<img rel="external nofollow" referrerpolicy="no-referrer" ', $img_new); } $_show['content'] = str_replace_once($img, $img_new, $_show['content']); $img_count++; } unset($match[0]); } unset($find_arr); } if($show_prev_next) { if($prev_next_cid){ $prev_where = array('cid'=>$prev_next_cid, 'id'=>array('<'=> $id)); $next_where = array('cid'=>$prev_next_cid, 'id'=>array('>'=> $id)); }else{ $prev_where = array('id'=>array('<'=> $id)); $next_where = array('id'=>array('>'=> $id)); } $_show['prev'] = $run->cms_content->list_arr($prev_where, 'id', -1, 0, 1, 1, $extra); if($_show['prev']){ $_show['prev'] = current($_show['prev']); $run->cms_content->format($_show['prev'], $mid, $dateformat); } $_show['next'] = $run->cms_content->list_arr($next_where, 'id', 1, 0, 1, 1, $extra); if($_show['next']){ $_show['next'] = current($_show['next']); $run->cms_content->format($_show['next'], $mid, $dateformat); } }else{ $_show['prev'] = $_show['next'] = array(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $_show, $life); } if( isset($_show['favorites']) ){ $uid = isset($run->_user['uid']) ? (int)$run->_user['uid'] : 0; if( empty($uid) ){ $_show['has_favorites'] = 0; }else{ if( $run->favorites->find_fetch_key(array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id)) ){ $_show['has_favorites'] = 1; }else{ $_show['has_favorites'] = 0; } } $_show['favorites_url'] = $run->_cfg['webdir'].'index.php?show-favorites-mid-'.$mid.'-id-'.$id.'-uid-'.$uid.'.html'; }$le_keywords_links_setting = $run->kv->xget('le_keywords_links_setting'); if($le_keywords_links_setting){ $class = $le_keywords_links_setting['class'] == '' ? '' : ' class="'.$le_keywords_links_setting['class'].'"'; $target = $le_keywords_links_setting['target'] == 0 ? '' : ' target="_blank"'; $style = $class.$target; $keywords_links_arr = $run->keywords_links->find_fetch(array(), array('orderby'=>1, 'id' => 1)); if( $keywords_links_arr ){ $contentstr = $_show['content']; foreach ($keywords_links_arr as $keywords){ $patterns = '#(?=[^>]*(?=<(?!/a>)|$))'.$keywords['keyword'].'#'; $replacements = '<a href="'.$keywords['url'].'" '.$style.' title="'.$keywords['keyword'].'">'.$keywords['keyword'].'</a>'; $contentstr = preg_replace($patterns, $replacements, $contentstr, $keywords['count']); } $_show['content'] = $contentstr; unset($keywords_links_arr); } } return $_show; }
$gdata = block_global_show(array (
  'show_prev_next' => '1',
));
 function block_taglike($conf) { global $run, $_show; if(!isset($_show['tags']) || empty($_show['tags'])) return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $type = isset($conf['type']) ? (int)$conf['type'] : 1; $type = $type <= -1 ? -1 : (int)$type; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglike'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; $table = &$run->_var['table']; if($type == -1){ $end = array_slice($_show['tags'],-1, 1, true); $tagid = key( $end ); }elseif ($type == 0){ $tagid = array_rand($_show['tags']); }else{ $tagid = key( array_slice($_show['tags'], $type-1, 1, true) ); if( empty($tagid) ){ return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); } } $tag_name = $_show['tags'][$tagid]; $tag_url = $run->cms_content->tag_url($mid, array('tagid'=>$tagid, 'name'=>$tag_name)); if(isset($conf['mid']) && $conf['mid'] > 1 && $mid != $conf['mid'] && isset($run->_cfg['table_arr'][$conf['mid']])){ $mid = $conf['mid']; $table = $run->_cfg['table_arr'][$conf['mid']]; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $tags = $run->cms_content_tag->find_fetch(array('name'=>$tag_name), array(), 0, 1); if(empty($tags)){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }else{ $tags = current($tags); $tagid = $tags['tagid']; $tag_url = $run->cms_content->tag_url($mid, $tags); } } $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; $tag_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$tagid), array('id'=>$orderway), $start, $limit+1); $keys = array(); foreach($tag_arr as $lv) { if($lv['id'] != $_show['id']){ $keys[] = $lv['id']; } } if( empty($keys) ){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }elseif (count($keys) > $limit){ $keys = array_slice($keys, 0, $limit); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $cids = empty($conf['cids']) ? '' : $conf['cids']; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list'); $cache_key = $life ? md5('list'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if ($cids){ $cid_arr = explode(',', $cids); $where = array('cid' => array("IN" => $cid_arr)); $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; }else{ if($cid == 0) { $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); if(empty($cate_arr)) return; $cate_name = $cate_arr['name']; $cate_url = $run->category->category_url($cate_arr); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('cate_name'=> $cate_name, 'cate_url'=> $cate_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_by_uid($conf) { global $run; $uid = _int($conf, 'uid', 0); $mid = _int($conf, 'mid', 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list_by_uid'); $cache_key = $life ? md5('list_by_uid'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $run->cms_content->table = 'cms_'.$table; if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if( empty($uid) ){ global $_show; $uid = isset($_show['uid']) ? $_show['uid'] : 0; if( empty($uid) ){ return array('username'=>'', 'author'=>'', 'list'=>array()); } } $user = $run->user->get($uid); if( empty($user) ){ return array('username'=>'', 'author'=>'', 'list'=>array()); } $author = empty($user['author']) ? $user['username'] : $user['author']; $where['uid'] = $uid; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('username'=>$user['username'], 'author'=>$author, 'list'=>$list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?><!DOCTYPE html>
    <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
    <meta http-equiv="Cache-Control" content="no-transform"/>
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta name="applicable-device" content="pc,mobile">
    <link rel="shortcut icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.ico" />
    <link rel="icon" sizes="32x32" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png">
    <link rel="Bookmark" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png" />
    <title><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - 飞雪ACG</title>
    <?php if ($gdata['seo_keywords']) { ?>
    <meta name="keywords" content="<?php echo(isset($gdata['seo_keywords']) ? $gdata['seo_keywords'] : ''); ?>" />
    <?php }elseif($gdata['tags']) { ?>
    <meta name="keywords" content="<?php echo implode(',',$gdata['tags']); ?>" />
    <?php }else{ ?>
    <meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
    <?php } ?>
    <meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>">
    <meta property="og:locale" content="zh_CN"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>"/>
    <meta property="og:width" content="800"/>
    <meta property="og:author" content="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>">
    <meta property="og:update_time" content="<?php  echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']); ?>">
    <meta property="og:published_time" content="<?php echo date('Y-m-d H:i:s'); ?>">
    <meta property="og:title" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"/>
    <meta property="og:keywords" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"/>
    <meta property="og:description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>"/>
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-v2.css?1.0">
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-bbs-v2.css?1.2">
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/tag.css?1.0">
<?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?>
       <style>
	img{width:100%;height:auto;display:block;clip-path:inset(0 0 25% 0)}.notice{background:linear-gradient(135deg,#007bff,#00c6ff);color:white;border-radius:10px;padding:20px;text-align:center;margin:20px 0;box-shadow:0 4px 15px rgba(0,123,255,0.5);transition:transform 0.3s}.notice a{color:white;text-decoration:none;font-size:18px;font-weight:bold;display:inline-block;padding:10px 20px;border-radius:5px;background-color:rgba(255,255,255,0.2);transition:background-color 0.3s}.notice a:hover{background-color:rgba(255,255,255,0.4);text-decoration:none}.notice:hover{transform:scale(1.02).message img{max-width:100%}}
    </style>
</head>

<body>

	<header class="acgb-fec5ef navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="acgb-e849bd container">
			<button class="acgb-3ed93f navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="acgb-98211e navbar-toggler-icon"></span>
			</button>

			<a class="acgb-901cd3 navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			
			<a class="acgb-57f644 navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="acgb-529e13 icon-search"></i></a></a>
			
			<div class="acgb-cac1f4 collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				<?php $data = block_navigate(array (
)); ?>
				<ul class="acgb-dc5293 navbar-nav mr-auto">
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="acgb-d4e0a6 nav-item home  active"<?php }else{ ?> class="nav-item home"<?php } ?>><a class="nav-link" href="/"><i class="acgb-0169a5 icon-home d-md-none"></i> 首页</a></li>	
					<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="nav-item  active"<?php }else{ ?> class="nav-item"<?php } ?>>
						<a class="acgb-4df27e nav-link" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><i class="acgb-b7603e icon-circle-o d-md-none"></i><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
					</li>
					<?php }} ?>
                  	<li class="acgb-980980 nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	<?php if ($_uid) { ?>
                        <a href="/my-index.html" class="acgb-88f4ee nav-link">个人中心</a>
                    <?php }else{ ?>
                        <a href="/user-login.html" class="acgb-88f4ee nav-link">登录/注册</a>
                    <?php } ?>
                  	</li>
				</ul><?php unset($data); ?>
				<!-- 右侧：用户 -->
				<ul class="acgb-a21420 navbar-nav">
					<li class="acgb-2d9907 nav-item"><a class="acgb-454400 nav-link" href="/search/"><i class="acgb-529e13 icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="acgb-e849bd container">
<style>.col-lg-9.main .mb-3, .my-3 {margin-bottom: 0.5rem !important;} .card.card-postlist {margin-bottom: 1.3rem;}.bilibili{position:relative;width:100%;height:0;padding-bottom:75%}.bilibili iframe{position:absolute;width:100%;height:100%;left:0;top:0}</style>
<div class="acgb-693376 row u_2006">
	<div class="acgb-7a1c9a col-lg-9 main">
		<ol class="acgb-c1db89 breadcrumb d-none d-md-flex">
			<li class="acgb-8150fc breadcrumb-item"><a href="/" aria-label="首页"><i class="acgb-95efde icon-home"></i></a></li>
			<?php if(isset($cfg_var['place']) && is_array($cfg_var['place'])) { foreach($cfg_var['place'] as &$v) { ?>
			<li class="acgb-8150fc breadcrumb-item"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
			<?php }} ?>
			<li class="acgb-ebe4a7 breadcrumb-item active">正文</li>
		</ol>

		<div class="acgb-174c93 jan card card-thread">
			<div class="acgb-fd351d card-body">
				<div class="acgb-a41380 media">
					<div class="acgb-b131e6 media-body">
						
						<h1 class="acgb-b5b0ee break-all"><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h1>
						
						<div class="acgb-e09172 d-flex justify-content-between small">
							<div>
								<span class="acgb-f7afdb username">
									<a href="#" class="acgb-156e9f text-muted font-weight-bold"><?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?></a>
								</span>
								<span class="acgb-26ad87 date text-grey ml-2"><i class="acgb-2b1cc5 jan-icon-clock-1"></i><?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?></span>
								<span class="acgb-f4fb90 text-grey ml-2"><i class="acgb-1e8eff jan-icon-eye-4"></i><?php echo(isset($gdata['views']) ? $gdata['views'] : ''); ?></span>
							</div>
						</div>
					</div>
				</div>
				<hr class="acgb-39327f jan-hr-1">
				<div class="acgb-100569 message break-all">

				<div id="content" style="padding: 0 0 8px;">
                 <p>如果您是小白,不会解压,分包解压等转区,英文路径请勿下载,没有任何指导</p>
                 <!--<div class="acgb-749090 notice">
                  <a href="https://acgk.cc/" target="_blank" rel="noopener noreferrer">
                      本站体验不佳,点我前往新网站,账号密码同步,无需注册即可登录,同步数据
                  </a>
                  </div>-->
                <?php echo(isset($gdata['content']) ? $gdata['content'] : ''); ?>
                <br /><br /><p>
<?php if (isset($gdata['favorites'])) { ?>
<style>
        .content_favorites {
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background-color: #f9f9f9; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
        }

        #favorites_do {
            margin-right: 5px; 
            color: #ffcc00;
        }

        #favorites_html {
            font-weight: bold; 
        }

        #favorites_count {
            color: #888;
        }
</style>
<div class="acgb-ead73c content_favorites">
    <?php if ($gdata['has_favorites']) { ?>
    <i id="favorites_do" class="acgb-044cd4 fa fa-star" aria-hidden="true"><font id="favorites_html">取消收藏</font></i> 
    <?php }else{ ?>
    <i id="favorites_do" class="acgb-1980eb fa fa-star-o" aria-hidden="true"><font id="favorites_html">加入收藏</font></i>
    <?php } ?>
</div>
<script type="text/javascript">
    $("#favorites_do").click(function () {
        $.getJSON("<?php echo(isset($gdata['favorites_url']) ? $gdata['favorites_url'] : ''); ?>", function(data){
            if(data.err){
                alert(data.msg);
            }else{
                if( data.has_favorites == 1 ){
                    $("#favorites_do").attr("class", "fa fa-star");
                    $("#favorites_html").html("取消收藏");
                }else{
                    $("#favorites_do").attr("class", "fa fa-star-o");
                    $("#favorites_html").html("加入收藏");
                }
                $("#favorites_count").html(data.favorites_count);
                alert(data.msg);
            }
        });
    });
</script>
<?php } ?></p><br /><br /><br /></div>
				
<?php if ($gdata['tag_arr']) { ?>
	<ul id="tag">
		<li>标签:</li><?php if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?><li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="查看标签为《<?php echo(isset($v['name']) ? $v['name'] : ''); ?>》的所有文章"><i class="acgb-ba17a6 icon-tag"></i><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
	</ul><?php } ?>

</div>

		<div class="acgb-698ab8 plugin d-flex justify-content-center mt-3">
							
		<div class="acgb-919e30 haya-post-like px-2">
			<span class="acgb-cfa908 btn-group" role="group">
						<a href="javascript:;" id="likes_do" class="acgb-15f007 btn btn-outline-secondary js-haya-post-like-thread-tip" data-pid="<?php echo(isset($gdata['id']) ? $gdata['id'] : ''); ?>"><i class="acgb-a8d6d4 icon icon-thumbs-o-up" aria-label="点赞文章"></i>
						<span class="acgb-6c3310 haya-post-like-thread-btn">点赞</span></a>

<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery.min.js?1.0"></script>
<script type="text/javascript">
$("#likes_do").click(function () {
    $.getJSON("<?php echo(isset($gdata['likes_url']) ? $gdata['likes_url'] : ''); ?>", function(data){
        if(data.err){
            alert(data.msg);
        }else{
            $("#likes_count").html(data.likes_count);
            alert(data.msg);
        }
    });
});

$(document).ready(function(){
        $('#likes_do').click(function(){
            // 获取当前点赞数量
            var countElement = $(this).siblings('button').find('.haya-post-like-thread-user-count');
            var currentLikes = parseInt(countElement.text());

            // 这里假设点赞成功，直接更新前端数量
            currentLikes++;
            countElement.text(currentLikes);
        });
    });
</script>
				<button class="acgb-35ba19 btn btn-outline-secondary" title="点赞数量" data-tid="<?php echo(isset($gdata['id']) ? $gdata['id'] : ''); ?>">
					<span class="acgb-009ba8 haya-post-like-thread-user-count"><?php echo(isset($gdata['likes']) ? $gdata['likes'] : ''); ?></span>
				</button>
				
			</span>
		</div>
			<button type="button" onclick="clickBut('content')" class="acgb-1cfa4a btn btn-outline-secondary js-haya-post-like-thread-tip">一键复制</button>
<script>
function clickBut(id){
var value=document.getElementById(id).innerText;
var temDom = document.createElement('input');
temDom.setAttribute('value', value);
document.body.appendChild(temDom);
temDom.select();
document.execCommand("Copy");
temDom.style.display='none';
alert('复制成功');
document.body.removeChild(temDom);
}
</script>
				</div>
			</div>
		</div>
		
	<div class="acgb-c3f6cc jan-re card card-body">
		<?php $taglikedata = 0; ?>
		<?php $data = block_taglike(array (
  'type' => '1',
  'dateformat' => 'Y-m-d',
  'limit' => '5',
)); ?>
		<?php if ($data['list']) { ?>
		<?php $taglikedata = 1; ?>
		<div class="acgb-d50584 card-title"><b>相关资源</b></div>			
				<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
				<hr class="acgb-f6c147 jan-hr-2">
				<div class="acgb-585303 relate_post">
				    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>	
                       <span style="float:right;"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
				</div>
				<?php }} ?>
		<?php } ?>
		<?php unset($data); ?>
		<?php if ($taglikedata == 0) { ?>
		<?php $data = block_list(array (
  'mid' => '2',
  'limit' => '10',
  'dateformat' => 'Y-m-d',
  'showviews' => '1',
  'life' => '120',
)); ?>
		<div class="acgb-d50584 card-title"><b>相关资源</b></div>			
				<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
				<hr class="acgb-f6c147 jan-hr-2">
				<div class="acgb-585303 relate_post">
				    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>	
                       <span style="float:right;"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
				</div>
				<?php }} ?>
		<?php unset($data); ?>
		<?php } ?>
   </div>
		
		<div><a role="button" class="acgb-3b672e btn btn-secondary btn-block xn-back col-lg-6 mx-auto mb-3" href="javascript:history.back();">返回</a></div>
	
	</div>
	
	<div class="acgb-f7af45 col-lg-3 d-none d-lg-block aside">

<div class="acgb-4a3d1d card card-user-info">
			<div class="acgb-b3878b m-3 text-center">
					<img class="acgb-5ed5c6 avatar-5" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/tx.png" alt="<?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?>的头像">
				<h5><?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?></h5>
			</div>
</div>

<!--<div class="acgb-58ec43 form-group">
  <form action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form" name="search" method="get">
      <div class="acgb-d52830 input-group">
        <input type="hidden" name="u" value="search-index" />
        <input type="hidden" name="mid" value="2" />
        <input type="text" class="acgb-b051c8 form-control" placeholder="关键词" name="keyword">
        <div class="acgb-885483 input-group-append">
          <button class="acgb-51afda btn btn-primary" type="submit">搜索</button>
        </div>
      </div>
  </form>
</div>-->

<div class="acgb-7a073b card">
		  <div class="acgb-abe027 card-header">作者最近文章</div>
			<div class="acgb-25494a card-body user-recent">
				<?php $data = block_list_by_uid(array (
  'mid' => '2',
  'limit' => '5',
  'dateformat' => 'Y-m-d',
)); ?>
				<ul class="acgb-7b6b71 small break-all">
					<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
					<li class="acgb-e80888 line-height-2">
						<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
					</li>
					<?php }} ?>
				</ul><?php unset($data); ?>
			</div>
</div>

</div>

		</div>
	</main>
	
	<footer class="acgb-a9e8f9 text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="acgb-e849bd container">
		<div class="acgb-a2c59a row">
			<div class="acgb-735abb col">
			<?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?>
			</div>
			<!--<div class="acgb-c0f5b0 col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-3.1.0.js?1.0"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/bootstrap.js?1.0"></script>
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/scroll.css">
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
	<a id="scroll_to_top" href="javascript:void(0);"  class="acgb-982394 mui-rightlist"  title="返回顶部" style="display: none;"><i class="acgb-4dd66e icon-angle-double-up"></i></a>
</div>

<script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>

<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/js/jquery.js"></script>
<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/layer/layer.js"></script>
<script>
    $('#sj_kami_buy').on('click',function (e){
        var aid=$(this).attr('data-id');
        var golds=$(this).attr('data-golds');
        $.get('/my-userinfo.html','',function(data){
            if(!data.err){
                layer.confirm('您当前有 <span style="color: red">'+data.data.golds+' </span>金币。本次购买需消耗 <span style="color: red">'+golds+' </span>金币!<br/>确认进行购买操作？', {
                  btn: ['确定','取消'],icon:3
                }, function(){
                    $.post('/my-userinfo.html',{id:aid},function(data){
                        if(!data.err){
                            layer.alert(data.msg,{icon:1},function (e){
                                window.location.reload()
                            })
                        }else{
                            layer.alert(data.msg,{icon:2})
                        }
                    },'json');
                });
            }else{
                layer.msg('余额获取失败！')
            }
        },'json');
    })
</script>
<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui/layers-V2.8.js"></script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>
