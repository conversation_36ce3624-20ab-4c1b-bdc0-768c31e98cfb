<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_show($conf) { global $run, $_show, $_user; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $show_prev_next = isset($conf['show_prev_next']) && (int)$conf['show_prev_next'] ? true : false; $prev_next_cid = isset($conf['cid']) ? (int)$conf['cid'] : intval($_GET['cid']); $field_format = _int($conf, 'field_format', 0); $pageoffset = _int($conf, 'pageoffset', 5); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_show'); $cache_key = $life ? md5('global_show'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; if($mid == 1) return FALSE; $uid = isset($_user['uid']) ? (int)$_user['uid'] : 0; $run->cms_content_data->table = 'cms_'.$run->_var['table'].'_data'; $run->cms_content->format($_show, $mid, $dateformat, 0, 0, $field_format); $id = &$_show['id']; $_show['comment_url'] = $run->cms_content->comment_url($run->_var['cid'], $id); $_show['views_url'] = $run->_cfg['webdir'].'index.php?views--cid-'.$run->_var['cid'].'-id-'.$id; $data = $run->cms_content_data->get($id); if($data){ if($field_format && plugin_is_enable('models_filed')){ $models_field = $run->models_field->user_defined_field($mid); $run->models_field->field_val_format($models_field, $data, 0); } $_show += $data; $page = max(1,(int)R('page','G')); $_show = $run->cms_content_data->format_content($_show, $page); if( isset($_show['content_page']) && isset($_show['maxpage']) ){ $_show['pages'] = paginator::$page_function($page, $_show['maxpage'], $run->cms_content->content_url($_show, $mid, TRUE), $pageoffset); }else{ $_show['pages'] = false; } }else{ $_show['pages'] = false; } $run->cms_content_views->table = 'cms_'.$run->_var['table'].'_views'; $views_data = $run->cms_content_views->get($id); if($views_data){ if( empty($run->_cfg['close_views']) ){ $_show['views'] = $views_data['views']+1; $run->cms_content_views->update_views($id); }else{ $_show['views'] = $views_data['views']; } }else{ $_show['views'] = 1; empty($run->_cfg['close_views']) && $run->cms_content_views->set($id, array('views'=>1,'cid'=>$_show['cid'])); } if(isset($_show['filenum']) && !empty($_show['filenum'])){ list($attachlist, $imagelist, $filelist) = $run->cms_content_attach->attach_find_by_id($run->_var['table'], $id, array('id'=>$id, 'isimage'=>0)); $_show['filelist'] = $filelist; if($_show['uid'] == $uid && $uid){ $file_delete = true; }else{ $file_delete = false; } $_show['filelist_html'] = $run->cms_content_attach->file_list_html($filelist, $mid, $file_delete); }else{ $_show['filelist'] = array(); $_show['filelist_html'] = ''; } if( isset($run->_cfg['le_content_img_seo']) && !empty($run->_cfg['le_content_img_seo']) ){ $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/"; preg_match_all($pattern, $_show['content'], $match); $find_arr = array('{title}', '{cate_name}', '{webname}', '{count}'); if( isset($match[0]) ){ $img_count = 1; foreach ($match[0] as $k=>$img){ $replace_arr = array("{$_show['title']}", "{$run->_var['name']}", "{$run->_cfg['webname']}", $img_count); $new_alt = str_replace($find_arr, $replace_arr, $run->_cfg['le_content_img_seo']); if( stripos($img, "alt=") != false ){ $img_new = preg_replace('/alt=["\'](.*?)["\']/', 'alt="'.$new_alt.'"', $img); }else{ $img_new = str_replace_once('<img', '<img alt="'.$new_alt.'" ', $img); } if( stripos($img_new, "title=") != false ){ $img_new = preg_replace('/title=["\'](.*?)["\']/', '', $img_new); } if( strpos($img_new, $run->_cfg['webdomain']) === false ){ $img_new = str_replace_once('<img', '<img rel="external nofollow" referrerpolicy="no-referrer" ', $img_new); } $_show['content'] = str_replace_once($img, $img_new, $_show['content']); $img_count++; } unset($match[0]); } unset($find_arr); } if($show_prev_next) { if($prev_next_cid){ $prev_where = array('cid'=>$prev_next_cid, 'id'=>array('<'=> $id)); $next_where = array('cid'=>$prev_next_cid, 'id'=>array('>'=> $id)); }else{ $prev_where = array('id'=>array('<'=> $id)); $next_where = array('id'=>array('>'=> $id)); } $_show['prev'] = $run->cms_content->list_arr($prev_where, 'id', -1, 0, 1, 1, $extra); if($_show['prev']){ $_show['prev'] = current($_show['prev']); $run->cms_content->format($_show['prev'], $mid, $dateformat); } $_show['next'] = $run->cms_content->list_arr($next_where, 'id', 1, 0, 1, 1, $extra); if($_show['next']){ $_show['next'] = current($_show['next']); $run->cms_content->format($_show['next'], $mid, $dateformat); } }else{ $_show['prev'] = $_show['next'] = array(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $_show, $life); } if( isset($_show['favorites']) ){ $uid = isset($run->_user['uid']) ? (int)$run->_user['uid'] : 0; if( empty($uid) ){ $_show['has_favorites'] = 0; }else{ if( $run->favorites->find_fetch_key(array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id)) ){ $_show['has_favorites'] = 1; }else{ $_show['has_favorites'] = 0; } } $_show['favorites_url'] = $run->_cfg['webdir'].'index.php?show-favorites-mid-'.$mid.'-id-'.$id.'-uid-'.$uid.'.html'; }$le_keywords_links_setting = $run->kv->xget('le_keywords_links_setting'); if($le_keywords_links_setting){ $class = $le_keywords_links_setting['class'] == '' ? '' : ' class="'.$le_keywords_links_setting['class'].'"'; $target = $le_keywords_links_setting['target'] == 0 ? '' : ' target="_blank"'; $style = $class.$target; $keywords_links_arr = $run->keywords_links->find_fetch(array(), array('orderby'=>1, 'id' => 1)); if( $keywords_links_arr ){ $contentstr = $_show['content']; foreach ($keywords_links_arr as $keywords){ $patterns = '#(?=[^>]*(?=<(?!/a>)|$))'.$keywords['keyword'].'#'; $replacements = '<a href="'.$keywords['url'].'" '.$style.' title="'.$keywords['keyword'].'">'.$keywords['keyword'].'</a>'; $contentstr = preg_replace($patterns, $replacements, $contentstr, $keywords['count']); } $_show['content'] = $contentstr; unset($keywords_links_arr); } } return $_show; }
$gdata = block_global_show(array (
  'show_prev_next' => '1',
  'dateformat' => 'Y-m-d',
));
 function block_taglike($conf) { global $run, $_show; if(!isset($_show['tags']) || empty($_show['tags'])) return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $type = isset($conf['type']) ? (int)$conf['type'] : 1; $type = $type <= -1 ? -1 : (int)$type; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglike'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; $table = &$run->_var['table']; if($type == -1){ $end = array_slice($_show['tags'],-1, 1, true); $tagid = key( $end ); }elseif ($type == 0){ $tagid = array_rand($_show['tags']); }else{ $tagid = key( array_slice($_show['tags'], $type-1, 1, true) ); if( empty($tagid) ){ return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); } } $tag_name = $_show['tags'][$tagid]; $tag_url = $run->cms_content->tag_url($mid, array('tagid'=>$tagid, 'name'=>$tag_name)); if(isset($conf['mid']) && $conf['mid'] > 1 && $mid != $conf['mid'] && isset($run->_cfg['table_arr'][$conf['mid']])){ $mid = $conf['mid']; $table = $run->_cfg['table_arr'][$conf['mid']]; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $tags = $run->cms_content_tag->find_fetch(array('name'=>$tag_name), array(), 0, 1); if(empty($tags)){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }else{ $tags = current($tags); $tagid = $tags['tagid']; $tag_url = $run->cms_content->tag_url($mid, $tags); } } $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; $tag_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$tagid), array('id'=>$orderway), $start, $limit+1); $keys = array(); foreach($tag_arr as $lv) { if($lv['id'] != $_show['id']){ $keys[] = $lv['id']; } } if( empty($keys) ){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }elseif (count($keys) > $limit){ $keys = array_slice($keys, 0, $limit); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - 快萌ACG/南+社区</title>
<?php if ($gdata['seo_keywords']) { ?>
<meta name="keywords" content="<?php echo(isset($gdata['seo_keywords']) ? $gdata['seo_keywords'] : ''); ?>" />
<?php }elseif($gdata['tags']) { ?>
<meta name="keywords" content="<?php echo implode(',',$gdata['tags']); ?>" />
<?php }else{ ?>
<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
<?php } ?>
<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>">
<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1"/>
<meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width">
<meta name="apple-mobile-web-app-title" content="">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="green">
<meta name="format-detection" content="telphone=no, email=no">
<meta name="HandheldFriendly" content="true">
<meta name="screen-orientation" content="portrait">
<meta name="x5-orientation" content="portrait">
<meta name="full-screen" content="yes">
<meta name="x5-fullscreen" content="true">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="renderer" content="webkit">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/font_3913661_iikaqjykdll.js"></script>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font_3913661_iikaqjykdll.css" rel="stylesheet">
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font-awesome.min.css" rel="stylesheet">
<style type="text/css">
.iconfont {
    font-size: inherit;
}
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
.th_padding, .left_padding {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 992px) {
  .col-md-12 {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  @media only screen and (min-width: 320px) and (max-width: 992px) {
    .th_padding {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  .pagebar {
    text-align: center;
    margin-top: 15px;
  }
 }
.pagebar {
  text-align: center;
  margin-top: 15px;
}
  
  .pagebar a, .pagebar b {
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 16px;
  margin: 0px 5px;
  border: 1px solid #f0f0f5;
  color: #666;
  padding: 0px 8px;
  border-radius: 2px;
  box-sizing: border-box;
}
  .pagebar b {
  background: #7cbeff;
  border: 1px solid #7cbeff;
  color: #fff;
  font-weight: normal;
}
</style>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/style.css?2024" rel="stylesheet">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/jquery.min.js"></script>
<div class="tdacg-0cd2f1 ui progress" style="position: fixed; top: 0; left: 0; z-index: 999; width: 100vw;">
  <div id="page-reading-percent" class="tdacg-0b16ae percent" style="width: 0;"></div>
</div>
<script>
    $(function (){
         $(document).scroll(function (){
             let height = $(this).height() - $(window).height();
             let top = $(this).scrollTop();
             let percent = top / height * 100;
             $("#page-reading-percent").width(percent.toFixed(2) + "%");
        });
    });
     function scrollToTop(){
        $("html, body").animate({ scrollTop:0 }, 500);
    }
     function showModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("show");
        setTimeout(() => {
            modalPageDiv.addClass("open");
        }, 50);
    }
     function hideModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("close");
        setTimeout(() => {
            modalPageDiv.removeClass("show");
            modalPageDiv.removeClass("open");
            modalPageDiv.removeClass("close");
        }, 500);
    }
</script>
<div class="tdacg-e65031 ui modal-page right" id="modal-page"> 
   <div class="tdacg-39ae83 modal-background" onclick="hideModal()"></div>
   <div class="tdacg-0f04ec modal-container padding">
    <div class="tdacg-d0bab5 ui flex-item padding">
      <div class="tdacg-2df666 center"></div>
      <div class="tdacg-59f765 end">
        <div class="tdacg-14d889 ui button circle outline" onclick="hideModal()"><i class="tdacg-07322e iconfont icon-close"></i></div>
      </div>
    </div>
     <div class="tdacg-1ee7ec panel-block">
      <div class="tdacg-4209da title">导航菜单</div>
      <div class="tdacg-b77a93 ui tree linear-split" style="padding: 20px 0;"> <?php $data = block_navigate(array (
)); ?>
        <ul>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold"><a href="/" class="tdacg-fbdb65 padding-half">首页</a></div>
            </div>
          </li>
          <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="tdacg-fbdb65 padding-half"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></div>
            </div>
          </li>
          <?php }} ?>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold">
               <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="tdacg-fbdb65 padding-half">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="tdacg-fbdb65 padding-half">登录/注册</a>
                <?php } ?></div>
            </div>
          </li>
        </ul>
        <?php unset($data); ?> </div>
    </div>
  </div>
</div>
<header>
  <div class="tdacg-8dca09 container auto-margin ui flex-item"> <a class="tdacg-2f82ca start web-logo" href="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); ?>"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"></a> <a class="tdacg-27ac43 start web-name padding-left" href="/">快萌ACG</a>
<div class="tdacg-e47538 search center padding-left justify-center">
      <div class="tdacg-7f0202 ui input radius">
        <input type="text" placeholder="前往acgk.cc搜索,本站待修复">
        <div class="tdacg-36a14a ">
          <div class="tdacg-09d874 ui button clear" ><i class="iconfont icon-sousuo"></i></div>
        </div>
      </div>
    </div>
    <?php $data = block_navigate(array (
)); ?>
    <div class="tdacg-43735b end justify-center">
      <nav> <a class="tdacg-96319d item" href="/">首页</a> <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> <a class="tdacg-0a824c item" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?> 
      <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="tdacg-1f54da item">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="tdacg-1f54da item">登录/注册</a>
      <?php } ?>
      </nav>
    </div>
    <?php unset($data); ?>
    <div class="tdacg-614bc4 end ext-menu justify-center">
      <div class="tdacg-a2b9b7 ui button circle" onclick="showModal()"><i class="tdacg-3805f5 fa fa-list"></i></div>
    </div>
  </div>
</header>



<div class="tdacg-9937df detail-page">
  <main>
    <div class="tdacg-ed8148 article-title ui flex-item padding-start padding-end">
      <div class="tdacg-aca046 block text-large"> 资源 </div>
      <div class="tdacg-d437fc center padding-left">
        <div class="tdacg-6b2b86 ui flex-item">
          <h1 class="tdacg-595b32 center text-bigger"><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h1>
          <div class="tdacg-59f765 end"> <a class="tdacg-a9da71 ui button tiny clear text-small"><i class="tdacg-67d690 iconfont icon-Share"></i> 分享</a> <a class="tdacg-a9da71 ui button tiny clear text-small"><i class="tdacg-6fb478 iconfont icon-jubao"></i> 举报</a> </div>
        </div>
        <div class="tdacg-313f9b ui flex-item padding-top-half">
          <div class="tdacg-7ed4a6 start">
            <div class="tdacg-478c23 ui avatar tiny circle"><img src="assets/images/face.jpg" alt=""></div>
          </div>
          <div class="tdacg-8b9dd2 start justify-center padding-left-half"><?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?></div>
          <div class="tdacg-546ba6 center medium-text justify-center padding-left">时间：<?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?> 阅读：<?php echo(isset($gdata['views']) ? $gdata['views'] : ''); ?></div>
          <div class="tdacg-43735b end justify-center">
            <div class="tdacg-a4500a danger-background text-small radius" style="padding:3px 5px; line-height: 1em;"><i class="tdacg-a62f8e fa fa-warning"></i> 老资源可能失效,选择下载！！</div>
          </div>
        </div>
      </div>
    </div>
     <style>
      img{width:100%;height:auto;display:block;clip-path:inset(0 0 25% 0)}.notice{background:linear-gradient(135deg,#007bff,#00c6ff);color:white;border-radius:10px;padding:20px;text-align:center;margin:20px 0;box-shadow:0 4px 15px rgba(0,123,255,0.5);transition:transform 0.3s}.notice a{color:white;text-decoration:none;font-size:18px;font-weight:bold;display:inline-block;padding:10px 20px;border-radius:5px;background-color:rgba(255,255,255,0.2);transition:background-color 0.3s}.notice a:hover{background-color:rgba(255,255,255,0.4);text-decoration:none}.notice:hover{transform:scale(1.02)}
    </style>
    <div class="tdacg-698d8a article-content">
      <div class="tdacg-6b2b86 ui flex-item">
        <div class="tdacg-bfd427 start text-bigger"><i class="tdacg-aa012c iconfont icon-qiangdadaan"></i> </div>
        <div class="tdacg-294488 start text-bigger padding-left">帖子内容</div>
      </div>
      <!-- 内容 -->
      <?php if ($_uid) { ?>
      <div class="tdacg-ef281f article-html"> 
        <div class="tdacg-749090 notice">
        <a href="https://www.777723.xyz/" target="_blank" rel="noopener noreferrer">
            本站体验不佳,点我前往新网站,账号密码同步,无需注册即可登录,同步数据
        </a>
		</div>
      <?php echo(isset($gdata['content']) ? $gdata['content'] : ''); ?> 
      <br /><br /><p>
<?php if (isset($gdata['favorites'])) { ?>
<style>
        .content_favorites {
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background-color: #f9f9f9; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
        }

        #favorites_do {
            margin-right: 5px; 
            color: #ffcc00;
        }

        #favorites_html {
            font-weight: bold; 
        }

        #favorites_count {
            color: #888;
        }
</style>
<div class="tdacg-ead73c content_favorites">
    <?php if ($gdata['has_favorites']) { ?>
    <i id="favorites_do" class="tdacg-044cd4 fa fa-star" aria-hidden="true"><font id="favorites_html">取消收藏</font></i> 
    <?php }else{ ?>
    <i id="favorites_do" class="tdacg-1980eb fa fa-star-o" aria-hidden="true"><font id="favorites_html">加入收藏</font></i>
    <?php } ?>
</div>
<script type="text/javascript">
    $("#favorites_do").click(function () {
        $.getJSON("<?php echo(isset($gdata['favorites_url']) ? $gdata['favorites_url'] : ''); ?>", function(data){
            if(data.err){
                alert(data.msg);
            }else{
                if( data.has_favorites == 1 ){
                    $("#favorites_do").attr("class", "fa fa-star");
                    $("#favorites_html").html("取消收藏");
                }else{
                    $("#favorites_do").attr("class", "fa fa-star-o");
                    $("#favorites_html").html("加入收藏");
                }
                $("#favorites_count").html(data.favorites_count);
                alert(data.msg);
            }
        });
    });
</script>
<?php } ?></p><br /><br /><br />
      </div>
      <?php }else{ ?>请先 <a href="/user-login.html">登录</a> 或者 <a href="/user-register.html">注册</a> 后在进行查看哦！<?php } ?>
    </div>
<?php if (isset($gdata['prev']['url']) || isset($gdata['next']['url'])) { ?>
    <div class="tdacg-ae63e7 padding ui flex-item border-top"> <?php if (isset($gdata['prev']['url'])) { ?>
      <p><span>上一篇：</span><a href='<?php echo(isset($gdata['prev']['url']) ? $gdata['prev']['url'] : ''); ?>'><?php echo(isset($gdata['prev']['title']) ? $gdata['prev']['title'] : ''); ?></a></p>
      <?php }else{ ?>
      <p><span>上一篇：</span><a>没有了</a></p>
      <?php } ?>
      <?php if (isset($gdata['next']['url'])) { ?>
      <p><span>下一篇：</span><a href='<?php echo(isset($gdata['next']['url']) ? $gdata['next']['url'] : ''); ?>'><?php echo(isset($gdata['next']['title']) ? $gdata['next']['title'] : ''); ?></a></p>
      <?php }else{ ?>
      <p><span>下一篇：</span><a>没有了</a></p>
      <?php } ?> </div>
    <?php } ?> 
    
    <!-- 关联内容 -->
    <div class="tdacg-b03cf5 panel-block no-prefix padding-bottom-half border-top">
      <div class="tdacg-1fba49 title ui flex-item" style="font-weight: normal;">
        <div class="tdacg-7ed4a6 start"><i class="tdacg-1fd73e iconfont icon-receipt"></i></div>
        <div class="tdacg-8b50ce center padding-left-half">相关内容</div>
      </div>
      <?php $data = block_taglike(array (
  'type' => '0',
  'limit' => '12',
  'dateformat' => 'Y-m-d',
)); ?>
      <?php if ($data['list']) { ?>
      <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
      <ul class="tdacg-9912b9 text-list">
        <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"  target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></li>
      </ul>
      <?php }} ?>
      <?php } ?>
      <?php unset($data); ?> </div>
  </main>
  <!-- 侧栏 --> 
  <aside>
  <div class="tdacg-f189a9 panel-block no-prefix padding-bottom-half">
    <div class="tdacg-1fba49 title ui flex-item" style="font-weight: normal;">
      <div class="tdacg-7ed4a6 start"><i class="tdacg-1fd73e iconfont icon-receipt"></i></div>
      <div class="tdacg-8b50ce center padding-left-half">热门文章</div>
    </div>
    <div class="tdacg-fbdb65 padding-half"></div>
    <ul class="tdacg-9912b9 text-list">
		<?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '12',
  'titlenum' => '24',
)); ?>
<?php $rank = 1; ?>
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>

      <li><span class="tdacg-6c0dcf tag"><?php echo(isset($rank) ? $rank : ''); ?></span> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></li>
		<?php $rank++; ?>
		<?php }} ?>
		<?php unset($data); ?>
		
    </ul>
  </div>
<!--  <div class="tdacg-f45ccc panel-block no-prefix margin-top">
    <div class="tdacg-1fba49 title ui flex-item" style="font-weight: normal;">
      <div class="tdacg-7ed4a6 start"><i class="tdacg-1fd73e iconfont icon-receipt"></i></div>
      <div class="tdacg-8b50ce center padding-left-half">贷款产品</div>
      <div class="tdacg-941355 end text-default-size"> <a href="">更多</a> </div>
    </div>
    <div class="tdacg-c76cbb dk-list padding-top">
      <div class="tdacg-1f54da item">
        <div class="tdacg-7ed4a6 start"> <img src="assets/images/logo.jpg"> </div>
        <div class="tdacg-da054c center justify-center">
          <div class="tdacg-ba3a15 text-bold">XXXX金融</div>
          <div class="tdacg-c1115b danger-text padding-top-half">￥3-20万</div>
        </div>
        <div class="tdacg-43735b end justify-center"> <a href="https://baidu.com" target="_blank" class="tdacg-646a62 ui button round small">查看</a> </div>
      </div>
    </div>
  </div>-->
</aside>
 </div>
<script>
window.onload = function() {
    var elements = document.getElementsByClassName('hidden-password');
    Array.prototype.forEach.call(elements, function(el) {
        var time = el.getAttribute('data-time');
        var countdown = el.getElementsByClassName('countdown')[0];
        var interval = setInterval(function() {
            time--;
            countdown.innerText = time;
            if(time === 0) {
                clearInterval(interval);
                el.style.display = 'none';
            }
        }, 1000);
    });
}
</script>
<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui/layers-V2.8.js"></script>

<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/js/jquery.js"></script>
<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/layer/layer.js"></script>
<script>
    $('#sj_kami_buy').on('click',function (e){
        var aid=$(this).attr('data-id');
        var golds=$(this).attr('data-golds');
        $.get('/my-userinfo.html','',function(data){
            if(!data.err){
                layer.confirm('您当前有 <span style="color: red">'+data.data.golds+' </span>金币。本次购买需消耗 <span style="color: red">'+golds+' </span>金币!<br/>确认进行购买操作？', {
                  btn: ['确定','取消'],icon:3
                }, function(){
                    $.post('/my-userinfo.html',{id:aid},function(data){
                        if(!data.err){
                            layer.alert(data.msg,{icon:1},function (e){
                                window.location.reload()
                            })
                        }else{
                            layer.alert(data.msg,{icon:2})
                        }
                    },'json');
                });
            }else{
                layer.msg('余额获取失败！')
            }
        },'json');
    })
</script>
<footer>
  <div class="tdacg-8dca09 container auto-margin ui flex-item">
    <div class="tdacg-2df666 center">
      <?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?> <a rel="nofollow" href="https://beian.miit.gov.cn"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a> <?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?></div>
  </div>
</footer>
<!-- 随航组件 -->
<div class="tdacg-bb9d5e fixed-menu">
  <div class="tdacg-1f54da item"> 
    <!-- 此处的二维码为自动生成 -->
    <div class="tdacg-cd14a0 hover-display url-qrcode" id="pageUrlQrcode"></div>
    <i class="tdacg-75841e fa fa-qrcode"></i> </div>
  <div class="tdacg-1f54da item">
    <div class="tdacg-04200a hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="QQ"> </div>
    <i class="tdacg-c8185c fa fa-qq"></i> </div>
  <div class="tdacg-1f54da item">
    <div class="tdacg-04200a hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="微信"> </div>
    <i class="tdacg-65d1a0 fa fa-weixin"></i> </div>
  <div class="tdacg-b7188f item" onclick="scrollToTop()"><i class="tdacg-1fa263 fa fa-arrow-up"></i></div>
</div>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/qrcode.js"></script> 
<script>
    const qrcode = new QRCode("pageUrlQrcode", {
        text: window.location.href,
        width: 150,
        height: 150,
        correctLevel : QRCode.CorrectLevel.H
    });
</script>
<footer style="background-color: #f1f1f1;padding: 1px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body></html> 