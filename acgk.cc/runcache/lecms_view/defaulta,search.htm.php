<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_search($conf) { global $run, $keyword; $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $maxcount = isset($conf['maxcount']) ? (int)$conf['maxcount'] : 100000; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $extra = array('block_name'=>'block_global_search'); $mid = max(2, (int)R('mid')); if( empty($keyword) ) return array('total'=> 0, 'pages'=> '', 'list'=> array()); if(isset($run->_cfg['close_search']) && !empty($run->_cfg['close_search'])){ return array('total'=> 0, 'pages'=> '', 'list'=> array()); } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return array('total'=> 0, 'pages'=> '', 'list'=> array()); } $where = array('title'=>array('LIKE'=>$keyword)); $run->cms_content->table = 'cms_'.$table; if($run->cms_content->count() > $maxcount) return array('total'=> 0, 'pages'=> '', 'list'=> array()); $total = $run->cms_content->find_count($where); $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); $search_url = $run->urls->search_url($mid, $keyword, true); $pages = paginator::$page_function($page, $maxpage, $search_url, $pageoffset); if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); $v['subject'] = str_ireplace($keyword, '<font color="red">'.$keyword.'</font>', $v['subject']); $v['intro'] = str_ireplace($keyword, '<font color="red">'.$keyword.'</font>', $v['intro']); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } return array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); }
$gdata = block_global_search(array (
  'pagenum' => '10',
  'showcate' => '1',
  'showviews' => '1',
  'maxcount' => '99999999999999999',
  'dateformat' => 'Y-m-d',
  'pageoffset' => '3',
));
?><!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
<meta http-equiv="Cache-Control" content="no-transform"/>
<meta http-equiv="Cache-Control" content="no-siteapp"/>
<meta name="applicable-device" content="pc,mobile">
<link rel="shortcut icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.ico" />
<link rel="icon" sizes="32x32" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png">
<link rel="Bookmark" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png" />
<title>“<?php echo(isset($keyword) ? $keyword : ''); ?>”的搜索结果 - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></title>
<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>">
<meta name="description" content="飞雪ACG免费二次元聚集地,欢迎分享！！">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-v2.css?1.0">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-bbs-v2.css?1.2">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/tag.css?1.0">
<?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?>
</head>

<body>

	<header class="acgb-fec5ef navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="acgb-e849bd container">
			<button class="acgb-3ed93f navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="acgb-98211e navbar-toggler-icon"></span>
			</button>

			<a class="acgb-901cd3 navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			<a class="acgb-57f644 navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="acgb-529e13 icon-search"></i></a></a>
			<div class="acgb-cac1f4 collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				<?php $data = block_navigate(array (
)); ?>
				<ul class="acgb-dc5293 navbar-nav mr-auto">
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="acgb-d4e0a6 nav-item home  active"<?php }else{ ?> class="nav-item home"<?php } ?>><a class="nav-link" href="/"><i class="acgb-0169a5 icon-home d-md-none"></i> 首页</a></li>	
					<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="nav-item  active"<?php }else{ ?> class="nav-item"<?php } ?>>
						<a class="acgb-4df27e nav-link" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><i class="acgb-b7603e icon-circle-o d-md-none"></i><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
					</li>
					<?php }} ?>
                  	<li class="acgb-980980 nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	<?php if ($_uid) { ?>
                        <a href="/my-index.html" class="acgb-88f4ee nav-link">个人中心</a>
                    <?php }else{ ?>
                        <a href="/user-login.html" class="acgb-88f4ee nav-link">登录/注册</a>
                    <?php } ?>
                  	</li>
				</ul><?php unset($data); ?>
				<!-- 右侧：用户 -->
				<ul class="acgb-a21420 navbar-nav">
					<li class="acgb-2d9907 nav-item"><a class="acgb-454400 nav-link" href="/search/"><i class="acgb-529e13 icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="acgb-e849bd container">

<style>.mt-3, .my-3 {margin-top: 0.3rem !important;}.card.search { margin-bottom: 1.3rem !important;}</style>

<div class="acgb-a2c59a row">
	<div class="acgb-19250a col-lg-10 mx-auto">
		<div class="acgb-7a073b card">
			<div class="acgb-7024e7 card-body pb-0">
				<form action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form" name="search" method="get">
					<div class="acgb-2a38f8 input-group mb-3">
						<input type="hidden" name="u" value="search-index" />
						<input type="hidden" name="mid" value="2" />
						<input type="text" class="acgb-b051c8 form-control" placeholder="关键词" name="keyword">
						<div class="acgb-885483 input-group-append">
							<button class="acgb-b56d3c btn btn-primary" type="submit" id="submit">搜索</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<style> em{color: #c6303e !important;}</style>
			
			<?php if (empty($gdata['list'])) { ?>
			<div class="acgb-7a073b card">
			<div class="acgb-fd351d card-body">暂无结果，请换个关键字！</div>
		</div>
			<?php }else{ ?>
			<div class="acgb-918a71 card search">
			<div class="acgb-abe027 card-header">
				<ul class="acgb-b7facd nav nav-tabs card-header-tabs">
					<li class="acgb-2d9907 nav-item">
						<a class="acgb-877b85 nav-link " href="./">“<?php echo(isset($keyword) ? $keyword : ''); ?>”的搜索结果</a>
					</li>
				</ul>
			</div>
			<div class="acgb-fd351d card-body">
				<ul class="acgb-86a005 list-unstyled threadlist mb-0">
					<?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
					<li class="acgb-8e5b56 media thread tap" data-href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>">
						<div class="acgb-b131e6 media-body">
							<div class="acgb-9f213a subject break-all">
							<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
							</div>
							<div class="acgb-f523d5 d-flex justify-content-between small mt-1">
								<div>
								<span class="acgb-2ccd24 haya-post-info-username ">
								<a style="color:#748594;" href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>"><span class="acgb-e95f6f board-bg" style="border-radius: 2px;background-color: #F21120;width: 0.64rem;height: 0.64rem;display: inline-block;margin-right: 5px;"></span><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>
								<span class="acgb-87508f koox-g  hidden-sm "> • </span>
									<span class="acgb-ecd13b username text-grey mr-1"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
									<span class="acgb-28213e date text-grey"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
									</span>																		
								</div>
								<div class="acgb-67caf1 text-muted small">
									<span class="acgb-6eea17 eye comment-o ml-2 hidden-sm d-none"><i class="acgb-6f23e8 jan-icon-fire-1"></i><?php echo(isset($v['views']) ? $v['views'] : ''); ?></span>
									<span class="acgb-8ab4e8 likes comment-o ml-2"><i class="acgb-39fd7e icon icon-thumbs-o-up" aria-label="点赞"></i><?php echo(isset($v['likes']) ? $v['likes'] : ''); ?></span>
								</div>
							</div>
						</div>
					</li>
					<div class="acgb-cd7489 jan-hr"></div>
					<?php }} ?>										
				</ul>
			</div>
			
		</div>
		<div class="acgb-14968a pagenav ajax-pag"><?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?></div>
		<?php } ?>
<style>
.pagenav.ajax-pag {text-align:center;margin:5px 0px 5px;}
.pagenav.ajax-pag a {padding:15px;}
</style>

</div>
		
		</div>
	</div>
	</main>

	<footer class="acgb-a9e8f9 text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="acgb-e849bd container">
		<div class="acgb-a2c59a row">
			<div class="acgb-735abb col">
			<?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?>
			</div>
			<!--<div class="acgb-c0f5b0 col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-3.1.0.js?1.0"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/bootstrap.js?1.0"></script>
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/scroll.css">
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
<a id="scroll_to_top" href="javascript:void(0);"  class="acgb-982394 mui-rightlist"  title="返回顶部" style="display: none;"><i class="acgb-4dd66e icon-angle-double-up"></i></a>
</div>

<script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>