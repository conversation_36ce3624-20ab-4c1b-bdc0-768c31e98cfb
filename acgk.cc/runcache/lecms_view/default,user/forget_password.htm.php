<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="renderer" content="webkit">
  <link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/frontend.min.css" media="all">
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/user.css" media="all">
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
  <!--[if lt IE 9]>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/html5shiv.js"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/respond.min.js"></script>
  <![endif]-->
  <script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js" charset="utf-8"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
</head>
<body>
<nav class="jgacg-dc9e93 navbar navbar-white navbar-fixed-top" role="navigation">
  <div class="jgacg-e849bd container">
    <div class="jgacg-ecb9eb navbar-header">
      <button type="button" class="jgacg-808760 navbar-toggle" data-toggle="collapse" data-target="#header-navbar">
        <span class="jgacg-27673d sr-only"><?php echo '切换'; ?></span>
        <span class="jgacg-82f9ad icon-bar"></span>
        <span class="jgacg-82f9ad icon-bar"></span>
        <span class="jgacg-82f9ad icon-bar"></span>
      </button>
      <a class="jgacg-c91a04 navbar-brand" href="/"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>
    </div>
    <div class="jgacg-0ee60b collapse navbar-collapse" id="header-navbar">
      <ul class="jgacg-f2cbb6 nav navbar-nav navbar-right">
        <li><a href="/" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"><?php echo '首页'; ?></a></li>
      </ul>
    </div>
  </div>
</nav>

<main class="jgacg-1bc3a4 content">
  <div id="content-container" class="jgacg-e849bd container">
    <div class="jgacg-3f26a6 user-section login-section">
      <div class="jgacg-8972ea logon-tab clearfix">
        <a class="jgacg-9a3bf6 active" title="<?php echo '忘记密码'; ?>" rel="nofollow"><?php echo '忘记密码'; ?></a><?php if ($cfg['open_user_login']) { ?><a href="<?php echo(isset($login_url) ? $login_url : ''); ?>" title="<?php echo '登录'; ?>" rel="nofollow"><?php echo '登录'; ?></a><?php } ?>
      </div>
      <div class="jgacg-42ffbd login-main">
        <p style="font-size: 14px;color: red;">邮件发送成功,请查看邮件垃圾箱,有可能在那里</p>
        <p style="font-size: 14px;color: red;">如果邮件发送失败,请联系发卡网在线客服处理！！</p>
        <form id="login-form" class="jgacg-7e1ec8 form-horizontal layui-form" action="index.php?user-forget.html" method="post">
          <input type="hidden" name="FORM_HASH" value="<?php echo(isset($form_hash) ? $form_hash : ''); ?>" />
          <div class="jgacg-58ec43 form-group">
            <label for="username" class="jgacg-659a73 col-sm-4 control-label"><?php echo '用户名'; ?></label>
            <div class="jgacg-190d23 col-sm-8">
              <input class="jgacg-aba265 form-control" id="username" type="text" name="username" value="" placeholder="<?php echo '请输入用户名'; ?>" autocomplete="off">
            </div>
          </div>

          <div class="jgacg-58ec43 form-group">
            <label for="email" class="jgacg-659a73 col-sm-4 control-label"><?php echo '邮箱'; ?></label>
            <div class="jgacg-190d23 col-sm-8">
              <input class="jgacg-4067bb form-control" id="email" type="email" name="email" value="" placeholder="<?php echo '邮箱'; ?>" autocomplete="off">
            </div>
          </div>
          <div class="jgacg-58ec43 form-group">
            <label for="vcode" class="jgacg-659a73 col-sm-4 control-label"><?php echo '验证码'; ?></label>
            <div class="jgacg-d44acc col-sm-4">
              <input class="jgacg-c70a58 form-control" id="vcode" type="text" name="vcode" value="" placeholder="<?php echo '验证码'; ?>" autocomplete="off">
            </div>
            <div class="jgacg-d44acc col-sm-4">
              <img src="index.php?user-vcode-name-forgetvcode.html" alt="<?php echo '验证码'; ?>" onclick="this.src='index.php?user-vcode-name-forgetvcode-r-'+Math.random();" id="vcodeimg" style="width: 100%;" />
            </div>
          </div>

          <div class="jgacg-58ec43 form-group">
            <label class="jgacg-61412c col-sm-3 control-label"></label>
            <div class="jgacg-4aab75 col-sm-9">
            <button type="submit" class="jgacg-f2dc0b btn btn-primary btn-lg btn-block" lay-submit lay-filter="form"><?php echo '发送邮件'; ?></button>
            </div>
          </div>
          

        </form>
      </div>
    </div>
  </div>
</main>

<footer class="jgacg-bb0985 footer" style="clear:both">
  <p class="jgacg-3f0d57 copyright">Copyright&nbsp;©&nbsp;<?php echo date('Y'); ?> <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?> All Rights Reserved.</p>
</footer>
<script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    form.on('submit(form)', function (data) {
      data = data.field;
      if (data.username == '') {
        layer.msg('<?php echo '请输入用户名'; ?>', {icon: 5});
        return false;
      }else if (data.email == '') {
        layer.msg('<?php echo '邮箱不能为空'; ?>', {icon: 5});
        return false;
      }else if (data.vcode == '') {
        layer.msg('<?php echo '验证码不能为空'; ?>', {icon: 5});
        return false;
      }else{
        $.post("index.php?user-forget-ajax-1",data,function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location.reload(); }, 1000);
          return false;
        },'json');
        return false;
      }
    });
  });
</script>
</body>
</html>