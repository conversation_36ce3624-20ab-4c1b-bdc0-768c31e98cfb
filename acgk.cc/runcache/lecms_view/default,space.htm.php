<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_space($conf) { global $run; $mid = _int($conf, 'mid', 2); $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_space'); $cache_key = $life ? md5('global_space'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $uid = &$run->_var['uid']; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $run->cms_content->table = 'cms_'.$table; $where = array('uid' => $uid); $total = $run->cms_content->find_count($where); $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); $pages = paginator::$page_function($page, $maxpage, $run->urls->space_url($uid, TRUE), $pageoffset); if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
$gdata = block_global_space(array (
  'pagenum' => '10',
  'dateformat' => 'Y-m-d',
  'showviews' => '1',
));
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $table = isset($run->_cfg['table_arr'][$mid]) ? $run->_cfg['table_arr'][$mid] : 'article'; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('tagid', 'count', 'orderby')) ? $conf['orderby'] : 'count'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = isset($conf['limit']) ? (int)$conf['limit'] : 10; $cms_limit = _int($conf, 'cms_limit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_taglist'); $cache_key = $life ? md5('taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $run->cms_content_tag->table = 'cms_'.$table.'_tag'; if($cms_limit){ $run->cms_content->table = 'cms_'.$table; $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; } $where = array(); $list_arr = $run->cms_content_tag->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } if($cms_limit){ $tag_data_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$v['tagid']), array('id'=>-1), 0, $cms_limit); $keys = array(); foreach($tag_data_arr as $lv) { $keys[] = $lv['id']; } $cms_arr = $run->cms_content->mget($keys); foreach($cms_arr as &$cv) { $run->cms_content->format($cv, $mid); } $v['cms'] = $cms_arr; unset($cms_arr); } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?><!doctype html>
<html lang="zh-Hans">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="ie=edge" />
	<meta name="generator" content="acg" />
	<meta name="renderer" content="webkit">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
	<link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
	<link rel="stylesheet" href="//cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css" media="all">
	<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/style.css" type="text/css" media="all"/>
	<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js"></script>
	<link href="/css/home.css" rel="stylesheet" type="text/css">
	<script type="text/javascript">
		window._LE = {
			uri: "<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>",
			uid: "<?php echo(isset($_uid) ? $_uid : ''); ?>",
			parseurl: "<?php echo(isset($_parseurl) ? $_parseurl : ''); ?>"
		};
	</script>
	<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>script/main.js"></script>
</head>
<body>
<div class="jgacg-a6948a topmenu" id="tophead">
  <div class="jgacg-56e85d wrap">
    <div id="mobilemenu"></div>
    <div class="jgacg-c921c0 mask"></div>
    <!--<div class="jgacg-7b53a8 logo"><a href="/"><img src="次元库" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" width="280" height="70"></a></div>-->
    <?php $data = block_navigate(array (
)); ?>
    <div class="jgacg-f7604b menu">
      <ul id="nav">
        <li class="jgacg-65014f closex"><i class="jgacg-e835d3 iconfont icon-guanbi"></i></li>
        <li class="jgacg-bf3200 mainlevel"><a href="/"<?php if (empty($cfg_var['topcid'])) { ?> class="hover"<?php } ?>>首页</a></li>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
        <li class="jgacg-bf3200 mainlevel"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"<?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="hover"<?php } ?>><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
        <div class="jgacg-d3c3da clear"></div>
      </ul>
    </div><?php unset($data); ?>
   <div class="jgacg-304597 search" style="margin-right: 80px;width: 150px;">
    <?php if ($_uid) { ?>
        <a href="/my-index.html" class="jgacg-a87964 personal-center">个人中心</a>
    <?php }else{ ?>
        <a href="/user-login.html" class="jgacg-f8f8b5 login-register">登录/注册</a>
    <?php } ?>
</div>

<style>
    .search a {
        display: inline-block; 
        padding: 10px 20px; 
        border-radius: 5px; 
        text-decoration: none; 
        font-size: 16px; 
        font-weight: bold; 
        transition: background-color 0.3s, transform 0.3s; 
    }

    .personal-center {
        background-color: rgba(45, 191, 191, 0.8);
        color: white;
    }

    .login-register {
        background-color: #ff7f50;
        color: white;
    }

    .search a:hover {
        transform: scale(1.05);
    }

    .personal-center:hover {
        background-color: rgba(45, 191, 191, 1);
    }

    .login-register:hover {
      background-color: #ff6347; }
</style>
    <div class="jgacg-c144ef search"><i class="jgacg-d83b34 iconfont icon-sousuo"></i></div>
  </div>
</div>
<p class="jgacg-14dfcd pheaderpad"></p>

<section>
	<div class="jgacg-c9bff4 container th_margintop">
		<div class="jgacg-a2c59a row">
			<!--左侧用户文章列表start-->
			<div class="jgacg-b0cc27 col-md-9 col-xs-12">
				<div>
					<div class="jgacg-9b6aa8 thjingxuan">
						<div class="jgacg-69579a thjingxuan_title categorylist"><i></i>
							<a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>">首页</a><span>/</span> <a href="<?php echo(isset($cfg_var['user_url']) ? $cfg_var['user_url'] : ''); ?>"><?php echo(isset($cfg_var['author']) ? $cfg_var['author'] : ''); ?></a>
						</div>
						
						<section class="jgacg-baf491 thjingxuan_sec">
							<?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
							<div class="jgacg-d96257 col-md-12 col-xs-12 th_padding post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>">
								<div class="jgacg-936bd6 thliorder1">
									<div class="jgacg-7ef28c thnews-con">
										<div class="jgacg-2e73d7 news-con-tit"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a>
										</div>
										<div class="jgacg-bba882 thinfo">
											时间：<?php echo(isset($v['date']) ? $v['date'] : ''); ?>&nbsp;&nbsp;|&nbsp;&nbsp;阅读：<?php echo(isset($v['views']) ? $v['views'] : ''); ?>
										</div>
									</div>
								</div>
							</div>
							<?php }} ?>
							<?php if ($gdata['pages']) { ?>
							<div class="jgacg-c21d3e col-md-12 col-xs-12  th_padding">
								<div class="jgacg-4954ee list-title pagebar">
									<?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?>
								</div>
							</div>
							<?php } ?>
						</section>
						
					</div>
				</div>
			</div>
			<!--左侧用户文章列表end-->
			<div class="jgacg-53aaf2 col-md-3 col-xs-12 wap_margintop">
				<div>
					<section>
						<div class="jgacg-d5cae3 thleftcon" style="height: 335px;">
							<div class="jgacg-a37502 thleftcon-1">
								<img class="jgacg-61a635 th-img jsimg-height" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/img/tx.jpg">
								<img class="jgacg-52a66e th-img jsimg-toux" src="<?php echo(isset($cfg_var['avatar']) ? $cfg_var['avatar'] : ''); ?>">
							</div>
							<div class="jgacg-6862df thjs_infor">
								<h4><?php echo(isset($cfg_var['author']) ? $cfg_var['author'] : ''); ?></h4>
								<div class="jgacg-d3c3da clear"></div>
							</div>
							<div class="jgacg-55f5a0 thleftcon-2">
								<dl>
									<dd><?php echo(isset($cfg_var['intro']) ? $cfg_var['intro'] : ''); ?></dd>
								</dl>
							</div>
							<div class="jgacg-7538e5 aut_count">
								<ul>
									<li><span>内容</span><strong><?php echo(isset($cfg_var['contents']) ? $cfg_var['contents'] : ''); ?></strong></li>
									<li><span>积分</span><strong><?php echo(isset($cfg_var['credits']) ? $cfg_var['credits'] : ''); ?></strong></li>
									<li><span>金币</span><strong><?php echo(isset($cfg_var['golds']) ? $cfg_var['golds'] : ''); ?></strong></li>
								</ul>
							</div>
						</div>
					</section>
				</div>
				<!--文章阅读排行榜start-->
				<div class="jgacg-25b65b th_margintop">
					<?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '8',
  'titlenum' => '24',
)); ?>
					<section>
						<div class="jgacg-33bd1e thleftcon">
							<div class="jgacg-686f42 thleftbt"><span>阅读排行</span></div>
							<ul class="jgacg-8cd5fc th-5">
								<?php $rank = 1; ?>
								<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
								<li class="jgacg-1cec22 post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>"><span class="jgacg-e1992f date"><?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span><i><?php echo(isset($rank) ? $rank : ''); ?></i><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
								<?php $rank++; ?>
								<?php }} ?>
							</ul>
						</div>
					</section>
					<?php unset($data); ?>
				</div>
				<!--当前分类阅读排行榜end-->
				<!--随机文章start-->
				<div class="jgacg-25b65b th_margintop">
					<section><?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '18',
  'life' => '600',
  'showviews' => '1',
)); ?>
<div class="jgacg-33bd1e thleftcon">
	<div class="jgacg-686f42 thleftbt"><span>猜你喜欢</span></div>
	<ul class="jgacg-8cd5fc th-5">
                        <?php $rank = 1; ?>
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <li class="jgacg-1cec22 post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>"><span class="jgacg-e1992f date"><?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span><i><?php echo(isset($rank) ? $rank : ''); ?></i><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
                        <?php $rank++; ?>
                        <?php }} ?>
                    </ul>
</div>
<?php unset($data); ?></section>
				</div>
				<!--随机文章end-->
				<!--热门标签start-->
				<div class="jgacg-25b65b th_margintop">
					<section>
						<div class="jgacg-33bd1e thleftcon">
							<div class="jgacg-686f42 thleftbt"><span>热门标签</span></div>
							<ul class="jgacg-9e001c th-7">
								<?php $data = block_taglist(array (
  'mid' => '2',
  'limit' => '18',
  'orderby' => 'count',
)); ?>
								<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
								<li class="jgacg-f6715d tag_id_<?php echo(isset($v['tagid']) ? $v['tagid'] : ''); ?>"><a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
								<?php }} ?>
								<?php unset($data); ?>
							</ul>
						</div>
					</section>
				</div>
				<!--热门标签end-->
			</div>
		</div>
	</div>
</section>

<p class="jgacg-14dfcd pheaderpad"></p>
	<div>

		<div class="jgacg-89105f footer2">
		<p>COPYRIGHT © 2023 <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>. ALL RIGHTS RESERVED.  
		 	     <br/><a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow" <?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?>><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a></p>
		 	     <p>只给您分享有用的学习知识！</p>
		</div>


		<div class="jgacg-3bce60 thgotop">
			<ul>
				<li id="guan" class="jgacg-c178af ditop">
					<div class="jgacg-73b7b0 yewan">
						<i class="jgacg-dfbbd8 fa fa-circle" aria-hidden="true"></i>
						<span class="jgacg-36a14a ">我要关灯</span>
					</div>

					<div class="jgacg-987bbc baitian">
						<i class="jgacg-143cc1 fa fa-circle-o" aria-hidden="true"></i>
						<span class="jgacg-36a14a ">我要开灯</span>
					</div>
				</li>
				
				<li id="go_top" class="jgacg-c178af ditop">
					<i class="jgacg-cceff5 fa fa-arrow-up" aria-hidden="true"></i>
					<span>返回顶部</span>
				</li>
			</ul>
		</div>
		<?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?>
	</div>

<!-- 搜索框-->
<div class="jgacg-c2b9e3 search-box">
  <div class="jgacg-cf3acb search-close"><i class="jgacg-e835d3 iconfont icon-guanbi"></i></div>
  <div class="jgacg-dee095 search-con">
    <dl class="jgacg-418446 se">
      <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form">
						<dt><input type="hidden" name="u" value="search-index" />
						<input type="hidden" name="mid" value="2" />
						<input type="text" class="search-keyword" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" /></dt>
						<dd>
          <button type="submit"><i class="jgacg-d83b34 iconfont icon-sousuo"></i></button>
        </dd>
					</form>
    </dl>
  </div>
<script type="text/javascript" src="/js/jquery.min.js"></script> 
<script type="text/javascript" src="/js/swiper.min.js"></script> 
<script type="text/javascript" src="/js/slide.js"></script>
</body>
</html>
