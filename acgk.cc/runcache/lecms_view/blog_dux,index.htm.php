<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglist_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table.'_tag'; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total = $run->cms_content_tag->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.tagid FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(tagid) FROM {$table_full})) AS tagid) AS t2 WHERE t1.tagid >= t2.tagid LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['tagid'], $keys)){ $keys[] = $arr['tagid']; $i++; } } $list_arr = $run->cms_content_tag->mget($keys); }else{ $keys = array(); $sql = "SELECT tagid FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['tagid']; } $list_arr = $run->cms_content_tag->mget($keys); } $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_global_blog($conf) { global $run; $mid = isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : _int($conf, 'mid', 2); $cid = _int($conf, 'cid', 0); $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_blog'); $cache_key = $life ? md5('global_blog'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $run->cms_content->table = 'cms_'.$table; $where = array(); if($cid){ $where['cid'] = $cid; $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); if(R('control', 'G') == 'model'){ $url = $run->urls->model_url($table, $mid, TRUE); }else{ $url = $run->urls->index_url($mid, TRUE); } $pages = paginator::$page_function($page, $maxpage, $url, $pageoffset); if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { isset($v['id']) AND $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
$gdata = block_global_blog(array (
  'mid' => '2',
  'pagenum' => '15',
  'dateformat' => 'Y年m月d日',
  'pageoffset' => '3',
  'showcate' => '1',
  'showviews' => '1',
));
 function block_list($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $cids = empty($conf['cids']) ? '' : $conf['cids']; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list'); $cache_key = $life ? md5('list'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if ($cids){ $cid_arr = explode(',', $cids); $where = array('cid' => array("IN" => $cid_arr)); $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; }else{ if($cid == 0) { $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); if(empty($cate_arr)) return; $cate_name = $cate_arr['name']; $cate_url = $run->category->category_url($cate_arr); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('cate_name'=> $cate_name, 'cate_url'=> $cate_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $table = isset($run->_cfg['table_arr'][$mid]) ? $run->_cfg['table_arr'][$mid] : 'article'; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('tagid', 'count', 'orderby')) ? $conf['orderby'] : 'count'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = isset($conf['limit']) ? (int)$conf['limit'] : 10; $cms_limit = _int($conf, 'cms_limit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_taglist'); $cache_key = $life ? md5('taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $run->cms_content_tag->table = 'cms_'.$table.'_tag'; if($cms_limit){ $run->cms_content->table = 'cms_'.$table; $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; } $where = array(); $list_arr = $run->cms_content_tag->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } if($cms_limit){ $tag_data_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$v['tagid']), array('id'=>-1), 0, $cms_limit); $keys = array(); foreach($tag_data_arr as $lv) { $keys[] = $lv['id']; } $cms_arr = $run->cms_content->mget($keys); foreach($cms_arr as &$cv) { $run->cms_content->format($cv, $mid); } $v['cms'] = $cms_arr; unset($cms_arr); } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_comment_list($conf) { global $run; $id = _int($conf, 'id', 0); $uid = _int($conf, 'uid', 0); $mid = _int($conf, 'mid', 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $humandate = isset($conf['humandate']) ? ($conf['humandate'] == 1 ? TRUE : FALSE) : TRUE; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('commentid', 'id', 'dateline')) ? $conf['orderby'] : 'dateline'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcms = _int($conf, 'showcms', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_comment_list'); $cache_key = $life ? md5('comment_list'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($mid == 1){return array('list'=> array());} $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array('mid'=>$mid); if($id) $where['id'] = $id; if($uid) $where['uid'] = $uid; $list_arr = $run->cms_content_comment->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); $keys = array(); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content_comment->format($v, $dateformat, $humandate); if( $showcms && !in_array($v['id'], $keys) ){ $keys[] = $v['id']; } $v['xuhao'] = $xuhao; $xuhao++; } if($showcms && $keys){ $run->cms_content->table = 'cms_'.$table; $content_list_arr = $run->cms_content->mget($keys); foreach($content_list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); } $content_key = 'cms_'.$table.'-id-'; foreach ($list_arr as &$v){ if(isset($content_list_arr[$content_key.$v['id']])){ $v['cms'] = $content_list_arr[$content_key.$v['id']]; }else{ $v['cms'] = array(); } } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_data_total($conf) { global $run; $mid = _int($conf, 'mid', 2); $source = empty($conf['source']) ? '' : $conf['source']; $showviews = _int($conf, 'showviews', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $allow_source = array('content','comment','tag','views','category'); if($source && !in_array($source, $allow_source)){ return array(); } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return array(); } $cache_key = $life ? md5('data_total'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $total = array(); switch ($source){ case 'content': $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); break; case 'comment': $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); break; case 'tag': $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); break; case 'views': $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } break; case 'category': $total['category'] = $run->category->find_count(array('mid'=>$mid)); break; default: $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); $total['category'] = $run->category->find_count(array('mid'=>$mid)); if($showviews){ $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_content_total_by_date($conf) { global $run; $mid = _int($conf, 'mid', 2); $type = isset($conf['type']) ? $conf['type'] : 'all'; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return 0; } $cache_key = $life ? md5('content_total_by_date'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return (int)$cache_data; } } $where = array(); $run->cms_content->table = 'cms_'.$table; $total = 0; switch($type) { case 'all': $where = array(); break; case 'today': $starttime = mktime(0,0,0,date('m'),date('d'),date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'yesterday': $starttime = mktime(0,0,0,date('m'),date('d')-1,date('Y')); $endtime = mktime(0,0,0,date('m'),date('d'),date('Y'))-1; $where = array('dateline'=>array('>'=>$starttime, '<='=>$endtime)); break; case 'week': $starttime = mktime(0, 0, 0, date('m'), (date('d') - (date('w')>0 ? date('w') : 7) + 1), date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'month': $starttime = mktime(0,0,0,date('m'),1,date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'year': $starttime = strtotime(date('Y',time())."-1"."-1"); $where = array('dateline'=>array('>'=>$starttime)); break; } if($where){ $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<?php 
date_default_timezone_set('PRC'); //设定时区，PRC就是中国
$hour = date('H');
 ?>
<!--如果不需要到时间暗黑切换注释下一行-->
<!--html lang="zh-CN" <?php if ($hour >= 18 || $hour < 6) { ?>class="darking"<?php } ?>-->
<html lang="zh-CN">
 <head> 
  <meta charset="UTF-8" /> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0" />
  	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="ie=edge" />
	<meta name="generator" content="acg" />
	<meta name="renderer" content="webkit">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
	<link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
  

  <link rel="stylesheet" id="wp-block-library-css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/style.min.css" type="text/css" media="all" /> 
  <link rel="stylesheet" id="style-css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/style.css" type="text/css" media="all" /> 
  <link rel='stylesheet' id='fonts-css' href='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>fonts/iconfont.css' type='text/css' media='all' />
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery.min.js" id="jquery-js"></script>  
  <style>
  /*哀悼灰色 html, body {filter:grayscale(1);}*/
  :root{--tb--main: #007bff}a.tbas{border:2px dashed #aaa;padding:40px 15px;font-size:14px;background-color:#fff;display:block;text-decoration:none;color:#888;font-weight:bold;text-align:center;}
  a.tbas:hover{border-color:#666;color:#444;}
  @media (max-width:640px){
  a.tbas{font-size:12px;padding:25px 15px;}}
  </style> 
 </head> 
 <?php if ($control=='show' && $action == 'index') { ?>  
 <body class="kaqu-8aaa1d post-template-default single single-post  single-format-standard home nav_fixed m-excerpt-cat p_indent comment-open site-layout-2 text-justify-on m-sidebar m-user-on dark-on">
 <?php }elseif($control=='cate' && $action == 'index') { ?>  
 <body class="kaqu-8f493c archive category category-tech  home nav_fixed m-excerpt-cat site-layout-2 text-justify-on m-sidebar m-user-on dark-on">
 <?php }elseif($control=='cate' && $cfg_var['mid']==1 && $action == 'index') { ?>  
 <body class="kaqu-de4fcd page-template page-template-pages page-template-no-sidebar page-template-pagesno-sidebar-php page home nav_fixed m-excerpt-cat p_indent comment-open site-layout-2 text-justify-on m-sidebar m-user-on dark-on"> 
 <?php }else{ ?>
 <body class="kaqu-fd01f9 home blog nav_fixed m-excerpt-cat site-layout-2 text-justify-on m-sidebar m-user-on dark-on"> 
 <?php } ?>  
  <header class="kaqu-396998 header"> 
   <div class="kaqu-e849bd container"> 
    <h1 class="kaqu-7b53a8 logo"><a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /><img class="-dark" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/logo-dark.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a></h1> 
    <div class="kaqu-adbc90 brand">
     欢迎光临<br />我们一直在努力
    </div> 
	<?php $data = block_navigate(array (
)); ?>	
    <ul class="kaqu-9928d5 site-nav site-navbar"> 
     <li <?php if (empty($cfg_var['topcid'])) { ?> class="kaqu-74e87b current-menu-item" <?php } ?>><a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" aria-current="page">首页</a></li>
	 <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
	 <li class="kaqu-8c7137 current-category-ancestor current-menu-ancestor <?php if ($cfg_var['topcid']==$v['cid']) { ?> current-menu-item<?php } ?> <?php if (isset($v['son'])) { ?> menu-item-has-children<?php } ?>">
      <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
	  <?php if (isset($v['son'])) { ?>
      <ul class="kaqu-7cbcd4 sub-menu"> 
        <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?> 
      </ul>
      <?php } ?>	  
	 </li> 
	 <?php }} ?>
     <li class="kaqu-2edea5 navto-search"><a href="javascript:;" class="kaqu-0ed9ad search-show"><i class="kaqu-78771f tbfa">&#xe611;</i></a></li> 
     <?php if (!is_mobile()) { ?><li class="kaqu-0ad896 sitedark" etap="darking" style="display:none;"><i class="kaqu-78771f tbfa">&#xe6a0;</i><i class="kaqu-78771f tbfa">&#xe635;</i></li> <?php } ?> 
    </ul> 
	<?php unset($data); ?>  
    <div class="kaqu-4f3bed topbar"> 
     <ul class="kaqu-2ed135 site-nav topmenu"> 
     <li><a href="/special/1.html">专题</a></li>
      <li><a href="/tags">标签云</a></li> 
      <li><a href="/archives">页面存档</a></li> 
      <li><a href="/links">友情链接</a></li> 
      <li class="kaqu-755c62 menusns menu-item-has-children"> <a href="javascript:;">关注我们</a> 
       <ul class="kaqu-7cbcd4 sub-menu"> 
        <li><a class="kaqu-da4f5a sns-wechat" href="javascript:;" title="关注微信" data-src="">关注微信</a></li> 
        <li><a target="_blank" rel="external nofollow" href="">分类一</a></li>
        <li><a target="_blank" rel="external nofollow" href="">分类二</a></li> 
       </ul> </li> 
     </ul> 
     <a rel="nofollow" target="_blank" href="<?php echo(isset($login_url) ? $login_url : ''); ?>" class="kaqu-cc959a signin-loader">Hi, 请登录</a> &nbsp; &nbsp; 
     <a rel="nofollow" target="_blank" href="<?php echo(isset($register_url) ? $register_url : ''); ?>" class="kaqu-e519de signup-loader">我要注册</a> &nbsp; &nbsp; 
     <a rel="nofollow" target="_blank" href="/user-forget.html">找回密码</a> 
    </div> 
    <?php if (!is_mobile()) { ?><a rel="nofollow" href="javascript:;" class="kaqu-6e8340 signin-loader m-icon-user"><i class="kaqu-78771f tbfa">&#xe641;</i></a><?php } ?> 
   </div> 
  </header>
  <i class="kaqu-836afd tbfa m-icon-nav">&#xe612;</i>
  <div class="kaqu-7e702f site-search"> 
   <div class="kaqu-e849bd container"> 
    <form id="search_form" method="get" target="_blank" class="kaqu-470904 site-search-form"> 
	 <input type="hidden" name="u" value="search-index" />
     <input class="search-input" name="keyword" type="text" placeholder="输入关键字" value="" required="required" /> 
     <button class="kaqu-078e95 search-btn" type="submit"><i class="kaqu-78771f tbfa">&#xe611;</i></button> 
    </form> 
   </div> 
  </div>    
  <section class="kaqu-e849bd container"> 
   <div class="kaqu-f774a2 content-wrap"> 
    <div class="kaqu-1bc3a4 content"> 
	<?php $page = R('page'); ?>
	<?php if ($page<2) { ?>
    		
	 <?php if (!is_mobile()) { ?>
     <div class="kaqu-16e370 mo-topics">
	   <ul>
		<li><a class="kaqu-ecfe92 -pic" href=""><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/special/1.jpg" alt="专题示例"><div class="kaqu-073366 -info"><dfn><i class="kaqu-78771f tbfa">&#xe60e;</i>专题示例</dfn><h2>专题示例介绍</h2></div></a></li><li><a class="kaqu-ecfe92 -pic" href=""><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/special/2.jpg" alt="专题示例"><div class="kaqu-073366 -info"><dfn><i class="kaqu-78771f tbfa">&#xe60e;</i>专题示例</dfn><h2>专题示例介绍</h2></div></a></li><li><a class="kaqu-ecfe92 -pic" href=""><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/special/3.jpg" alt="专题示例"><div class="kaqu-073366 -info"><dfn><i class="kaqu-78771f tbfa">&#xe60e;</i>专题示例</dfn><h2>专题示例介绍</h2></div></a></li>  
	  </ul>
     </div>
	 <?php } ?>
	 <?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '1',
  'dateformat' => 'm-d',
  'titlenum' => '30',
)); ?>
     <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
     <article class="kaqu-d71f12 excerpt-minic-index">
      <h2><a class="kaqu-761402 red" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank">今日观点：</a><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2>
      <p class="kaqu-dc8b77 note"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"></a></p>
     </article> 
	 <?php }} ?> 
	 <?php unset($data); ?>
	 <?php } ?>
     <div class="kaqu-602ed9 title excerpts-title"> 
      <h3> 最新发布 </h3> 
      <div class="kaqu-59b066 more">
	   <?php $data = block_taglist_rand(array (
  'limit' => '4',
)); ?> 
	   <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?> 
	   <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"  target="_blank" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"># <?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
	  <?php }} ?> 
	  <?php unset($data); ?>
      </div> 
     </div> 
	  
	 <?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
	 <!-- 本地图片大于0 作者uid为1的文章列表 -->
	 <?php if ($v['imagenum']>0 && ($v['uid']==1)) { ?>
     <article class="kaqu-352e1f excerpt excerpt-1 excerpt-full excerpt-sticky">
      <header>
       <h2><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2>
      </header>
      <a class="kaqu-9483c4 focus" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><img data-src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/thumbnail.png" class="thumb" /></a>
      <p class="kaqu-dc8b77 note">
       </p>
      <div class="kaqu-683ac4 meta">
       <span class="kaqu-839cd9 author"><img class="kaqu-8c0572 avatar" data-src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>" src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>" alt="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>" /><a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></a></span>
       <time><?php echo(isset($v['date']) ? $v['date'] : ''); ?></time>
       <a class="kaqu-914df3 cat" href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" title="更多<?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?>的文章"><i class="kaqu-78771f tbfa">&#xe60e;</i><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a> 
       <span class="kaqu-a515be pv">阅读(<?php echo(isset($v['views']) ? $v['views'] : ''); ?>)</span>
       <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#divCommentPost" etap="like" class="kaqu-40bd58 post-like"><i class="kaqu-78771f tbfa">&#xe64c;</i>评论(<span><?php echo(isset($v['comments']) ? $v['comments'] : ''); ?></span>)</a>
      </div>
     </article>
	 <!-- 作者uid大于1的文章列表 -->
	 <?php }elseif($v['uid']>10) { ?>
     <article class="kaqu-0810fa excerpt excerpt-text">
	 <header><h2><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2></header>
	 <p class="kaqu-dc8b77 note">{</p>
      <div class="kaqu-683ac4 meta">
       <span class="kaqu-839cd9 author"><img class="kaqu-8c0572 avatar" data-src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>" src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>" alt="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>" /><a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></a></span>
       <time><?php echo(isset($v['date']) ? $v['date'] : ''); ?></time>
       <a class="kaqu-914df3 cat" href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" title="更多<?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?>的文章"><i class="kaqu-78771f tbfa">&#xe60e;</i><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a> 
       <span class="kaqu-a515be pv">阅读(<?php echo(isset($v['views']) ? $v['views'] : ''); ?>)</span>
       <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#divCommentPost" etap="like" class="kaqu-40bd58 post-like"><i class="kaqu-78771f tbfa">&#xe64c;</i>评论(<span><?php echo(isset($v['comments']) ? $v['comments'] : ''); ?></span>)</a>
      </div>
	 </article>	
      <!-- 默认图文文章列表 -->	 
	 <?php }else{ ?>
     <article class="kaqu-f63c33 excerpt excerpt">
      <a class="kaqu-9483c4 focus" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><img data-src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/thumbnail.png" class="thumb" /></a>
      <header>
       <h2><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></h2>
      </header>
      <p class="kaqu-dc8b77 note"></p>
      <div class="kaqu-683ac4 meta">
       <span class="kaqu-839cd9 author"><img class="kaqu-8c0572 avatar" data-src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>" src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>" alt="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>" /><a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></a></span>
       <time><?php echo(isset($v['date']) ? $v['date'] : ''); ?></time>
       <a class="kaqu-914df3 cat" href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" title="更多<?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?>的文章"><i class="kaqu-78771f tbfa">&#xe60e;</i><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a> 
       <span class="kaqu-a515be pv">阅读(<?php echo(isset($v['views']) ? $v['views'] : ''); ?>)</span>
       <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#divCommentPost" etap="like" class="kaqu-40bd58 post-like"><i class="kaqu-78771f tbfa">&#xe64c;</i>评论(<span><?php echo(isset($v['comments']) ? $v['comments'] : ''); ?></span>)</a>
      </div>
     </article>
     <?php } ?>	 
	 <?php }} ?> 
     <div class="kaqu-ef9ed9 pages"><?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?></div> 	 
     	  
    </div> 
   </div> 
      <div class="kaqu-297768 sidebar"> 
    <div class="kaqu-ac714c widget-on-phone widget widget_ui_textorbui">
     <a class="kaqu-cb16dd style02" href="" target="_blank"><strong>吐血推荐</strong><h2>卡趣萌游次元</h2><p>卡趣游戏次元站 - 专注二次元游戏生态，实时更新新游资讯、角色攻略、抽卡模拟器，提供手游礼包与主机游戏资源下载...</p></a>
    </div>   
    <?php if ($control=='index' && $action == 'index') { ?>  
    <div class="kaqu-c84e32 widget widget-tops">
     <ul class="kaqu-7dbeae widget-nav"> 
      <li class="kaqu-eb5d92 active">网站公告</li> 
      <li>会员中心</li> 
     </ul> 
     <ul class="kaqu-8199ca widget-navcontent"> 
      <li class="kaqu-2ea172 item item-01 active"> 
       <ul> 
		 <?php $data = block_list_rand(array (
  'limit' => '5',
  'dateformat' => 'Y-m-d',
  'titlenum' => '28',
  'showcate' => '1',
)); ?> 
	     <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>	   
        <li><time><?php echo(isset($v['date']) ? $v['date'] : ''); ?></time><a target="_blank" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
         <?php }} ?> 
	     <?php unset($data); ?>
       </ul> 
	  </li> 
      <li class="kaqu-b426cd item item-02"> <h4>需要登录才能进入会员中心</h4> <p> <a target="_blank" href="<?php echo(isset($login_url) ? $login_url : ''); ?>" class="kaqu-b66d0c btn btn-primary signin-loader">立即登录</a> <a target="_blank" href="<?php echo(isset($register_url) ? $register_url : ''); ?>" class="kaqu-977949 btn btn-default signup-loader">现在注册</a> </p> </li> 
     </ul> 
    </div> 
	<?php } ?>
	<?php $data = block_list(array (
  'cid' => '1',
  'limit' => '5',
  'dateformat' => 'human_date',
  'intronum' => '160',
  'showcate' => '1',
  'showviews' => '1',
)); ?>
    <div class="kaqu-821179 widget-on-phone widget widget_ui_flash">
     <h3>快讯</h3>
     <ul>
	  <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>		 
      <li><h4><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></h4>
       <div class="kaqu-dc8b77 note">
        ​<?php echo(isset($v['intro']) ? $v['intro'] : ''); ?>
       </div><time><?php echo(isset($v['date']) ? $v['date'] : ''); ?></time>
	  </li>
      <?php }} ?> 
     </ul>
     <a class="kaqu-6a8a40 -more" href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" target="_blank">查看更多</a>
    </div>
	<?php unset($data); ?>
    <?php if ($control=='index' && $action == 'index') { ?>  	
    <div class="kaqu-31b454 widget-on-phone widget widget_ui_slider">
     <div class="kaqu-76d68f swiper-container"> 
      <div class="kaqu-688301 swiper-wrapper">
	   <?php $data = block_list_rand(array (
  'limit' => '3',
  'dateformat' => 'Y-m-d',
  'titlenum' => '28',
  'showcate' => '1',
)); ?> 
	   <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>	  
       <div class="kaqu-6bf235 swiper-slide">
        <a target="_blank" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><img src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" /></a>
       </div>
       <?php }} ?> 
	   <?php unset($data); ?>
      </div>
      <div class="kaqu-637464 swiper-pagination"></div>
      <div class="kaqu-85947f swiper-button-next swiper-button-white">
       <i class="kaqu-78771f tbfa">&#xe603;</i>
      </div>
      <div class="kaqu-81136c swiper-button-prev swiper-button-white">
       <i class="kaqu-78771f tbfa">&#xe610;</i>
      </div>
     </div>
    </div>
    <div class="kaqu-1dd868 widget widget_ui_topics">
     <h3>热门专题</h3>
     <ul class="kaqu-387eff -p2">
      <li><a href=""><img src="https://tse4.mm.bing.net/th/id/OIP.v46mufsriUbq0kp6dw-ndwHaEK?w=1093&h=615&rs=1&pid=ImgDetMain&o=7&rm=3" alt="主题专题示例" /><strong>主题专题示例</strong></a></li>
      <li><a href=""><img src="https://tse4.mm.bing.net/th/id/OIP.v46mufsriUbq0kp6dw-ndwHaEK?w=1093&h=615&rs=1&pid=ImgDetMain&o=7&rm=3" alt="主题专题示例" /><strong>主题专题示例</strong></a></li>
      <li><a href=""><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/special/3.jpg" alt="主题专题示例" /><strong>主题专题示例</strong></a></li>
      <li><a href=""><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/special/4.jpg" alt="主题专题示例" /><strong>主题专题示例</strong></a></li>
     </ul>
    </div>
    <div class="kaqu-2352e5 widget-on-phone widget widget_ui_topics">
     <h3>热门专题</h3>
     <ul class="kaqu-1d8f7e -text">
      <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><i class="kaqu-78771f tbfa">&#xe603;</i><i class="kaqu-78771f tbfa">&#xe60e;</i><strong>主题专题示例</strong></a></li>
      <li><a href=""><i class="kaqu-78771f tbfa">&#xe603;</i><i class="kaqu-78771f tbfa">&#xe60e;</i><strong>主题专题示例</strong></a></li>
      <li><a href=""><i class="kaqu-78771f tbfa">&#xe603;</i><i class="kaqu-78771f tbfa">&#xe60e;</i><strong>主题专题示例</strong></a></li>
      <li><a href=""><i class="kaqu-78771f tbfa">&#xe603;</i><i class="kaqu-78771f tbfa">&#xe60e;</i><strong>主题专题示例</strong></a></li>
     </ul>
    </div>
	<?php } ?>
    <div class="kaqu-e1e461 widget-on-phone widget widget_ui_posts">
     <h3>猜你喜欢</h3>
     <ul>
	   <?php $data = block_list_rand(array (
  'limit' => '5',
  'dateformat' => 'Y-m-d',
  'titlenum' => '28',
  'showcate' => '1',
)); ?> 
	   <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>	 
      <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><span class="kaqu-d04776 thumbnail"><img data-src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/thumbnail.png" class="kaqu-f4c865 thumb" /></span><span class="text"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></span><span class="kaqu-012699 muted"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span></a></li>
       <?php }} ?> 
	   <?php unset($data); ?>
     </ul>
    </div>
    <div class="kaqu-eb2607 widget-on-phone widget widget_ui_orbui">
     <div class="kaqu-1f54da item">
      <a class="kaqu-b4b704 tbas" href="" target="_blank">广告位，可在右侧栏任意位置，可放任何广告代码</a>
     </div>
    </div>
    <div class="kaqu-a9a73d widget-on-phone widget widget_ui_tags">
     <h3>热门标签</h3>
     <div class="kaqu-ae321e items">
	  <?php $data = block_taglist(array (
  'limit' => '30',
)); ?> 
	  <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
      <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><span><?php echo(isset($v['name']) ? $v['name'] : ''); ?></span></a> 
      <?php }} ?> 
	  <?php unset($data); ?>
     </div>
    </div>
	<?php if ($control=='index' && $action == 'index') { ?>  
    <div class="kaqu-d0fcee widget widget_ui_comments">
     <h3>最新评论</h3>
     <ul>
	  <?php $data = block_comment_list(array (
  'mid' => '2',
  'limit' => '6',
  'showcms' => '1',
)); ?>
      <?php if ($data['list']) { ?>
	  <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
      <li><a href="<?php echo(isset($v['cms']['url']) ? $v['cms']['url'] : ''); ?>#comment" title="<?php echo(isset($v['cms']['subject']) ? $v['cms']['subject'] : ''); ?>评论"><img alt="" src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>"  class="avatar avatar-50 photo" height="50" width="50" /> <strong><?php echo(isset($v['author']) ? $v['author'] : ''); ?></strong> <?php echo(isset($v['date']) ? $v['date'] : ''); ?> 说：<br /><?php echo(isset($v['content']) ? $v['content'] : ''); ?></a></li>
	  <?php }} ?>
      <?php } ?>
      <?php unset($data); ?>	  
     </ul>
    </div>
	<?php } ?>
	<?php if ($control=='show' && $action == 'index') { ?> 
    <div class="kaqu-caad98 widget widget_ui_posts">
     <h3>热门推荐</h3>
     <ul>
	  <?php $data = block_list_top(array (
  'mid' => '2',
  'cid' => '0',
  'orderby' => 'views',
  'limit' => '5',
  'titlenum' => '35',
  'dateformat' => 'Y-m-d',
)); ?>
	  <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
      <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><span class="kaqu-d04776 thumbnail"><img data-src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/thumbnail.png" class="kaqu-f4c865 thumb" /></span><span class="text"><?php echo(isset($v['intro']) ? $v['intro'] : ''); ?></span><span class="kaqu-012699 muted"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span></a></li> 
      <?php }} ?>
	  <?php unset($data); ?>	
     </ul>
    </div>
	<?php } ?>
	<?php if ($control=='index' && $action == 'index') { ?>  
    <div class="kaqu-cee5c2 widget widget_ui_statistics">
     <h3>网站统计</h3>
     <ul>
	  <?php $data = block_data_total(array (
  'mid' => '2',
  'showviews' => '1',
)); ?>
	  <li><strong>分类总数：</strong><?php echo(isset($data['category']) ? $data['category'] : ''); ?></li>
      <li><strong>文章总数：</strong><?php echo(isset($data['content']) ? $data['content'] : ''); ?></li>
      <li><strong>评论总数：</strong><?php echo(isset($data['comment']) ? $data['comment'] : ''); ?></li>
      <li><strong>标签总数：</strong><?php echo(isset($data['tag']) ? $data['tag'] : ''); ?></li>
      <li><strong>访问总数：</strong><?php echo(isset($data['views']) ? $data['views'] : ''); ?></li>
	  <?php unset($data); ?>
      <?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'today',
)); ?><li><strong>今日更新：</strong><?php echo(isset($data) ? $data : ''); ?></li><?php unset($data); ?>
      <?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'week',
)); ?><li><strong>本周更新：</strong><?php echo(isset($data) ? $data : ''); ?></li><?php unset($data); ?>
      <?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'month',
)); ?><li><strong>当月更新：</strong><?php echo(isset($data) ? $data : ''); ?></li><?php unset($data); ?>
     </ul>
    </div>
	<?php } ?>
   </div>
  </section> 
   <div class="kaqu-fa7210 branding"> 
   <div class="kaqu-e849bd container"> 
    <h2>卡趣資源網 更专业 更方便</h2> 
    <h4>支持快讯、专题、百度收录推送、人机验证、多级分类筛选器，适用于垂直站点、科技博客、个人站，扁平化设计、简洁白色、超多功能配置、会员中心、直达链接、文章图片弹窗、自动缩略图等...</h4> 
    <a target="blank" class="kaqu-457c7e btn btn-primary" href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes">关于我们<i class="kaqu-78771f tbfa">&#xe87e;</i></a>
    <a target="blank" class="kaqu-457c7e btn btn-primary" href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes">联系我们<i class="kaqu-78771f tbfa">&#xe87e;</i></a> 
   </div> 
  </div>
 <footer class="kaqu-4a1794 footer"> 
   <div class="kaqu-e849bd container"> 
    <?php if ($control=='index' && $action == 'index') { ?> 
    <div class="kaqu-37278c flinks"> 
     <strong>友情链接</strong> 
     <ul class="kaqu-5b1896 xoxo blogroll"> 
	  <?php $data = block_links(array (
)); if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
      <li>
	  <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" rel="noopener" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
	  </li> 
	  <?php }} unset($data); ?>
     </ul> 
    </div>
    <?php } ?>	
    <div class="kaqu-a04ec5 fcode">
      该块显示在网站底部版权上方，可已定义放一些链接或者图片之类的内容。 
    </div> 
    <p>Copyright&nbsp;©&nbsp;2022 All rights reserved. <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>  <a href="https://beian.miit.gov.cn" target="_blank"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a></p> 
   </div> 
  </footer> 
  <?php if ($control=='show' && $action == 'index') { ?>  
  <div class="kaqu-034c3c rewards-popover-mask" data-event="rewards-close"></div>
  <div class="kaqu-d94f32 rewards-popover">
		<h3>觉得文章有用就打赏一下文章作者</h3>
		<h5>非常感谢你的打赏，我们将继续给力更多优质内容，让我们一起创建更加美好的网络世界！</h5>
		<div class="kaqu-cd7b9d rewards-popover-item">
			<h4>支付宝扫一扫打赏</h4><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/wechat.jpg">
		</div>
		<div class="kaqu-cd7b9d rewards-popover-item">
			<h4>微信扫一扫打赏</h4><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/wechat.jpg">
		</div>
		<span class="kaqu-59ad74 rewards-popover-close" data-event="rewards-close"><i class="kaqu-78771f tbfa">&#xe606;</i></span>
  </div>
  <?php } ?>
  <div class="kaqu-db7103 karbar karbar-rb">
   <ul>
    <li><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes"><i class="kaqu-78771f tbfa">&#xe60f;</i><span>QQ咨询</span></a></li>
    <li><a href="tel:<?php echo(isset($cfg['webtel']) ? $cfg['webtel'] : ''); ?>"><i class="kaqu-78771f tbfa">&#xe679;</i><span>电话咨询</span></a></li>
    <li class="kaqu-2e6678 karbar-qrcode"><a href="javascript:;"><i class="kaqu-78771f tbfa">&#xe61e;</i><span>关注微信</span></a><span class="kaqu-82ea48 karbar-qrcode-wrap"><?php echo(isset($cfg['webweixin']) ? $cfg['webweixin'] : ''); ?><br /><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/wechat.jpg" /></span></li>
    <li><a target="_blank" href=""><i class="kaqu-78771f tbfa">&#xe68d;</i><span>在线咨询</span></a></li>
	<?php if ($control=='show' && $action == 'index') { ?>  
	<li><a href="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['url']) ? $gdata['url'] : ''); ?>#divCommentPost"><i class="kaqu-78771f tbfa">&#xe8c5;</i><span>去评论</span></a></li>
	<?php } ?>
    <li class="kaqu-24cfd2 karbar-totop"><a href="javascript:(TBUI.scrollTo());"><i class="kaqu-78771f tbfa">&#xe613;</i><span>回顶部</span></a></li>
   </ul>
  </div>
  <script>
	window.TBUI = {"www":"http:\/\/<?php echo(isset($cfg['webdomain']) ? $cfg['webdomain'] : ''); echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>","uri":"http:\/\/<?php echo(isset($cfg['webdomain']) ? $cfg['webdomain'] : ''); ?>\/view\/<?php echo(isset($cfg['theme']) ? $cfg['theme'] : ''); ?>","ver":"8.1","roll":"1","ajaxpager":"0","fullimage":"1"}
  </script>
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/loader.js" id="loader-js"></script> 
  

  

  

 </body>
</html>