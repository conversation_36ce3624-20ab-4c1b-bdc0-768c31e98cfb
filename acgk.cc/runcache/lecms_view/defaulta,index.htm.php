<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_content_total_by_date($conf) { global $run; $mid = _int($conf, 'mid', 2); $type = isset($conf['type']) ? $conf['type'] : 'all'; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return 0; } $cache_key = $life ? md5('content_total_by_date'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return (int)$cache_data; } } $where = array(); $run->cms_content->table = 'cms_'.$table; $total = 0; switch($type) { case 'all': $where = array(); break; case 'today': $starttime = mktime(0,0,0,date('m'),date('d'),date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'yesterday': $starttime = mktime(0,0,0,date('m'),date('d')-1,date('Y')); $endtime = mktime(0,0,0,date('m'),date('d'),date('Y'))-1; $where = array('dateline'=>array('>'=>$starttime, '<='=>$endtime)); break; case 'week': $starttime = mktime(0, 0, 0, date('m'), (date('d') - (date('w')>0 ? date('w') : 7) + 1), date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'month': $starttime = mktime(0,0,0,date('m'),1,date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'year': $starttime = strtotime(date('Y',time())."-1"."-1"); $where = array('dateline'=>array('>'=>$starttime)); break; } if($where){ $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_list_flag($conf) { global $run; $flag = _int($conf, 'flag', 0); $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = _int($conf, 'mid', 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list_flag'); $cache_key = $life ? md5('list_flag'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($flag == 0){ return array('list'=> array()); } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array('flag' => $flag); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('flag' => $flag, 'cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('flag' => $flag, 'cid' => $cid); } } if($table == 'page'){ return array(); } $run->cms_content_flag->table = 'cms_'.$table.'_flag'; $key_arr = $run->cms_content_flag->list_arr($where, 'id', $orderway, $start, $limit, $limit, $extra); $keys = array(); foreach($key_arr as $v) { $keys[] = $v['id']; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_global_blog($conf) { global $run; $mid = isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : _int($conf, 'mid', 2); $cid = _int($conf, 'cid', 0); $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_blog'); $cache_key = $life ? md5('global_blog'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $run->cms_content->table = 'cms_'.$table; $where = array(); if($cid){ $where['cid'] = $cid; $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); if(R('control', 'G') == 'model'){ $url = $run->urls->model_url($table, $mid, TRUE); }else{ $url = $run->urls->index_url($mid, TRUE); } $pages = paginator::$page_function($page, $maxpage, $url, $pageoffset); if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { isset($v['id']) AND $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
$gdata = block_global_blog(array (
  'mid' => '2',
  'cid' => '0',
  'showcate' => '1',
  'showviews' => '1',
  'pagenum' => '12',
  'dateformat' => 'Y-m-d',
  'pageoffset' => '3',
));
 function block_data_total($conf) { global $run; $mid = _int($conf, 'mid', 2); $source = empty($conf['source']) ? '' : $conf['source']; $showviews = _int($conf, 'showviews', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $allow_source = array('content','comment','tag','views','category'); if($source && !in_array($source, $allow_source)){ return array(); } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return array(); } $cache_key = $life ? md5('data_total'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $total = array(); switch ($source){ case 'content': $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); break; case 'comment': $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); break; case 'tag': $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); break; case 'views': $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } break; case 'category': $total['category'] = $run->category->find_count(array('mid'=>$mid)); break; default: $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); $total['category'] = $run->category->find_count(array('mid'=>$mid)); if($showviews){ $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglist_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table.'_tag'; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total = $run->cms_content_tag->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.tagid FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(tagid) FROM {$table_full})) AS tagid) AS t2 WHERE t1.tagid >= t2.tagid LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['tagid'], $keys)){ $keys[] = $arr['tagid']; $i++; } } $list_arr = $run->cms_content_tag->mget($keys); }else{ $keys = array(); $sql = "SELECT tagid FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['tagid']; } $list_arr = $run->cms_content_tag->mget($keys); } $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
<meta http-equiv="Cache-Control" content="no-transform"/>
<meta http-equiv="Cache-Control" content="no-siteapp"/>
<meta name="applicable-device" content="pc,mobile">
<link rel="shortcut icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.ico" />
<link rel="icon" sizes="32x32" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png">
<link rel="Bookmark" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png" />
<title>飞雪ACG</title>
<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
<meta name="description" content="飞雪ACG免费二次元聚集地,欢迎分享！！" />
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-v2.css?1.1">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-bbs-v2.css?1.2">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/tag.css?1.1">
<?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?>
</head>
<style type="text/css">
.tupiansa{display:flex;flex-wrap:wrap}.tupiansa img{width:100px;margin-bottom:10px;border-radius:5px;clip-path:inset(0 0 10% 0);height:80px;margin-left:2px;margin-right:2px}@media (max-width:768px){.tupiansa img{width:calc(30%);clip-path:inset(0 0 15% 0)}}
</style>
<body>

	<header class="acgb-fec5ef navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="acgb-e849bd container">
			<button class="acgb-3ed93f navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="acgb-98211e navbar-toggler-icon"></span>
			</button>

			<a class="acgb-901cd3 navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			<a class="acgb-57f644 navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="acgb-529e13 icon-search"></i></a></a>
			<div class="acgb-cac1f4 collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				<?php $data = block_navigate(array (
)); ?>
				<ul class="acgb-dc5293 navbar-nav mr-auto">
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="acgb-d4e0a6 nav-item home  active"<?php }else{ ?> class="nav-item home"<?php } ?>><a class="nav-link" href="/"><i class="acgb-0169a5 icon-home d-md-none"></i>首页</a></li>	
					<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="nav-item  active"<?php }else{ ?> class="nav-item"<?php } ?>>
						<a class="acgb-4df27e nav-link" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><i class="acgb-b7603e icon-circle-o d-md-none"></i><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
					</li>
					<?php }} ?>
                  	<li class="acgb-980980 nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	<?php if ($_uid) { ?>
                        <a href="/my-index.html" class="acgb-88f4ee nav-link">个人中心</a>
                    <?php }else{ ?>
                        <a href="/user-login.html" class="acgb-88f4ee nav-link">登录/注册</a>
                    <?php } ?>
                  	</li>
				</ul><?php unset($data); ?>
				<!-- 右侧：用户 -->
				<ul class="acgb-a21420 navbar-nav">
					<li class="acgb-2d9907 nav-item"><a class="acgb-454400 nav-link" href="/search/"><i class="acgb-529e13 icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="acgb-e849bd container">
<div class="acgb-a2c59a row">
	<div class="acgb-7a1c9a col-lg-9 main">
		<div class="acgb-772608 card card-threadlist ">
			<div class="acgb-abe027 card-header">
				<div class="acgb-b7facd nav nav-tabs card-header-tabs">
					<div class="acgb-2d9907 nav-item">
                      	<?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'today',
)); ?><div class="acgb-913fe7 today-posts" style="background-color: #fff;color: #343a40;border-radius: 5px;text-align: center;font-size: 1.9rem;transition: background-color 0.3s;font-weight: 900;">今日发布：<m style="font-size: 2.8rem;color: red;"> <?php echo(isset($data) ? $data : ''); ?> </m>篇</div><?php unset($data); ?>
						<div class="acgb-e8c5cf nav-link active">最新</div>
                      
					</div>
				</div>
			</div>
			
			<div class="acgb-fd351d card-body">
				<ul class="acgb-86a005 list-unstyled threadlist mb-0">
				<?php $data = block_list_flag(array (
  'flag' => '1',
  'limit' => '5',
  'dateformat' => 'Y-m-d',
  'showcate' => '1',
  'showviews' => '1',
  'life' => '60',
)); ?>
<?php 
$curr_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
 ?>
<?php if ($curr_page == 1) { ?>
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
					<li class="acgb-c91e66 media thread tap top_3" style="background-color: #0b0c0d;" data-href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>">
						<div class="acgb-b131e6 media-body">
							<div class="acgb-9f213a subject break-all">
								<i class="acgb-971201 icon-top-3"></i>
								<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"><span style="color: red;font-weight: bold"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></span></a>
							</div>
							<div class="acgb-f523d5 d-flex justify-content-between small mt-1">
								<div>
									<span class="acgb-2ccd24 haya-post-info-username ">
									<a style="color: #212529;text-decoration: none;background-color: #edb91d;display: inline-block;padding: 0.25em 0.4em;font-weight: 500;line-height: 1;text-align: center;white-space: nowrap;vertical-align: baseline;padding-right: 0.6em;padding-left: 0.6em;border-radius: 10rem;" href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" ><span class="acgb-e95f6f board-bg" style="border-radius: 2px;background-color: #F21120;width: 0.64rem;height: 0.64rem;display: inline-block;margin-right: 5px;"></span><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>
									<span class="acgb-f18a94 koox-g"> • </span>
									<span class="acgb-ecd13b username text-grey mr-1"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
									<span class="acgb-3a65dd date text-grey"  data-date="<?php echo(isset($v['date']) ? $v['date'] : ''); ?>"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
									</span>
								</div>
								<div class="acgb-67caf1 text-muted small">
									<span class="acgb-6eea17 eye comment-o ml-2 hidden-sm d-none"><i class="acgb-6f23e8 jan-icon-fire-1"></i><?php echo(isset($v['views']) ? $v['views'] : ''); ?></span>
									<span class="acgb-8ab4e8 likes comment-o ml-2"><i class="acgb-39fd7e icon icon-thumbs-o-up" aria-label="点赞"></i><?php echo(isset($v['likes']) ? $v['likes'] : ''); ?></span>
								</div>
							</div>
						</div>
					</li>
					<div class="acgb-cd7489 jan-hr"></div>
					<?php }} ?>
<?php } ?>
<?php unset($data); ?>

<?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
<?php 
switch ($v['cid']){
    case 1:
        $color = '#0df226';
        break;
    case 2:
        $color = '#53BEF1';
        break;
    case 3:
        $color = '#F21120';
        break;
    default:
        $color = '#53BEF1';
}
 ?>
					<li class="acgb-8e5b56 media thread tap" data-href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>">
						<div class="acgb-b131e6 media-body">
							<div class="acgb-9f213a subject break-all">
							<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"  target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
							</div>
                              <?php if ($_uid) { ?>
                              <div class="acgb-04ce20 tupiansa">
                              <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                              <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                              <?php }} ?>
                              </div>
                              <?php }else{ } ?>
							<div class="acgb-f523d5 d-flex justify-content-between small mt-1">
								<div>
									<span class="acgb-2ccd24 haya-post-info-username ">
									<a style="color: #212529;text-decoration: none;background-color: #edb91d;display: inline-block;padding: 0.25em 0.4em;font-weight: 500;line-height: 1;text-align: center;white-space: nowrap;vertical-align: baseline;padding-right: 0.6em;padding-left: 0.6em;border-radius: 10rem;" href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>"  target="_blank"><span class="acgb-ad20b6 board-bg" style="border-radius: 2px;background-color: <?php echo(isset($color) ? $color : ''); ?>;width: 0.64rem;height: 0.64rem;display: inline-block;margin-right: 5px;"></span><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>
									<span class="acgb-f18a94 koox-g"> • </span>
									<span class="acgb-ecd13b username text-grey mr-1"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
									<span class="acgb-2aa4f2 date text-grey" data-date="<?php echo(isset($v['date']) ? $v['date'] : ''); ?>"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
									</span>
								</div>
								<div class="acgb-67caf1 text-muted small">
									<span class="acgb-6eea17 eye comment-o ml-2 hidden-sm d-none"><i class="acgb-6f23e8 jan-icon-fire-1"></i><?php echo(isset($v['views']) ? $v['views'] : ''); ?></span>
									<span class="acgb-8ab4e8 likes comment-o ml-2"><i class="acgb-39fd7e icon icon-thumbs-o-up" aria-label="点赞"></i><?php echo(isset($v['likes']) ? $v['likes'] : ''); ?></span>
								</div>
							</div>
						</div>
					</li>
					<div class="acgb-cd7489 jan-hr"></div>
					<?php }} ?>
					
				</ul>
			</div>
		</div>
				
		<div class="acgb-14968a pagenav ajax-pag"><?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?></div>
<style>
.pagenav.ajax-pag {text-align:center;margin:5px 0px 5px;}
.pagenav.ajax-pag a {padding:15px;}
</style>
		
	</div>
	<div class="acgb-f7af45 col-lg-3 d-none d-lg-block aside">

		<div class="acgb-00e375 card card-site-info">
			<div class="acgb-6b19f1 m-3">
				<div class="acgb-d6be9c small line-height-3">
				<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>
				</div>
			</div>
				<div class="acgb-8bbe84 card-footer p-2">
				<?php $data = block_data_total(array (
  'mid' => '2',
  'showviews' => '1',
)); ?>
				<table class="acgb-58de42 w-100 small">
					<tr align="center">
						<td>
							<span class="acgb-4bce82 text-muted">文章数</span><br>
							<b><?php echo(isset($data['content']) ? $data['content'] : ''); ?></b>
						</td>
						<td>
							<span class="acgb-4bce82 text-muted">标签数</span><br>
							<b><?php echo(isset($data['tag']) ? $data['tag'] : ''); ?></b>
						</td>
						<td>
							<span class="acgb-4bce82 text-muted">阅读数</span><br>
							<b><?php echo(isset($data['views']) ? $data['views'] : ''); ?></b>
						</td>
					</tr>
				</table><?php unset($data); ?>
			</div>
			
		</div>

<!--<div class="acgb-58ec43 form-group">
  <form action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form" name="search" method="get">
      <div class="acgb-d52830 input-group">
        <input type="hidden" name="u" value="search-index" />
        <input type="hidden" name="mid" value="2" />
        <input type="text" class="acgb-b051c8 form-control" placeholder="关键词" name="keyword">
        <div class="acgb-885483 input-group-append">
          <button class="acgb-51afda btn btn-primary" type="submit">搜索</button>
        </div>
      </div>
  </form>
</div>-->

<!-- 主页右侧 -->
<div class="acgb-7a073b card">
		  <div class="acgb-abe027 card-header">热门推荐</div>
			<div class="acgb-25494a card-body user-recent">
				<?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '8',
  'dateformat' => 'Y-m-d',
  'showviews' => '1',
  'titlenum' => '24',
  'life' => '120',
)); ?>
				<ul class="acgb-7b6b71 small break-all">
					<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
					<li class="acgb-e80888 line-height-2">
						<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
					</li>
					<?php }} ?>
				</ul><?php unset($data); ?>
			</div>
</div>

<div class="acgb-7a073b card">
	<div class="acgb-abe027 card-header">热门标签</div>
<div id="taghot">
	<?php $data = block_taglist_rand(array (
  'mid' => '2',
  'limit' => '20',
)); ?>
	<ul>
		<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
		<li><a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
		<?php }} ?> 				  
	</ul><?php unset($data); ?>
</div>
</div>

		<?php $data = block_links(array (
)); ?>
		<?php if ($data) { ?>
		<div class="acgb-b642a8 card friendlink">
			<div class="acgb-abe027 card-header">友情链接</div>
			<div class="acgb-53e7fe card-body small">
				<ul>
					<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
					<li class="acgb-e1cb2b mb-1 small line-height-2">
						<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
					</li>
					<?php }} ?>
				</ul>
			</div>
		</div>
		<?php } ?>
		<?php unset($data); ?>
	</div>
</div>	
		</div>
	</main>

	<footer class="acgb-a9e8f9 text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="acgb-e849bd container">
		<div class="acgb-a2c59a row">
			<div class="acgb-735abb col">
			<?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?>
			</div>
			<!--<div class="acgb-c0f5b0 col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-3.1.0.js?1.0"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/bootstrap.js?1.0"></script>
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/scroll.css">
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
<a id="scroll_to_top" href="javascript:void(0);"  class="acgb-982394 mui-rightlist"  title="返回顶部" style="display: none;"><i class="acgb-4dd66e icon-angle-double-up"></i></a>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const todayFormatted = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');

    // 获取所有包含 data-date 的元素
    const dateElements = document.querySelectorAll('.date[data-date]');

    dateElements.forEach(function(element) {
        // 获取元素中的发布时间
        const publishDate = element.getAttribute('data-date');
        
        // 如果发布时间和今天的日期相同，修改字体颜色为红色
        if (publishDate === todayFormatted) {
            element.style.setProperty('color', 'red', 'important');
            element.style.fontWeight = 'bold'; // 可选，设置粗体
        }
    });
});
 </script>
 <script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>