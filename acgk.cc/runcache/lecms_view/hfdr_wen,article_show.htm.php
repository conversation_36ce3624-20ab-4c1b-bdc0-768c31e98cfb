<?php defined('APP_NAME') || exit('Access Denied');  function block_global_show($conf) { global $run, $_show, $_user; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $show_prev_next = isset($conf['show_prev_next']) && (int)$conf['show_prev_next'] ? true : false; $prev_next_cid = isset($conf['cid']) ? (int)$conf['cid'] : intval($_GET['cid']); $field_format = _int($conf, 'field_format', 0); $pageoffset = _int($conf, 'pageoffset', 5); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_show'); $cache_key = $life ? md5('global_show'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; if($mid == 1) return FALSE; $uid = isset($_user['uid']) ? (int)$_user['uid'] : 0; $run->cms_content_data->table = 'cms_'.$run->_var['table'].'_data'; $run->cms_content->format($_show, $mid, $dateformat, 0, 0, $field_format); $id = &$_show['id']; $_show['comment_url'] = $run->cms_content->comment_url($run->_var['cid'], $id); $_show['views_url'] = $run->_cfg['webdir'].'index.php?views--cid-'.$run->_var['cid'].'-id-'.$id; $data = $run->cms_content_data->get($id); if($data){ if($field_format && plugin_is_enable('models_filed')){ $models_field = $run->models_field->user_defined_field($mid); $run->models_field->field_val_format($models_field, $data, 0); } $_show += $data; $page = max(1,(int)R('page','G')); $_show = $run->cms_content_data->format_content($_show, $page); if( isset($_show['content_page']) && isset($_show['maxpage']) ){ $_show['pages'] = paginator::$page_function($page, $_show['maxpage'], $run->cms_content->content_url($_show, $mid, TRUE), $pageoffset); }else{ $_show['pages'] = false; } }else{ $_show['pages'] = false; } $run->cms_content_views->table = 'cms_'.$run->_var['table'].'_views'; $views_data = $run->cms_content_views->get($id); if($views_data){ if( empty($run->_cfg['close_views']) ){ $_show['views'] = $views_data['views']+1; $run->cms_content_views->update_views($id); }else{ $_show['views'] = $views_data['views']; } }else{ $_show['views'] = 1; empty($run->_cfg['close_views']) && $run->cms_content_views->set($id, array('views'=>1,'cid'=>$_show['cid'])); } if(isset($_show['filenum']) && !empty($_show['filenum'])){ list($attachlist, $imagelist, $filelist) = $run->cms_content_attach->attach_find_by_id($run->_var['table'], $id, array('id'=>$id, 'isimage'=>0)); $_show['filelist'] = $filelist; if($_show['uid'] == $uid && $uid){ $file_delete = true; }else{ $file_delete = false; } $_show['filelist_html'] = $run->cms_content_attach->file_list_html($filelist, $mid, $file_delete); }else{ $_show['filelist'] = array(); $_show['filelist_html'] = ''; } if( isset($run->_cfg['le_content_img_seo']) && !empty($run->_cfg['le_content_img_seo']) ){ $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/"; preg_match_all($pattern, $_show['content'], $match); $find_arr = array('{title}', '{cate_name}', '{webname}', '{count}'); if( isset($match[0]) ){ $img_count = 1; foreach ($match[0] as $k=>$img){ $replace_arr = array("{$_show['title']}", "{$run->_var['name']}", "{$run->_cfg['webname']}", $img_count); $new_alt = str_replace($find_arr, $replace_arr, $run->_cfg['le_content_img_seo']); if( stripos($img, "alt=") != false ){ $img_new = preg_replace('/alt=["\'](.*?)["\']/', 'alt="'.$new_alt.'"', $img); }else{ $img_new = str_replace_once('<img', '<img alt="'.$new_alt.'" ', $img); } if( stripos($img_new, "title=") != false ){ $img_new = preg_replace('/title=["\'](.*?)["\']/', '', $img_new); } if( strpos($img_new, $run->_cfg['webdomain']) === false ){ $img_new = str_replace_once('<img', '<img rel="external nofollow" referrerpolicy="no-referrer" ', $img_new); } $_show['content'] = str_replace_once($img, $img_new, $_show['content']); $img_count++; } unset($match[0]); } unset($find_arr); } if($show_prev_next) { if($prev_next_cid){ $prev_where = array('cid'=>$prev_next_cid, 'id'=>array('<'=> $id)); $next_where = array('cid'=>$prev_next_cid, 'id'=>array('>'=> $id)); }else{ $prev_where = array('id'=>array('<'=> $id)); $next_where = array('id'=>array('>'=> $id)); } $_show['prev'] = $run->cms_content->list_arr($prev_where, 'id', -1, 0, 1, 1, $extra); if($_show['prev']){ $_show['prev'] = current($_show['prev']); $run->cms_content->format($_show['prev'], $mid, $dateformat); } $_show['next'] = $run->cms_content->list_arr($next_where, 'id', 1, 0, 1, 1, $extra); if($_show['next']){ $_show['next'] = current($_show['next']); $run->cms_content->format($_show['next'], $mid, $dateformat); } }else{ $_show['prev'] = $_show['next'] = array(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $_show, $life); } if( isset($_show['favorites']) ){ $uid = isset($run->_user['uid']) ? (int)$run->_user['uid'] : 0; if( empty($uid) ){ $_show['has_favorites'] = 0; }else{ if( $run->favorites->find_fetch_key(array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id)) ){ $_show['has_favorites'] = 1; }else{ $_show['has_favorites'] = 0; } } $_show['favorites_url'] = $run->_cfg['webdir'].'index.php?show-favorites-mid-'.$mid.'-id-'.$id.'-uid-'.$uid.'.html'; }$le_keywords_links_setting = $run->kv->xget('le_keywords_links_setting'); if($le_keywords_links_setting){ $class = $le_keywords_links_setting['class'] == '' ? '' : ' class="'.$le_keywords_links_setting['class'].'"'; $target = $le_keywords_links_setting['target'] == 0 ? '' : ' target="_blank"'; $style = $class.$target; $keywords_links_arr = $run->keywords_links->find_fetch(array(), array('orderby'=>1, 'id' => 1)); if( $keywords_links_arr ){ $contentstr = $_show['content']; foreach ($keywords_links_arr as $keywords){ $patterns = '#(?=[^>]*(?=<(?!/a>)|$))'.$keywords['keyword'].'#'; $replacements = '<a href="'.$keywords['url'].'" '.$style.' title="'.$keywords['keyword'].'">'.$keywords['keyword'].'</a>'; $contentstr = preg_replace($patterns, $replacements, $contentstr, $keywords['count']); } $_show['content'] = $contentstr; unset($keywords_links_arr); } } return $_show; }
$gdata = block_global_show(array (
  'show_prev_next' => '1',
  'dateformat' => 'Y-m-d H:i:s',
));
 function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_taglike($conf) { global $run, $_show; if(!isset($_show['tags']) || empty($_show['tags'])) return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $type = isset($conf['type']) ? (int)$conf['type'] : 1; $type = $type <= -1 ? -1 : (int)$type; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglike'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; $table = &$run->_var['table']; if($type == -1){ $end = array_slice($_show['tags'],-1, 1, true); $tagid = key( $end ); }elseif ($type == 0){ $tagid = array_rand($_show['tags']); }else{ $tagid = key( array_slice($_show['tags'], $type-1, 1, true) ); if( empty($tagid) ){ return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); } } $tag_name = $_show['tags'][$tagid]; $tag_url = $run->cms_content->tag_url($mid, array('tagid'=>$tagid, 'name'=>$tag_name)); if(isset($conf['mid']) && $conf['mid'] > 1 && $mid != $conf['mid'] && isset($run->_cfg['table_arr'][$conf['mid']])){ $mid = $conf['mid']; $table = $run->_cfg['table_arr'][$conf['mid']]; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $tags = $run->cms_content_tag->find_fetch(array('name'=>$tag_name), array(), 0, 1); if(empty($tags)){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }else{ $tags = current($tags); $tagid = $tags['tagid']; $tag_url = $run->cms_content->tag_url($mid, $tags); } } $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; $tag_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$tagid), array('id'=>$orderway), $start, $limit+1); $keys = array(); foreach($tag_arr as $lv) { if($lv['id'] != $_show['id']){ $keys[] = $lv['id']; } } if( empty($keys) ){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }elseif (count($keys) > $limit){ $keys = array_slice($keys, 0, $limit); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $table = isset($run->_cfg['table_arr'][$mid]) ? $run->_cfg['table_arr'][$mid] : 'article'; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('tagid', 'count', 'orderby')) ? $conf['orderby'] : 'count'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = isset($conf['limit']) ? (int)$conf['limit'] : 10; $cms_limit = _int($conf, 'cms_limit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_taglist'); $cache_key = $life ? md5('taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $run->cms_content_tag->table = 'cms_'.$table.'_tag'; if($cms_limit){ $run->cms_content->table = 'cms_'.$table; $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; } $where = array(); $list_arr = $run->cms_content_tag->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } if($cms_limit){ $tag_data_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$v['tagid']), array('id'=>-1), 0, $cms_limit); $keys = array(); foreach($tag_data_arr as $lv) { $keys[] = $lv['id']; } $cms_arr = $run->cms_content->mget($keys); foreach($cms_arr as &$cv) { $run->cms_content->format($cv, $mid); } $v['cms'] = $cms_arr; unset($cms_arr); } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<title><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></title>
<meta property="update_time" content="<?php  echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']); ?>">
    <meta property="published_time" content="<?php echo date('Y-m-d H:i:s'); ?>">
    <meta property="og:locale" content="zh_CN"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>"/>
    <meta property="og:width" content="800"/>
    <meta property="og:author" content="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>">
    <meta property="og:update_time" content="<?php  echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']); ?>">
    <meta property="og:published_time" content="<?php echo date('Y-m-d H:i:s'); ?>">
    <meta property="og:title" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"/>
    <meta property="og:keywords" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"/>
    <meta property="og:description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>"/>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/article.css">
<script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/interactive.js" type="text/javascript"></script>
<meta http-equiv="Cache-Control" content="no-transform" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<meta name="applicable-device" content="pc,mobile" />
<body>
<header>
<div class="hgacg-3c0116 xmedia_nav">
<a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" class="hgacg-d9e418 aikahao_logo" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"></a>
<div class="hgacg-faacab nevigate">
<?php $data = block_navigate(array (
)); ?>
<ul class="hgacg-408375 nevigate_ul">
<li <?php if (empty($cfg_var['topcid'])) { ?>class="active"<?php } ?>><a href="/">首页</a><i class="hgacg-f7d5bd ico_bg"></i></li>
<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
<li <?php if ($cfg_var['topcid'] == $v['cid']) { ?>class="active"<?php } ?>><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><i class="hgacg-f7d5bd ico_bg"></i></li><?php }} ?>
<?php if ($_uid) { ?>
                    <li><a href="/my-index.html" class="hgacg-fbdb65 padding-half">个人中心</a></li>
                <?php }else{ ?>
                    <li><a href="/user-login.html" class="hgacg-fbdb65 padding-half">登录/注册</a></li>
                <?php } ?>
</ul>
<?php unset($data); ?>
</div>
<div class="hgacg-33de5d search_box">
<div class="hgacg-823656 seek">
<form name="get" class="hgacg-599cf9 model_form_con clearfix"  method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form" target="_blank">
<div class="hgacg-191545 seek_cn">
<span class="hgacg-3e2cda seek_ico"></span>
<input type="text" id="hsearchkey" name="keyword" autocomplete="off" class="hgacg-b361f4 input_txt padding_input" placeholder="请输入关键字">
<input type="hidden" name="u" value="search-index" />
<input type="hidden" name="mid" value="2" />
</div>
<button  type="submit" class="hgacg-9ce515 blue_but" id="bdcs-search-form-submit">搜索</button>
</form>
</div>
</div>
</div>
</header>
<div class="hgacg-4198f5 current_path">
<div class="hgacg-c12c68 p1">
当前位置：<a href="/">首页</a><?php if(isset($cfg_var['place']) && is_array($cfg_var['place'])) { foreach($cfg_var['place'] as &$v) { ?> > <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?> > 正文
</div>
</div>
<div class="hgacg-e0aa24 detail_content clearfix">
<div class="hgacg-8fae37 detail_lf">
<div class="hgacg-5d440f article-body">
<div class="hgacg-07f13f detail_main clearfix">
<h1 class="hgacg-78081b detail_title"><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h1>
<div class="hgacg-12beb0 article-intro">
<div  class="hgacg-d596a6 detail_avatar" target="_blank"><img src="<?php echo(isset($gdata['avatar']) ? $gdata['avatar'] : ''); ?>" alt="<?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?>"/>
<span class="hgacg-a812ce admit admit_organ"></span>
</div>
<span class="hgacg-4d5dcc detail_txt_lf"><?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?></span>
<span class="hgacg-44ec45 browse_time"><?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?></span>
</div>
</div>
<article class="hgacg-952cf7 detail_list_p"  id="c_more">
				<?php if ($_uid) { ?>
                 <?php echo(isset($gdata['content']) ? $gdata['content'] : ''); ?>
              	<br /><br /><p>
<?php if (isset($gdata['favorites'])) { ?>
<style>
        .content_favorites {
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background-color: #f9f9f9; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
        }

        #favorites_do {
            margin-right: 5px; 
            color: #ffcc00;
        }

        #favorites_html {
            font-weight: bold; 
        }

        #favorites_count {
            color: #888;
        }
</style>
<div class="hgacg-ead73c content_favorites">
    <?php if ($gdata['has_favorites']) { ?>
    <i id="favorites_do" class="hgacg-044cd4 fa fa-star" aria-hidden="true"><font id="favorites_html">取消收藏</font></i> 
    <?php }else{ ?>
    <i id="favorites_do" class="hgacg-1980eb fa fa-star-o" aria-hidden="true"><font id="favorites_html">加入收藏</font></i>
    <?php } ?>
</div>
<script type="text/javascript">
    $("#favorites_do").click(function () {
        $.getJSON("<?php echo(isset($gdata['favorites_url']) ? $gdata['favorites_url'] : ''); ?>", function(data){
            if(data.err){
                alert(data.msg);
            }else{
                if( data.has_favorites == 1 ){
                    $("#favorites_do").attr("class", "fa fa-star");
                    $("#favorites_html").html("取消收藏");
                }else{
                    $("#favorites_do").attr("class", "fa fa-star-o");
                    $("#favorites_html").html("加入收藏");
                }
                $("#favorites_count").html(data.favorites_count);
                alert(data.msg);
            }
        });
    });
</script>
<?php } ?></p><br /><br /><br />
                
                <?php }else{ ?>请先 <a href="/user-login.html">登录</a> 或者 <a href="/user-register.html">注册</a> 后在进行查看哦！<?php } ?>
<?php if (isset($gdata['tag_arr'])) { ?>
<div class="hgacg-898115 article-tags">
<?php if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?>
<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="hgacg-ca231b article-tag ablock"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
<?php }} ?>
</div>
<?php } ?>
<div class="hgacg-da811a art-copyright br mb">
<b class="hgacg-5a1628 addr">免责申明：</b>本站所发布的文字与图片素材为非商业目的改编或整理，版权归原作者所有，如侵权或涉及违法，请联系我们删除，如需转载请保留原文地址。  
</div>
<?php if (isset($gdata['prev']['url']) || isset($gdata['next']['url'])) { ?>
<div class="hgacg-89aacd m_ssxx">
<?php if (isset($gdata['prev']['url'])) { ?>
<p><span>上一篇：</span><a href='<?php echo(isset($gdata['prev']['url']) ? $gdata['prev']['url'] : ''); ?>'><?php echo(isset($gdata['prev']['title']) ? $gdata['prev']['title'] : ''); ?></a></p>
<?php }else{ ?>
<p><span>上一篇：</span><a>没有了</a></p>
<?php } ?>
<?php if (isset($gdata['next']['url'])) { ?>
<p><span>下一篇：</span><a href='<?php echo(isset($gdata['next']['url']) ? $gdata['next']['url'] : ''); ?>'><?php echo(isset($gdata['next']['title']) ? $gdata['next']['title'] : ''); ?></a></p>
<?php }else{ ?>
<p><span>下一篇：</span><a>没有了</a></p>
<?php } ?>
</div>
<?php } ?>
</article>
</div>
<div class="hgacg-a5afc3 column">
<div class="hgacg-1631c3 choices">
<div class="hgacg-33e428 choices_cut">
<ul>
<li class="hgacg-eb5d92 active">推荐阅读</li>
</ul>
</div>
<div class="hgacg-dc8b88 choices_cont">
<ul class="hgacg-c91d12 choices_main">
<?php $taglikedata = 0; ?>
<?php $data = block_taglike(array (
  'type' => '1',
  'limit' => '5',
  'dateformat' => 'Y-m-d',
)); ?>
<?php if ($data['list']) { ?>
<?php $taglikedata = 1; ?>
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li class="hgacg-b92513 item_content">
<div class="hgacg-97c8fa leftImg">
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a>
</div>
<div class="hgacg-10a7d8 txtCon_one">
<a class="hgacg-2e0487 txt_title" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
<p class="hgacg-81e687 txt_con"><?php echo(isset($v['intro']) ? $v['intro'] : ''); ?></p>
<div class="hgacg-a54b8e txt_label">
<span class="hgacg-c27a0c user_name"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
<div class="hgacg-ae5a4f label_right">
<em class="hgacg-4c8fb4 dateline"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></em>
<em class="hgacg-33cc09 discuss">
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#comment"><span class="hgacg-38479c reply"></span>评论</a>
</em>
</div>
</div>
</div>
</li>
<?php }} ?>
<?php } ?>
<?php unset($data); ?>
<?php if ($taglikedata == 0) { ?>
<?php $data = block_list_rand(array (
  'limit' => '5',
  'life' => '0',
)); ?>
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li class="hgacg-b92513 item_content">
<div class="hgacg-97c8fa leftImg">
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a>
</div>
<div class="hgacg-10a7d8 txtCon_one">
<a class="hgacg-2e0487 txt_title" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
<p class="hgacg-81e687 txt_con"><?php echo(isset($v['intro']) ? $v['intro'] : ''); ?></p>
<div class="hgacg-a54b8e txt_label">
<span class="hgacg-c27a0c user_name"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span>
<div class="hgacg-ae5a4f label_right">
<em class="hgacg-4c8fb4 dateline"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></em>
<em class="hgacg-33cc09 discuss">
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>#comment"><span class="hgacg-38479c reply"></span>评论</a>
</em>
</div>
</div>
</div>
</li>
<?php }} ?>
<?php unset($data); ?>
<?php } ?>
</ul>
</div>
</div>
</div>
</div>

<div class="hgacg-91c17d detail_rh">
<?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '8',
)); ?>
<div class="hgacg-9d9afb details_right_username">
<h3 class="hgacg-33fcf9 right-title right_tit"><span>热门文章</span></h3>
<ul class="hgacg-2b647a author_list clearfix">
<?php $rank = 1; ?>
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li>
<div class="hgacg-4b4a15 author_box"> 
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a> 
</div>
<span class="hgacg-82b3f8 author_txt"> 
<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a> 
</span>
</li>
<?php $rank++; ?>
<?php }} ?>
</ul>
</div><?php unset($data); ?>
<?php $data = block_taglist(array (
  'mid' => '2',
  'limit' => '18',
  'orderby' => 'count',
)); ?>
<div class="hgacg-9d9afb details_right_username">
<h3 class="hgacg-33fcf9 right-title right_tit"><span>热门标签</span></h3>
<ul class="hgacg-2d9827 popular_label clearfix">
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li><a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
</ul>
</div><?php unset($data); ?>
<?php $data = block_list_rand(array (
  'limit' => '8',
  'life' => '600',
)); ?>
<div class="hgacg-4f35e7 details_right_username fixedDiv">
<h3 class="hgacg-33fcf9 right-title right_tit"><span>猜你喜欢</span></h3>
<ul class="hgacg-2b647a author_list clearfix">
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
<li>
<div class="hgacg-4b4a15 author_box"> 
<a rel="nofollow" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a> 
</div>
<span class="hgacg-82b3f8 author_txt"> 
<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a> 
</span>
</li><?php }} ?>
   </ul>
</div><?php unset($data); ?>
</div>
</div>

<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/js/jquery.js"></script>
<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/layer/layer.js"></script>
<script>
    $('#sj_kami_buy').on('click',function (e){
        var aid=$(this).attr('data-id');
        var golds=$(this).attr('data-golds');
        $.get('/my-userinfo.html','',function(data){
            if(!data.err){
                layer.confirm('您当前有 <span style="color: red">'+data.data.golds+' </span>金币。本次购买需消耗 <span style="color: red">'+golds+' </span>金币!<br/>确认进行购买操作？', {
                  btn: ['确定','取消'],icon:3
                }, function(){
                    $.post('/my-userinfo.html',{id:aid},function(data){
                        if(!data.err){
                            layer.alert(data.msg,{icon:1},function (e){
                                window.location.reload()
                            })
                        }else{
                            layer.alert(data.msg,{icon:2})
                        }
                    },'json');
                });
            }else{
                layer.msg('余额获取失败！')
            }
        },'json');
    })
</script>
<footer class="hgacg-936831 content_nt mar_top8">
<div class="hgacg-b03135 footer_nt">
<p><a href="/sitemap.html" target="_blank">网站地图</a>|<a href="/sitemap.xml" target="_blank">RS订阅</a></p>
<p> Copyright 2023 <?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?> <b>【<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>】</b> 版权所有 | <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>picture/gonganbeian.png"><a rel="nofollow" href="//beian.miit.gov.cn"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a></p>
<p>声明：本站为非赢利网站，作品与素材版权均归作者所有，如内容侵权与违规请与本站联系，将在三个工作日内处理，互联网违法和不良信息举报邮箱：<?php echo(isset($cfg['webmail']) ? $cfg['webmail'] : ''); ?></p>
</div>
</footer>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/common.js" type="text/javascript"></script>
<script>
(function() {
$("#search_form").submit(function() {
var mid = $(this).find("[name='mid']").val();
var keyword = $(this).find("[name='keyword']").val();
window.location.href = "/search/" + encodeURIComponent(keyword);
return false;
});
})();
(function() {
var feedBackUrl = "//wpa.qq.com/msgrd?v=3&uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&site=qq&menu=yes";
var strHml = '<div class="hgacg-eedeca feedBackWrap" > <div class="fq"><a href="' + feedBackUrl + '" target="_blank"></a></div> <div class="hgacg-ad6621 feedBackWraphd"></div></div>';
$("body").append(strHml);
$("body").on("click", ".feedBackWraphd", function() {
$(document).scrollTop(0);
})
$(window).scroll(function() {
if ($(document).scrollTop() < 100) {
$('.feedBackWraphd').hide();
} else {
$('.feedBackWraphd').show();
}
})
})()
</script>
</body>
</html>