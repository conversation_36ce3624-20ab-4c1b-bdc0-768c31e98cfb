<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<?php 
date_default_timezone_set('PRC'); //设定时区，PRC就是中国
$hour = date('H');
 ?>
<!--如果不需要到时间暗黑切换注释下一行-->
<!--html lang="zh-CN" <?php if ($hour >= 18 || $hour < 6) { ?>class="darking"<?php } ?>-->
<html lang="zh-CN">
 <head> 
  <meta charset="UTF-8" /> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0" />
  	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="ie=edge" />
	<meta name="generator" content="acg" />
	<meta name="renderer" content="webkit">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
	<link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
  

  <link rel="stylesheet" id="wp-block-library-css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/style.min.css" type="text/css" media="all" /> 
  <link rel="stylesheet" id="style-css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/style.css" type="text/css" media="all" /> 
  <link rel='stylesheet' id='fonts-css' href='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>fonts/iconfont.css' type='text/css' media='all' />
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery.min.js" id="jquery-js"></script>  
  <style>
  /*哀悼灰色 html, body {filter:grayscale(1);}*/
  :root{--tb--main: #007bff}a.tbas{border:2px dashed #aaa;padding:40px 15px;font-size:14px;background-color:#fff;display:block;text-decoration:none;color:#888;font-weight:bold;text-align:center;}
  a.tbas:hover{border-color:#666;color:#444;}
  @media (max-width:640px){
  a.tbas{font-size:12px;padding:25px 15px;}}
  </style> 
 </head> 
 <?php if ($control=='show' && $action == 'index') { ?>  
 <body class="post-template-default single single-post  single-format-standard home nav_fixed m-excerpt-cat p_indent comment-open site-layout-2 text-justify-on m-sidebar m-user-on dark-on">
 <?php }elseif($control=='cate' && $action == 'index') { ?>  
 <body class="archive category category-tech  home nav_fixed m-excerpt-cat site-layout-2 text-justify-on m-sidebar m-user-on dark-on">
 <?php }elseif($control=='cate' && $cfg_var['mid']==1 && $action == 'index') { ?>  
 <body class="page-template page-template-pages page-template-no-sidebar page-template-pagesno-sidebar-php page home nav_fixed m-excerpt-cat p_indent comment-open site-layout-2 text-justify-on m-sidebar m-user-on dark-on"> 
 <?php }else{ ?>
 <body class="home blog nav_fixed m-excerpt-cat site-layout-2 text-justify-on m-sidebar m-user-on dark-on"> 
 <?php } ?>  
  <header class="header"> 
   <div class="container"> 
    <h1 class="logo"><a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /><img class="-dark" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/logo-dark.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a></h1> 
    <div class="brand">
     欢迎光临<br />我们一直在努力
    </div> 
	<?php $data = block_navigate(array (
)); ?>	
    <ul class="site-nav site-navbar"> 
     <li <?php if (empty($cfg_var['topcid'])) { ?> class="current-menu-item" <?php } ?>><a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" aria-current="page">首页</a></li>
	 <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
	 <li class="current-category-ancestor current-menu-ancestor <?php if ($cfg_var['topcid']==$v['cid']) { ?> current-menu-item<?php } ?> <?php if (isset($v['son'])) { ?> menu-item-has-children<?php } ?>">
      <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
	  <?php if (isset($v['son'])) { ?>
      <ul class="sub-menu"> 
        <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?> 
      </ul>
      <?php } ?>	  
	 </li> 
	 <?php }} ?>
     <li class="navto-search"><a href="javascript:;" class="search-show"><i class="tbfa">&#xe611;</i></a></li> 
     <?php if (!is_mobile()) { ?><li class="sitedark" etap="darking" style="display:none;"><i class="tbfa">&#xe6a0;</i><i class="tbfa">&#xe635;</i></li> <?php } ?> 
    </ul> 
	<?php unset($data); ?>  
    <div class="topbar"> 
     <ul class="site-nav topmenu"> 
     <li><a href="/special/1.html">专题</a></li>
      <li><a href="/tags">标签云</a></li> 
      <li><a href="/archives">页面存档</a></li> 
      <li><a href="/links">友情链接</a></li> 
      <li class="menusns menu-item-has-children"> <a href="javascript:;">关注我们</a> 
       <ul class="sub-menu"> 
        <li><a class="sns-wechat" href="javascript:;" title="关注微信" data-src="">关注微信</a></li> 
        <li><a target="_blank" rel="external nofollow" href="">分类一</a></li>
        <li><a target="_blank" rel="external nofollow" href="">分类二</a></li> 
       </ul> </li> 
     </ul> 
     <a rel="nofollow" target="_blank" href="<?php echo(isset($login_url) ? $login_url : ''); ?>" class="signin-loader">Hi, 请登录</a> &nbsp; &nbsp; 
     <a rel="nofollow" target="_blank" href="<?php echo(isset($register_url) ? $register_url : ''); ?>" class="signup-loader">我要注册</a> &nbsp; &nbsp; 
     <a rel="nofollow" target="_blank" href="/user-forget.html">找回密码</a> 
    </div> 
    <?php if (!is_mobile()) { ?><a rel="nofollow" href="javascript:;" class="signin-loader m-icon-user"><i class="tbfa">&#xe641;</i></a><?php } ?> 
   </div> 
  </header>
  <i class="tbfa m-icon-nav">&#xe612;</i>
  <div class="site-search"> 
   <div class="container"> 
    <form id="search_form" method="get" target="_blank" class="site-search-form"> 
	 <input type="hidden" name="u" value="search-index" />
     <input class="search-input" name="keyword" type="text" placeholder="输入关键字" value="" required="required" /> 
     <button class="search-btn" type="submit"><i class="tbfa">&#xe611;</i></button> 
    </form> 
   </div> 
  </div>   
<style>
	.error .clip .shadow {height:180px;}
	.error .clip:nth-of-type(2) .shadow {width:130px;}
	.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:250px;}
	.error .digit {width:150px;height:150px;line-height:150px;font-size:120px;font-weight:bold;}
	.error h2 {font-size:32px;}
	.error .msg {top:-190px;left:30%;width:80px;height:80px;line-height:80px;font-size:32px;}
	.error span.triangle {top:70%;right:0%;border-left:20px solid #535353;border-top:15px solid transparent;border-bottom:15px solid transparent;}
	.error .container-error-404 {top: 50%;margin: 200px;position:relative;height:250px;padding-top:40px;}
	.error .container-error-404 .clip {display:inline-block;transform:skew(-45deg);}
	.error .clip .shadow {overflow:hidden;}
	.error .clip:nth-of-type(2) .shadow {overflow:hidden;position:relative;box-shadow:inset 20px 0px 20px -15px rgba(150,150,150,0.8),20px 0px 20px -15px rgba(150,150,150,0.8);}
	.error .clip:nth-of-type(3) .shadow:after,.error .clip:nth-of-type(1) .shadow:after {content:"";position:absolute;right:-8px;bottom:0px;z-index:9999;height:100%;width:10px;background:linear-gradient(90deg,transparent,rgba(173,173,173,0.8),transparent);border-radius:50%;}
	.error .clip:nth-of-type(3) .shadow:after {left:-8px;}
	.error .digit {position:relative;top:8%;color:white;background:#1E9FFF;border-radius:50%;display:inline-block;transform:skew(45deg);}
	.error .clip:nth-of-type(2) .digit {left:-10%;}
	.error .clip:nth-of-type(1) .digit {right:-20%;}
	.error .clip:nth-of-type(3) .digit {left:-20%;}
	.error h2 {font-size:24px;color:#A2A2A2;font-weight:bold;padding-bottom:20px;}
	.error .tohome {font-size:16px;color:#07B3F9;}
	.error .msg {position:relative;z-index:9999;display:block;background:#535353;color:#A2A2A2;border-radius:50%;font-style:italic;}
	.error .triangle {position:absolute;z-index:999;transform:rotate(45deg);content:"";width:0;height:0;}
	@media(max-width:767px) {.error .clip .shadow {height:100px;}
		.error .clip:nth-of-type(2) .shadow {width:80px;}
		.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:100px;}
		.error .digit {width:80px;height:80px;line-height:80px;font-size:52px;}
		.error h2 {font-size:18px;}
		.error .msg {top:-110px;left:15%;width:40px;height:40px;line-height:40px;font-size:18px;}
		.error span.triangle {top:70%;right:-3%;border-left:10px solid #535353;border-top:8px solid transparent;border-bottom:8px solid transparent;}
		.error .container-error-404 {height:150px;}
	}
</style>
<main class="main">
	<div class="error">
		<div class="container-floud">
			<div style="text-align: center">
				<div class="container-error-404">
					<div class="clip">
						<div class="shadow">
							<span class="digit thirdDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit secondDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit firstDigit"></span>
						</div>
					</div>
					<div class="msg">OH!
						<span class="triangle"></span>
					</div>
				</div>
				<h2 class="h1">很抱歉，你访问的页面找不到了</h2>
			</div>
		</div>
	</div>
</main>
<script type="text/javascript">
	function randomNum() {
		return Math.floor(Math.random() * 9) + 1;
	}

	var loop1, loop2, loop3, time = 30, i = 0, number;
	loop3 = setInterval(function () {
		if (i > 40) {
			clearInterval(loop3);
			document.querySelector('.thirdDigit').textContent = 4;
		} else {
			document.querySelector('.thirdDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop2 = setInterval(function () {
		if (i > 80) {
			clearInterval(loop2);
			document.querySelector('.secondDigit').textContent = 0;
		} else {
			document.querySelector('.secondDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop1 = setInterval(function () {
		if (i > 100) {
			clearInterval(loop1);
			document.querySelector('.firstDigit').textContent = 4;
		} else {
			document.querySelector('.firstDigit').textContent = randomNum();
			i++;
		}
	}, time);
</script>
   <div class="branding"> 
   <div class="container"> 
    <h2>卡趣資源網 更专业 更方便</h2> 
    <h4>支持快讯、专题、百度收录推送、人机验证、多级分类筛选器，适用于垂直站点、科技博客、个人站，扁平化设计、简洁白色、超多功能配置、会员中心、直达链接、文章图片弹窗、自动缩略图等...</h4> 
    <a target="blank" class="btn btn-primary" href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes">关于我们<i class="tbfa">&#xe87e;</i></a>
    <a target="blank" class="btn btn-primary" href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes">联系我们<i class="tbfa">&#xe87e;</i></a> 
   </div> 
  </div>
 <footer class="footer"> 
   <div class="container"> 
    <?php if ($control=='index' && $action == 'index') { ?> 
    <div class="flinks"> 
     <strong>友情链接</strong> 
     <ul class="xoxo blogroll"> 
	  <?php $data = block_links(array (
)); if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
      <li>
	  <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" rel="noopener" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
	  </li> 
	  <?php }} unset($data); ?>
     </ul> 
    </div>
    <?php } ?>	
    <div class="fcode">
      该块显示在网站底部版权上方，可已定义放一些链接或者图片之类的内容。 
    </div> 
    <p>Copyright&nbsp;©&nbsp;2022 All rights reserved. <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>  <a href="https://beian.miit.gov.cn" target="_blank"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a></p> 
   </div> 
  </footer> 
  <?php if ($control=='show' && $action == 'index') { ?>  
  <div class="rewards-popover-mask" data-event="rewards-close"></div>
  <div class="rewards-popover">
		<h3>觉得文章有用就打赏一下文章作者</h3>
		<h5>非常感谢你的打赏，我们将继续给力更多优质内容，让我们一起创建更加美好的网络世界！</h5>
		<div class="rewards-popover-item">
			<h4>支付宝扫一扫打赏</h4><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/wechat.jpg">
		</div>
		<div class="rewards-popover-item">
			<h4>微信扫一扫打赏</h4><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/wechat.jpg">
		</div>
		<span class="rewards-popover-close" data-event="rewards-close"><i class="tbfa">&#xe606;</i></span>
  </div>
  <?php } ?>
  <div class="karbar karbar-rb">
   <ul>
    <li><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes"><i class="tbfa">&#xe60f;</i><span>QQ咨询</span></a></li>
    <li><a href="tel:<?php echo(isset($cfg['webtel']) ? $cfg['webtel'] : ''); ?>"><i class="tbfa">&#xe679;</i><span>电话咨询</span></a></li>
    <li class="karbar-qrcode"><a href="javascript:;"><i class="tbfa">&#xe61e;</i><span>关注微信</span></a><span class="karbar-qrcode-wrap"><?php echo(isset($cfg['webweixin']) ? $cfg['webweixin'] : ''); ?><br /><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/wechat.jpg" /></span></li>
    <li><a target="_blank" href=""><i class="tbfa">&#xe68d;</i><span>在线咨询</span></a></li>
	<?php if ($control=='show' && $action == 'index') { ?>  
	<li><a href="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['url']) ? $gdata['url'] : ''); ?>#divCommentPost"><i class="tbfa">&#xe8c5;</i><span>去评论</span></a></li>
	<?php } ?>
    <li class="karbar-totop"><a href="javascript:(TBUI.scrollTo());"><i class="tbfa">&#xe613;</i><span>回顶部</span></a></li>
   </ul>
  </div>
  <script>
	window.TBUI = {"www":"http:\/\/<?php echo(isset($cfg['webdomain']) ? $cfg['webdomain'] : ''); echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>","uri":"http:\/\/<?php echo(isset($cfg['webdomain']) ? $cfg['webdomain'] : ''); ?>\/view\/<?php echo(isset($cfg['theme']) ? $cfg['theme'] : ''); ?>","ver":"8.1","roll":"1","ajaxpager":"0","fullimage":"1"}
  </script>
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/loader.js" id="loader-js"></script> 
  

  

  

 </body>
</html>

