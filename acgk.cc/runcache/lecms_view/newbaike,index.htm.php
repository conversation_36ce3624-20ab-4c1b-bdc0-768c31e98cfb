<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_taglist_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglist_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table.'_tag'; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total = $run->cms_content_tag->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.tagid FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(tagid) FROM {$table_full})) AS tagid) AS t2 WHERE t1.tagid >= t2.tagid LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['tagid'], $keys)){ $keys[] = $arr['tagid']; $i++; } } $list_arr = $run->cms_content_tag->mget($keys); }else{ $keys = array(); $sql = "SELECT tagid FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['tagid']; } $list_arr = $run->cms_content_tag->mget($keys); } $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $cids = empty($conf['cids']) ? '' : $conf['cids']; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list'); $cache_key = $life ? md5('list'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if ($cids){ $cid_arr = explode(',', $cids); $where = array('cid' => array("IN" => $cid_arr)); $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; }else{ if($cid == 0) { $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); if(empty($cate_arr)) return; $cate_name = $cate_arr['name']; $cate_url = $run->category->category_url($cate_arr); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('cate_name'=> $cate_name, 'cate_url'=> $cate_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <link rel="icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/img/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/style.css">
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/iconfont.css">
    <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/jquery.min.js"></script>
    <!--[if lt IE 9]>
      <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/html5shiv.min.js"></script>
      <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/respond.min.js"></script>
    <![endif]-->
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/m.js"></script>
<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/a00.js"></script>
</head>
<body>
<header class="wp0-f7604b menu">
    <div class="wp0-e849bd container">
        <a href="/" class="wp0-f1fd44 logo-a">
            <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/img/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" style="width: 180px;"/>
        </a>
        <?php $data = block_navigate(array (
)); ?>
        <ul class="wp0-d4dab2 navbar">
            <li <?php if (empty($cfg_var['topcid'])) { ?> class="wp0-928a6a act"<?php } ?>><a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"><?php echo '首页'; ?></a></li>
            <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
            <li<?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="act"<?php } ?>>
                <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); if (isset($v['son'])) { ?><i class="wp0-0182c7 iconfont icon-jiantou"></i><?php } ?></a>
                <?php if (isset($v['son'])) { ?>
                <ul>
                    <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?>
                </ul>
                <?php } ?>
              	
            <?php }} ?>
              
              <?php if ($_uid) { ?>
                  <a href="/my-index.html" class="highlight-btn" 
                     style="padding:10px 20px; background:#3b82f6; color:white!important; border-radius:20px; font-weight:bold;">
                      个人中心
                  </a>
              <?php }else{ ?>
                  <a href="/user-login.html" class="highlight-btn" 
                     style="padding:10px 20px; background:#ef4444; color:white!important; border-radius:20px; font-weight:bold;">
                      登录/注册
                  </a>
              <?php } ?>
            </li>
          	
                    </ul>
                    <?php unset($data); ?>
        <a href="javascript:;" class="wp0-487abe sou"><i class="wp0-75d15b iconfont iconsou"></i></a>
                <div class="wp0-c144ef search">
            <div class="wp0-e849bd container">
            <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" class="wp0-470904 site-search-form">
                <input type="hidden" name="u" value="search-index" />
                <input type="hidden" name="mid" value="2" />
                <input class="search-input" type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" placeholder="输入关键字回车" />
                <input class="search-btn" type="submit" name="submit"  value=""  />
                    <i class="wp0-3d30c4 icon iconfont iconclose"></i>
                </form>
            </div>
        </div>
    </div>
</header>
<section class="wp0-31e2ae banner">
    <div class="wp0-e849bd container">
        <h2><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></h2>
        <div class="wp0-a640d1 search-form">
            <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" class="wp0-470904 site-search-form">
                <input type="hidden" name="u" value="search-index" />
                <input type="hidden" name="mid" value="2" />
                <input class="search-input" type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" />
                <input class="search-btn" type="submit" name="submit" value="<?php echo '搜索'; ?>" />
            </form>
        </div>
        <p>
        大家都在搜：
        <?php $data = block_taglist_rand(array (
  'mid' => '2',
  'limit' => '5',
  'life' => '600',
)); ?>
	    <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
        <a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" id="popular"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
	    <?php }} ?>
	    <?php unset($data); ?>
        </p>
    </div>
</section>
<section class="wp0-1db12a container mt-50 mb-50">
    <div class="wp0-c9e670 mtitle">
        <h2><span>最新知识<i>NEW</i></span></h2>
    </div>
    <div class="wp0-b248b9 piclist">
        <?php $data = block_list(array (
  'cid' => '0',
  'mid' => '2',
  'limit' => '28',
  'intronum' => '102',
  'showcate' => '1',
  'showviews' => '1',
)); ?>
        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                <div class="wp0-7dd6d9 li">
            <div class="wp0-e14748 img">
                <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a>
            </div>
            <a href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" class="wp0-aa640e cat"><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>
            <h3><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></h3>
            <div class="wp0-683ac4 meta">
                <span class="wp0-b74196 time"><i class="wp0-492abb iconfont iconshijian"></i> <?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
                <span class="wp0-170fe1 views"><i class="wp0-ec0aa6 iconfont iconchakan"></i> 热度:<?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span>
                            </div>
        </div>
        <?php }} ?>
      
	<?php if ($gdata['pages']) { ?>
<div class="wp0-2ec436 col-md-12 col-xs-12  th_padding" style="text-align:center; margin:20px 0">
  <div class="wp0-376edc list-title pagebar" libiao style="display:inline-block">
    <?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?>
  </div>
</div>
<?php } ?>

<style>
/* 基础分页样式 */
.pagebar a, .pagebar strong {
  display: inline-block;
  padding: 8px 12px;
  margin: 0 3px;
  color: #666;
  text-decoration: none;
}

/* 当前页样式 */
.pagebar strong {
  color: #333;
  font-weight: bold;
}

/* 悬停效果 */
.pagebar a:hover {
  color: #000;
}
</style>
      
        <?php unset($data); ?>
    </div>

    <div class="wp0-d3c3da clear"></div>
</section>

<footer class="wp0-4a1794 footer">
    <div class="wp0-e849bd container">
        <div class="wp0-c32f49 footer-links">
            <?php $data = block_links(array (
)); ?>
            <?php if ($data) { ?>
            <ul>
                <li>友情链接：</li>
                <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
                <?php }} ?>
                            </ul>
            <?php } ?>
            <?php unset($data); ?>
        </div>
        <p class="wp0-3f0d57 copyright">© <?php echo date("Y"); ?> <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>. ALL RIGHTS RESERVED.   |   备案号：<a href="https://beian.miit.gov.cn/" rel="nofollow">京ICP备88888888号</a></p>
    </div>
</footer>
<a href="javascript:void(0);" class="wp0-416ef9 back-to-top iconfont iconxiangshang cd-is-visible"></a>
<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/dscm.js"></script>
</body>
</html>