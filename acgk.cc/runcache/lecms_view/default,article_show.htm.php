<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_show($conf) { global $run, $_show, $_user; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $show_prev_next = isset($conf['show_prev_next']) && (int)$conf['show_prev_next'] ? true : false; $prev_next_cid = isset($conf['cid']) ? (int)$conf['cid'] : intval($_GET['cid']); $field_format = _int($conf, 'field_format', 0); $pageoffset = _int($conf, 'pageoffset', 5); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_show'); $cache_key = $life ? md5('global_show'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; if($mid == 1) return FALSE; $uid = isset($_user['uid']) ? (int)$_user['uid'] : 0; $run->cms_content_data->table = 'cms_'.$run->_var['table'].'_data'; $run->cms_content->format($_show, $mid, $dateformat, 0, 0, $field_format); $id = &$_show['id']; $_show['comment_url'] = $run->cms_content->comment_url($run->_var['cid'], $id); $_show['views_url'] = $run->_cfg['webdir'].'index.php?views--cid-'.$run->_var['cid'].'-id-'.$id; $data = $run->cms_content_data->get($id); if($data){ if($field_format && plugin_is_enable('models_filed')){ $models_field = $run->models_field->user_defined_field($mid); $run->models_field->field_val_format($models_field, $data, 0); } $_show += $data; $page = max(1,(int)R('page','G')); $_show = $run->cms_content_data->format_content($_show, $page); if( isset($_show['content_page']) && isset($_show['maxpage']) ){ $_show['pages'] = paginator::$page_function($page, $_show['maxpage'], $run->cms_content->content_url($_show, $mid, TRUE), $pageoffset); }else{ $_show['pages'] = false; } }else{ $_show['pages'] = false; } $run->cms_content_views->table = 'cms_'.$run->_var['table'].'_views'; $views_data = $run->cms_content_views->get($id); if($views_data){ if( empty($run->_cfg['close_views']) ){ $_show['views'] = $views_data['views']+1; $run->cms_content_views->update_views($id); }else{ $_show['views'] = $views_data['views']; } }else{ $_show['views'] = 1; empty($run->_cfg['close_views']) && $run->cms_content_views->set($id, array('views'=>1,'cid'=>$_show['cid'])); } if(isset($_show['filenum']) && !empty($_show['filenum'])){ list($attachlist, $imagelist, $filelist) = $run->cms_content_attach->attach_find_by_id($run->_var['table'], $id, array('id'=>$id, 'isimage'=>0)); $_show['filelist'] = $filelist; if($_show['uid'] == $uid && $uid){ $file_delete = true; }else{ $file_delete = false; } $_show['filelist_html'] = $run->cms_content_attach->file_list_html($filelist, $mid, $file_delete); }else{ $_show['filelist'] = array(); $_show['filelist_html'] = ''; } if( isset($run->_cfg['le_content_img_seo']) && !empty($run->_cfg['le_content_img_seo']) ){ $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/"; preg_match_all($pattern, $_show['content'], $match); $find_arr = array('{title}', '{cate_name}', '{webname}', '{count}'); if( isset($match[0]) ){ $img_count = 1; foreach ($match[0] as $k=>$img){ $replace_arr = array("{$_show['title']}", "{$run->_var['name']}", "{$run->_cfg['webname']}", $img_count); $new_alt = str_replace($find_arr, $replace_arr, $run->_cfg['le_content_img_seo']); if( stripos($img, "alt=") != false ){ $img_new = preg_replace('/alt=["\'](.*?)["\']/', 'alt="'.$new_alt.'"', $img); }else{ $img_new = str_replace_once('<img', '<img alt="'.$new_alt.'" ', $img); } if( stripos($img_new, "title=") != false ){ $img_new = preg_replace('/title=["\'](.*?)["\']/', '', $img_new); } if( strpos($img_new, $run->_cfg['webdomain']) === false ){ $img_new = str_replace_once('<img', '<img rel="external nofollow" referrerpolicy="no-referrer" ', $img_new); } $_show['content'] = str_replace_once($img, $img_new, $_show['content']); $img_count++; } unset($match[0]); } unset($find_arr); } if($show_prev_next) { if($prev_next_cid){ $prev_where = array('cid'=>$prev_next_cid, 'id'=>array('<'=> $id)); $next_where = array('cid'=>$prev_next_cid, 'id'=>array('>'=> $id)); }else{ $prev_where = array('id'=>array('<'=> $id)); $next_where = array('id'=>array('>'=> $id)); } $_show['prev'] = $run->cms_content->list_arr($prev_where, 'id', -1, 0, 1, 1, $extra); if($_show['prev']){ $_show['prev'] = current($_show['prev']); $run->cms_content->format($_show['prev'], $mid, $dateformat); } $_show['next'] = $run->cms_content->list_arr($next_where, 'id', 1, 0, 1, 1, $extra); if($_show['next']){ $_show['next'] = current($_show['next']); $run->cms_content->format($_show['next'], $mid, $dateformat); } }else{ $_show['prev'] = $_show['next'] = array(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $_show, $life); } if( isset($_show['favorites']) ){ $uid = isset($run->_user['uid']) ? (int)$run->_user['uid'] : 0; if( empty($uid) ){ $_show['has_favorites'] = 0; }else{ if( $run->favorites->find_fetch_key(array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id)) ){ $_show['has_favorites'] = 1; }else{ $_show['has_favorites'] = 0; } } $_show['favorites_url'] = $run->_cfg['webdir'].'index.php?show-favorites-mid-'.$mid.'-id-'.$id.'-uid-'.$uid.'.html'; }$le_keywords_links_setting = $run->kv->xget('le_keywords_links_setting'); if($le_keywords_links_setting){ $class = $le_keywords_links_setting['class'] == '' ? '' : ' class="'.$le_keywords_links_setting['class'].'"'; $target = $le_keywords_links_setting['target'] == 0 ? '' : ' target="_blank"'; $style = $class.$target; $keywords_links_arr = $run->keywords_links->find_fetch(array(), array('orderby'=>1, 'id' => 1)); if( $keywords_links_arr ){ $contentstr = $_show['content']; foreach ($keywords_links_arr as $keywords){ $patterns = '#(?=[^>]*(?=<(?!/a>)|$))'.$keywords['keyword'].'#'; $replacements = '<a href="'.$keywords['url'].'" '.$style.' title="'.$keywords['keyword'].'">'.$keywords['keyword'].'</a>'; $contentstr = preg_replace($patterns, $replacements, $contentstr, $keywords['count']); } $_show['content'] = $contentstr; unset($keywords_links_arr); } } return $_show; }
$gdata = block_global_show(array (
  'show_prev_next' => '1',
  'dateformat' => 'Y-m-d H:i:s',
));
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_comment($conf) { global $run, $_show; $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $firstnum = empty($conf['firstnum']) ? $pagenum : max(1, (int)$conf['firstnum']); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $humandate = isset($conf['humandate']) ? ($conf['humandate'] == 1 ? 1 : 0) : 1; $orderway = isset($conf['orderway']) && $conf['orderway'] == -1 ? -1 : 1; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_comment'); $cache_key = $life ? md5('comment'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $cid = &$run->_var['cid']; $mid = &$run->_var['mid']; if( isset($_show) ){ if($mid < 2){return false;} $id = &$_show['id']; }else{ $id = $cid; } if(isset($_show['comments']) && empty($_show['comments'])) return FALSE; $where = array('mid' => $mid, 'id' => $id); $list_arr = $run->cms_content_comment->list_arr($where, 'dateline', $orderway, 0, $firstnum, $pagenum, $extra); $reply_key = array(); $xuhao = $floor = 1; foreach($list_arr as &$v) { $run->cms_content_comment->format($v, $dateformat, $humandate); if($v['reply_commentid']) $reply_key[$v['commentid']] = $v['reply_commentid']; $v['floor'] = $floor++; $v['xuhao'] = $xuhao; $xuhao++; } if($reply_key){ $reply_list_arr = $run->cms_content_comment->mget($reply_key); foreach ($reply_key as $commentid=>$reply_commentid){ if( isset($list_arr['cms_comment-commentid-'.$commentid]) && isset($reply_list_arr['cms_comment-commentid-'.$reply_commentid]) ){ $reply_comment = $reply_list_arr['cms_comment-commentid-'.$reply_commentid]; $run->cms_content_comment->format($reply_comment, $dateformat, $humandate); $list_arr['cms_comment-commentid-'.$commentid]['reply_comment'] = $reply_comment; $list_arr['cms_comment-commentid-'.$commentid]['reply_comment_content'] = $reply_comment['content']; } } } $end_arr = end($list_arr); $commentid = $end_arr['commentid']; $orderway = max(0, $orderway); $dateformat = base64_encode($dateformat); $next_url = $run->_cfg['weburl']."index.php?comment-json-cid-$cid-id-$id-commentid-$commentid-orderway-$orderway-pagenum-$pagenum-dateformat-".encrypt($dateformat)."-humandate-$humandate-floor-{$floor}-ajax-1"; $isnext = count($list_arr) < $firstnum ? 0 : 1; $comment_url = $run->cms_content->comment_url($cid, $id); $ret = array('list' => $list_arr, 'next_url' => $next_url, 'isnext' => $isnext, 'comment_url' => $comment_url); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_user_info($conf) { global $run; $uid = _int($conf, 'uid', 0); $showgroup = _int($conf, 'showgroup', 0); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('user_info'.serialize($conf)) : ''; if($cache_key){ $list_arr = $run->runtime->get_block_data_cache($cache_key); if($list_arr){ return $list_arr; } } if( empty($uid) ){ global $_show; $uid = isset($_show['uid']) ? $_show['uid'] : 0; if( empty($uid) ){ return array(); } } $user = $run->user->get_user_by_uid($uid); if( empty($user) ){ return array(); } empty($user['author']) && $user['author'] = $user['username']; $run->user->format($user, $dateformat, $showgroup); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $user, $life); } return $user; }
 function block_list_by_uid($conf) { global $run; $uid = _int($conf, 'uid', 0); $mid = _int($conf, 'mid', 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list_by_uid'); $cache_key = $life ? md5('list_by_uid'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $run->cms_content->table = 'cms_'.$table; if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if( empty($uid) ){ global $_show; $uid = isset($_show['uid']) ? $_show['uid'] : 0; if( empty($uid) ){ return array('username'=>'', 'author'=>'', 'list'=>array()); } } $user = $run->user->get($uid); if( empty($user) ){ return array('username'=>'', 'author'=>'', 'list'=>array()); } $author = empty($user['author']) ? $user['username'] : $user['author']; $where['uid'] = $uid; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('username'=>$user['username'], 'author'=>$author, 'list'=>$list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $table = isset($run->_cfg['table_arr'][$mid]) ? $run->_cfg['table_arr'][$mid] : 'article'; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('tagid', 'count', 'orderby')) ? $conf['orderby'] : 'count'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = isset($conf['limit']) ? (int)$conf['limit'] : 10; $cms_limit = _int($conf, 'cms_limit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_taglist'); $cache_key = $life ? md5('taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $run->cms_content_tag->table = 'cms_'.$table.'_tag'; if($cms_limit){ $run->cms_content->table = 'cms_'.$table; $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; } $where = array(); $list_arr = $run->cms_content_tag->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } if($cms_limit){ $tag_data_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$v['tagid']), array('id'=>-1), 0, $cms_limit); $keys = array(); foreach($tag_data_arr as $lv) { $keys[] = $lv['id']; } $cms_arr = $run->cms_content->mget($keys); foreach($cms_arr as &$cv) { $run->cms_content->format($cv, $mid); } $v['cms'] = $cms_arr; unset($cms_arr); } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?> <!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;" name="viewport"/>
    <meta http-equiv="Cache-Control" content="no-transform"/>
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta name="applicable-device" content="pc,mobile">
    <title><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></title>
    <?php if ($gdata['seo_keywords']) { ?>
	<meta name="keywords" content="<?php echo(isset($gdata['seo_keywords']) ? $gdata['seo_keywords'] : ''); ?>" />
	<?php }elseif($gdata['tags']) { ?>
	<meta name="keywords" content="<?php echo implode(',',$gdata['tags']); ?>" />
	<?php }else{ ?>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<?php } ?>
    <meta name="androis" content=""/>
    <meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>"/>
    <link href="/css/swiper.min.css" rel="stylesheet" type="text/css">
    <link href="/css/mian.css" rel="stylesheet" type="text/css">
  	<link rel="stylesheet" href="//cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css" media="all">
	<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/style.css" type="text/css" media="all"/>
	<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js"></script>
  	<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>script/main.js"></script>
	<link href="/css/home.css" rel="stylesheet" type="text/css">
	<script src="/js/jquery.min.js"></script>
	<meta property="update_time" content="<?php  echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']); ?>">
    <meta property="published_time" content="<?php echo date('Y-m-d H:i:s'); ?>">
    <meta property="og:locale" content="zh_CN"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>"/>
    <meta property="og:width" content="800"/>
    <meta property="og:author" content="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>">
    <meta property="og:update_time" content="<?php  echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']); ?>">
    <meta property="og:published_time" content="<?php echo date('Y-m-d H:i:s'); ?>">
    <meta property="og:title" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"/>
    <meta property="og:keywords" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"/>
    <meta property="og:description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>"/>
    <style type="text/css">
	.entry-tag{font-size:0}.entry-tag a{display:inline-block;margin:0 10px 5px 0;padding:5px 15px;font-size:12px;font-size:0.8rem;line-height:1.2;color:#666;border:1px solid #999;border-radius:3px}.entry-tag a:focus,.entry-tag a:hover{color:#fff;background:#4285f4;border-color:#4285f4;text-decoration:none}img{width:100%;height:auto;display:block;clip-path:inset(0 0 25% 0)}
    </style>
</head>
<body>
<div class="jgacg-a6948a topmenu" id="tophead">
  <div class="jgacg-56e85d wrap">
    <div id="mobilemenu"></div>
    <div class="jgacg-c921c0 mask"></div>
    <?php $data = block_navigate(array (
)); ?>
    <div class="jgacg-f7604b menu">
      <ul id="nav">
        <li class="jgacg-65014f closex"><i class="jgacg-e835d3 iconfont icon-guanbi"></i></li>
        <li class="jgacg-bf3200 mainlevel"><a href="/"<?php if (empty($cfg_var['topcid'])) { ?> class="hover"<?php } ?>>首页</a></li>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
        <li class="jgacg-bf3200 mainlevel"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"<?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="hover"<?php } ?>><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
        <div class="jgacg-d3c3da clear"></div>
      </ul>
    </div><?php unset($data); ?>
   <div class="jgacg-304597 search" style="margin-right: 80px;width: 150px;">
    <?php if ($_uid) { ?>
        <a href="/my-index.html" class="jgacg-a87964 personal-center">个人中心</a>
    <?php }else{ ?>
        <a href="/user-login.html" class="jgacg-f8f8b5 login-register">登录/注册</a>
    <?php } ?>
</div>

<style>
.search a{display:inline-block;padding:10px 20px;border-radius:5px;text-decoration:none;font-size:16px;font-weight:bold;transition:background-color 0.3s,transform 0.3s}.personal-center{background-color:rgba(45,191,191,0.8);color:white}.login-register{background-color:#ff7f50;color:white}.search a:hover{transform:scale(1.05)}.personal-center:hover{background-color:rgba(45,191,191,1)}.login-register:hover{background-color:#ff6347}
</style>

    <div class="jgacg-c144ef search"><i class="jgacg-d83b34 iconfont icon-sousuo"></i></div>
  </div>
</div>

<div class="jgacg-20c82a subbody">
    <div class="jgacg-56e85d wrap">
        <div class="jgacg-a2c59a row">
            <div class="jgacg-d2576c left">
                <div class="jgacg-48ce35 article-con">
                    <div class="jgacg-64de1b postion">当前位置：<a href='<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>'><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a> > <a href="<?php echo(isset($cfg_var['place'][0]['url']) ? $cfg_var['place'][0]['url'] : ''); ?>"><?php echo(isset($cfg_var['place'][0]['name']) ? $cfg_var['place'][0]['name'] : ''); ?></a> > <?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>
                    </div>
                    <span id="shipinpp" style="display:none;"></span>
                    <div id="shipin" style="display:none">
                        <video width="100%" poster="" controls style="width: 100%;object-fit: fill;">
                            <source src="" type="video/mp4">
                        </video>
                        <div class="jgacg-13329a mbd_ad">
                            <div style="overflow:hidden;margin-top:-10px;">
                                <script type="text/javascript" src=""></script>
                            </div>
                        </div>
                    </div>

                    <!--视频自动播放 src="" autoplay="autoplay" -->
                    
                    <div class="jgacg-9fff1a art-con">
                        <h1><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h1>
                        <div class="jgacg-08ff87 posts-default-info" id="neironga">
                          	<div class="jgacg-891f06 detail-icon">
									<p class="jgacg-683ac4 meta">
										<span><i class="jgacg-16109c fa fa-calendar" aria-hidden="true"></i>&nbsp;<?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?></span>
										<span><i class="jgacg-5ea52c fa fa-eye" aria-hidden="true"></i>&nbsp;阅读&nbsp;<?php echo(isset($gdata['views']) ? $gdata['views'] : ''); ?></span>
										<span><i class="jgacg-58067c fa fa-comments" aria-hidden="true"></i>&nbsp;评论&nbsp;<?php echo(isset($gdata['comments']) ? $gdata['comments'] : ''); ?></span>
									</p>
							</div>
                        </div>
                        <div class="jgacg-7d4933 art-txt" id="quanwen">
                            <?php echo(isset($gdata['content']) ? $gdata['content'] : ''); ?>
                        <br /><br /><p>
<?php if (isset($gdata['favorites'])) { ?>
<style>
        .content_favorites {
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background-color: #f9f9f9; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
        }

        #favorites_do {
            margin-right: 5px; 
            color: #ffcc00;
        }

        #favorites_html {
            font-weight: bold; 
        }

        #favorites_count {
            color: #888;
        }
</style>
<div class="jgacg-ead73c content_favorites">
    <?php if ($gdata['has_favorites']) { ?>
    <i id="favorites_do" class="jgacg-044cd4 fa fa-star" aria-hidden="true"><font id="favorites_html">取消收藏</font></i> 
    <?php }else{ ?>
    <i id="favorites_do" class="jgacg-1980eb fa fa-star-o" aria-hidden="true"><font id="favorites_html">加入收藏</font></i>
    <?php } ?>
</div>
<script type="text/javascript">
    $("#favorites_do").click(function () {
        $.getJSON("<?php echo(isset($gdata['favorites_url']) ? $gdata['favorites_url'] : ''); ?>", function(data){
            if(data.err){
                alert(data.msg);
            }else{
                if( data.has_favorites == 1 ){
                    $("#favorites_do").attr("class", "fa fa-star");
                    $("#favorites_html").html("取消收藏");
                }else{
                    $("#favorites_do").attr("class", "fa fa-star-o");
                    $("#favorites_html").html("加入收藏");
                }
                $("#favorites_count").html(data.favorites_count);
                alert(data.msg);
            }
        });
    });
</script>
<?php } ?></p><br /><br />
                        </div>
                        <div class="jgacg-743b9a shareBox">
                            <div class="jgacg-b587e3 entry-tag" align="left">
                                <?php if (isset($gdata['tag_arr'])) { ?>
                                <?php if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?><a class="jgacg-ca7e45 th_hover_a1" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?>
                                <?php } ?>
                            </div>
                        </div>
                        <?php if (isset($gdata['prev']['url']) || isset($gdata['next']['url'])) { ?>
                        <div class="jgacg-fb93ef pronext">
                            <div class="jgacg-1b0119 propage"><span>上一篇：<?php if (isset($gdata['prev']['url'])) { ?><a href="<?php echo(isset($gdata['prev']['url']) ? $gdata['prev']['url'] : ''); ?>" title="<?php echo(isset($gdata['prev']['title']) ? $gdata['prev']['title'] : ''); ?>"><?php echo(isset($gdata['prev']['title']) ? $gdata['prev']['title'] : ''); ?></a><?php }else{ ?>没有了<?php } ?>  </span></div>
                            <div class="jgacg-9d3823 nextpage"><span>下一篇：<?php if (isset($gdata['next']['url'])) { ?><a href="<?php echo(isset($gdata['next']['url']) ? $gdata['next']['url'] : ''); ?>" title="<?php echo(isset($gdata['next']['title']) ? $gdata['next']['title'] : ''); ?>"><?php echo(isset($gdata['next']['title']) ? $gdata['next']['title'] : ''); ?></a><?php }else{ ?>没有了<?php } ?>  </span></div>
                        </div><?php } ?>
                    </div>
                    
                    <?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '10',
  'life' => '600',
  'showviews' => '1',
)); ?>
                    <div class="jgacg-33bd1e thleftcon">
                        <div class="jgacg-686f42 thleftbt"><span>猜你喜欢</span></div>
                        <ul class="jgacg-8cd5fc th-5">
                            <?php $rank = 1; ?>
                            <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                            <li class="jgacg-1cec22 post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>"><span class="jgacg-e1992f date"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span><i><?php echo(isset($rank) ? $rank : ''); ?></i><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
                            <?php $rank++; ?>
                            <?php }} ?>
                        </ul>
                    </div>
                    <?php unset($data); ?>
					<!--评论列表start-->
							<?php $data = block_comment(array (
  'pagenum' => '10',
  'firstnum' => '10',
)); ?>
							<?php if (!empty($data['list'])) { ?>
							<div class="jgacg-baf81c comment_list" id="ctf">
								<p class="jgacg-bd6f38 posttop" style="border-bottom: 1px solid #eaeaea;"><a name="comment" href="javascript:;"> 评论列表:</a></p>
								<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
								<ul class="jgacg-d154bc msg" id="comment_<?php echo(isset($v['commentid']) ? $v['commentid'] : ''); ?>">
									<li class="jgacg-c7e7fc msgname">
										<img class="jgacg-a4ff4e avatar" src="<?php echo(isset($v['avatar']) ? $v['avatar'] : ''); ?>" width="32">&nbsp;
										<span class="jgacg-5c9efa commentname"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></span><br><small>&nbsp;发布于&nbsp;<?php echo(isset($v['date']) ? $v['date'] : ''); ?></small><small><a class="jgacg-99cd24 reply_comment" href="javascript:;" onclick="reply_comment(<?php echo(isset($v['commentid']) ? $v['commentid'] : ''); ?>);">回复该评论</a></small>
									</li>
									<?php if ($v['reply_comment_content']) { ?><li class="jgacg-23132f reply_msgarticle">回复：<?php echo(isset($v['reply_comment_content']) ? $v['reply_comment_content'] : ''); ?></li><?php } ?>
									<li class="jgacg-c6ed30 msgarticle"><?php echo(isset($v['content']) ? $v['content'] : ''); ?></li>
								</ul>
								<?php }} ?>
								<a id="load_more" href="javascript:;" next_url="<?php echo(isset($data['next_url']) ? $data['next_url'] : ''); ?>" isnext="<?php echo(isset($data['isnext']) ? $data['isnext'] : ''); ?>">显示更多评论</a>
							</div>
							<?php } ?>
							<?php unset($data); ?>
							<!--评论列表end-->
						
							<!--发布评论start-->
                  			<?php $data = block_comment(array (
  'pagenum' => '20',
  'firstnum' => '20',
)); ?>
							<div id="divCommentPost" class="jgacg-1a4e1b post">
								<p class="jgacg-59955d posttop"><a name="comment" href="javascript:;">发表评论:</a></p>
								<form id="ctf_form" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-post-ajax-1.html" method="post">
									<input type="hidden" name="cid" value="<?php echo(isset($gdata['cid']) ? $gdata['cid'] : ''); ?>" />
									<input type="hidden" name="id" value="<?php echo(isset($gdata['id']) ? $gdata['id'] : ''); ?>" />
									<input type="hidden" name="reply_commentid" value="0" />
									<div id="reply_comment_div">
										<p class="jgacg-59955d posttop"><a>回复评论:</a>
											<a rel="nofollow" id="cancel_reply_comment" href="javascript:;" onclick="cancel_reply_comment();"><small>取消回复</small></a>
										</p>
										<div id="reply_comment_content"></div>
									</div>
									<?php if ($_uid) { ?>
									<input type="hidden" name="author" value="<?php echo(isset($author) ? $author : ''); ?>" />
									<?php }else{ ?>
									<p style="margin-top:10px"><input type="text" name="author" id="ctf_author" class="jgacg-63d447 text" value="<?php echo(isset($author) ? $author : ''); ?>" placeholder="昵称" size="28" tabindex="1"></p>
									<?php } ?>
									<?php if ($cfg['open_comment_vcode']) { ?>
									<p style="margin-top:10px;margin-left: 0;" class="jgacg-a2c59a row">
										<span class="jgacg-b118e1 col-md-4 col-xs-6" style="padding-left: 0;">
											<input name="vcode" id="ctf_vcode" type="text" class="text" required placeholder="图形验证码" autocomplete="off" />
										</span>
										<span class="jgacg-aba66a col-md-4 col-xs-6">
											<img id="captchaPic" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode" onclick="this.src='<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode-r-'+Math.random();" alt="验证码" />
										</span>
									</p>
									<?php } ?>
									<p style="margin-top:10px"><textarea name="content" id="ctf_content" maxlength="255" class="jgacg-edb4e1 text" cols="50" rows="4" tabindex="5" placeholder="文明上网，理性发言" autocomplete="off"></textarea></p>
									<p><input name="sumbit" type="submit" tabindex="6" value="提交" id="ctf_submit" class="jgacg-e6a628 button"></p>
								</form>
								<p class="jgacg-8f8065 postbottom"></p>
							</div>
                  			<?php unset($data); ?>
							<!--发布评论end-->
                </div>
				</div>
                <!--左侧文章列表end-->
                <div class="jgacg-53aaf2 col-md-3 col-xs-12 wap_margintop">
                    <div><?php $data = block_user_info(array (
)); ?>
<section>
	<div class="jgacg-f6cbca thleftcon" style="">
		<div class="jgacg-a37502 thleftcon-1">
			<img class="jgacg-df1d0e th-img jsimg-height" src="https://th.bing.com/th/id/OIP.6KIPaw1hWSTCfEpeBR7KfgAAAA?rs=1&pid=ImgDetMain1">
			<img class="jgacg-a7a81e th-img jsimg-toux" src="https://th.bing.com/th/id/OIP.6KIPaw1hWSTCfEpeBR7KfgAAAA?rs=1&pid=ImgDetMain1">
		</div>
		<div class="jgacg-6862df thjs_infor">
			<h4><?php echo(isset($data['author']) ? $data['author'] : ''); ?></h4>
			<div class="jgacg-d3c3da clear"></div>
		</div>
		<div class="jgacg-55f5a0 thleftcon-2">
			<dl>
				<dd><?php echo(isset($data['intro']) ? $data['intro'] : ''); ?></dd>
			</dl>
		</div>
		<div class="jgacg-7538e5 aut_count">
			<ul>
				<li><span>内容</span><strong><?php echo(isset($data['contents']) ? $data['contents'] : ''); ?></strong></li>
				<li><span>积分</span><strong><?php echo(isset($data['credits']) ? $data['credits'] : ''); ?></strong></li>
				<li><span>金币</span><strong><?php echo(isset($data['golds']) ? $data['golds'] : ''); ?></strong></li>
			</ul>
		</div>
	</div>
<?php unset($data); ?>
<?php $data = block_list_by_uid(array (
  'mid' => '2',
  'limit' => '12',
  'titlenum' => '24',
  'dateformat' => 'm-d',
)); ?>
	<div class="jgacg-33bd1e thleftcon">
		<ul class="jgacg-8cd5fc th-5">
			<?php $rank = 1; ?>
			<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
			<li class="jgacg-1cec22 post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>"><span class="jgacg-e1992f date"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span><i><?php echo(isset($rank) ? $rank : ''); ?></i><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
			<?php $rank++; ?>
			<?php }} ?>
		</ul>
	</div>
</section>
<?php unset($data); ?></div>
                    <!--当前分类阅读排行榜start-->
                    <div class="jgacg-25b65b th_margintop">
                        <?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '18',
  'titlenum' => '24',
)); ?>
                        <section>
                            <div class="jgacg-33bd1e thleftcon">
                                <div class="jgacg-686f42 thleftbt"><span>阅读排行</span></div>
                                <ul class="jgacg-8cd5fc th-5">
                                    <?php $rank = 1; ?>
                                    <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                                    <li class="jgacg-1cec22 post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>"><span class="jgacg-e1992f date"><?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span><i><?php echo(isset($rank) ? $rank : ''); ?></i><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
                                    <?php $rank++; ?>
                                    <?php }} ?>
                                </ul>
                            </div>
                        </section>
                        <?php unset($data); ?>
                    </div>
                    <!--当前分类阅读排行榜end-->
                    <!--热门标签start-->
                    <div class="jgacg-25b65b th_margintop">
                        <section>
                            <div class="jgacg-33bd1e thleftcon">
                                <div class="jgacg-686f42 thleftbt"><span>热门标签</span></div>
                                <ul class="jgacg-9e001c th-7">
                                    <?php $data = block_taglist(array (
  'mid' => '2',
  'limit' => '18',
  'orderby' => 'count',
)); ?>
                                    <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                                    <li class="jgacg-f6715d tag_id_<?php echo(isset($v['tagid']) ? $v['tagid'] : ''); ?>"><a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
                                    <?php }} ?>
                                    <?php unset($data); ?>
                                </ul>
                            </div>
                        </section>
                    </div>
                    <!--热门标签end-->
                </div>
            </div>
        </div>
    </div>
</div>

<div class="jgacg-4a1794 footer">
    <div class="jgacg-56e85d wrap">
        <div class="jgacg-203fb9 copyright-footer">
            <p>COPYRIGHT © 2023 <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>. ALL RIGHTS RESERVED.  
                <br/><a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow" <?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?>><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a></p>
            <p>只给您分享有用的学习知识！</p>
        </div>
    </div>
</div>
<!-- 返回顶部按钮-->
<div class="jgacg-7aa8b7 backtop" id="backtop"><i class="jgacg-ee7567 iconfont icon-xiangshang"></i></div>
<!-- 搜索框-->
<div class="jgacg-c2b9e3 search-box">
  <div class="jgacg-cf3acb search-close"><i class="jgacg-e835d3 iconfont icon-guanbi"></i></div>
  <div class="jgacg-dee095 search-con">
    <dl class="jgacg-418446 se">
      <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form">
						<dt><input type="hidden" name="u" value="search-index" />
						<input type="hidden" name="mid" value="2" />
						<input type="text" class="search-keyword" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" /></dt>
						<dd>
          <button type="submit"><i class="jgacg-d83b34 iconfont icon-sousuo"></i></button>
        </dd>
					</form>
    </dl>
  </div>
<script type="text/javascript" src="/js/jquery.min.js"></script> 
<script type="text/javascript" src="/js/swiper.min.js"></script> 
<script type="text/javascript" src="/js/slide.js"></script>
<script language="javascript" type="text/javascript" src="/js/go.js"></script>
<script>
(function() {
            $("#search_form,#search_form2").submit(function() {
            var mid = $(this).find("[name='mid']").val();
            var keyword = $(this).find("[name='keyword']").val();
            window.location.href = "/search/" + encodeURIComponent(keyword) + "/";
            return false;
            });
            })();
</script>
<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/pxmu.min.js"></script>
<script type="text/javascript">
//回复某评论
function reply_comment(commentid){
	$("input[name='reply_commentid']").val(commentid);

	var comment = $("#comment_"+commentid+">.msgarticle").html();
	$("#reply_comment_content").html(comment);
	$("#reply_comment_div").show();
}
//取消回复某评论
function cancel_reply_comment() {
	$("input[name='reply_commentid']").val(0);
	$("#reply_comment_div").hide();
}
//加载更多评论
(function(){
	var obj = $("#load_more");
	var next_url = obj.attr("next_url");
	var isnext = obj.attr("isnext");

	var no_more = function() {
		obj.html("没有更多评论了");
		if(typeof load_more != "undefined") obj.off("click", load_more);
	}

	if(isnext < 1) { no_more(); return; }

	var leJosnLock = false;
	var load_more = function() {
		if(!next_url || leJosnLock) return;
		obj.html("玩命加载中...");
		leJosnLock = true;

		$.get(next_url, function(data) {
			try{
				var json = eval("("+data+")");
				next_url = json.next_url;
				$.each(json.list_arr, function(i,item) {
					var s = '<ul class="jgacg-1ce205 msg" id="comment_'+item.commentid+'">';
						s += '<li class="jgacg-c7e7fc msgname"><img class="jgacg-3c8bef avatar" src="'+item.avatar+'" width="32">&nbsp;';
						s += '<span class="jgacg-5c9efa commentname">'+item.author+'</span><br><small>发布于&nbsp;'+item.date+'</small><small><a class="jgacg-0b4e4c reply_comment" href="javascript:;" onclick="reply_comment('+item.commentid+');">回复该评论</a></small></li>';

						if(item.reply_comment_content){
							s += '<li class="jgacg-23132f reply_msgarticle">回复：'+item.reply_comment_content+'</div>';
						}

						s += '<li class="jgacg-c6ed30 msgarticle">'+item.content+'</div>';
						s += '</ul>';
					$(".comment_list>ul:last").after(s);
				});

				obj.html("显示更多评论");
				leJosnLock = false;
				if(json.isnext < 1) no_more();
			}catch(e){
				alert(data);
			}
		});
	}
	obj.click(load_more);
})();
</script>

<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/js/jquery.js"></script>
<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/layer/layer.js"></script>
<script>
    $('#sj_kami_buy').on('click',function (e){
        var aid=$(this).attr('data-id');
        var golds=$(this).attr('data-golds');
        $.get('/my-userinfo.html','',function(data){
            if(!data.err){
                layer.confirm('您当前有 <span style="color: red">'+data.data.golds+' </span>金币。本次购买需消耗 <span style="color: red">'+golds+' </span>金币!<br/>确认进行购买操作？', {
                  btn: ['确定','取消'],icon:3
                }, function(){
                    $.post('/my-userinfo.html',{id:aid},function(data){
                        if(!data.err){
                            layer.alert(data.msg,{icon:1},function (e){
                                window.location.reload()
                            })
                        }else{
                            layer.alert(data.msg,{icon:2})
                        }
                    },'json');
                });
            }else{
                layer.msg('余额获取失败！')
            }
        },'json');
    })
</script>
<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui/layers-V2.8.js"></script>
  <footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>