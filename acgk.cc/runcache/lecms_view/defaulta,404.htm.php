<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
?><!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" >
<meta http-equiv="Cache-Control" content="no-transform"/>
<meta http-equiv="Cache-Control" content="no-siteapp"/>
<meta name="applicable-device" content="pc,mobile">
<link rel="shortcut icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.ico" />
<link rel="icon" sizes="32x32" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png">
<link rel="Bookmark" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>img/favicon.png" />
<title>404页面未找到 - 飞雪ACG</title>
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-v2.css?1.0">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/bootstrap-bbs-v2.css?1.0">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/huux-notice.css" name="huux_notice">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/tag.css?1.0">
<style>
.haya-post-info-username.today .username {color: var(--danger) !important;}
.haya-post-info-username.today .date {color: var(--danger) !important;}
.nav-link.buy:hover{color: red !important;}
.thread .badge{display: none;} .t_icon {display: none;}.mt-3, .my-3{margin-top: 0.3rem !important;}.card.card-threadlist {margin-bottom: 1.3rem;}
</style>
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/sign.css">
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/jquery-labelauty.css">
<link rel="stylesheet" type="text/css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/verify.css">
<?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?>
</head>

<body>

	<header class="navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="container">
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="展开菜单">
				<span class="navbar-toggler-icon"></span>
			</button>

			<a class="navbar-brand text-truncate" href="/" title="飞雪ACG">飞雪ACG</a>
			<a class="navbar-brand hidden-lg" href="/search/" aria-label="搜索"><i class="icon-search"></i></a></a>
			<div class="collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				<?php $data = block_navigate(array (
)); ?>
				<ul class="navbar-nav mr-auto">
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="nav-item home  active"<?php }else{ ?> class="nav-item home"<?php } ?>><a class="nav-link" href="/"><i class="icon-home d-md-none"></i> 首页</a></li>	
					<?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
					<li<?php if ($cfg_var['cid'] == $v['cid']) { ?> class="nav-item  active"<?php }else{ ?> class="nav-item"<?php } ?>>
						<a class="nav-link" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><i class="icon-circle-o d-md-none"></i><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
					</li>
					<?php }} ?>
                  	<li class="nav-item home" class="nav-item home" style="color: red;font-size: 18px;">
                  	<?php if ($_uid) { ?>
                        <a href="/my-index.html" class="nav-link">个人中心</a>
                    <?php }else{ ?>
                        <a href="/user-login.html" class="nav-link">登录/注册</a>
                    <?php } ?>
                  	</li>
				</ul><?php unset($data); ?>
				<!-- 右侧：用户 -->
				<ul class="navbar-nav">
					<li class="nav-item"><a class="nav-link" href="/search/"><i class="icon-search"></i> 搜索</a></li>
				</ul>
			</div>
		</div>
	</header>
	
	<main id="body">
		<div class="container">
<div class="row">
	<div class="col-lg-8 mx-auto">
		<div class="card mt-4">
			<div class="card-body">
				<h4 class="card-title text-center mb-0">
					<i class="icon-warning-sign"></i>404，文章未找到！</h4>
				
			</div>
		</div>
	</div>
</div>
				
		</div>
	</main>

	<footer class="text-muted small bg-dark py-4 mt-3" id="footer">
	<div class="container">
		<div class="row">
			<div class="col">
			<?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?>
			</div>
			<!--<div class="col text-right">
			
			</div>-->
		</div>
	</div>
</footer>
<style>@media (max-width:576px){.col-lg-3.msign{padding:0 0.5rem 0 0.5rem !important;}}@media (min-width:768px){.container{padding-left:0;}}@media (min-width:576px){.container{padding-left:0;}.col-lg-3.msign{padding-left:1rem !important;}}@media (min-width:992px){.col-lg-3.msign{padding-left:0rem !important;}}@media (max-width:576px){#body > .container > .row > div{padding-bottom:0 !important;}}</style>
	
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-3.1.0.js?1.0"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/bootstrap.js?1.0"></script>
<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/scroll.css">
<div id="scroll_to_list" style="position: fixed; _position: absolute; bottom: 20px; right: 10px; width: 70px; height: 70px;">
<a id="scroll_to_top" href="javascript:void(0);"  class="mui-rightlist"  title="返回顶部" style="display: none;"><i class="icon-angle-double-up"></i></a>
</div>

<script>
var jscroll_to_top = $('#scroll_to_top');
$(window).scroll(function() {
	if ($(window).scrollTop() >= 500) {
	   jscroll_to_top.fadeIn(300);
	} else {
		jscroll_to_top.fadeOut(300);
	}
});

jscroll_to_top.on('click', function() {
	$('html,body').animate({scrollTop: '0px' }, 100);
});
</script>
<footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>