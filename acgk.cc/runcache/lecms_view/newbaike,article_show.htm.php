<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_show($conf) { global $run, $_show, $_user; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $show_prev_next = isset($conf['show_prev_next']) && (int)$conf['show_prev_next'] ? true : false; $prev_next_cid = isset($conf['cid']) ? (int)$conf['cid'] : intval($_GET['cid']); $field_format = _int($conf, 'field_format', 0); $pageoffset = _int($conf, 'pageoffset', 5); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_show'); $cache_key = $life ? md5('global_show'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; if($mid == 1) return FALSE; $uid = isset($_user['uid']) ? (int)$_user['uid'] : 0; $run->cms_content_data->table = 'cms_'.$run->_var['table'].'_data'; $run->cms_content->format($_show, $mid, $dateformat, 0, 0, $field_format); $id = &$_show['id']; $_show['comment_url'] = $run->cms_content->comment_url($run->_var['cid'], $id); $_show['views_url'] = $run->_cfg['webdir'].'index.php?views--cid-'.$run->_var['cid'].'-id-'.$id; $data = $run->cms_content_data->get($id); if($data){ if($field_format && plugin_is_enable('models_filed')){ $models_field = $run->models_field->user_defined_field($mid); $run->models_field->field_val_format($models_field, $data, 0); } $_show += $data; $page = max(1,(int)R('page','G')); $_show = $run->cms_content_data->format_content($_show, $page); if( isset($_show['content_page']) && isset($_show['maxpage']) ){ $_show['pages'] = paginator::$page_function($page, $_show['maxpage'], $run->cms_content->content_url($_show, $mid, TRUE), $pageoffset); }else{ $_show['pages'] = false; } }else{ $_show['pages'] = false; } $run->cms_content_views->table = 'cms_'.$run->_var['table'].'_views'; $views_data = $run->cms_content_views->get($id); if($views_data){ if( empty($run->_cfg['close_views']) ){ $_show['views'] = $views_data['views']+1; $run->cms_content_views->update_views($id); }else{ $_show['views'] = $views_data['views']; } }else{ $_show['views'] = 1; empty($run->_cfg['close_views']) && $run->cms_content_views->set($id, array('views'=>1,'cid'=>$_show['cid'])); } if(isset($_show['filenum']) && !empty($_show['filenum'])){ list($attachlist, $imagelist, $filelist) = $run->cms_content_attach->attach_find_by_id($run->_var['table'], $id, array('id'=>$id, 'isimage'=>0)); $_show['filelist'] = $filelist; if($_show['uid'] == $uid && $uid){ $file_delete = true; }else{ $file_delete = false; } $_show['filelist_html'] = $run->cms_content_attach->file_list_html($filelist, $mid, $file_delete); }else{ $_show['filelist'] = array(); $_show['filelist_html'] = ''; } if( isset($run->_cfg['le_content_img_seo']) && !empty($run->_cfg['le_content_img_seo']) ){ $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/"; preg_match_all($pattern, $_show['content'], $match); $find_arr = array('{title}', '{cate_name}', '{webname}', '{count}'); if( isset($match[0]) ){ $img_count = 1; foreach ($match[0] as $k=>$img){ $replace_arr = array("{$_show['title']}", "{$run->_var['name']}", "{$run->_cfg['webname']}", $img_count); $new_alt = str_replace($find_arr, $replace_arr, $run->_cfg['le_content_img_seo']); if( stripos($img, "alt=") != false ){ $img_new = preg_replace('/alt=["\'](.*?)["\']/', 'alt="'.$new_alt.'"', $img); }else{ $img_new = str_replace_once('<img', '<img alt="'.$new_alt.'" ', $img); } if( stripos($img_new, "title=") != false ){ $img_new = preg_replace('/title=["\'](.*?)["\']/', '', $img_new); } if( strpos($img_new, $run->_cfg['webdomain']) === false ){ $img_new = str_replace_once('<img', '<img rel="external nofollow" referrerpolicy="no-referrer" ', $img_new); } $_show['content'] = str_replace_once($img, $img_new, $_show['content']); $img_count++; } unset($match[0]); } unset($find_arr); } if($show_prev_next) { if($prev_next_cid){ $prev_where = array('cid'=>$prev_next_cid, 'id'=>array('<'=> $id)); $next_where = array('cid'=>$prev_next_cid, 'id'=>array('>'=> $id)); }else{ $prev_where = array('id'=>array('<'=> $id)); $next_where = array('id'=>array('>'=> $id)); } $_show['prev'] = $run->cms_content->list_arr($prev_where, 'id', -1, 0, 1, 1, $extra); if($_show['prev']){ $_show['prev'] = current($_show['prev']); $run->cms_content->format($_show['prev'], $mid, $dateformat); } $_show['next'] = $run->cms_content->list_arr($next_where, 'id', 1, 0, 1, 1, $extra); if($_show['next']){ $_show['next'] = current($_show['next']); $run->cms_content->format($_show['next'], $mid, $dateformat); } }else{ $_show['prev'] = $_show['next'] = array(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $_show, $life); } if( isset($_show['favorites']) ){ $uid = isset($run->_user['uid']) ? (int)$run->_user['uid'] : 0; if( empty($uid) ){ $_show['has_favorites'] = 0; }else{ if( $run->favorites->find_fetch_key(array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id)) ){ $_show['has_favorites'] = 1; }else{ $_show['has_favorites'] = 0; } } $_show['favorites_url'] = $run->_cfg['webdir'].'index.php?show-favorites-mid-'.$mid.'-id-'.$id.'-uid-'.$uid.'.html'; }$le_keywords_links_setting = $run->kv->xget('le_keywords_links_setting'); if($le_keywords_links_setting){ $class = $le_keywords_links_setting['class'] == '' ? '' : ' class="'.$le_keywords_links_setting['class'].'"'; $target = $le_keywords_links_setting['target'] == 0 ? '' : ' target="_blank"'; $style = $class.$target; $keywords_links_arr = $run->keywords_links->find_fetch(array(), array('orderby'=>1, 'id' => 1)); if( $keywords_links_arr ){ $contentstr = $_show['content']; foreach ($keywords_links_arr as $keywords){ $patterns = '#(?=[^>]*(?=<(?!/a>)|$))'.$keywords['keyword'].'#'; $replacements = '<a href="'.$keywords['url'].'" '.$style.' title="'.$keywords['keyword'].'">'.$keywords['keyword'].'</a>'; $contentstr = preg_replace($patterns, $replacements, $contentstr, $keywords['count']); } $_show['content'] = $contentstr; unset($keywords_links_arr); } } return $_show; }
$gdata = block_global_show(array (
  'show_prev_next' => '1',
));
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?>
<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
	<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
  	<title><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></title>
    <?php if ($gdata['seo_keywords']) { ?>
	<meta name="keywords" content="<?php echo(isset($gdata['seo_keywords']) ? $gdata['seo_keywords'] : ''); ?>" />
	<?php }elseif($gdata['tags']) { ?>
	<meta name="keywords" content="<?php echo implode(',',$gdata['tags']); ?>" />
	<?php }else{ ?>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<?php } ?>
  	<meta property="update_time" content="<?php  echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']); ?>">
    <meta property="published_time" content="<?php echo date('Y-m-d H:i:s'); ?>">
    <meta property="og:locale" content="zh_CN"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>"/>
    <meta property="og:width" content="800"/>
    <meta property="og:author" content="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>">
    <meta property="og:update_time" content="<?php  echo date('Y-m-d', $gdata['dateline']).' '.date('H:i:s', $gdata['dateline']); ?>">
    <meta property="og:published_time" content="<?php echo date('Y-m-d H:i:s'); ?>">
    <meta property="og:title" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?> - <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"/>
    <meta property="og:keywords" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"/>
    <meta property="og:description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>"/>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <link rel="icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/img/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/style.css">
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/iconfont.css">
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/share.min.css" type="text/css">
    <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/jquery.min.js"></script>
    <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/ckplayer.js"></script>
    <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/ajax.js"></script>
    <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/a00.js"></script>
    <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/js/m.js"></script>
    <!--[if lt IE 9]>
      <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/js/html5shiv.min.js"></script>
      <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/js/respond.min.js"></script>
    <![endif]-->
</head>
<body>
    <header class="xylt-9079aa menu active">
        <div class="xylt-e849bd container">
            <a href="/" class="xylt-f1fd44 logo-a">
                <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/img/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" style="width: 180px;"/>
            </a>
            <?php $data = block_navigate(array (
)); ?>
            <ul class="xylt-d4dab2 navbar">
                <li <?php if (empty($cfg_var['topcid'])) { ?> class="xylt-928a6a act"<?php } ?>><a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"><?php echo '首页'; ?></a></li>
                <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                <li<?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="act"<?php } ?>>
                    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); if (isset($v['son'])) { ?><i class="xylt-0182c7 iconfont icon-jiantou"></i><?php } ?></a>
                    <?php if (isset($v['son'])) { ?>
                    <ul>
                        <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?>
                    </ul>
                    <?php } ?>
                </li>
                <?php }} ?>
              	<?php if ($_uid) { ?>
                      <a href="/my-index.html" class="highlight-btn" 
                         style="padding:10px 20px; background:#3b82f6; color:white!important; border-radius:20px; font-weight:bold;">
                          个人中心
                      </a>
                  <?php }else{ ?>
                      <a href="/user-login.html" class="highlight-btn" 
                         style="padding:10px 20px; background:#ef4444; color:white!important; border-radius:20px; font-weight:bold;">
                          登录/注册
                      </a>
                  <?php } ?>
                        </ul>
                        <?php unset($data); ?>
            <a href="javascript:;" class="xylt-487abe sou"><i class="xylt-75d15b iconfont iconsou"></i></a>
                    <div class="xylt-c144ef search">
            <div class="xylt-e849bd container">
            <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" class="xylt-470904 site-search-form">
                <input type="hidden" name="u" value="search-index" />
                <input type="hidden" name="mid" value="2" />
                <input class="search-input" type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" placeholder="输入关键字回车" />
                <input class="search-btn" type="submit" name="submit"  value=""  />
                    <i class="xylt-3d30c4 icon iconfont iconclose"></i>
                </form>
            </div>
        </div>
        </div>
    </header>

<section class="xylt-1db12a container mt-50 mb-50">

    
    <div class="xylt-024de5 breadcrumbs clear">当前位置：<a href="/">首页</a>&nbsp;>&nbsp;<?php if(isset($cfg_var['place']) && is_array($cfg_var['place'])) { foreach($cfg_var['place'] as &$v) { ?><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?></div>
    <div class="xylt-059710 detail">
        <div class="xylt-1bc3a4 content">
            <div class="xylt-489a4a article">
                <div class="xylt-396998 header">
                    <h1 class="xylt-4209da title"><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h1>
                    <div class="xylt-683ac4 meta">
                        <span class="xylt-1f54da item"><i class="xylt-492abb iconfont iconshijian"></i><?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?></span>
                        <span class="xylt-1f54da item"><i class="xylt-8d3fbb iconfont icondizhi"></i><?php if(isset($cfg_var['place']) && is_array($cfg_var['place'])) { foreach($cfg_var['place'] as &$v) { ?><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?></span>
                        <span class="xylt-1f54da item"><i class="xylt-ec0aa6 iconfont iconchakan"></i> 热度:<?php echo(isset($gdata['views']) ? $gdata['views'] : ''); ?>℃</span>
                    </div>
                </div>
                <!-- 摘要 -->
                <div class="xylt-3639cb smalltext"><span class="xylt-d6194e answer">摘要</span><?php echo(isset($gdata['intro']) ? $gdata['intro'] : ''); ?></div>
                <!-- 正文 -->
 
                <div class="xylt-728195 text">
                 <?php if ($_uid) { ?>
                  <div class="notice" style="
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    border-radius: 12px;
    padding: 18px;
    margin: 25px 0;
    box-shadow: 0 4px 15px rgba(255, 77, 77, 0.3);
    border: 2px solid #fff;
    position: relative;
    overflow: hidden;
    animation: alertPulse 2s infinite;
">
    <a href="https://www.777723.xyz/" 
       target="_blank" 
       rel="noopener noreferrer"
       style="
           color: #fff !important;
           font-size: 1.2em;
           font-weight: 700;
           text-decoration: none;
           display: block;
           text-align: center;
           letter-spacing: 1px;
           position: relative;
           z-index: 2;
           transition: all 0.3s;
       ">
        ⚠️ 重要通知！点击切换新网站<br>
        <small style="
            display: block;
            font-size: 0.8em;
            margin-top: 8px;
            opacity: 0.9;
            font-weight: 400;
        ">
            账号数据自动同步 · 无需重新注册 · 立即跳转
        </small>
    </a>
    <!-- 装饰性波纹 -->
    <div style="
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.1) 0,
            rgba(255,255,255,0.1) 5px,
            transparent 5px,
            transparent 10px
        );
        animation: shine 3s infinite;
        z-index: 1;
    "></div>
</div>

<style>
@keyframes alertPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}
@keyframes shine {
    from { transform: translateX(-50%) rotate(45deg); }
    to { transform: translateX(50%) rotate(45deg); }
}
.notice:hover {
    animation: none;
    transform: scale(1.02) rotate(-0.5deg);
}
@media (max-width: 768px) {
    .notice {
        padding: 12px;
        margin: 15px 0;
    }
    .notice a {
        font-size: 1em;
    }
}
</style>
                 <?php echo(isset($gdata['content']) ? $gdata['content'] : ''); ?>
              	<br /><br /><p>
<?php if (isset($gdata['favorites'])) { ?>
<style>
        .content_favorites {
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background-color: #f9f9f9; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
        }

        #favorites_do {
            margin-right: 5px; 
            color: #ffcc00;
        }

        #favorites_html {
            font-weight: bold; 
        }

        #favorites_count {
            color: #888;
        }
</style>
<div class="xylt-ead73c content_favorites">
    <?php if ($gdata['has_favorites']) { ?>
    <i id="favorites_do" class="xylt-044cd4 fa fa-star" aria-hidden="true"><font id="favorites_html">取消收藏</font></i> 
    <?php }else{ ?>
    <i id="favorites_do" class="xylt-1980eb fa fa-star-o" aria-hidden="true"><font id="favorites_html">加入收藏</font></i>
    <?php } ?>
</div>
<script type="text/javascript">
    $("#favorites_do").click(function () {
        $.getJSON("<?php echo(isset($gdata['favorites_url']) ? $gdata['favorites_url'] : ''); ?>", function(data){
            if(data.err){
                alert(data.msg);
            }else{
                if( data.has_favorites == 1 ){
                    $("#favorites_do").attr("class", "fa fa-star");
                    $("#favorites_html").html("取消收藏");
                }else{
                    $("#favorites_do").attr("class", "fa fa-star-o");
                    $("#favorites_html").html("加入收藏");
                }
                $("#favorites_count").html(data.favorites_count);
                alert(data.msg);
            }
        });
    });
</script>
<?php } ?></p><br /><br /><br />
                </div>
                <?php }else{ ?>请先 <a href="/user-login.html">登录</a> 或者 <a href="/user-register.html">注册</a> 后在进行查看哦！<?php } ?>
              	</div>
                <!-- 标签 -->
                <div class="xylt-36e2a5 tags">
                    <i class="xylt-453937 fa fa-tags"></i>标签：<?php if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?> <a class="xylt-ca7e45 th_hover_a1" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?>
                </div>
                <!-- 版权声明 -->
                <div class="xylt-d3c3da clear">
                    <div class="xylt-10a793 shares"><b>声明：业百科所有作品（图文、音视频）均由用户自行上传分享，仅供网友学习交流。</b><div id="share"></div></div>
                </div>
                <div class="xylt-d3c3da clear"></div>
            </div>
                <!-- 上下篇 -->
                <?php if (isset($gdata['prev']['url']) || isset($gdata['next']['url'])) { ?>
            <nav class="xylt-41fc03 article-nav">
                <span class="xylt-206fcb article-nav-prev">上一篇：<?php if (isset($gdata['prev']['url'])) { ?><a href="<?php echo(isset($gdata['prev']['url']) ? $gdata['prev']['url'] : ''); ?>" title="<?php echo(isset($gdata['prev']['title']) ? $gdata['prev']['title'] : ''); ?>"><?php echo(isset($gdata['prev']['title']) ? $gdata['prev']['title'] : ''); ?></a><?php }else{ ?>没有了<?php } ?></span>
                <span class="xylt-292640 article-nav-next">下一篇：<?php if (isset($gdata['next']['url'])) { ?><a href="<?php echo(isset($gdata['next']['url']) ? $gdata['next']['url'] : ''); ?>" title="<?php echo(isset($gdata['next']['title']) ? $gdata['next']['title'] : ''); ?>"><?php echo(isset($gdata['next']['title']) ? $gdata['next']['title'] : ''); ?></a><?php }else{ ?>没有了<?php } ?></span>
            </nav>
            <?php } ?>
    
        <!-- 猜你喜欢 -->
            <div class="xylt-c04b3f like">
                <h3 class="xylt-4209da title">猜你喜欢</h3>
                <div class="xylt-a418f3 piclist likep">
                <?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '6',
  'life' => '600',
  'dateformat' => 'Y-m-d',
  'showviews' => '1',
)); ?>
                <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                    <div class="xylt-7dd6d9 li">
                        <div class="xylt-e14748 img">
                            <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a>
                        </div>
                        <a href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" class="xylt-aa640e cat"></i><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>
                        <h3><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></h3>
                        <div class="xylt-683ac4 meta">
                            <span class="xylt-b74196 time"><i class="xylt-492abb iconfont iconshijian"></i><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
                            <span class="xylt-170fe1 views"><i class="xylt-ec0aa6 iconfont iconchakan"></i>热度：<?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span>
                        </div>
                    </div>
                 <?php }} ?>
                <?php unset($data); ?>
                <div class="xylt-d3c3da clear"></div>
                </div>
            </div>
        </div>
        <!-- 右侧最新文章 -->
        <div class="xylt-297768 sidebar">
            <div class="xylt-7259d1 widget postlist">
                <h3>最新文章</h3>
                <?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '8',
  'titlenum' => '24',
  'dateformat' => 'Y-m-d',
)); ?>
                <ul class="xylt-104095 hasimg">
                    <?php $rank = 1; ?>
                    <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                    <li>
                        <a href="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="" class="xylt-e14748 img"><?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?></a>
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target=""><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a>
                        <p class="xylt-683ac4 meta">
                            <span><i class="xylt-492abb iconfont iconshijian"></i><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
                            <span><i class="xylt-ec0aa6 iconfont iconchakan"></i> <?php echo(isset($v['views']) ? $v['views'] : ''); ?></span>
                        </p>
                    </li>
                    <?php $rank++; ?>
                    <?php }} ?>
                </ul>
                <?php unset($data); ?>
				<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/js/a03.js"></script>
            </div>
        </div>
    </div>
</section>
<footer class="xylt-4a1794 footer">
    <div class="xylt-e849bd container">
        <p class="xylt-3f0d57 copyright">© <?php echo date("Y"); ?> <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>. ALL RIGHTS RESERVED.   |   备案号：<a href="https://beian.miit.gov.cn/" rel="nofollow">京ICP备88888888号</a></p>
    </div>
</footer>
<a href="javascript:void(0);" class="xylt-416ef9 back-to-top iconfont iconxiangshang cd-is-visible"></a>
<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/dscm.js"></script>
<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/share/jquery.share.min.js"></script>

<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/js/jquery.js"></script>
<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/layer/layer.js"></script>
<script>
    $('#sj_kami_buy').on('click',function (e){
        var aid=$(this).attr('data-id');
        var golds=$(this).attr('data-golds');
        $.get('/my-userinfo.html','',function(data){
            if(!data.err){
                layer.confirm('您当前有 <span style="color: red">'+data.data.golds+' </span>金币。本次购买需消耗 <span style="color: red">'+golds+' </span>金币!<br/>确认进行购买操作？', {
                  btn: ['确定','取消'],icon:3
                }, function(){
                    $.post('/my-userinfo.html',{id:aid},function(data){
                        if(!data.err){
                            layer.alert(data.msg,{icon:1},function (e){
                                window.location.reload()
                            })
                        }else{
                            layer.alert(data.msg,{icon:2})
                        }
                    },'json');
                });
            }else{
                layer.msg('余额获取失败！')
            }
        },'json');
    })
</script>
</body>
</html>