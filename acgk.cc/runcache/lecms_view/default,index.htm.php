<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_content_total_by_date($conf) { global $run; $mid = _int($conf, 'mid', 2); $type = isset($conf['type']) ? $conf['type'] : 'all'; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return 0; } $cache_key = $life ? md5('content_total_by_date'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return (int)$cache_data; } } $where = array(); $run->cms_content->table = 'cms_'.$table; $total = 0; switch($type) { case 'all': $where = array(); break; case 'today': $starttime = mktime(0,0,0,date('m'),date('d'),date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'yesterday': $starttime = mktime(0,0,0,date('m'),date('d')-1,date('Y')); $endtime = mktime(0,0,0,date('m'),date('d'),date('Y'))-1; $where = array('dateline'=>array('>'=>$starttime, '<='=>$endtime)); break; case 'week': $starttime = mktime(0, 0, 0, date('m'), (date('d') - (date('w')>0 ? date('w') : 7) + 1), date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'month': $starttime = mktime(0,0,0,date('m'),1,date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'year': $starttime = strtotime(date('Y',time())."-1"."-1"); $where = array('dateline'=>array('>'=>$starttime)); break; } if($where){ $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_list_flag($conf) { global $run; $flag = _int($conf, 'flag', 0); $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = _int($conf, 'mid', 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list_flag'); $cache_key = $life ? md5('list_flag'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($flag == 0){ return array('list'=> array()); } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array('flag' => $flag); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('flag' => $flag, 'cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('flag' => $flag, 'cid' => $cid); } } if($table == 'page'){ return array(); } $run->cms_content_flag->table = 'cms_'.$table.'_flag'; $key_arr = $run->cms_content_flag->list_arr($where, 'id', $orderway, $start, $limit, $limit, $extra); $keys = array(); foreach($key_arr as $v) { $keys[] = $v['id']; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_global_blog($conf) { global $run; $mid = isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : _int($conf, 'mid', 2); $cid = _int($conf, 'cid', 0); $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_blog'); $cache_key = $life ? md5('global_blog'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $run->cms_content->table = 'cms_'.$table; $where = array(); if($cid){ $where['cid'] = $cid; $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); if(R('control', 'G') == 'model'){ $url = $run->urls->model_url($table, $mid, TRUE); }else{ $url = $run->urls->index_url($mid, TRUE); } $pages = paginator::$page_function($page, $maxpage, $url, $pageoffset); if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { isset($v['id']) AND $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
$gdata = block_global_blog(array (
  'mid' => '2',
  'pagenum' => '20',
  'showmaxpage' => '1000',
  'dateformat' => 'Y-m-d',
  'showviews' => '1',
  'showcate' => '1',
));
 function block_data_total($conf) { global $run; $mid = _int($conf, 'mid', 2); $source = empty($conf['source']) ? '' : $conf['source']; $showviews = _int($conf, 'showviews', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $allow_source = array('content','comment','tag','views','category'); if($source && !in_array($source, $allow_source)){ return array(); } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return array(); } $cache_key = $life ? md5('data_total'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $total = array(); switch ($source){ case 'content': $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); break; case 'comment': $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); break; case 'tag': $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); break; case 'views': $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } break; case 'category': $total['category'] = $run->category->find_count(array('mid'=>$mid)); break; default: $run->cms_content->table = 'cms_'.$table; $total['content'] = $run->cms_content->count(); $total['comment'] = $run->cms_content_comment->find_count(array('mid'=>$mid)); $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $total['tag'] = $run->cms_content_tag->count(); $total['category'] = $run->category->find_count(array('mid'=>$mid)); if($showviews){ $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $sql = "SELECT SUM(views) as views FROM {$table_prefix}cms_{$table}_views"; $res = $run->db->fetch_first($sql); if(isset($res['views'])){ if($res['views'] > 1000000){ $total['views'] = '100W+'; }elseif ($res['views'] > 100000){ $total['views'] = '10W+'; }else{ $total['views'] = (int)$res['views']; } }else{ $total['views'] = 0; } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_taglist($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $table = isset($run->_cfg['table_arr'][$mid]) ? $run->_cfg['table_arr'][$mid] : 'article'; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('tagid', 'count', 'orderby')) ? $conf['orderby'] : 'count'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = isset($conf['limit']) ? (int)$conf['limit'] : 10; $cms_limit = _int($conf, 'cms_limit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_taglist'); $cache_key = $life ? md5('taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $run->cms_content_tag->table = 'cms_'.$table.'_tag'; if($cms_limit){ $run->cms_content->table = 'cms_'.$table; $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; } $where = array(); $list_arr = $run->cms_content_tag->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); $xuhao = 1; foreach($list_arr as &$v) { $v['url'] = $run->cms_content->tag_url($mid, $v); if( empty($v['pic']) ){ $v['pic'] = $run->_cfg['weburl'].'static/img/nopic.gif'; }else{ if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ $v['pic'] = $run->_cfg['weburl'].$v['pic']; } } if($cms_limit){ $tag_data_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$v['tagid']), array('id'=>-1), 0, $cms_limit); $keys = array(); foreach($tag_data_arr as $lv) { $keys[] = $lv['id']; } $cms_arr = $run->cms_content->mget($keys); foreach($cms_arr as &$cv) { $run->cms_content->format($cv, $mid); } $v['cms'] = $cms_arr; unset($cms_arr); } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!doctype html>
<html lang="zh-Hans">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="ie=edge" />
	<meta name="generator" content="acg" />
	<meta name="renderer" content="webkit">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
	<link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
	<link rel="stylesheet" href="//cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css" media="all">
	<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/style.css" type="text/css" media="all"/>
	<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js"></script>
	<link href="/css/home.css" rel="stylesheet" type="text/css">
	<script type="text/javascript">
		window._LE = {
			uri: "<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>",
			uid: "<?php echo(isset($_uid) ? $_uid : ''); ?>",
			parseurl: "<?php echo(isset($_parseurl) ? $_parseurl : ''); ?>"
		};
	</script>
	<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>script/main.js"></script>
</head>
<body>
<div class="jgacg-a6948a topmenu" id="tophead">
  <div class="jgacg-56e85d wrap">
    <div id="mobilemenu"></div>
    <div class="jgacg-c921c0 mask"></div>
    <!--<div class="jgacg-7b53a8 logo"><a href="/"><img src="次元库" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" width="280" height="70"></a></div>-->
    <?php $data = block_navigate(array (
)); ?>
    <div class="jgacg-f7604b menu">
      <ul id="nav">
        <li class="jgacg-65014f closex"><i class="jgacg-e835d3 iconfont icon-guanbi"></i></li>
        <li class="jgacg-bf3200 mainlevel"><a href="/"<?php if (empty($cfg_var['topcid'])) { ?> class="hover"<?php } ?>>首页</a></li>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
        <li class="jgacg-bf3200 mainlevel"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"<?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="hover"<?php } ?>><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
        <div class="jgacg-d3c3da clear"></div>
      </ul>
    </div><?php unset($data); ?>
   <div class="jgacg-304597 search" style="margin-right: 80px;width: 150px;">
    <?php if ($_uid) { ?>
        <a href="/my-index.html" class="jgacg-a87964 personal-center">个人中心</a>
    <?php }else{ ?>
        <a href="/user-login.html" class="jgacg-f8f8b5 login-register">登录/注册</a>
    <?php } ?>
</div>

<style>
    .search a {
        display: inline-block; 
        padding: 10px 20px; 
        border-radius: 5px; 
        text-decoration: none; 
        font-size: 16px; 
        font-weight: bold; 
        transition: background-color 0.3s, transform 0.3s; 
    }

    .personal-center {
        background-color: rgba(45, 191, 191, 0.8);
        color: white;
    }

    .login-register {
        background-color: #ff7f50;
        color: white;
    }

    .search a:hover {
        transform: scale(1.05);
    }

    .personal-center:hover {
        background-color: rgba(45, 191, 191, 1);
    }

    .login-register:hover {
      background-color: #ff6347; }
</style>
    <div class="jgacg-c144ef search"><i class="jgacg-d83b34 iconfont icon-sousuo"></i></div>
  </div>
</div>
<p class="jgacg-14dfcd pheaderpad"></p>

<link rel="stylesheet"  href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/swiper.min.css" media="all" />
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>script/swiper.min.js" type="text/javascript"></script>
<style type="text/css">
.tupiansa{display:flex;flex-wrap:wrap}.tupiansa img{width:110px;margin-bottom:10px;border-radius:5px;clip-path:inset(0 0 10% 0);height:80px;margin-left:2px;margin-right:2px}@media (max-width:768px){.tupiansa img{width:calc(30%);clip-path:inset(0 0 15% 0)}}
</style>
<!--首页主体第三部分start-->
<div class="jgacg-c9bff4 container th_margintop">
    <div class="jgacg-a2c59a row">
        <div class="jgacg-b0cc27 col-md-9 col-xs-12">
            <!--最新属性文章start-->
          <?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'today',
)); ?><div class="jgacg-913fe7 today-posts" style="background-color: #fff;color: #343a40;border-radius: 5px;text-align: center;font-size: 1.9rem;transition: background-color 0.3s;font-weight: 900;">今日发布：<m style="font-size: 2.8rem;color: red;"> <?php echo(isset($data) ? $data : ''); ?> </m>篇</div><?php unset($data); ?>
            <section>
                <div class="jgacg-9b6aa8 thjingxuan">
                    <div class="jgacg-b3ea2c thjingxuan_title"><i></i>最新发布</div>
                    <section class="jgacg-baf491 thjingxuan_sec">
                      <?php $data = block_list_flag(array (
  'flag' => '1',
  'limit' => '5',
  'dateformat' => 'Y-m-d',
  'showcate' => '1',
  'showviews' => '1',
  'life' => '60',
)); ?>
                      <?php 
                      $curr_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
                       ?>
                      <?php if ($curr_page == 1) { ?>
                      <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                      <div class="jgacg-ace782 col-md-12 col-xs-12  th_padding post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>">
                            <div class="jgacg-dd5725 thliorder1" style="background-color: #0b0c0d;">
									<div class="jgacg-7ef28c thnews-con">
										<div class="jgacg-b0364e news-con-tit" style="color: red;font-weight: bold;"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><m style="color: white;">【置顶】</m><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a>
										</div>
										<div class="jgacg-bba882 thinfo">
											<span class="jgacg-d0ec04 date" data-date="<?php echo(isset($v['date']) ? $v['date'] : ''); ?>">发布时间：<?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>&nbsp;&nbsp;|&nbsp;&nbsp;分类：<a href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" target="_blank" style="color: #212529;text-decoration: none;background-color: #edb91d;display: inline-block;padding: 0.25em 0.4em;font-weight: 500;line-height: 1;text-align: center;white-space: nowrap;vertical-align: baseline;padding-right: 0.6em;padding-left: 0.6em;border-radius: 10rem;"><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>&nbsp;&nbsp;|&nbsp;&nbsp;作者：<a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></a>
										</div>
									</div>
								</div>
                        </div>
                    <?php }} ?>
                    <?php } ?>
                    <?php unset($data); ?>
                    
					<?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
                        <div class="jgacg-ace782 col-md-12 col-xs-12  th_padding post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>">
                            <div class="jgacg-936bd6 thliorder1">
									<div class="jgacg-7ef28c thnews-con">
										<div class="jgacg-2e73d7 news-con-tit"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a>
										</div>
                                          <?php if ($_uid) { ?>
                                          <div class="jgacg-04ce20 tupiansa">
                                          <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
                                          <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
                                          <?php }} ?>
                                          </div>
                                          <?php }else{ } ?>
										<div class="jgacg-bba882 thinfo">
											<span class="jgacg-d0ec04 date" data-date="<?php echo(isset($v['date']) ? $v['date'] : ''); ?>">发布时间：<?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>&nbsp;&nbsp;|&nbsp;&nbsp;分类：<a href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" target="_blank" style="color: #212529;text-decoration: none;background-color: #edb91d;display: inline-block;padding: 0.25em 0.4em;font-weight: 500;line-height: 1;text-align: center;white-space: nowrap;vertical-align: baseline;padding-right: 0.6em;padding-left: 0.6em;border-radius: 10rem;"><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>&nbsp;&nbsp;|&nbsp;&nbsp;作者：<a href="<?php echo(isset($v['user_url']) ? $v['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['author']) ? $v['author'] : ''); ?>"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></a>
										</div>
									</div>
								</div>
                        </div>
                        <?php }} ?>
                      	
                    </section>
                </div>
				<?php if ($gdata['pages']) { ?>
							<div class="jgacg-c21d3e col-md-12 col-xs-12  th_padding">
								<div class="jgacg-4954ee list-title pagebar">
									<?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?>
								</div>
							</div>
				<?php } ?>
            </section>
            <!--推荐属性文章end-->
        </div>
      	<!--左侧文章列表end-->
			<div class="jgacg-53aaf2 col-md-3 col-xs-12 wap_margintop">
				<div><section>
	<div class="jgacg-d5cae3 thleftcon" style="height: 335px;">
		<div class="jgacg-a37502 thleftcon-1">
			<img class="jgacg-e72c06 th-img jsimg-height" src="https://th.bing.com/th/id/OIP.6KIPaw1hWSTCfEpeBR7KfgAAAA?rs=1&pid=ImgDetMain">
			<img class="jgacg-a7a81e th-img jsimg-toux" src="https://th.bing.com/th/id/OIP.6KIPaw1hWSTCfEpeBR7KfgAAAA?rs=1&pid=ImgDetMain1">
		</div>
		<div class="jgacg-6862df thjs_infor">
			<h4><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></h4>
			<div class="jgacg-d3c3da clear"></div>
		</div>
		<div class="jgacg-55f5a0 thleftcon-2">
			<dl>
				<dd>一个免费二次元分析社区</dd>
			</dl>
		</div>
		<?php $data = block_data_total(array (
  'mid' => '2',
  'showviews' => '1',
)); ?>
		<div class="jgacg-7538e5 aut_count">
			<ul>
				<li><span>文章</span><strong><?php echo(isset($data['content']) ? $data['content'] : ''); ?></strong></li>
				<li><span>标签</span><strong><?php echo(isset($data['tag']) ? $data['tag'] : ''); ?></strong></li>
				<li><span>浏览量</span><strong><?php echo(isset($data['views']) ? $data['views'] : ''); ?></strong></li>
			</ul>
		</div>
		<?php unset($data); ?>
	</div>
</section></div>
				<!--当前分类阅读排行榜start-->
				<div class="jgacg-25b65b th_margintop">
					<?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '8',
  'titlenum' => '24',
)); ?>
					<section>
						<div class="jgacg-33bd1e thleftcon">
							<div class="jgacg-686f42 thleftbt"><span>阅读排行</span></div>
							<ul class="jgacg-8cd5fc th-5">
								<?php $rank = 1; ?>
								<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
								<li class="jgacg-1cec22 post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>"><span class="jgacg-e1992f date"><?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span><i><?php echo(isset($rank) ? $rank : ''); ?></i><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
								<?php $rank++; ?>
								<?php }} ?>
							</ul>
						</div>
					</section>
					<?php unset($data); ?>
				</div>
				<!--当前分类阅读排行榜end-->
				<!--随机文章start-->
				<div class="jgacg-25b65b th_margintop">
					<section><?php $data = block_list_rand(array (
  'mid' => '2',
  'limit' => '18',
  'life' => '600',
  'showviews' => '1',
)); ?>
<div class="jgacg-33bd1e thleftcon">
	<div class="jgacg-686f42 thleftbt"><span>猜你喜欢</span></div>
	<ul class="jgacg-8cd5fc th-5">
                        <?php $rank = 1; ?>
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <li class="jgacg-1cec22 post_id_<?php echo(isset($v['id']) ? $v['id'] : ''); ?>"><span class="jgacg-e1992f date"><?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span><i><?php echo(isset($rank) ? $rank : ''); ?></i><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
                        <?php $rank++; ?>
                        <?php }} ?>
                    </ul>
</div>
<?php unset($data); ?></section>
				</div>
				<!--随机文章end-->
				<!--热门标签start-->
				<div class="jgacg-25b65b th_margintop">
					<section>
						<div class="jgacg-33bd1e thleftcon">
							<div class="jgacg-686f42 thleftbt"><span>热门标签</span></div>
							<ul class="jgacg-9e001c th-7">
								<?php $data = block_taglist(array (
  'mid' => '2',
  'limit' => '18',
  'orderby' => 'count',
)); ?>
								<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
								<li class="jgacg-f6715d tag_id_<?php echo(isset($v['tagid']) ? $v['tagid'] : ''); ?>"><a title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
								<?php }} ?>
								<?php unset($data); ?>
							</ul>
						</div>
					</section>
				</div>
				<!--热门标签end-->
    </div>
</div>
<!--首页主体第三部分end-->

<!--友情链接start-->
<?php $data = block_links(array (
)); ?>
<?php if ($data) { ?>
<section>
    <div class="jgacg-c9bff4 container th_margintop">
        <div class="jgacg-a2c59a row">
            <!--line-->
            <div class="jgacg-11b6d6 col-md-12 col-xs-12">
                <section>
                    <div class="jgacg-33bd1e thleftcon">
                        <div class="jgacg-686f42 thleftbt"><span>友情链接</span></div>
                        <ul class="jgacg-0d868b th-8">
                            <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?><li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
                        </ul>
                    </div>
                </section>
            </div>
        </div>
    </div>
</section>
<?php } ?>
<?php unset($data); ?>

<!-- 搜索框-->
<div class="jgacg-c2b9e3 search-box">
  <div class="jgacg-cf3acb search-close"><i class="jgacg-e835d3 iconfont icon-guanbi"></i></div>
  <div class="jgacg-dee095 search-con">
    <dl class="jgacg-418446 se">
      <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" id="search_form">
						<dt><input type="hidden" name="u" value="search-index" />
						<input type="hidden" name="mid" value="2" />
						<input type="text" class="search-keyword" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" /></dt>
						<dd>
          <button type="submit"><i class="jgacg-d83b34 iconfont icon-sousuo"></i></button>
        </dd>
					</form>
    </dl>
  </div>
<script type="text/javascript" src="/js/jquery.min.js"></script> 
<script type="text/javascript" src="/js/swiper.min.js"></script> 
<script type="text/javascript" src="/js/slide.js"></script>
<script>
// 获取当前日期，格式为 YYYY-MM-DD
const today = new Date();
const todayFormatted = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');

// 获取所有包含 data-date 的元素
const dateElements = document.querySelectorAll('.date[data-date]');

dateElements.forEach(function(element) {
    // 获取元素中的发布时间
    const publishDate = element.getAttribute('data-date');
    
    // 如果发布时间和今天的日期相同，修改字体颜色为红色
    if (publishDate === todayFormatted) {
        element.style.color = 'red';
        element.style.fontWeight = 'bold'; // 可选，设置粗体
    }
});
</script>
  <footer style="background-color: #f1f1f1;padding: 20px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body>
</html>
