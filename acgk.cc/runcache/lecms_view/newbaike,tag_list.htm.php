<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_taglist($conf) { global $run, $tags, $mid, $table; $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_taglist'); $cache_key = $life ? md5('global_taglist'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if(empty($tags)){ return false; } $tagid = $tags['tagid']; $total = $tags['count']; $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); $pages = paginator::$page_function($page, $maxpage, $run->cms_content->tag_url($mid, $tags, TRUE), $pageoffset); $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; $tag_arr = $run->cms_content_tag_data->list_arr($tagid, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); $keys = array(); foreach($tag_arr as $v) { $keys[] = $v['id']; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]) ? $views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
$gdata = block_global_taglist(array (
  'mid' => '2',
  'limit' => '28',
  'intronum' => '102',
  'showcate' => '1',
  'showviews' => '1',
));
?><!doctype html>
<html>
<head>
    <meta charset="UTF-8">
	<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <link rel="icon" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/img/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/style.css">
    <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/css/iconfont.css">
    <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/jquery.min.js"></script>
    <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/m.js"></script>
    <!--[if lt IE 9]>
      <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/html5shiv.min.js"></script>
      <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<header class="xylt-9079aa menu active">
    <div class="xylt-e849bd container">
        <a href="/" class="xylt-f1fd44 logo-a">
            <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/img/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" style="width: 180px;"/>
        </a>
        <?php $data = block_navigate(array (
)); ?>
        <ul class="xylt-d4dab2 navbar">
            <li <?php if (empty($cfg_var['topcid'])) { ?> class="xylt-928a6a act"<?php } ?>><a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"><?php echo '首页'; ?></a></li>
            <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
            <li<?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="act"<?php } ?>>
                <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); if (isset($v['son'])) { ?><i class="xylt-0182c7 iconfont icon-jiantou"></i><?php } ?></a>
                <?php if (isset($v['son'])) { ?>
                <ul>
                    <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?>
                </ul>
                <?php } ?>
            </li>
            <?php }} ?>
          <?php if ($_uid) { ?>
                  <a href="/my-index.html" class="highlight-btn" 
                     style="padding:10px 20px; background:#3b82f6; color:white!important; border-radius:20px; font-weight:bold;">
                      个人中心
                  </a>
              <?php }else{ ?>
                  <a href="/user-login.html" class="highlight-btn" 
                     style="padding:10px 20px; background:#ef4444; color:white!important; border-radius:20px; font-weight:bold;">
                      登录/注册
                  </a>
              <?php } ?>
                    </ul>
                    <?php unset($data); ?>
        <a href="javascript:;" class="xylt-487abe sou"><i class="xylt-75d15b iconfont iconsou"></i></a>
                <div class="xylt-c144ef search">
            <div class="xylt-e849bd container">
            <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" class="xylt-470904 site-search-form">
                <input type="hidden" name="u" value="search-index" />
                <input type="hidden" name="mid" value="2" />
                <input class="search-input" type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" placeholder="输入关键字回车" />
                <input class="search-btn" type="submit" name="submit"  value=""  />
                    <i class="xylt-3d30c4 icon iconfont iconclose"></i>
                </form>
            </div>
        </div>
    </div>
</header>
<section class="xylt-31e2ae banner">
    <div class="xylt-e849bd container">
        <h2><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></h2>
        <div class="xylt-a640d1 search-form">
            <form name="search" method="get" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php" class="xylt-470904 site-search-form">
                <input type="hidden" name="u" value="search-index" />
                <input type="hidden" name="mid" value="2" />
                <input class="search-input" type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" size="11" />
                <input class="search-btn" type="submit" name="submit" value="<?php echo '搜索'; ?>" />
            </form>
        </div>
        <p>
        您正在查看与：<span class="xylt-83e6e0 banner_search"> “<strong><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></strong>” 相关的内容</span>
        </p>
    </div>
</section>
<section class="xylt-1db12a container mt-50 mb-50">
    <div class="xylt-b248b9 piclist">
        
        <?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
         <div class="xylt-7dd6d9 li">
            <div class="xylt-e14748 img">
                <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><img src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"></a>
            </div>
            <a href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" class="xylt-aa640e cat"><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>
            <h3><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></h3>
            <div class="xylt-683ac4 meta">
                <span class="xylt-b74196 time"><i class="xylt-492abb iconfont iconshijian"></i> <?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
                <span class="xylt-170fe1 views"><i class="xylt-ec0aa6 iconfont iconchakan"></i> 热度:<?php echo(isset($v['views']) ? $v['views'] : ''); ?>℃</span>
            </div>
        </div>
        <?php }} ?>
        
    </div>
    <div class="xylt-d3c3da clear"></div>
</section>
<footer class="xylt-4a1794 footer">
    <div class="xylt-e849bd container">
        <p class="xylt-3f0d57 copyright">© <?php echo date("Y"); ?> <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>. ALL RIGHTS RESERVED.   |   备案号：<a href="https://beian.miit.gov.cn/" rel="nofollow">京ICP备88888888号</a></p>
    </div>
</footer>
<a href="javascript:void(0);" class="xylt-416ef9 back-to-top iconfont iconxiangshang cd-is-visible"></a>
<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/js/dscm.js"></script>
<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/share/jquery.share.min.js"></script>
</body>
</html>