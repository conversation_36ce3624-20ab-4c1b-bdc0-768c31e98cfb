<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="renderer" content="webkit">
  <link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/frontend.min.css" media="all">
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/user.css" media="all">
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
  <!--[if lt IE 9]>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/html5shiv.js"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/respond.min.js"></script>
  <![endif]-->
  <script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js" charset="utf-8"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/bootstrap.min.js"></script>
  <style>
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand{margin-left: 0;}
    .navbar-brand{padding: 0;}
    .navbar-brand img{height: 45px;vertical-align: center;padding-top: 5px;}
  </style>
</head>
<body>
<nav class="acgk-dc9e93 navbar navbar-white navbar-fixed-top" role="navigation">
  <div class="acgk-e849bd container">
    <div class="acgk-ecb9eb navbar-header">
      <button type="button" class="acgk-808760 navbar-toggle" data-toggle="collapse" data-target="#header-navbar">
        <span class="acgk-27673d sr-only"><?php echo '切换'; ?></span>
        <span class="acgk-82f9ad icon-bar"></span>
        <span class="acgk-82f9ad icon-bar"></span>
        <span class="acgk-82f9ad icon-bar"></span>
      </button>
      <a class="acgk-6af25c navbar-brand" href="<?php echo(isset($my_url) ? $my_url : ''); ?>" title="用户中心">
      </a>
    </div>
    <div class="acgk-0ee60b collapse navbar-collapse" id="header-navbar">
      <ul class="acgk-f2cbb6 nav navbar-nav navbar-right">
        <li><a href="/" title="/"><?php echo '首页'; ?></a></li>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
        <li class="acgk-bf3200 mainlevel"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" <?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="hover"<?php } ?>><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
        <li class="acgk-d44435 dropdown">
          <a href="<?php echo(isset($my_url) ? $my_url : ''); ?>" class="acgk-0d48bd dropdown-toggle" data-toggle="dropdown">
            <span class="acgk-13bf6f avatar-img"><img src="http://img.soogif.com/x3CvazRkvwy4qgYNt5K8bOzSI4tdRTUj.gif?v=1.0" alt="<?php echo(isset($_user['username']) ? $_user['username'] : ''); ?>"></span>
            <span class="acgk-ccbaaa visible-xs-inline-block" style="padding:5px;"><?php echo(isset($_user['username']) ? $_user['username'] : ''); ?></span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</nav>
<style>
  .order-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s;
  }
  .order-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }
  .order-no {
    font-weight: bold;
    color: #333;
  }
  .order-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
  }
  .status-unpaid {
    background-color: #fff3cd;
    color: #856404;
  }
  .status-paid {
    background-color: #d4edda;
    color: #155724;
  }
  .status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }
  .order-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .order-details {
    flex: 1;
  }
  .order-amount {
    font-size: 18px;
    font-weight: bold;
    color: #e74c3c;
  }
  .order-actions {
    margin-left: 20px;
  }
  .btn-check {
    background-color: #17a2b8;
    border-color: #17a2b8;
  }
  .btn-check:hover {
    background-color: #138496;
    border-color: #117a8b;
  }
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
  }
  .empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    display: block;
  }
</style>

<main class="acgk-1bc3a4 content">
  <div id="content-container" class="acgk-e849bd container">
    <div class="acgk-a2c59a row">
      <style>
    #logout{cursor: pointer;}
  .sidebar-toggle {
            display: block;
            position: fixed;
            right: 20px;
            top: 70px;
            border-radius: 50%;
            background: #007bff; /* 更鲜艳的背景颜色 */
            color: white; /* 字体颜色 */
            font-size: 24px; /* 增大字体大小 */
            padding: 10px;
            line-height: 30px;
            height: 60px; /* 增加按钮高度 */
            width: 60px; /* 增加按钮宽度 */
            text-align: center;
            z-index: 999999;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.5); /* 添加阴影效果 */
            transition: background-color 0.3s, transform 0.3s; /* 添加过渡效果 */
        }
</style>
<div class="acgk-9e1a63 col-md-3">
  <div class="acgk-5b2d1e sidebar-toggle"><i class="acgk-f9e8a2 fa fa-bars"></i></div>
  <div id="sidebar-nav" class="acgk-ae932d sidenav">
    <?php if(isset($_navs) && is_array($_navs)) { foreach($_navs as $k=>&$v) { ?>
    <ul class="acgk-4e2ce3 list-group">
      <?php if (!empty($v['href'])) { ?>
      <li id="<?php echo(isset($k) ? $k : ''); ?>" class="acgk-2c0bb8 list-group-heading">
        <a href="<?php echo(isset($v['href']) ? $v['href'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
      </li>
      <?php }else{ ?>
      <li id="<?php echo(isset($k) ? $k : ''); ?>" class="acgk-2c0bb8 list-group-heading"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></li>
      <?php } ?>
      <?php if(isset($v['child']) && is_array($v['child'])) { foreach($v['child'] as &$v2) { ?>
      <li id="<?php echo(isset($v2['id']) ? $v2['id'] : ''); ?>" class="acgk-61f5d0 list-group-item">
        <?php if (!empty($v2['href'])) { ?>
        <a href="<?php echo(isset($v2['href']) ? $v2['href'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>"><?php if (!empty($v2['icon'])) { ?><i class="acgk-8d0b79 <?php echo(isset($v2['icon']) ? $v2['icon'] : ''); ?>"></i> <?php } echo(isset($v2['title']) ? $v2['title'] : ''); ?></a>
        <?php }else{ ?>
        <?php if (!empty($v2['icon'])) { ?><i class="acgk-8d0b79 <?php echo(isset($v2['icon']) ? $v2['icon'] : ''); ?>"></i> <?php } echo(isset($v2['title']) ? $v2['title'] : ''); ?>
        <?php } ?>
      </li>
      <?php }} ?>
    </ul>
    <?php }} ?>
  </div>
</div>
<script type="text/javascript">
  $(document).on("click", ".sidebar-toggle", function () {
    $("body").toggleClass("sidebar-open");
  });
</script>
      <!--右侧主体部分 start-->
      <div class="acgk-a3c241 col-md-9">
        <div class="acgk-e54ecf panel panel-default">
          <div class="acgk-95a19a panel-body">
            <h2 class="acgk-4ada80 page-header">
              <i class="acgk-4a0fe8 fa fa-list-alt"></i> 充值记录
            </h2>
            
            <?php if (empty($data)) { ?>
            <div class="acgk-951e5b empty-state">
              <i class="acgk-b84c56 fa fa-inbox"></i>
              <h4>暂无充值记录</h4>
              <p>您还没有进行过充值，<a href="/my-recharge.html">立即充值</a></p>
            </div>
            <?php }else{ ?>
            <div class="acgk-d2e06a orders-list">
              <?php if(isset($data) && is_array($data)) { foreach($data as &$order) { ?>
              <div class="acgk-44d850 order-item">
                <div class="acgk-e5dcb3 order-header">
                  <div class="acgk-83803c order-no">订单号：<?php echo(isset($order['order_no']) ? $order['order_no'] : ''); ?></div>
                  <div class="acgk-5af1c7 order-status <?php if ($order['status'] == 0) { ?>status-unpaid<?php }elseif($order['status'] == 1) { ?>status-paid<?php }else{ ?>status-cancelled<?php } ?>">
                    <?php echo(isset($order['status_text']) ? $order['status_text'] : ''); ?>
                  </div>
                </div>
                
                <div class="acgk-c80e63 order-info">
                  <div class="acgk-142c76 order-details">
                    <div class="acgk-a2c59a row">
                      <div class="acgk-9e1a63 col-md-3">
                        <strong>类型：</strong><?php echo(isset($order['type_text']) ? $order['type_text'] : ''); ?>
                      </div>
                      <div class="acgk-9e1a63 col-md-3">
                        <strong>奖励：</strong>
                        <?php if ($order['type'] == 1) { ?>
                          <?php echo(isset($order['value']) ? $order['value'] : ''); ?> 金币
                        <?php }else{ ?>
                          <?php echo(isset($order['value']) ? $order['value'] : ''); ?> 天VIP
                        <?php } ?>
                      </div>
                      <div class="acgk-9e1a63 col-md-3">
                        <strong>支付方式：</strong><?php echo(isset($order['pay_type_text']) ? $order['pay_type_text'] : ''); ?>
                      </div>
                      <div class="acgk-9e1a63 col-md-3">
                        <strong>创建时间：</strong><?php echo(isset($order['create_time_text']) ? $order['create_time_text'] : ''); ?>
                      </div>
                    </div>
                    <?php if ($order['pay_time_text'] != '-') { ?>
                    <div class="acgk-2780b1 row" style="margin-top: 10px;">
                      <div class="acgk-de6c37 col-md-12">
                        <strong>支付时间：</strong><?php echo(isset($order['pay_time_text']) ? $order['pay_time_text'] : ''); ?>
                      </div>
                    </div>
                    <?php } ?>
                  </div>

                  <div class="acgk-2c9dd1 order-amount">
                    ￥<?php echo(isset($order['amount']) ? $order['amount'] : ''); ?>
                  </div>

                  <?php if ($order['status'] == 0) { ?>
                  <div class="acgk-1cb4c5 order-actions">
                    <button type="button" class="acgk-83604e btn btn-sm btn-check" onclick="checkOrder('<?php echo(isset($order['order_no']) ? $order['order_no'] : ''); ?>')">
                      <i class="acgk-7c882a fa fa-refresh"></i> 检查状态
                    </button>
                  </div>
                  <?php } ?>
                </div>
              </div>
              <?php }} ?>
            </div>
            
            <!-- 分页 -->
            <div class="acgk-646993 text-center">
              <?php echo(isset($pages) ? $pages : ''); ?>
            </div>
            <?php } ?>
            
            <!-- 帮助信息 -->
            <div class="acgk-ffc60a alert alert-info" style="margin-top: 30px;">
              <h5><i class="acgk-713942 fa fa-question-circle"></i> 常见问题</h5>
              <p><strong>Q：支付后多久到账？</strong></p>
              <p>A：一般在1-3分钟内自动到账，最长不超过10分钟。</p>
              <p><strong>Q：支付成功但未到账怎么办？</strong></p>
              <p>A：请点击"检查状态"按钮，或联系客服处理。</p>
              <p><strong>Q：可以申请退款吗？</strong></p>
              <p>A：虚拟商品一经发放不支持退款，请谨慎购买。</p>
            </div>
          </div>
        </div>
      </div>
      <!--右侧主体部分 end-->
    </div>
  </div>
</main>

<footer class="acgk-bb0985 footer" style="clear:both">
  <p class="acgk-3f0d57 copyright">Copyright&nbsp;©&nbsp;<?php echo date('Y'); ?> <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?> All Rights Reserved.</p>
</footer>
<script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var layer = layui.layer, $ = layui.$;
    $("#logout").click(function () {
      layer.confirm('确定退出登录？', function () {
        $.post("index.php?my-logout-ajax-1",{do: 1},function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location = "/"; }, 1000);
          return false;
        },'json');
      });
    });
  });
</script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    $("#my-rainbow-pay-orders").addClass("active");
  });
  
  // 检查订单状态
  function checkOrder(orderNo) {
    var loadIndex = layer.load(2, {shade: [0.3, '#000']});
    
    $.ajax({
      type: "POST",
      url: "my-check.html",
      data: {order_no: orderNo},
      dataType: 'json',
      success: function(result) {
        layer.close(loadIndex);
        console.log('检查订单状态返回:', result); // 调试信息

        // 检查返回数据的结构
        if (typeof result !== 'object' || result === null) {
          layer.alert('服务器返回数据格式错误！');
          return;
        }

        if(!result.err){
          // 确保data存在且包含必要字段
          if(result.data && typeof result.data === 'object') {
            if(result.data.status == 1) {
              layer.alert('订单已支付成功！页面即将刷新...', function(){
                location.reload();
              });
            } else {
              var statusText = result.data.status_text || '未知状态';
              layer.alert('订单状态：' + statusText);
            }
          } else {
            layer.alert('服务器返回数据不完整！');
          }
        } else {
          var errorMsg = result.msg || '未知错误';
          layer.alert('检查失败：' + errorMsg);
        }
      },
      error: function(){
        layer.close(loadIndex);
        layer.alert('网络错误，请重试！');
      }
    });
  }
</script>
