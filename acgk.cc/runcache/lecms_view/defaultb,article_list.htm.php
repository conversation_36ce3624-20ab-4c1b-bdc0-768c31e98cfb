<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_cate($conf) { global $run; $pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $pageoffset = _int($conf, 'pageoffset', 5); $showmaxpage = _int($conf, 'showmaxpage', 0); $field_format = _int($conf, 'field_format', 0); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_cate'); $cache_key = $life ? md5('global_cate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $cid = &$run->_var['cid']; $mid = &$run->_var['mid']; if($mid == 1) return FALSE; if(!empty($run->_var['son_cids']) && is_array($run->_var['son_cids'])) { $where = array('cid' => array("IN" => $run->_var['son_cids'])); $total = 0; $cate_arr = array(); foreach($run->_var['son_cids'] as $v) { $cate_arr[$v] = $run->category->get_cache($v); $total += $cate_arr[$v]['count']; } }else{ $where = array('cid' => $cid); $cate_arr[$cid] = $run->_var; $total = &$run->_var['count']; } $maxpage = max(1, ceil($total/$pagenum)); if( R('page', 'G') > $maxpage || ($showmaxpage && R('page', 'G') > $showmaxpage)){core::error404();} if($showmaxpage && $maxpage > $showmaxpage){ $maxpage = $showmaxpage; } $page = min($maxpage, max(1, intval(R('page')))); $pages = paginator::$page_function($page, $maxpage, $run->category->category_url($run->_var, TRUE), $pageoffset); $run->cms_content->table = 'cms_'.$run->_var['table']; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$run->_var['table'].'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$run->_var['table'].'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && isset($cate_arr[$v['cid']])){ $run->category->getCategoryInfoByList($v, $cate_arr[$v['cid']]); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
$gdata = block_global_cate(array (
  'pagenum' => '20',
  'showviews' => '1',
  'showcate' => '1',
  'dateformat' => 'Y-m-d',
  'showmaxpage' => '10000',
));
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>快萌ACG - 南+社区</title>
<meta name="keyword" content="快萌ACG,南+社区,快萌论坛,游戏,百合,acg,和谐,同人,C94,韩漫,游戏,百合,萝莉,魔法少女,初音,东方,伪娘,末途,校园奴隶契约,驱灵师,小黄游,HS2,游戏合集包,漫画合集包,写真,套图">
<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>">
<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1"/>
<meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width">
<meta name="apple-mobile-web-app-title" content="">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="green">
<meta name="format-detection" content="telphone=no, email=no">
<meta name="HandheldFriendly" content="true">
<meta name="screen-orientation" content="portrait">
<meta name="x5-orientation" content="portrait">
<meta name="full-screen" content="yes">
<meta name="x5-fullscreen" content="true">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="renderer" content="webkit">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/font_3913661_iikaqjykdll.js"></script>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font_3913661_iikaqjykdll.css" rel="stylesheet">
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font-awesome.min.css" rel="stylesheet">
<style type="text/css">
.iconfont {
    font-size: inherit;
}
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
.th_padding, .left_padding {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 992px) {
  .col-md-12 {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  @media only screen and (min-width: 320px) and (max-width: 992px) {
    .th_padding {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  .pagebar {
    text-align: center;
    margin-top: 15px;
  }
 }
.pagebar {
  text-align: center;
  margin-top: 15px;
}
  
  .pagebar a, .pagebar b {
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 16px;
  margin: 0px 5px;
  border: 1px solid #f0f0f5;
  color: #666;
  padding: 0px 8px;
  border-radius: 2px;
  box-sizing: border-box;
}
  .pagebar b {
  background: #7cbeff;
  border: 1px solid #7cbeff;
  color: #fff;
  font-weight: normal;
}
</style>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/style.css?2024" rel="stylesheet">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/jquery.min.js"></script>
<div class="tdacg-0cd2f1 ui progress" style="position: fixed; top: 0; left: 0; z-index: 999; width: 100vw;">
  <div id="page-reading-percent" class="tdacg-0b16ae percent" style="width: 0;"></div>
</div>
<script>
    $(function (){
         $(document).scroll(function (){
             let height = $(this).height() - $(window).height();
             let top = $(this).scrollTop();
             let percent = top / height * 100;
             $("#page-reading-percent").width(percent.toFixed(2) + "%");
        });
    });
     function scrollToTop(){
        $("html, body").animate({ scrollTop:0 }, 500);
    }
     function showModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("show");
        setTimeout(() => {
            modalPageDiv.addClass("open");
        }, 50);
    }
     function hideModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("close");
        setTimeout(() => {
            modalPageDiv.removeClass("show");
            modalPageDiv.removeClass("open");
            modalPageDiv.removeClass("close");
        }, 500);
    }
</script>
<div class="tdacg-e65031 ui modal-page right" id="modal-page"> 
   <div class="tdacg-39ae83 modal-background" onclick="hideModal()"></div>
   <div class="tdacg-0f04ec modal-container padding">
    <div class="tdacg-d0bab5 ui flex-item padding">
      <div class="tdacg-2df666 center"></div>
      <div class="tdacg-59f765 end">
        <div class="tdacg-14d889 ui button circle outline" onclick="hideModal()"><i class="tdacg-07322e iconfont icon-close"></i></div>
      </div>
    </div>
     <div class="tdacg-1ee7ec panel-block">
      <div class="tdacg-4209da title">导航菜单</div>
      <div class="tdacg-b77a93 ui tree linear-split" style="padding: 20px 0;"> <?php $data = block_navigate(array (
)); ?>
        <ul>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold"><a href="/" class="tdacg-fbdb65 padding-half">首页</a></div>
            </div>
          </li>
          <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="tdacg-fbdb65 padding-half"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></div>
            </div>
          </li>
          <?php }} ?>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold">
               <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="tdacg-fbdb65 padding-half">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="tdacg-fbdb65 padding-half">登录/注册</a>
                <?php } ?></div>
            </div>
          </li>
        </ul>
        <?php unset($data); ?> </div>
    </div>
  </div>
</div>
<header>
  <div class="tdacg-8dca09 container auto-margin ui flex-item"> <a class="tdacg-2f82ca start web-logo" href="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); ?>"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"></a> <a class="tdacg-27ac43 start web-name padding-left" href="/">快萌ACG</a>
<div class="tdacg-e47538 search center padding-left justify-center">
      <div class="tdacg-7f0202 ui input radius">
        <input type="text" placeholder="前往acgk.cc搜索,本站待修复">
        <div class="tdacg-36a14a ">
          <div class="tdacg-09d874 ui button clear" ><i class="iconfont icon-sousuo"></i></div>
        </div>
      </div>
    </div>
    <?php $data = block_navigate(array (
)); ?>
    <div class="tdacg-43735b end justify-center">
      <nav> <a class="tdacg-96319d item" href="/">首页</a> <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> <a class="tdacg-0a824c item" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?> 
      <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="tdacg-1f54da item">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="tdacg-1f54da item">登录/注册</a>
      <?php } ?>
      </nav>
    </div>
    <?php unset($data); ?>
    <div class="tdacg-614bc4 end ext-menu justify-center">
      <div class="tdacg-a2b9b7 ui button circle" onclick="showModal()"><i class="tdacg-3805f5 fa fa-list"></i></div>
    </div>
  </div>
</header>

<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/fenye.css?2024" rel="stylesheet">
<style type="text/css">
.tupiansa{display:flex;flex-wrap:wrap}.tupiansa img{width:110px;margin-bottom:10px;border-radius:5px;clip-path:inset(0 0 10% 0);height:80px;margin-left:2px;margin-right:2px}@media (max-width:768px){.tupiansa img{width:calc(30%);clip-path:inset(0 0 15% 0)}}
</style>
<div class="tdacg-59d1a8 list-page">
  <main>
    <div class="tdacg-4209da title"><?php echo(isset($cfg_var['place'][0]['name']) ? $cfg_var['place'][0]['name'] : ''); ?></div>
    <div class="tdacg-fbdb65 padding-half"></div>
    <div class="tdacg-32a655 ui list linear-split double-item-space"> 
      <?php if (empty($gdata['list'])) { ?>
      <div class="tdacg-9138e9 empty-list text-center medium-text" style="padding: 50px;">
        <div class="tdacg-2a87f0 text-maximum"><i class="tdacg-1fd73e iconfont icon-receipt"></i></div>
        <div class="tdacg-a4d016 padding-top">没有文章内容</div>
      </div>
      <?php }else{ ?>
      <?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
      <div class="tdacg-1f54da item">
        <div class="tdacg-059710 detail">
          <div class="tdacg-e786e3 tupias"> <a class="tdacg-15cbcf text-bold max-two-line-text" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a> 
            <?php if ($_uid) { ?>
            <div class="tdacg-04ce20 tupiansa">
            <?php if(isset($v['piclist']) && is_array($v['piclist'])) { foreach($v['piclist'] as &$src) { ?>
            <img src="<?php echo(isset($src) ? $src : ''); ?>" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>">
            <?php }} ?>
            </div>
            <?php }else{ } ?>
          </div>
          <div class="tdacg-4feec0 ui flex-itemss medium-text padding-top">
            <div class="tdacg-9aa2e8 ui avatar small circle"><img src="assets/images/face.jpg" alt=""></div>
            <div class="tdacg-0abc93 justify-center padding-start">作者：<?php echo(isset($v['author']) ? $v['author'] : ''); ?></div>
            <div class="tdacg-0abc93 justify-center padding-start">阅读：<?php echo(isset($v['views']) ? $v['views'] : ''); ?></div>
            <div class="tdacg-2df666 center"></div>
            <div class="tdacg-4be7a9 end justify-center" data-date="<?php echo(isset($v['date']) ? $v['date'] : ''); ?>">发布时间：<?php echo(isset($v['date']) ? $v['date'] : ''); ?></div>
          </div>
        </div>
      </div>
      <?php }} ?>
      <?php } ?>
      
      <div class="tdacg-ef9ed9 pages">

							<div class="tdacg-c21d3e col-md-12 col-xs-12  th_padding">
								<div class="tdacg-4954ee list-title pagebar">
									<?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?>
								</div>
							</div>

	 </div>
    </div>
  </main>
  <aside>
  <div class="tdacg-f189a9 panel-block no-prefix padding-bottom-half">
    <div class="tdacg-1fba49 title ui flex-item" style="font-weight: normal;">
      <div class="tdacg-7ed4a6 start"><i class="tdacg-1fd73e iconfont icon-receipt"></i></div>
      <div class="tdacg-8b50ce center padding-left-half">热门文章</div>
    </div>
    <div class="tdacg-fbdb65 padding-half"></div>
    <ul class="tdacg-9912b9 text-list">
		<?php $data = block_list_top(array (
  'orderby' => 'views',
  'limit' => '12',
  'titlenum' => '24',
)); ?>
<?php $rank = 1; ?>
<?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>

      <li><span class="tdacg-6c0dcf tag"><?php echo(isset($rank) ? $rank : ''); ?></span> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></li>
		<?php $rank++; ?>
		<?php }} ?>
		<?php unset($data); ?>
		
    </ul>
  </div>
<!--  <div class="tdacg-f45ccc panel-block no-prefix margin-top">
    <div class="tdacg-1fba49 title ui flex-item" style="font-weight: normal;">
      <div class="tdacg-7ed4a6 start"><i class="tdacg-1fd73e iconfont icon-receipt"></i></div>
      <div class="tdacg-8b50ce center padding-left-half">贷款产品</div>
      <div class="tdacg-941355 end text-default-size"> <a href="">更多</a> </div>
    </div>
    <div class="tdacg-c76cbb dk-list padding-top">
      <div class="tdacg-1f54da item">
        <div class="tdacg-7ed4a6 start"> <img src="assets/images/logo.jpg"> </div>
        <div class="tdacg-da054c center justify-center">
          <div class="tdacg-ba3a15 text-bold">XXXX金融</div>
          <div class="tdacg-c1115b danger-text padding-top-half">￥3-20万</div>
        </div>
        <div class="tdacg-43735b end justify-center"> <a href="https://baidu.com" target="_blank" class="tdacg-646a62 ui button round small">查看</a> </div>
      </div>
    </div>
  </div>-->
</aside>
 </div>
<footer>
  <div class="tdacg-8dca09 container auto-margin ui flex-item">
    <div class="tdacg-2df666 center">
      <?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?> <a rel="nofollow" href="https://beian.miit.gov.cn"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a> <?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?></div>
  </div>
</footer>
<!-- 随航组件 -->
<div class="tdacg-bb9d5e fixed-menu">
  <div class="tdacg-1f54da item"> 
    <!-- 此处的二维码为自动生成 -->
    <div class="tdacg-cd14a0 hover-display url-qrcode" id="pageUrlQrcode"></div>
    <i class="tdacg-75841e fa fa-qrcode"></i> </div>
  <div class="tdacg-1f54da item">
    <div class="tdacg-04200a hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="QQ"> </div>
    <i class="tdacg-c8185c fa fa-qq"></i> </div>
  <div class="tdacg-1f54da item">
    <div class="tdacg-04200a hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="微信"> </div>
    <i class="tdacg-65d1a0 fa fa-weixin"></i> </div>
  <div class="tdacg-b7188f item" onclick="scrollToTop()"><i class="tdacg-1fa263 fa fa-arrow-up"></i></div>
</div>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/qrcode.js"></script> 
<script>
    const qrcode = new QRCode("pageUrlQrcode", {
        text: window.location.href,
        width: 150,
        height: 150,
        correctLevel : QRCode.CorrectLevel.H
    });
</script>
<footer style="background-color: #f1f1f1;padding: 1px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body></html> 
<script>
// 获取当前日期，格式化为 YYYY-MM-DD
const today = new Date();
const todayFormatted = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');

// 获取包含 data-date 属性的元素
const dateElement = document.querySelector('.end[data-date]');

// 获取该元素的发布时间
const publishDate = dateElement.getAttribute('data-date');

// 判断发布时间是否为今天，如果是，改变字体颜色为红色
if (publishDate === todayFormatted) {
    dateElement.style.color = 'red';
}
</script>