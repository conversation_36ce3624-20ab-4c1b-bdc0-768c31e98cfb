<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<html lang="zh-CN">
 <head> 
  <meta charset="UTF-8" /> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <meta name="viewport" content="width=device-width, initial-scale=1" /> 
  <link href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>favicon.ico" rel="icon" /> 
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
   
   
  <?php $control = isset($_GET['control'])?strtolower($_GET['control']):'';$action = isset($_GET['action'])?strtolower($_GET['action']):''; ?>
  <?php if ($control=='show' && $action == 'index') { ?>
  <meta property="og:type" content="acticle" />
  <meta property="og:image" content="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>" />
  <meta property="og:author" content="<?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?>" />
  <meta property="og:site_name" content="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" />
  <meta property="og:title" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>" />
  <meta property="og:keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
  <meta property="og:description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
  <meta property="og:url" content="<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>" />
  <meta property="og:release_date" content="<?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?>" />	
  <?php } ?>  
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/sweetalert2.min.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>font-awesome/css/font-awesome.min.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/external.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/default.css" media="all" /> 
  <?php if ($control=='show' && $action == 'index') { ?>
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/jquery.fancybox.min.css" type="text/css" media="all" />
  <?php } ?>
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-2.2.4.min.js"></script> 
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/sweetalert2.all.min.js"></script> 
  <?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/readmore.min.js"></script>
  <?php } ?>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/login.js"></script>
  <style>.nice-color,a:hover{color:#4a86e8}.site-header,.home-filter--content{background-color:#4a86e8}.button,input[type="submit"],button[type="submit"],.navigation .nav-previous a,.navigation .nav-next a{background-color:#4a86e8}.owl .owl-prev,.owl .owl-next,.term-bar{background-color:#4a86e8}.on{color:#4a86e8}.filter--content .filter-item a.on i{background:#4a86e895}.off-canvas .logo{background:#fff}<?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>.entry-media .placeholder{padding-bottom:70%!important;}<?php } ?>.navbar li ul{min-width:190px;}.lanse{text-indent:0em !important;font-size:16px;font-weight:normal;color:#FFF;margin:10px 0;padding:5px 10px;background-color:#ed2d38;display:none;}.lanseseo{text-indent:0em !important;font-size:16px;font-weight:normal;color:#FFF;margin:10px 0;padding:5px 10px;background-color:#ed2d38;display:inline-block;}.article-content .post-album li img{width:auto!important;height:auto!important;}.navbar li ul{min-width:100% !important;}.u-text-format h2{font-size:20px!important;border-left:5px solid #4a86e8!important;padding-left:15px!important;font-weight:bold!important;}p{margin:0 0 30px;}</style> 
 </head>
 <?php if ($control=='index' && $action == 'index') { ?>
 <body class="index home blog modular-title-2 paged-next"> 
 <?php }elseif($control=='cate' && $action == 'index') { ?>
 <body class="category paged-next ">
 <?php }elseif($control=='show' && $action == 'index') { ?>
 <body class="article single single-post sidebar-right ">
 <?php }elseif($control=='tag' && $action == 'index') { ?>
 <body class="tag paged-next ">
 <?php }elseif($control=='search' && $action == 'index') { ?>
 <body class="search archive searchplus paged-next "> 
 <?php }else{ ?>
 <body class="page page sidebar-right " style="transform: none;">
 <?php } ?>
  <div class="site"> 
    <header class="site-header">
    <div class="container"> 
     <div class="navbar"> 
      <div class="logo-wrapper"> 
       <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"> <img class="logo regular tap-logo"  alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> </a> 
      </div> 
      <div class="sep"></div> 
      <nav class="main-menu hidden-xs hidden-sm hidden-md"> 
       <ul id="menu-menu-1" class="nav-list u-plain-list"> 
        <nav class="main-menu hidden-xs hidden-sm hidden-md"> 
         <ul id="menu-menu-1" class="nav-list u-plain-list"> 
		  <?php $data = block_navigate(array (
)); ?> 
          <li> <a href="/">首页</a></li> 
          <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> 
          <li class="<?php if (isset($v['son'])) { ?>menu-item-has-children<?php } ?> <?php if ($cfg_var['topcid'] == $v['cid']) { ?> current-menu-item<?php } ?>"> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
		   <?php if (isset($v['son'])) { ?>
           <ul> 
            <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li> <a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?>
           </ul> 
		   <?php } ?> 
		  </li> 
		  <?php }} ?>
          <?php unset($data); ?>
         </ul>
        </nav> 
       </ul> 
      </nav> 
      <div class="actions">  
       <div class="login-btn navbar-button main_nav"><!--main_nav弹窗登录注册-->
        <?php if (empty($_uid)) { ?><a href="/user-login.html" title="会员登录"><i class="mdi mdi-account"></i> 登录/注册</a><?php }else{ ?><a href="/my-index.html" title="用户中心"><i class="mdi mdi-account"></i> 用户中心</a><?php } ?>
       </div> 
	    <!--点击弹窗--><div class="note-open navbar-button"><i class="fa fa-bell-o"></i></div>
       <div class="burger"></div> 
      </div> 
     </div> 
    </div> 
   </header>
<style>
	.error .clip .shadow {height:180px;}
	.error .clip:nth-of-type(2) .shadow {width:130px;}
	.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:250px;}
	.error .digit {width:150px;height:150px;line-height:150px;font-size:120px;font-weight:bold;}
	.error h2 {font-size:32px;}
	.error .msg {top:-190px;left:30%;width:80px;height:80px;line-height:80px;font-size:32px;}
	.error span.triangle {top:70%;right:0%;border-left:20px solid #535353;border-top:15px solid transparent;border-bottom:15px solid transparent;}
	.error .container-error-404 {top: 50%;margin: 200px;position:relative;height:250px;padding-top:40px;}
	.error .container-error-404 .clip {display:inline-block;transform:skew(-45deg);}
	.error .clip .shadow {overflow:hidden;}
	.error .clip:nth-of-type(2) .shadow {overflow:hidden;position:relative;box-shadow:inset 20px 0px 20px -15px rgba(150,150,150,0.8),20px 0px 20px -15px rgba(150,150,150,0.8);}
	.error .clip:nth-of-type(3) .shadow:after,.error .clip:nth-of-type(1) .shadow:after {content:"";position:absolute;right:-8px;bottom:0px;z-index:9999;height:100%;width:10px;background:linear-gradient(90deg,transparent,rgba(173,173,173,0.8),transparent);border-radius:50%;}
	.error .clip:nth-of-type(3) .shadow:after {left:-8px;}
	.error .digit {position:relative;top:8%;color:white;background:#1E9FFF;border-radius:50%;display:inline-block;transform:skew(45deg);}
	.error .clip:nth-of-type(2) .digit {left:-10%;}
	.error .clip:nth-of-type(1) .digit {right:-20%;}
	.error .clip:nth-of-type(3) .digit {left:-20%;}
	.error h2 {font-size:24px;color:#A2A2A2;font-weight:bold;padding-bottom:20px;}
	.error .tohome {font-size:16px;color:#07B3F9;}
	.error .msg {position:relative;z-index:9999;display:block;background:#535353;color:#A2A2A2;border-radius:50%;font-style:italic;}
	.error .triangle {position:absolute;z-index:999;transform:rotate(45deg);content:"";width:0;height:0;}
	@media(max-width:767px) {.error .clip .shadow {height:100px;}
		.error .clip:nth-of-type(2) .shadow {width:80px;}
		.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:100px;}
		.error .digit {width:80px;height:80px;line-height:80px;font-size:52px;}
		.error h2 {font-size:18px;}
		.error .msg {top:-110px;left:15%;width:40px;height:40px;line-height:40px;font-size:18px;}
		.error span.triangle {top:70%;right:-3%;border-left:10px solid #535353;border-top:8px solid transparent;border-bottom:8px solid transparent;}
		.error .container-error-404 {height:150px;}
	}
</style>
<main class="main">
	<div class="error">
		<div class="container-floud">
			<div style="text-align: center">
				<div class="container-error-404">
					<div class="clip">
						<div class="shadow">
							<span class="digit thirdDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit secondDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit firstDigit"></span>
						</div>
					</div>
					<div class="msg">OH!
						<span class="triangle"></span>
					</div>
				</div>
				<h2 class="h1">很抱歉，你访问的页面找不到了</h2>
			</div>
		</div>
	</div>
</main>
<script type="text/javascript">
	function randomNum() {
		return Math.floor(Math.random() * 9) + 1;
	}

	var loop1, loop2, loop3, time = 30, i = 0, number;
	loop3 = setInterval(function () {
		if (i > 40) {
			clearInterval(loop3);
			document.querySelector('.thirdDigit').textContent = 4;
		} else {
			document.querySelector('.thirdDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop2 = setInterval(function () {
		if (i > 80) {
			clearInterval(loop2);
			document.querySelector('.secondDigit').textContent = 0;
		} else {
			document.querySelector('.secondDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop1 = setInterval(function () {
		if (i > 100) {
			clearInterval(loop1);
			document.querySelector('.firstDigit').textContent = 4;
		} else {
			document.querySelector('.firstDigit').textContent = randomNum();
			i++;
		}
	}, time);
</script>
   <!--登录留言 开始-->   
   <div class="cd-user-modal"> 
    <div class="cd-user-modal-container"> 
    <ul class="cd-switcher"> 
	 <li><a href="#login">会员登录</a></li> 
     <li><a href="#message">在线留言</a></li> 
    </ul> 
    <!-- 登录表单 --> 
    <div id="cd-login"> 
	<?php if (empty($_uid)) { ?>
    <form  class="cd-form" id="login-form" action="<?php echo(isset($login_url) ? $login_url : ''); ?>" method="post">
    <input type="hidden" name="FORM_HASH" value="<?php echo(isset($form_hash) ? $form_hash : ''); ?>" />
    <p class="fieldset"><input class="full-width has-padding has-border" id="username" type="text" name="username" value="" placeholder="* 请输入 用户名" autocomplete="off"></p>
    <p class="fieldset"><input class="full-width has-padding has-border" id="password" type="password" name="password" value="" placeholder="* 请输入 密码" autocomplete="off"></p>
    <?php if ($cfg['open_user_login_vcode']) { ?>
    <p class="fieldset"><input class="full-width3 has-padding has-border" id="vcode" type="text" name="vcode" value="" placeholder="* 请输入 验证码" autocomplete="off">&nbsp;&nbsp;<img src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?user-vcode-name-loginvcode" alt="验证码" onclick="this.src='index.php?user-vcode-loginvcode-r-'+Math.random();" id="vcodeimg" style="width: 40%;" />
    </p>
    <?php } ?>
    <p class="fieldset"><button type="submit" class="full-width2"  lay-submit lay-filter="form">登 录</button>
      <?php if ($cfg['open_user_register']) { ?><a href="/user-register.html" target="_blank" title="用户注册" class="btn btn-default btn-lg btn-block mt-3 no-border">还没有账号？点击注册</a><?php } ?>
	  <?php if ($cfg['open_user_reset_password']) { ?><a href="/user-forget.html" target="_blank" class="btn btn-default btn-lg btn-block mt-3 no-border">忘记密码?</a><?php } ?>
    </p>
    

    </form>
    <?php }else{ ?>
     <div class="cd-form"> 
      <p class="fieldset">即将进入会员中心，请稍等......</p> 
     </div>
	<?php } ?>
    </div>  	
     <!-- 留言提交 --> 
    <div id="cd-message"> 
     <form class="cd-form" id="ctf_message_form" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-domessage-ajax-1.html" method="post"> 
      <p class="fieldset"><input type="text" class="full-width has-padding has-border" name="message[title]" value="" placeholder="* 请输入 留言标题" required="required" /></p> 
      <p class="fieldset"><input type="text" class="full-width has-padding has-border" name="message[author]" value="" placeholder="* 请输入 您的称呼" required="required" /></p> 
      <p class="fieldset"><input type="text" class="full-width has-padding has-border" name="message[contact]" value="" placeholder="* 请输入 联系电话" required="required" /></p> 
      <p class="fieldset"><textarea type="text"  class="full-width has-padding has-border" name="message[content]" placeholder="* 请输入 留言内容，" required="required"></textarea></p> 
      <p class="fieldset"><input class="full-width3 has-padding has-border" name="message[vcode]" placeholder="* 请输入 验证码" type="text" required="required" />&nbsp;&nbsp;<img id="captchaPic" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode-name-message" onclick="this.src='<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode-name-message-r-'+Math.random();" alt="验证码" style="width: 40%;" /></p> 
      <p class="fieldset"><input class="full-width2" type="submit" id="ctf_submit" tabindex="6" value="提交" /></p> 
      <p id="ctf_message_tips"></p> 
     </form> 
    </div> 
    <a href="#0" class="cd-close-form">关闭</a> 
    </div> 
  </div>
  <!-- 留言提交 --> 
  <script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/pxmu.min.js"></script>
  <script>
    // 留言提交
    window.ctf_message_form_one = false;
    $("#ctf_message_form").submit(function() {
        if (window.ctf_message_form_one) return false;
        window.ctf_message_form_one = true;
        var content = $("textarea[name='message[content]']").val();
        var title = $("input[name='message[title]']").val();
        var contact = $("input[name='message[contact]']").val();
        var author = $("input[name='message[author]']").val();
        var vcode = $("input[name='message[vcode]']").val();
        setTimeout(function(){
            window.ctf_message_form_one = false;
        }, 2000);
        if( content == '' || title == '' || contact == '' || author == '' || vcode == '' ){
            pxmu.fail('各项信息都不能为空哦！');
        }else{
            var _this = $(this);
            $.post(_this.attr("action"), _this.serialize(), function(data){
                try{
                    var json = eval("("+data+")");
                    if(json.kong_status) {
                        pxmu.success({msg: json.message, time: 1000});
                        setTimeout(function(){
                            window.location.reload();
                        }, 2000);
						$("#ctf_message_form").find("input").val("");//递交成功清空input
						$("#ctf_message_form").find("textarea").val("");//递交成功清空textarea
                    }else{
                        pxmu.fail(json.message);
                    }
                }catch(e){
                    alert(data);
                }
            });
        }
        return false;
    });
  </script>
  <!--登录留言 END--> 
<!--边栏客服-->
<link rel="stylesheet" type="text/css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/supermenu.css"/>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/jquery.supermenu.js"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/share.js"></script>
<div class="hovermenu">
   <div class="hovermenu-box">
    <a href="javascript:;" class="a-show"><span class="i"></span></a> 
    <a href="javascript:;" class="a a-hide"><span class="i"></span></a> 
	<?php if (($control=='index' || $control=='cate' || $control=='show' || $control=='tag') && $action == 'index') { ?>
    <div class="a">
     <span class="i a-share" title="点击分享"></span> 
     <div class="d-share" style="display: none;"> 
      <h3>分享<span class="d-close iconfont"></span></h3>
      <div class="Hcopyurl">
       <p id="Hcopyurl" onclick="copyurl()">
	    <?php if ($control=='index' && $action == 'index') { ?>
		<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>
		<?php }elseif($control=='show' && $action == 'index') { ?>
		<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>
		<?php }else{ ?>
		<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($cfg_var['url']) ? $cfg_var['url'] : ''); ?>
		<?php } ?>
	   </p> 
       <span onclick="copyurl()">复制链接</span> 
      </div>
       <div class="social-share" data-initialized="true" style="text-align: center;"> 
          <a href="#" class="social-share-icon icon-weibo"></a> 
          <a href="#" class="social-share-icon icon-qq"></a> 
          <a href="#" class="social-share-icon icon-wechat"></a> 
          <a href="#" class="social-share-icon icon-qzone"></a> 
          <a href="#" class="social-share-icon icon-facebook"></a> 
          <a href="#" class="social-share-icon icon-twitter"></a> 
       </div> 
     </div> 
    </div> 
	<?php } ?>
    <div class="a">
     <span class="i a-qq"></span> 
     <div class="d d-qq" style="display: none;">
	  <span class="arrow"></span>
      <div class="hqq">售前咨询<a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="咨询网站客服QQ"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/qq.png" />QQ在线</a></div> 
	  <div class="hqq">售后服务<a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="咨询网站客服QQ"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/qq.png" />QQ在线</a></div> 
      <div class="worktime">
       上班时间：9：00-22：00
       <br />周六、周日：14：00-22：00
      </div>
     </div>
    </div> 
	<?php if ($control=='show' && $action == 'index'  &&  $gdata['payprice']>0) { ?>
    <div class="a">
     <span class="i a-buy"></span> 
     <div class="d d-buy" style="display: none;">
      <span class="arrow"></span> 
      <div class="Hbuy">
       <span class="hprice"><?php if ($gdata['payprice']>0) { echo(isset($gdata['payprice']) ? $gdata['payprice'] : ''); }else{ ?>免费<?php } ?></span>
       <a href="#pay" title="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"><img src="<?php if ($gdata['haspic']) { echo(isset($gdata['pic']) ? $gdata['pic'] : ''); }else{ echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); } ?>" alt="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>" class="buycover" /></a>
       <a href="#pay" title="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"><h3>购买主题</h3></a>
       <div class="htips">
        <?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>
       </div>
      </div>
     </div>
    </div> 	
	<?php }else{ ?>
    <div class="a">
     <span class="i a-buy"></span> 
     <div class="d d-buy" style="display: none;">
      <span class="arrow"></span> 
      <div class="Hbuy">
       <span class="hprice">399</span>
       <a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="购买本站同款主题"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/theme.jpg" alt="购买本站同款主题" class="buycover" /></a>
       <a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="购买本站同款主题"><h3>购买本站同款主题</h3></a>
       <div class="htips">
        本站LECMS付费资源主题
       </div>
      </div>
     </div>
    </div> 	
	<?php } ?>
	<?php if ($control=='show' && $action == 'index'  &&  $gdata['payprice']>0) { ?>
    <div class="a">
     <span class="i a-down"></span> 
     <div class="d d-down" style="display: none;">
      <i class="arrow"></i>
      <h3><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h3>
      <a href="#download" class="Hdown"><span class="iconfont"></span>点击下载</a>
      <div class="htips">
		<?php if (isset($gdata['tag_arr'])) { if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?>
         <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" rel="tag"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 			 
        <?php }} } ?>
      </div>
     </div>
    </div>	
	<?php }else{ ?>
    <div class="a">
     <span class="i a-down"></span> 
     <div class="d d-down" style="display: none;">
      <i class="arrow"></i>
      <h3>LECMS网站程序 LECMS 3.0 正式版</h3>
      <a target="_blank" rel="nofollow"  href="https://www.lecms.cc/?thread-205.htm" class="Hdown"><span class="iconfont"></span>点击下载</a>
      <div class="htips">
       LECMS是一款高负载、轻量级、可扩展建站程序。
      </div>
     </div>
    </div>
    <?php } ?>	
	<a href="#" class="a" title="VIP会员"><span class="i a-vip note-open"></span></a>
    <a href="javascript:;" class="a-top" style="display: block;" title="返回顶部"><span class="i"></span></a> 
   </div>
</div>   
 <script>
function copyurl(){
const range = document.createRange();
range.selectNode(document.getElementById("Hcopyurl"));
const selection = window.getSelection();
if(selection.rangeCount > 0) selection.removeAllRanges();
selection.addRange(range);
document.execCommand("copy");
alert("复制链接成功，感谢您的关注！");
};
function copytel(){
const range = document.createRange();
range.selectNode(document.getElementById("copytel"));
const selection = window.getSelection();
if(selection.rangeCount > 0) selection.removeAllRanges();
selection.addRange(range);
document.execCommand("copy");
alert("已经复制电话号码！期待您的来电！");
};
</script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/chinese-s.js"></script>     
   <div class="module parallax"> 
    <img class="jarallax-img lazyload" data-srcset="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/footbanner.jpg" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="" /> 
    <div class="container"> 
     <h4 class="entry-title" data-aos="fade-up">哥特动漫王国-哥特萝莉社</h4> 
     <a target="_blank" class="button" data-aos="fade-right" href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" rel="nofollow" title="全站免费"><i class="fa fa-heartbeat"></i> 模板定制</a> 
     <a target="_blank" class="button transparent" data-aos="fade-left" href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" rel="nofollow" title="欢迎加入"><i class="fa fa-qq"></i> 联系我们</a> 
    </div> 
   </div>
   <footer class="site-footer"> 
    <div class="container"> 
     <div class="footer-widget"> 
      <div class="row"> 
       <div class="col-xs-12 col-sm-6 col-md-3 widget--about"> 
        <div class="widget--content"> 
         <div class="footer--logo mb-20"> 
          <img class="tap-logo" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/logofoot.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> 
         </div> 
         <p class="mb-10"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></p> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="col-xs-12 col-sm-3 col-md-2 col-md-offset-1 widget--links"> 
        <div class="widget--title"> 
         <h5>本站导航</h5> 
        </div> 
        <div class="widget--content"> 
         <ul class="list-unstyled mb-0"> 
          <li><a href="/about">关于我们</a></li> 
          <li><a href="/contact">联系我们</a></li> 
          <li><a href="/aftersale">售后保障</a></li> 
         </ul> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="col-xs-12 col-sm-3 col-md-2 widget--links"> 
        <div class="widget--title"> 
         <h5>更多介绍</h5> 
        </div> 
        <div class="widget--content"> 
         <ul class="list-unstyled mb-0"> 
          <li><a href="/process">购买流程</a></li> 
          <li><a href="/clause">交易条款</a></li> 
          <li><a href="/problem">常见问题</a></li> 
         </ul> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="col-xs-12 col-sm-12 col-md-4 widget--newsletter"> 
        <div class="widget--title"> 
         <h5>快速搜索</h5> 
        </div> 
        <div class="widget--content"> 
         <form id="search_form3" class="newsletter--form mb-30" method="get" target="_blank"> 
		  <input type="hidden" name="u" value="search-index" /> 
          <input type="text" class="full-width has-padding has-border" name="keyword" placeholder="关键词" /> 
          <button type="submit"><i class="fa fa-arrow-right"></i></button> 
         </form> 
         <h6><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>提供的大部分资源来于网络收集整理，仅供学习交流之用。请勿非法使用，否则产生的一切后果自行承担，本站概不负责。</h6>
        </div> 
       </div> 
      </div> 
     </div> 
    </div> 
	<?php if ($control=='index' && $action == 'index') { ?>
    <div class="links"> 
     <div class="container"> 
      <ul>
       <li>友情链接：</li>
	   <?php $data = block_links(array (
)); if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
       <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
       <?php }} unset($data); ?> 
      </ul> 
     </div> 
    </div>  
	<?php } ?>
    <div class="site-info">
	 本站所发布的大部分内容来源于互联网，仅限于小范围内传播学习和文献参考，请在下载后24小时内删除！如果有侵权之处请第一时间联系我们删除，敬请谅解！
     <br />任何人不得对本站资源进行倒卖、行骗、传播。严禁用于商业用途，请遵循相关法律法规，本站一切资源不代表本站立场！
     <br />Copyright&copy;2015-2021 <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" rel="home"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>
     <a href="/sitemap.xml" target="_blank">网站地图</a> 
     <a href="/tags" target="_blank">TAGS</a>
     <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a> 
    </div> 	
   </footer> 
   <!--<div class="rollbar">
   <div class="rollbar-item note-open"><i class="fa fa-bell-o"></i></div><!--点击弹窗-->
   <!--<div class="rollbar-item" etap="to_top"><i class="fa fa-angle-up"></i></div><!--返回顶部--> 
   <!--</div>-->  
   <!--移动端logo-->
   <div class="off-canvas"> 
    <div class="canvas-close">
     <i class="mdi mdi-close"></i> 
    </div> 
    <div class="logo-wrapper"> 
     <a href="/"><img class="logo regular" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/logom.jpg" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> </a> 
    </div> 
    <div class="mobile-menu hidden-lg hidden-xl"></div> 
    <aside class="widget-area"></aside> 
   </div> 
   <!--js-->
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/main.js'></script>
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/plugins.js'></script>
   <script language="javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/server.js"></script>
   <?php if ($control=='show' && $action == 'index') { ?>
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery.fancybox.min.js'></script>
   <script type="application/ld+json">
   {
    "@content": "https://ziyuan.baidu.com/contexts/cambrian.jsonld",
    "@id": "<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>",
    "appid": "",
    "title": "<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>",
    "images": ["<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>"],
    "description": "<?php echo(isset($gdata['intro']) ? $gdata['intro'] : ''); ?>",
    "pubDate": "<?php  echo date('Y-m-d', $gdata['dateline']).'T'.date('H:i:s', $gdata['dateline']); ?>",
    "upDate": "<?php  echo date('Y-m-d', $gdata['lasttime']).'T'.date('H:i:s', $gdata['lasttime']); ?>",
    "lrDate": "<?php  echo date('Y-m-d', $gdata['lasttime']).'T'.date('H:i:s', $gdata['lasttime']); ?>"
   }
   </script>  
   <script>
    $(".entry-content a").each(function(){
    var articleHref = $(this).attr("href").split('/')[2];
    if(articleHref != window.location.host){
    $(this).attr("rel","external nofollow");
    };
    })
   </script>
   <?php } ?>
   </div>
   <?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>
   <script>$(".moretag").readmore({moreLink: '<a href="#" class="more"><i class="fa fa-plus-square"></i>更多</a>',lessLink: '<a href="#" class="more"><i class="fa fa-minus-square"></i>收起</a>',speed: 100,collapsedHeight: 30});</script>
   <?php } ?>
   <script>   
	(function(){
		$("#search_form,#search_form2,#search_form3").submit(function(){
			var mid = $(this).find("[name='mid']").val();
			var keyword = $(this).find("[name='keyword']").val();
			window.location.href = "/search/"+encodeURIComponent(keyword)+"/";
			return false;
		});	
	})();  
   </script>    
  </div>
 </body>
</html> 

