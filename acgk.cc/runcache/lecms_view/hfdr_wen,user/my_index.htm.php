<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="renderer" content="webkit">
  <link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/frontend.min.css" media="all">
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/user.css" media="all">
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
  <!--[if lt IE 9]>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/html5shiv.js"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/respond.min.js"></script>
  <![endif]-->
  <script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js" charset="utf-8"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/bootstrap.min.js"></script>
  <style>
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand{margin-left: 0;}
    .navbar-brand{padding: 0;}
    .navbar-brand img{height: 45px;vertical-align: center;padding-top: 5px;}
  </style>
</head>
<body>
<nav class="hgacg-dc9e93 navbar navbar-white navbar-fixed-top" role="navigation">
  <div class="hgacg-e849bd container">
    <div class="hgacg-ecb9eb navbar-header">
      <button type="button" class="hgacg-808760 navbar-toggle" data-toggle="collapse" data-target="#header-navbar">
        <span class="hgacg-27673d sr-only"><?php echo '切换'; ?></span>
        <span class="hgacg-82f9ad icon-bar"></span>
        <span class="hgacg-82f9ad icon-bar"></span>
        <span class="hgacg-82f9ad icon-bar"></span>
      </button>
      <a class="hgacg-6af25c navbar-brand" href="<?php echo(isset($my_url) ? $my_url : ''); ?>" title="用户中心">
      </a>
    </div>
    <div class="hgacg-0ee60b collapse navbar-collapse" id="header-navbar">
      <ul class="hgacg-f2cbb6 nav navbar-nav navbar-right">
        <li><a href="/" title="/"><?php echo '首页'; ?></a></li>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
        <li class="hgacg-bf3200 mainlevel"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" <?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="hover"<?php } ?>><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
        <li class="hgacg-d44435 dropdown">
          <a href="<?php echo(isset($my_url) ? $my_url : ''); ?>" class="hgacg-0d48bd dropdown-toggle" data-toggle="dropdown">
            <span class="hgacg-13bf6f avatar-img"><img src="http://img.soogif.com/x3CvazRkvwy4qgYNt5K8bOzSI4tdRTUj.gif?v=1.0" alt="<?php echo(isset($_user['username']) ? $_user['username'] : ''); ?>"></span>
            <span class="hgacg-ccbaaa visible-xs-inline-block" style="padding:5px;"><?php echo(isset($_user['username']) ? $_user['username'] : ''); ?></span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</nav>
<style>
  .basicinfo {margin: 15px 0;}
  .basicinfo .row > .col-xs-4 {padding-right: 0;}
  .basicinfo .row > div {margin: 5px 0;}
</style>

<main class="hgacg-1bc3a4 content">
  <div id="content-container" class="hgacg-e849bd container">
    <div class="hgacg-a2c59a row">
      <style>
    #logout{cursor: pointer;}
  .sidebar-toggle {
            display: block;
            position: fixed;
            right: 20px;
            top: 70px;
            border-radius: 50%;
            background: #007bff; /* 更鲜艳的背景颜色 */
            color: white; /* 字体颜色 */
            font-size: 24px; /* 增大字体大小 */
            padding: 10px;
            line-height: 30px;
            height: 60px; /* 增加按钮高度 */
            width: 60px; /* 增加按钮宽度 */
            text-align: center;
            z-index: 999999;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.5); /* 添加阴影效果 */
            transition: background-color 0.3s, transform 0.3s; /* 添加过渡效果 */
        }
</style>
<div class="hgacg-9e1a63 col-md-3">
  <div class="hgacg-5b2d1e sidebar-toggle"><i class="hgacg-f9e8a2 fa fa-bars"></i></div>
  <div id="sidebar-nav" class="hgacg-ae932d sidenav">
    <?php if(isset($_navs) && is_array($_navs)) { foreach($_navs as $k=>&$v) { ?>
    <ul class="hgacg-4e2ce3 list-group">
      <?php if (!empty($v['href'])) { ?>
      <li id="<?php echo(isset($k) ? $k : ''); ?>" class="hgacg-2c0bb8 list-group-heading">
        <a href="<?php echo(isset($v['href']) ? $v['href'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
      </li>
      <?php }else{ ?>
      <li id="<?php echo(isset($k) ? $k : ''); ?>" class="hgacg-2c0bb8 list-group-heading"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></li>
      <?php } ?>
      <?php if(isset($v['child']) && is_array($v['child'])) { foreach($v['child'] as &$v2) { ?>
      <li id="<?php echo(isset($v2['id']) ? $v2['id'] : ''); ?>" class="hgacg-61f5d0 list-group-item">
        <?php if (!empty($v2['href'])) { ?>
        <a href="<?php echo(isset($v2['href']) ? $v2['href'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>"><?php if (!empty($v2['icon'])) { ?><i class="hgacg-8d0b79 <?php echo(isset($v2['icon']) ? $v2['icon'] : ''); ?>"></i> <?php } echo(isset($v2['title']) ? $v2['title'] : ''); ?></a>
        <?php }else{ ?>
        <?php if (!empty($v2['icon'])) { ?><i class="hgacg-8d0b79 <?php echo(isset($v2['icon']) ? $v2['icon'] : ''); ?>"></i> <?php } echo(isset($v2['title']) ? $v2['title'] : ''); ?>
        <?php } ?>
      </li>
      <?php }} ?>
    </ul>
    <?php }} ?>
  </div>
</div>
<script type="text/javascript">
  $(document).on("click", ".sidebar-toggle", function () {
    $("body").toggleClass("sidebar-open");
  });
</script>
      <!--右侧主体部分 start-->
      <div class="hgacg-a3c241 col-md-9">
        <div class="hgacg-e54ecf panel panel-default">
          <div class="hgacg-95a19a panel-body">
            <h2 class="hgacg-4ada80 page-header">
              <?php echo '基本信息'; ?>
            </h2>
            <div class="hgacg-c800be row user-baseinfo">

              <div class="hgacg-8da8f4 col-md-9 col-sm-9 col-xs-10">
                <div class="hgacg-1500d9 ui-content">
                  <h4><?php echo(isset($_user['username']) ? $_user['username'] : ''); ?></h4>
                  <p>用户组：<span style="color: red"><?php echo(isset($_group['groupname']) ? $_group['groupname'] : ''); ?></span> <?php if ($_user['vip_times']>0 && $_group['groupid']==12) { ?>到期时间：<?php echo(isset($_user['vip_times']) ? $_user['vip_times'] : ''); ?> <?php } ?>
                  </p>
                  <p><a href="/my-vip.html">开通/续费超级会员</a></p>
                  <p class="hgacg-4bce82 text-muted"><?php if (!empty($_user['intro'])) { echo(isset($_user['intro']) ? $_user['intro'] : ''); }else{ echo '这个人很懒,啥也没写！'; } ?></p>
                </div>
              </div>

              <div class="hgacg-e221a2 col-md-9 col-sm-9 col-xs-12">
                <div class="hgacg-1500d9 ui-content">
                  <div class="hgacg-c52cf9 basicinfo">
                    <div class="hgacg-a2c59a row">
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '作者'; ?></div>
                      <div class="hgacg-d91524 col-xs-8 col-md-10">
                        <?php echo(isset($_user['author']) ? $_user['author'] : ''); ?>
                      </div>
                    </div>
                    <div class="hgacg-a2c59a row">
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '邮箱'; ?></div>
                      <div class="hgacg-642965 col-xs-8 col-md-4"><?php if (!empty($_user['email'])) { echo(isset($_user['email']) ? $_user['email'] : ''); }else{ echo '未知'; } ?></div>
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '手机号'; ?></div>
                      <div class="hgacg-642965 col-xs-8 col-md-4"><?php if (!empty($_user['mobile'])) { echo(isset($_user['mobile']) ? $_user['mobile'] : ''); }else{ echo '未知'; } ?></div>
                    </div>
                    <div class="hgacg-a2c59a row">
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '本次登录'; ?></div>
                      <div class="hgacg-d91524 col-xs-8 col-md-10">
                        <?php echo(isset($_user['logindate']) ? $_user['logindate'] : ''); ?>（<?php echo(isset($_user['loginip']) ? $_user['loginip'] : ''); ?>）
                      </div>
                    </div>
                    <div class="hgacg-a2c59a row">
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '上次登录'; ?></div>
                      <div class="hgacg-d91524 col-xs-8 col-md-10">
                        <?php echo(isset($_user['lastdate']) ? $_user['lastdate'] : ''); ?>（<?php echo(isset($_user['lastip']) ? $_user['lastip'] : ''); ?>）
                      </div>
                    </div>
                    <div class="hgacg-a2c59a row">
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '注册时间'; ?></div>
                      <div class="hgacg-d91524 col-xs-8 col-md-10">
                        <?php echo(isset($_user['regdate']) ? $_user['regdate'] : ''); ?>（<?php echo(isset($_user['regip']) ? $_user['regip'] : ''); ?>）
                      </div>
                    </div>
                    <div class="hgacg-a2c59a row">
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '登录次数'; ?> </div>
                      <div class="hgacg-642965 col-xs-8 col-md-4"><?php echo(isset($_user['logins']) ? $_user['logins'] : ''); ?></div>
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '内容条数'; ?></div>
                      <div class="hgacg-642965 col-xs-8 col-md-4"><?php echo(isset($_user['contents']) ? $_user['contents'] : ''); ?></div>
                    </div>
                    <div class="hgacg-a2c59a row">
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '积分'; ?></div>
                      <div class="hgacg-642965 col-xs-8 col-md-4"><?php echo(isset($_user['credits']) ? $_user['credits'] : ''); ?></div>
                      <div class="hgacg-310ec6 col-xs-4 col-md-2"><?php echo '金币'; ?></div>
                      <div class="hgacg-642965 col-xs-8 col-md-4"><?php echo(isset($_user['golds']) ? $_user['golds'] : ''); ?></div>
                    </div>
                    
<!-- sj_rainbow_pay 账户状态显示 -->
</div>
</div>

<!-- 账户状态卡片 - 放在用户介绍下方 -->
<div class="hgacg-7976f1 col-md-9 col-sm-9 col-xs-12" style="margin-top: 15px;">
  <div class="hgacg-1500d9 ui-content">
    <style>
      .rainbow-pay-status {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin: 0;
      }
      .rainbow-pay-status h4 {
        margin-bottom: 15px;
        font-weight: 300;
        color: white;
        font-size: 18px;
      }
      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }
      .status-item-card {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        transition: all 0.3s;
      }
      .status-item-card:hover {
        background: rgba(255,255,255,0.2);
        transform: translateY(-2px);
      }
      .status-icon-small {
        font-size: 24px;
        margin-bottom: 8px;
        display: block;
      }
      .status-value-small {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        line-height: 1.2;
      }
      .status-label-small {
        font-size: 12px;
        opacity: 0.9;
      }
      .vip-expire-info {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 12px;
        margin-top: 15px;
        font-size: 14px;
        text-align: center;
      }
      .quick-actions-small {
        margin-top: 15px;
        text-align: center;
      }
      .quick-actions-small .btn {
        margin: 0 5px 5px 0;
        padding: 8px 15px;
        border-radius: 15px;
        font-size: 12px;
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        background: rgba(255,255,255,0.1);
        transition: all 0.3s;
        text-decoration: none;
        display: inline-block;
      }
      .quick-actions-small .btn:hover {
        background: rgba(255,255,255,0.2);
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
      }
      @media (max-width: 768px) {
        .status-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }
        .status-item-card {
          padding: 12px;
        }
        .status-value-small {
          font-size: 16px;
        }
        .rainbow-pay-status h4 {
          font-size: 16px;
        }
        .quick-actions-small .btn {
          font-size: 11px;
          padding: 6px 12px;
        }
      }
    </style>

    <!-- 彩虹易支付账户状态 -->
    <div class="hgacg-f4768d rainbow-pay-status">
      <h4><i class="hgacg-518316 fa fa-credit-card"></i> 账户状态</h4>

      <div class="hgacg-6ddf9b status-grid">
        <!-- 金币状态 -->
        <div class="hgacg-83c1ba status-item-card">
          <i class="hgacg-0c9ca0 fa fa-coins status-icon-small" style="color: #f39c12;"></i>
          <div class="hgacg-4b8c8d status-value-small"><?php echo(isset($_user['golds']) ? $_user['golds'] : ''); ?></div>
          <div class="hgacg-a73f45 status-label-small">当前金币</div>
        </div>

        <!-- VIP状态 -->
        <div class="hgacg-83c1ba status-item-card">
          <i class="hgacg-36b3d5 fa fa-crown status-icon-small" style="color: #e74c3c;"></i>
          <div class="hgacg-4b8c8d status-value-small">
            <?php if (stripos($_group['groupname'], 'VIP') !== false || stripos($_group['groupname'], 'vip') !== false || ($_user['groupid'] == 12 && $_user['vip_times'] > time()) || ($_user['vip_times'] > time())) { ?>
              VIP会员
            <?php }else{ ?>
              普通用户
            <?php } ?>
          </div>
          <div class="hgacg-a73f45 status-label-small">當前用戶組</div>
        </div>
      </div>
	
      <!-- VIP到期时间 -->
      <?php if ($_user['groupid'] == 12 && $_user['vip_times'] > time()) { ?>
      <div class="hgacg-ceb263 vip-expire-info">
        <strong>到期时间：</strong><?php echo(isset($vip_expire_time) ? $vip_expire_time : ''); ?>
      </div>
      <?php } ?>

      <!-- 快捷操作 -->
      <div class="hgacg-ebde63 quick-actions-small">
        <a href="/my-recharge.html" class="hgacg-a7a5cc btn btn-sm">
          <i class="hgacg-902baa fa fa-coins"></i> 金币充值
        </a>
        <a href="/my-recharge.html" class="hgacg-a7a5cc btn btn-sm">
          <i class="hgacg-e7900d fa fa-crown"></i> VIP充值
        </a>
        <a href="/my-orders.html" class="hgacg-a7a5cc btn btn-sm">
          <i class="hgacg-4a0fe8 fa fa-list-alt"></i> 充值记录
        </a>
      </div>
    </div>
  </div>
</div>

<!-- 恢复原有的布局结构 -->
<div class="hgacg-e221a2 col-md-9 col-sm-9 col-xs-12">
  <div class="hgacg-1500d9 ui-content">
    <div class="hgacg-c52cf9 basicinfo">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--右侧主体部分 end-->
    </div>
  </div>
</main>

<footer class="hgacg-bb0985 footer" style="clear:both">
  <p class="hgacg-3f0d57 copyright">Copyright&nbsp;©&nbsp;<?php echo date('Y'); ?> <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?> All Rights Reserved.</p>
</footer>
<script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var layer = layui.layer, $ = layui.$;
    $("#logout").click(function () {
      layer.confirm('确定退出登录？', function () {
        $.post("index.php?my-logout-ajax-1",{do: 1},function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location = "/"; }, 1000);
          return false;
        },'json');
      });
    });
  });
</script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    $("#my-index").addClass("active");
  });
</script>
</body>
</html>