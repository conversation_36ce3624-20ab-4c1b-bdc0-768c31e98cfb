<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
      <?php if ($gdata['seo_keywords']) { ?>
    <meta name="keywords" content="<?php echo(isset($gdata['seo_keywords']) ? $gdata['seo_keywords'] : ''); ?>" />
    <?php }elseif($gdata['tags']) { ?>
    <meta name="keywords" content="<?php echo implode(',',$gdata['tags']); ?>" />
    <?php }else{ ?>
    <meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
    <?php } ?>
  <meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="renderer" content="webkit">
  <link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/frontend.min.css" media="all">
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/user.css" media="all">
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
  <!--[if lt IE 9]>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/html5shiv.js"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/respond.min.js"></script>
  <![endif]-->
  <script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js" charset="utf-8"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/bootstrap.min.js"></script>
</head>
<body>
  <nav class="hgacg-dc9e93 navbar navbar-white navbar-fixed-top" role="navigation">
    <div class="hgacg-e849bd container">
      <div class="hgacg-ecb9eb navbar-header">
        <button type="button" class="hgacg-808760 navbar-toggle" data-toggle="collapse" data-target="#header-navbar">
          <span class="hgacg-27673d sr-only"><?php echo '切换'; ?></span>
          <span class="hgacg-82f9ad icon-bar"></span>
          <span class="hgacg-82f9ad icon-bar"></span>
          <span class="hgacg-82f9ad icon-bar"></span>
        </button>
        <a class="hgacg-c91a04 navbar-brand" href="/"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>
      </div>
      <div class="hgacg-0ee60b collapse navbar-collapse" id="header-navbar">
        <ul class="hgacg-f2cbb6 nav navbar-nav navbar-right">
          <li><a href="/" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"><?php echo '首页'; ?></a></li>
        </ul>
      </div>
    </div>
  </nav>

  <main class="hgacg-1bc3a4 content">
    <div id="content-container" class="hgacg-e849bd container">
      <div class="hgacg-3f26a6 user-section login-section">
        <div class="hgacg-8972ea logon-tab clearfix">
          <?php if ($cfg['open_user_login']) { ?><a href="<?php echo(isset($login_url) ? $login_url : ''); ?>" title="<?php echo '登录'; ?>" rel="nofollow"><?php echo '登录'; ?></a><?php } ?>
          <a class="hgacg-8d865b active" title="<?php echo '注册'; ?>" rel="nofollow"><?php echo '注册'; ?></a>
        </div>
        <div class="hgacg-42ffbd login-main">
          <form id="login-form" class="hgacg-bb55d1 form-horizontal layui-form" action="<?php echo(isset($register_url) ? $register_url : ''); ?>" method="post">
            <input type="hidden" name="FORM_HASH" value="<?php echo(isset($form_hash) ? $form_hash : ''); ?>" />
            <div class="hgacg-58ec43 form-group">
              <label for="username" class="hgacg-659a73 col-sm-4 control-label"><?php echo '用户名'; ?></label>
              <div class="hgacg-190d23 col-sm-8">
                <input class="hgacg-aba265 form-control" id="username" type="text" name="username" value="" placeholder="<?php echo '请输入用户名'; ?>" autocomplete="off">
              </div>
            </div>
			<div class="hgacg-58ec43 form-group">
              <label for="email" class="hgacg-659a73 col-sm-4 control-label"><?php echo '邮箱'; ?></label>
              <div class="hgacg-190d23 col-sm-8">
                <input class="hgacg-244e16 form-control" id="email" type="email" name="email" value="" placeholder="请输入邮箱" autocomplete="off">
              </div>
            </div>
            <div class="hgacg-58ec43 form-group">
              <label for="password" class="hgacg-659a73 col-sm-4 control-label"><?php echo '密码'; ?></label>
              <div class="hgacg-190d23 col-sm-8">
                <input class="hgacg-3ed95f form-control" id="password" type="password" name="password" value="" placeholder="<?php echo '请输入密码'; ?>" autocomplete="off">
              </div>
            </div>

            <div class="hgacg-58ec43 form-group">
              <label for="repassword" class="hgacg-659a73 col-sm-4 control-label"><?php echo '确认密码'; ?></label>
              <div class="hgacg-190d23 col-sm-8">
                <input class="hgacg-ea7abc form-control" id="repassword" type="password" name="repassword" value="" placeholder="<?php echo '请输入密码'; ?>" autocomplete="off">
              </div>
            </div>

            <?php if ($cfg['open_user_register_vcode']) { ?>
            <div class="hgacg-58ec43 form-group">
              <label for="vcode" class="hgacg-659a73 col-sm-4 control-label"><?php echo '验证码'; ?></label>
              <div class="hgacg-d44acc col-sm-4">
                <input class="hgacg-c70a58 form-control" id="vcode" type="text" name="vcode" value="" placeholder="<?php echo '验证码'; ?>" autocomplete="off">
              </div>
              <div class="hgacg-d44acc col-sm-4">
                <img src="index.php?user-vcode-name-registervcode.html" alt="<?php echo '验证码'; ?>" onclick="this.src='index.php?user-vcode-name-registervcode-r-'+Math.random();" id="vcodeimg" style="width: 100%;" />
              </div>
            </div>
            <?php } ?>
            

            <div class="hgacg-58ec43 form-group">
              <label class="hgacg-659a73 col-sm-4 control-label"></label>
              <div class="hgacg-190d23 col-sm-8">
                <button type="submit" class="hgacg-f2dc0b btn btn-primary btn-lg btn-block" lay-submit lay-filter="form"><?php echo '提交'; ?></button>
                <?php if ($cfg['open_user_login']) { ?><a href="<?php echo(isset($login_url) ? $login_url : ''); ?>" title="<?php echo '登录'; ?>" rel="nofollow" class="hgacg-e62455 btn btn-default btn-lg btn-block mt-3 no-border"><?php echo '已有账号？点击登录'; ?></a><?php } ?>
              </div>
            </div>
            

          </form>
        </div>
      </div>
    </div>
  </main>

  <footer class="hgacg-bb0985 footer" style="clear:both">
    <p class="hgacg-3f0d57 copyright">Copyright&nbsp;©&nbsp;<?php echo date('Y'); ?> <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?> All Rights Reserved.</p>
  </footer>

  <script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
  <script type="text/javascript">
    layui.use(['form'], function () {
      var form = layui.form, layer = layui.layer, $ = layui.$;

      form.on('submit(form)', function (data) {
        data = data.field;
        if (data.username == '') {
          layer.msg('<?php echo '请输入用户名'; ?>', {icon: 5});
          return false;
        } else if (data.email == '') {  // 新增邮箱验证
        layer.msg('<?php echo 'lang[please_input_email]'; ?>', {icon: 5});
        return false;
        } else if (data.password == '') {
          layer.msg('<?php echo '请输入密码'; ?>', {icon: 5});
          return false;
        }else if (data.repassword == '') {
          layer.msg('<?php echo '请输入确认密码'; ?>', {icon: 5});
          return false;
        }else if(data.password != data.repassword){
          layer.msg('<?php echo '两次输入的密码不一致'; ?>', {icon: 5});
          return false;
        }
        $.post("index.php?user-register-ajax-1",data,function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ location.href="/"; }, 1000);
          return false;
        },'json');
        return false;
      });
    });
  </script>
</body>
</html>