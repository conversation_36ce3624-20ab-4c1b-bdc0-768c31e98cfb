<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
?><!doctype html>
<html lang="zh-Hans">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="ie=edge" />
	<meta name="generator" content="acg" />
	<meta name="renderer" content="webkit">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
	<meta name="keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
	<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
	<link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
	<link rel="stylesheet" href="//cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css" media="all">
	<link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>style/style.css" type="text/css" media="all"/>
	<script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js"></script>
	<link href="/css/home.css" rel="stylesheet" type="text/css">
	<script type="text/javascript">
		window._LE = {
			uri: "<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>",
			uid: "<?php echo(isset($_uid) ? $_uid : ''); ?>",
			parseurl: "<?php echo(isset($_parseurl) ? $_parseurl : ''); ?>"
		};
	</script>
	<script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>script/main.js"></script>
</head>
<body>
<div class="topmenu" id="tophead">
  <div class="wrap">
    <div id="mobilemenu"></div>
    <div class="mask"></div>
    <!--<div class="logo"><a href="/"><img src="次元库" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" width="280" height="70"></a></div>-->
    <?php $data = block_navigate(array (
)); ?>
    <div class="menu">
      <ul id="nav">
        <li class="closex"><i class="iconfont icon-guanbi"></i></li>
        <li class="mainlevel"><a href="/"<?php if (empty($cfg_var['topcid'])) { ?> class="hover"<?php } ?>>首页</a></li>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
        <li class='mainlevel'><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"<?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="hover"<?php } ?>><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
        <div class="clear"></div>
      </ul>
    </div><?php unset($data); ?>
   <div class="search" style="margin-right: 80px;width: 150px;">
    <?php if ($_uid) { ?>
        <a href="/my-index.html" class="personal-center">个人中心</a>
    <?php }else{ ?>
        <a href="/user-login.html" class="login-register">登录/注册</a>
    <?php } ?>
</div>

<style>
    .search a {
        display: inline-block; 
        padding: 10px 20px; 
        border-radius: 5px; 
        text-decoration: none; 
        font-size: 16px; 
        font-weight: bold; 
        transition: background-color 0.3s, transform 0.3s; 
    }

    .personal-center {
        background-color: rgba(45, 191, 191, 0.8);
        color: white;
    }

    .login-register {
        background-color: #ff7f50;
        color: white;
    }

    .search a:hover {
        transform: scale(1.05);
    }

    .personal-center:hover {
        background-color: rgba(45, 191, 191, 1);
    }

    .login-register:hover {
      background-color: #ff6347; }
</style>
    <div class="search"><i class="iconfont icon-sousuo"></i></div>
  </div>
</div>
<p class="pheaderpad"></p>

<style>
	.error .clip .shadow {height:180px;}
	.error .clip:nth-of-type(2) .shadow {width:130px;}
	.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:250px;}
	.error .digit {width:150px;height:150px;line-height:150px;font-size:120px;font-weight:bold;}
	.error h2 {font-size:32px;}
	.error .msg {top:-190px;left:30%;width:80px;height:80px;line-height:80px;font-size:32px;}
	.error span.triangle {top:70%;right:0%;border-left:20px solid #535353;border-top:15px solid transparent;border-bottom:15px solid transparent;}
	.error .container-error-404 {top: 50%;margin-top: 250px;position:relative;height:250px;padding-top:40px;}
	.error .container-error-404 .clip {display:inline-block;transform:skew(-45deg);}
	.error .clip .shadow {overflow:hidden;}
	.error .clip:nth-of-type(2) .shadow {overflow:hidden;position:relative;box-shadow:inset 20px 0px 20px -15px rgba(150,150,150,0.8),20px 0px 20px -15px rgba(150,150,150,0.8);}
	.error .clip:nth-of-type(3) .shadow:after,.error .clip:nth-of-type(1) .shadow:after {content:"";position:absolute;right:-8px;bottom:0px;z-index:9999;height:100%;width:10px;background:linear-gradient(90deg,transparent,rgba(173,173,173,0.8),transparent);border-radius:50%;}
	.error .clip:nth-of-type(3) .shadow:after {left:-8px;}
	.error .digit {position:relative;top:8%;color:white;background:#1E9FFF;border-radius:50%;display:inline-block;transform:skew(45deg);}
	.error .clip:nth-of-type(2) .digit {left:-10%;}
	.error .clip:nth-of-type(1) .digit {right:-20%;}
	.error .clip:nth-of-type(3) .digit {left:-20%;}
	.error h2 {font-size:24px;color:#A2A2A2;font-weight:bold;padding-bottom:20px;}
	.error .tohome {font-size:16px;color:#07B3F9;}
	.error .msg {position:relative;z-index:9999;display:block;background:#535353;color:#A2A2A2;border-radius:50%;font-style:italic;}
	.error .triangle {position:absolute;z-index:999;transform:rotate(45deg);content:"";width:0;height:0;}
	@media(max-width:767px) {.error .clip .shadow {height:100px;}
		.error .clip:nth-of-type(2) .shadow {width:80px;}
		.error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:100px;}
		.error .digit {width:80px;height:80px;line-height:80px;font-size:52px;}
		.error h2 {font-size:18px;}
		.error .msg {top:-110px;left:15%;width:40px;height:40px;line-height:40px;font-size:18px;}
		.error span.triangle {top:70%;right:-3%;border-left:10px solid #535353;border-top:8px solid transparent;border-bottom:8px solid transparent;}
		.error .container-error-404 {height:150px;}
	}
</style>
<main class="main">
	<div class="error">
		<div class="container-floud">
			<div style="text-align: center">
				<div class="container-error-404">
					<div class="clip">
						<div class="shadow">
							<span class="digit thirdDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit secondDigit"></span>
						</div>
					</div>
					<div class="clip">
						<div class="shadow">
							<span class="digit firstDigit"></span>
						</div>
					</div>
					<div class="msg">OH!
						<span class="triangle"></span>
					</div>
				</div>
				<h2 class="h1">很抱歉，你访问的页面找不到了</h2>
			</div>
		</div>
	</div>
</main>

	<div>

		<div class="footer2">
		<p>COPYRIGHT © 2023 <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>. ALL RIGHTS RESERVED.  
		 	     <br/><a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow" <?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?>><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a></p>
		 	     <p>只给您分享有用的学习知识！</p>
		</div>


		<div class="thgotop">
			<ul>
				<li id="guan" class="ditop">
					<div class="yewan">
						<i class="fa fa-circle" aria-hidden="true"></i>
						<span class="">我要关灯</span>
					</div>

					<div class="baitian">
						<i class="fa fa-circle-o" aria-hidden="true"></i>
						<span class="">我要开灯</span>
					</div>
				</li>
				
				<li id="go_top" class="ditop">
					<i class="fa fa-arrow-up" aria-hidden="true"></i>
					<span>返回顶部</span>
				</li>
			</ul>
		</div>
		<?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?>
	</div>

<script type="text/javascript">
	function randomNum() {
		return Math.floor(Math.random() * 9) + 1;
	}

	var loop1, loop2, loop3, time = 30, i = 0, number;
	loop3 = setInterval(function () {
		if (i > 40) {
			clearInterval(loop3);
			document.querySelector('.thirdDigit').textContent = 4;
		} else {
			document.querySelector('.thirdDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop2 = setInterval(function () {
		if (i > 80) {
			clearInterval(loop2);
			document.querySelector('.secondDigit').textContent = 0;
		} else {
			document.querySelector('.secondDigit').textContent = randomNum();
			i++;
		}
	}, time);
	loop1 = setInterval(function () {
		if (i > 100) {
			clearInterval(loop1);
			document.querySelector('.firstDigit').textContent = 4;
		} else {
			document.querySelector('.firstDigit').textContent = randomNum();
			i++;
		}
	}, time);
</script>
</body>
</html>
