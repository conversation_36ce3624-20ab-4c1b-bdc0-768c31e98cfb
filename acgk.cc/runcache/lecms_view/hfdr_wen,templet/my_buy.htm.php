<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="renderer" content="webkit">
  <link rel="shortcut icon" type="image/x-icon" href= "<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>favicon.ico" />
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/frontend.min.css" media="all">
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/css/user.css" media="all">
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
  <!--[if lt IE 9]>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/html5shiv.js"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/respond.min.js"></script>
  <![endif]-->
  <script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/jquery.js" charset="utf-8"></script>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>user/js/bootstrap.min.js"></script>
  <style>
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand{margin-left: 0;}
    .navbar-brand{padding: 0;}
    .navbar-brand img{height: 45px;vertical-align: center;padding-top: 5px;}
  </style>
</head>
<body>
<nav class="hgacg-dc9e93 navbar navbar-white navbar-fixed-top" role="navigation">
  <div class="hgacg-e849bd container">
    <div class="hgacg-ecb9eb navbar-header">
      <button type="button" class="hgacg-808760 navbar-toggle" data-toggle="collapse" data-target="#header-navbar">
        <span class="hgacg-27673d sr-only"><?php echo '切换'; ?></span>
        <span class="hgacg-82f9ad icon-bar"></span>
        <span class="hgacg-82f9ad icon-bar"></span>
        <span class="hgacg-82f9ad icon-bar"></span>
      </button>
      <a class="hgacg-6af25c navbar-brand" href="<?php echo(isset($my_url) ? $my_url : ''); ?>" title="用户中心">
      </a>
    </div>
    <div class="hgacg-0ee60b collapse navbar-collapse" id="header-navbar">
      <ul class="hgacg-f2cbb6 nav navbar-nav navbar-right">
        <li><a href="/" title="/"><?php echo '首页'; ?></a></li>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
        <li class="hgacg-bf3200 mainlevel"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" <?php if ($cfg_var['topcid'] == $v['cid']) { ?> class="hover"<?php } ?>><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li><?php }} ?>
        <li class="hgacg-d44435 dropdown">
          <a href="<?php echo(isset($my_url) ? $my_url : ''); ?>" class="hgacg-0d48bd dropdown-toggle" data-toggle="dropdown">
            <span class="hgacg-13bf6f avatar-img"><img src="http://img.soogif.com/x3CvazRkvwy4qgYNt5K8bOzSI4tdRTUj.gif?v=1.0" alt="<?php echo(isset($_user['username']) ? $_user['username'] : ''); ?>"></span>
            <span class="hgacg-ccbaaa visible-xs-inline-block" style="padding:5px;"><?php echo(isset($_user['username']) ? $_user['username'] : ''); ?></span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</nav>
<style>
  .embed-responsive img {position: absolute;object-fit: cover;width: 100%;height: 100%;border: 0;}
  .comment-content {padding: 15px;background: #efefef;color: #444;}
  .panel-user h4 {font-weight: normal;font-size: 14px;}
  .float-right{float: right;}
  .pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span{border-bottom-right-radius:3px;border-top-right-radius:3px;}
  .pager li:first-child > a, .pager li:last-child > a, .pager li:first-child > span, .pager li:last-child > span{border-bottom-left-radius:3px;border-top-left-radius:3px;}
</style>
  <main class="hgacg-1bc3a4 content">
    <div id="content-container" class="hgacg-e849bd container">
      <div class="hgacg-a2c59a row">
        <style>
    #logout{cursor: pointer;}
  .sidebar-toggle {
            display: block;
            position: fixed;
            right: 20px;
            top: 70px;
            border-radius: 50%;
            background: #007bff; /* 更鲜艳的背景颜色 */
            color: white; /* 字体颜色 */
            font-size: 24px; /* 增大字体大小 */
            padding: 10px;
            line-height: 30px;
            height: 60px; /* 增加按钮高度 */
            width: 60px; /* 增加按钮宽度 */
            text-align: center;
            z-index: 999999;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.5); /* 添加阴影效果 */
            transition: background-color 0.3s, transform 0.3s; /* 添加过渡效果 */
        }
</style>
<div class="hgacg-9e1a63 col-md-3">
  <div class="hgacg-5b2d1e sidebar-toggle"><i class="hgacg-f9e8a2 fa fa-bars"></i></div>
  <div id="sidebar-nav" class="hgacg-ae932d sidenav">
    <?php if(isset($_navs) && is_array($_navs)) { foreach($_navs as $k=>&$v) { ?>
    <ul class="hgacg-4e2ce3 list-group">
      <?php if (!empty($v['href'])) { ?>
      <li id="<?php echo(isset($k) ? $k : ''); ?>" class="hgacg-2c0bb8 list-group-heading">
        <a href="<?php echo(isset($v['href']) ? $v['href'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a>
      </li>
      <?php }else{ ?>
      <li id="<?php echo(isset($k) ? $k : ''); ?>" class="hgacg-2c0bb8 list-group-heading"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></li>
      <?php } ?>
      <?php if(isset($v['child']) && is_array($v['child'])) { foreach($v['child'] as &$v2) { ?>
      <li id="<?php echo(isset($v2['id']) ? $v2['id'] : ''); ?>" class="hgacg-61f5d0 list-group-item">
        <?php if (!empty($v2['href'])) { ?>
        <a href="<?php echo(isset($v2['href']) ? $v2['href'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>"><?php if (!empty($v2['icon'])) { ?><i class="hgacg-8d0b79 <?php echo(isset($v2['icon']) ? $v2['icon'] : ''); ?>"></i> <?php } echo(isset($v2['title']) ? $v2['title'] : ''); ?></a>
        <?php }else{ ?>
        <?php if (!empty($v2['icon'])) { ?><i class="hgacg-8d0b79 <?php echo(isset($v2['icon']) ? $v2['icon'] : ''); ?>"></i> <?php } echo(isset($v2['title']) ? $v2['title'] : ''); ?>
        <?php } ?>
      </li>
      <?php }} ?>
    </ul>
    <?php }} ?>
  </div>
</div>
<script type="text/javascript">
  $(document).on("click", ".sidebar-toggle", function () {
    $("body").toggleClass("sidebar-open");
  });
</script>
        <!--右侧主体部分 start-->
        <div class="hgacg-a3c241 col-md-9">
          <div class="hgacg-e54ecf panel panel-default">
            <div class="hgacg-95a19a panel-body">
              <h2 class="hgacg-4ada80 page-header">我购买的内容</h2>
              <?php if (empty($data)) { ?>
              <div class="hgacg-aa19b5 alert alert-warning"><b><?php echo '暂无内容'; ?></b></div>
              <?php }else{ ?>
              <div class="hgacg-592763 table-responsive">
                <table class="hgacg-2e0a94 table table-bordered">
                <thead>
                  <tr>
                    <th>文章标题</th>
                    <th style="width:80px;text-align: center">消耗金币</th>
                    <th style="width:150px;text-align: center">购买时间</th>
                  </tr>
                </thead>
                <tbody>
                <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                  <tr>
                    <td><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></td>
                    <td style="width:80px;text-align: center"><?php echo(isset($v['fee']) ? $v['fee'] : ''); ?> </span></td>
                    <td><?php echo(isset($v['create_time']) ? $v['create_time'] : ''); ?></td>
                  </tr>
                <?php }} ?>
                </tbody>
              </table>
              </div>
              <?php } ?>
              <div class="hgacg-e2b34c pager"><?php echo(isset($pages) ? $pages : ''); ?></div>
            </div>
          </div>
        </div>
        <!--右侧主体部分 end-->
      </div>
    </div>
  </main>
<footer class="hgacg-bb0985 footer" style="clear:both">
  <p class="hgacg-3f0d57 copyright">Copyright&nbsp;©&nbsp;<?php echo date('Y'); ?> <?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?> All Rights Reserved.</p>
</footer>
<script src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var layer = layui.layer, $ = layui.$;
    $("#logout").click(function () {
      layer.confirm('确定退出登录？', function () {
        $.post("index.php?my-logout-ajax-1",{do: 1},function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location = "/"; }, 1000);
          return false;
        },'json');
      });
    });
  });
</script>
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    $("#my-buy").addClass("active");

  });
</script>
</body>
</html>