<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_global_show($conf) { global $run, $_show, $_user; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $show_prev_next = isset($conf['show_prev_next']) && (int)$conf['show_prev_next'] ? true : false; $prev_next_cid = isset($conf['cid']) ? (int)$conf['cid'] : intval($_GET['cid']); $field_format = _int($conf, 'field_format', 0); $pageoffset = _int($conf, 'pageoffset', 5); $page_function = empty($conf['page_function']) ? 'pages' : $conf['page_function']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_global_show'); $cache_key = $life ? md5('global_show'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; if($mid == 1) return FALSE; $uid = isset($_user['uid']) ? (int)$_user['uid'] : 0; $run->cms_content_data->table = 'cms_'.$run->_var['table'].'_data'; $run->cms_content->format($_show, $mid, $dateformat, 0, 0, $field_format); $id = &$_show['id']; $_show['comment_url'] = $run->cms_content->comment_url($run->_var['cid'], $id); $_show['views_url'] = $run->_cfg['webdir'].'index.php?views--cid-'.$run->_var['cid'].'-id-'.$id; $data = $run->cms_content_data->get($id); if($data){ if($field_format && plugin_is_enable('models_filed')){ $models_field = $run->models_field->user_defined_field($mid); $run->models_field->field_val_format($models_field, $data, 0); } $_show += $data; $page = max(1,(int)R('page','G')); $_show = $run->cms_content_data->format_content($_show, $page); if( isset($_show['content_page']) && isset($_show['maxpage']) ){ $_show['pages'] = paginator::$page_function($page, $_show['maxpage'], $run->cms_content->content_url($_show, $mid, TRUE), $pageoffset); }else{ $_show['pages'] = false; } }else{ $_show['pages'] = false; } $run->cms_content_views->table = 'cms_'.$run->_var['table'].'_views'; $views_data = $run->cms_content_views->get($id); if($views_data){ if( empty($run->_cfg['close_views']) ){ $_show['views'] = $views_data['views']+1; $run->cms_content_views->update_views($id); }else{ $_show['views'] = $views_data['views']; } }else{ $_show['views'] = 1; empty($run->_cfg['close_views']) && $run->cms_content_views->set($id, array('views'=>1,'cid'=>$_show['cid'])); } if(isset($_show['filenum']) && !empty($_show['filenum'])){ list($attachlist, $imagelist, $filelist) = $run->cms_content_attach->attach_find_by_id($run->_var['table'], $id, array('id'=>$id, 'isimage'=>0)); $_show['filelist'] = $filelist; if($_show['uid'] == $uid && $uid){ $file_delete = true; }else{ $file_delete = false; } $_show['filelist_html'] = $run->cms_content_attach->file_list_html($filelist, $mid, $file_delete); }else{ $_show['filelist'] = array(); $_show['filelist_html'] = ''; } if( isset($run->_cfg['le_content_img_seo']) && !empty($run->_cfg['le_content_img_seo']) ){ $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/"; preg_match_all($pattern, $_show['content'], $match); $find_arr = array('{title}', '{cate_name}', '{webname}', '{count}'); if( isset($match[0]) ){ $img_count = 1; foreach ($match[0] as $k=>$img){ $replace_arr = array("{$_show['title']}", "{$run->_var['name']}", "{$run->_cfg['webname']}", $img_count); $new_alt = str_replace($find_arr, $replace_arr, $run->_cfg['le_content_img_seo']); if( stripos($img, "alt=") != false ){ $img_new = preg_replace('/alt=["\'](.*?)["\']/', 'alt="'.$new_alt.'"', $img); }else{ $img_new = str_replace_once('<img', '<img alt="'.$new_alt.'" ', $img); } if( stripos($img_new, "title=") != false ){ $img_new = preg_replace('/title=["\'](.*?)["\']/', '', $img_new); } if( strpos($img_new, $run->_cfg['webdomain']) === false ){ $img_new = str_replace_once('<img', '<img rel="external nofollow" referrerpolicy="no-referrer" ', $img_new); } $_show['content'] = str_replace_once($img, $img_new, $_show['content']); $img_count++; } unset($match[0]); } unset($find_arr); } if($show_prev_next) { if($prev_next_cid){ $prev_where = array('cid'=>$prev_next_cid, 'id'=>array('<'=> $id)); $next_where = array('cid'=>$prev_next_cid, 'id'=>array('>'=> $id)); }else{ $prev_where = array('id'=>array('<'=> $id)); $next_where = array('id'=>array('>'=> $id)); } $_show['prev'] = $run->cms_content->list_arr($prev_where, 'id', -1, 0, 1, 1, $extra); if($_show['prev']){ $_show['prev'] = current($_show['prev']); $run->cms_content->format($_show['prev'], $mid, $dateformat); } $_show['next'] = $run->cms_content->list_arr($next_where, 'id', 1, 0, 1, 1, $extra); if($_show['next']){ $_show['next'] = current($_show['next']); $run->cms_content->format($_show['next'], $mid, $dateformat); } }else{ $_show['prev'] = $_show['next'] = array(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $_show, $life); } if( isset($_show['favorites']) ){ $uid = isset($run->_user['uid']) ? (int)$run->_user['uid'] : 0; if( empty($uid) ){ $_show['has_favorites'] = 0; }else{ if( $run->favorites->find_fetch_key(array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id)) ){ $_show['has_favorites'] = 1; }else{ $_show['has_favorites'] = 0; } } $_show['favorites_url'] = $run->_cfg['webdir'].'index.php?show-favorites-mid-'.$mid.'-id-'.$id.'-uid-'.$uid.'.html'; }$le_keywords_links_setting = $run->kv->xget('le_keywords_links_setting'); if($le_keywords_links_setting){ $class = $le_keywords_links_setting['class'] == '' ? '' : ' class="'.$le_keywords_links_setting['class'].'"'; $target = $le_keywords_links_setting['target'] == 0 ? '' : ' target="_blank"'; $style = $class.$target; $keywords_links_arr = $run->keywords_links->find_fetch(array(), array('orderby'=>1, 'id' => 1)); if( $keywords_links_arr ){ $contentstr = $_show['content']; foreach ($keywords_links_arr as $keywords){ $patterns = '#(?=[^>]*(?=<(?!/a>)|$))'.$keywords['keyword'].'#'; $replacements = '<a href="'.$keywords['url'].'" '.$style.' title="'.$keywords['keyword'].'">'.$keywords['keyword'].'</a>'; $contentstr = preg_replace($patterns, $replacements, $contentstr, $keywords['count']); } $_show['content'] = $contentstr; unset($keywords_links_arr); } } return $_show; }
$gdata = block_global_show(array (
  'show_prev_next' => '1',
  'dateformat' => 'm-d',
  'field_format' => '1',
));
 function block_taglike($conf) { global $run, $_show; if(!isset($_show['tags']) || empty($_show['tags'])) return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); $titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0; $intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0; $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $type = isset($conf['type']) ? (int)$conf['type'] : 1; $type = $type <= -1 ? -1 : (int)$type; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('taglike'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $mid = &$run->_var['mid']; $table = &$run->_var['table']; if($type == -1){ $end = array_slice($_show['tags'],-1, 1, true); $tagid = key( $end ); }elseif ($type == 0){ $tagid = array_rand($_show['tags']); }else{ $tagid = key( array_slice($_show['tags'], $type-1, 1, true) ); if( empty($tagid) ){ return array('tag_name'=>'', 'tag_url'=>'', 'list'=> array()); } } $tag_name = $_show['tags'][$tagid]; $tag_url = $run->cms_content->tag_url($mid, array('tagid'=>$tagid, 'name'=>$tag_name)); if(isset($conf['mid']) && $conf['mid'] > 1 && $mid != $conf['mid'] && isset($run->_cfg['table_arr'][$conf['mid']])){ $mid = $conf['mid']; $table = $run->_cfg['table_arr'][$conf['mid']]; $run->cms_content_tag->table = 'cms_'.$table.'_tag'; $tags = $run->cms_content_tag->find_fetch(array('name'=>$tag_name), array(), 0, 1); if(empty($tags)){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }else{ $tags = current($tags); $tagid = $tags['tagid']; $tag_url = $run->cms_content->tag_url($mid, $tags); } } $run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data'; $tag_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$tagid), array('id'=>$orderway), $start, $limit+1); $keys = array(); foreach($tag_arr as $lv) { if($lv['id'] != $_show['id']){ $keys[] = $lv['id']; } } if( empty($keys) ){ return array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> array()); }elseif (count($keys) > $limit){ $keys = array_slice($keys, 0, $limit); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('tag_name'=>$tag_name, 'tag_url'=>$tag_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list_top($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('lastdate', 'comments', 'views')) ? $conf['orderby'] : 'lastdate'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $newlimit = _int($conf, 'newlimit', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $field_format = _int($conf, 'field_format', 0); $cache_key = $life ? md5('list_top'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($cid == 0) { $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($orderby == 'views') { $run->cms_content_views->table = $table_key = 'cms_'.$table.'_views'; $table_key .= '-id-'; $views_key = 'cms_'.$table.'_views-id-'; $keys = array(); if($newlimit){ $tablefull = $_ENV['_config']['db']['master']['tablepre'].$run->cms_content_views->table; $sql = "SELECT * FROM (SELECT * FROM {$tablefull} ORDER BY id DESC LIMIT {$newlimit}) AS sub_query ORDER BY views DESC LIMIT {$start},{$limit};"; $list_arr = $run->db->fetch_all($sql); $key_arr = array(); foreach ($list_arr as $v){ $keys[] = $v['id']; $key_arr[$views_key.$v['id']] = $v; } unset($list_arr); }else{ $key_arr = $run->cms_content_views->find_fetch($where, array($orderby => $orderway), $start, $limit, $life); foreach($key_arr as $lk=>$lv) { $keys[] = isset($lv['id']) ? $lv['id'] : (int)str_replace($views_key,'',$lk); } } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); isset($v['id']) && $v['views'] = isset($key_arr[$table_key.$v['id']]['views']) ? (int)$key_arr[$table_key.$v['id']]['views'] : 0; if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'comments'){ $list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } }elseif ($orderby == 'lastdate'){ if($cid){ if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } }else{ $where = array('mid' => $mid); } $key_arr = $run->cms_content_comment_sort->find_fetch($where, array($orderby => $orderway), $start, $limit); $keys = array(); foreach($key_arr as $lv) { $keys[] = $lv['id']; } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->mget($keys); $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } $v['xuhao'] = $xuhao; $xuhao++; } } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<html lang="zh-CN">
 <head> 
  <meta charset="UTF-8" /> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
  <meta name="viewport" content="width=device-width, initial-scale=1" /> 
  <link href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>favicon.ico" rel="icon" /> 
  <title><?php echo(isset($cfg['titles']) ? $cfg['titles'] : ''); ?></title>
   
   
  <?php $control = isset($_GET['control'])?strtolower($_GET['control']):'';$action = isset($_GET['action'])?strtolower($_GET['action']):''; ?>
  <?php if ($control=='show' && $action == 'index') { ?>
  <meta property="og:type" content="acticle" />
  <meta property="og:image" content="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>" />
  <meta property="og:author" content="<?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?>" />
  <meta property="og:site_name" content="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" />
  <meta property="og:title" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>" />
  <meta property="og:keywords" content="<?php echo(isset($cfg['seo_keywords']) ? $cfg['seo_keywords'] : ''); ?>" />
  <meta property="og:description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>" />
  <meta property="og:url" content="<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>" />
  <meta property="og:release_date" content="<?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?>" />	
  <?php } ?>  
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/sweetalert2.min.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>font-awesome/css/font-awesome.min.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/external.css" media="all" /> 
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/default.css" media="all" /> 
  <?php if ($control=='show' && $action == 'index') { ?>
  <link rel="stylesheet" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>css/jquery.fancybox.min.css" type="text/css" media="all" />
  <?php } ?>
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery-2.2.4.min.js"></script> 
  <script type="text/javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/sweetalert2.all.min.js"></script> 
  <?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/readmore.min.js"></script>
  <?php } ?>
  <script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/login.js"></script>
  <style>.nice-color,a:hover{color:#4a86e8}.site-header,.home-filter--content{background-color:#4a86e8}.button,input[type="submit"],button[type="submit"],.navigation .nav-previous a,.navigation .nav-next a{background-color:#4a86e8}.owl .owl-prev,.owl .owl-next,.term-bar{background-color:#4a86e8}.on{color:#4a86e8}.filter--content .filter-item a.on i{background:#4a86e895}.off-canvas .logo{background:#fff}<?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>.entry-media .placeholder{padding-bottom:70%!important;}<?php } ?>.navbar li ul{min-width:190px;}.lanse{text-indent:0em !important;font-size:16px;font-weight:normal;color:#FFF;margin:10px 0;padding:5px 10px;background-color:#ed2d38;display:none;}.lanseseo{text-indent:0em !important;font-size:16px;font-weight:normal;color:#FFF;margin:10px 0;padding:5px 10px;background-color:#ed2d38;display:inline-block;}.article-content .post-album li img{width:auto!important;height:auto!important;}.navbar li ul{min-width:100% !important;}.u-text-format h2{font-size:20px!important;border-left:5px solid #4a86e8!important;padding-left:15px!important;font-weight:bold!important;}p{margin:0 0 30px;}</style> 
 </head>
 <?php if ($control=='index' && $action == 'index') { ?>
 <body class="gtlol-525381 index home blog modular-title-2 paged-next"> 
 <?php }elseif($control=='cate' && $action == 'index') { ?>
 <body class="gtlol-dd0427 category paged-next ">
 <?php }elseif($control=='show' && $action == 'index') { ?>
 <body class="gtlol-dc6fdc article single single-post sidebar-right ">
 <?php }elseif($control=='tag' && $action == 'index') { ?>
 <body class="gtlol-cab977 tag paged-next ">
 <?php }elseif($control=='search' && $action == 'index') { ?>
 <body class="gtlol-7efff4 search archive searchplus paged-next "> 
 <?php }else{ ?>
 <body class="gtlol-2b7b9b page page sidebar-right " style="transform: none;">
 <?php } ?>
  <div class="gtlol-70d612 site"> 
    <header class="gtlol-2d944f site-header">
    <div class="gtlol-e849bd container"> 
     <div class="gtlol-d4dab2 navbar"> 
      <div class="gtlol-cfa30a logo-wrapper"> 
       <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" title="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"> <img class="logo regular tap-logo"  alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> </a> 
      </div> 
      <div class="gtlol-ee9753 sep"></div> 
      <nav class="gtlol-cb627e main-menu hidden-xs hidden-sm hidden-md"> 
       <ul id="menu-menu-1" class="gtlol-223b2f nav-list u-plain-list"> 
        <nav class="gtlol-cb627e main-menu hidden-xs hidden-sm hidden-md"> 
         <ul id="menu-menu-1" class="gtlol-223b2f nav-list u-plain-list"> 
		  <?php $data = block_navigate(array (
)); ?> 
          <li> <a href="/">首页</a></li> 
          <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> 
          <li class="gtlol-f56ab3 <?php if (isset($v['son'])) { ?>menu-item-has-children<?php } ?> <?php if ($cfg_var['topcid'] == $v['cid']) { ?> current-menu-item<?php } ?>"> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 
		   <?php if (isset($v['son'])) { ?>
           <ul> 
            <?php if(isset($v['son']) && is_array($v['son'])) { foreach($v['son'] as &$v2) { ?><li> <a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="<?php echo(isset($v2['target']) ? $v2['target'] : ''); ?>" title="<?php echo(isset($v2['name']) ? $v2['name'] : ''); ?>"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a></li><?php }} ?>
           </ul> 
		   <?php } ?> 
		  </li> 
		  <?php }} ?>
          <?php unset($data); ?>
         </ul>
        </nav> 
       </ul> 
      </nav> 
      <div class="gtlol-3913c5 actions">  
       <div class="gtlol-d4764e login-btn navbar-button main_nav"><!--main_nav弹窗登录注册-->
        <?php if (empty($_uid)) { ?><a href="/user-login.html" title="会员登录"><i class="gtlol-3ad843 mdi mdi-account"></i> 登录/注册</a><?php }else{ ?><a href="/my-index.html" title="用户中心"><i class="gtlol-3ad843 mdi mdi-account"></i> 用户中心</a><?php } ?>
       </div> 
	    <!--点击弹窗--><div class="gtlol-277bd8 note-open navbar-button"><i class="gtlol-c26a81 fa fa-bell-o"></i></div>
       <div class="gtlol-0f7766 burger"></div> 
      </div> 
     </div> 
    </div> 
   </header>
  
   <div class="gtlol-9777aa header-gap"></div> 
   <div class="gtlol-ebdf6d site-content"> 
    <section class="gtlol-68b87a article-focusbox bgimg-fixed lazyload" data-bg="<?php echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>"> 
     <div class="gtlol-e849bd container"> 
      <header class="gtlol-174f3f article-header"> 
       <h1 class="gtlol-a066fc article-title"><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h1> 
       <div class="gtlol-71b852 article-meta"> 
        <span class="gtlol-1f54da item"><?php echo(isset($gdata['date']) ? $gdata['date'] : ''); ?></span> 
        <span class="gtlol-1f54da item">分类：<a href="<?php echo(isset($cfg_var['url']) ? $cfg_var['url'] : ''); ?>"><?php echo(isset($cfg_var['name']) ? $cfg_var['name'] : ''); ?></a></span> 
        <span class="gtlol-1f54da item">热度：<?php echo(isset($gdata['views']) ? $gdata['views'] : ''); ?></span> 
        <span class="gtlol-1f54da item">评论：<i class="gtlol-65a08e fa fa-comments-o"></i> <?php echo(isset($gdata['comments']) ? $gdata['comments'] : ''); ?></span> 
       </div> 
      </header> 
     </div> 
    </section> 
    <div class="gtlol-e849bd container"> 
     <div class="gtlol-a2c59a row"> 
      <div class="gtlol-bc8763 content-column col-lg-9"> 
       <div class="gtlol-2748c8 content-area"> 
        <main class="gtlol-6c82a9 site-main"> 
         <article id="post-39175" class="gtlol-698d8a article-content"> 
          <div class="gtlol-954541 breadcrumbs">
            <style>
          img{width:100%;height:auto;display:block;clip-path:inset(0 0 25% 0)}.notice{background:linear-gradient(135deg,#007bff,#00c6ff);color:white;border-radius:10px;padding:20px;text-align:center;margin:20px 0;box-shadow:0 4px 15px rgba(0,123,255,0.5);transition:transform 0.3s}.notice a{color:white;text-decoration:none;font-size:18px;font-weight:bold;display:inline-block;padding:10px 20px;border-radius:5px;background-color:rgba(255,255,255,0.2);transition:background-color 0.3s}.notice a:hover{background-color:rgba(255,255,255,0.4);text-decoration:none}.notice:hover{transform:scale(1.02).message img{max-width:100%}}
          </style>
           当前位置：
           <a title="首页" href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>">首页</a>  
           <?php if(isset($cfg_var['place']) && is_array($cfg_var['place'])) { foreach($cfg_var['place'] as &$v) { ?><i class="gtlol-58b5c5 fa fa-angle-right"></i> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?>
           <i class="gtlol-58b5c5 fa fa-angle-right"></i> <?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>
          </div> 
          <div class="gtlol-e849bd container"> 
           <div class="gtlol-8054f6 entry-wrapper"> 
            <div class="gtlol-a8832d entry-content u-text-format u-clearfix">
            <div class="gtlol-8c61d1 article-copyright"></div>                 
			 <?php if ($_uid && $gdata['usersee']) { ?>
			 <div class="gtlol-32139b usersee-tips">
			     <div class="gtlol-a11b19 lockico"></div>
			     <p><?php echo(isset($gdata['usersee']) ? $gdata['usersee'] : ''); ?></p>
			 </div>
			 <?php }elseif($gdata['usersee']) { ?>
			 <div class="gtlol-32139b usersee-tips">
			     <div class="gtlol-a11b19 lockico"></div>
			     <p>隐藏内容会员可见，请先登录再来查看!</p>
			     <div id="usersee-login" onclick="window.open('<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); ?>/user-login.html')">会员登录</div>
			 </div>	
			 <?php }else{ ?>
			 <?php } ?>                 
			 <div class="gtlol-05c730 show-content-box">
              <?php if ($_uid) { ?>
               <div class="notice" style="
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    border-radius: 12px;
    padding: 18px;
    margin: 25px 0;
    box-shadow: 0 4px 15px rgba(255, 77, 77, 0.3);
    border: 2px solid #fff;
    position: relative;
    overflow: hidden;
    animation: alertPulse 2s infinite;
">
    <a href="https://www.777723.xyz/" 
       target="_blank" 
       rel="noopener noreferrer"
       style="
           color: #fff !important;
           font-size: 1.2em;
           font-weight: 700;
           text-decoration: none;
           display: block;
           text-align: center;
           letter-spacing: 1px;
           position: relative;
           z-index: 2;
           transition: all 0.3s;
       ">
        ⚠️ 重要通知！点我进入主站，本站体验很差<br>
        <small style="
            display: block;
            font-size: 0.8em;
            margin-top: 8px;
            opacity: 0.9;
            font-weight: 400;
        ">
            账号数据自动同步 · 无需重新注册 · 立即跳转
        </small>
    </a>
    <!-- 装饰性波纹 -->
    <div style="
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.1) 0,
            rgba(255,255,255,0.1) 5px,
            transparent 5px,
            transparent 10px
        );
        animation: shine 3s infinite;
        z-index: 1;
    "></div>
</div>

<style>
@keyframes alertPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}
@keyframes shine {
    from { transform: translateX(-50%) rotate(45deg); }
    to { transform: translateX(50%) rotate(45deg); }
}
.notice:hover {
    animation: none;
    transform: scale(1.02) rotate(-0.5deg);
}
@media (max-width: 768px) {
    .notice {
        padding: 12px;
        margin: 15px 0;
    }
    .notice a {
        font-size: 1em;
    }
}
</style>
                  <?php echo(isset($gdata['content']) ? $gdata['content'] : ''); ?>
                  <br /><br /><p>
<?php if (isset($gdata['favorites'])) { ?>
<style>
        .content_favorites {
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background-color: #f9f9f9; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
        }

        #favorites_do {
            margin-right: 5px; 
            color: #ffcc00;
        }

        #favorites_html {
            font-weight: bold; 
        }

        #favorites_count {
            color: #888;
        }
</style>
<div class="gtlol-ead73c content_favorites">
    <?php if ($gdata['has_favorites']) { ?>
    <i id="favorites_do" class="gtlol-044cd4 fa fa-star" aria-hidden="true"><font id="favorites_html">取消收藏</font></i> 
    <?php }else{ ?>
    <i id="favorites_do" class="gtlol-1980eb fa fa-star-o" aria-hidden="true"><font id="favorites_html">加入收藏</font></i>
    <?php } ?>
</div>
<script type="text/javascript">
    $("#favorites_do").click(function () {
        $.getJSON("<?php echo(isset($gdata['favorites_url']) ? $gdata['favorites_url'] : ''); ?>", function(data){
            if(data.err){
                alert(data.msg);
            }else{
                if( data.has_favorites == 1 ){
                    $("#favorites_do").attr("class", "fa fa-star");
                    $("#favorites_html").html("取消收藏");
                }else{
                    $("#favorites_do").attr("class", "fa fa-star-o");
                    $("#favorites_html").html("加入收藏");
                }
                $("#favorites_count").html(data.favorites_count);
                alert(data.msg);
            }
        });
    });
</script>
<?php } ?></p><br /><br /><br />

                  <?php }else{ ?>请先 <a href="/user-login.html">登录</a> 或者 <a href="/user-register.html">注册</a> 后在进行查看哦！<?php } ?>
              </div>
            </div> 
            <div id="pay-single-box"></div> 
            <div class="gtlol-a3edc0 entry-tags"> 
			 <?php if (isset($gdata['tag_arr'])) { if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?>
             <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" rel="tag"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 			 
             <?php }} } ?>
            </div> 
            <div class="gtlol-8c61d1 article-copyright">
             <span><a title="首页" href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>   &raquo; <?php if(isset($cfg_var['place']) && is_array($cfg_var['place'])) { foreach($cfg_var['place'] as &$v) { ?><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?></span> 
            </div> 
            <div class="gtlol-64192e article-footer"> 
             <div class="gtlol-ac4d19 author-box"> 
              <div class="gtlol-14af8f author-image"> 
               <img alt="<?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?>" data-src="<?php echo(isset($gdata['avatar']) ? $gdata['avatar'] : ''); ?>" class="lazyload avatar" src="<?php echo(isset($gdata['avatar']) ? $gdata['avatar'] : ''); ?>" height="140" width="140" /> 
              </div> 
              <div class="gtlol-ee2a7f author-info"> 
               <h4 class="gtlol-4a05b4 author-name"><a href="<?php echo(isset($gdata['user_url']) ? $gdata['user_url'] : ''); ?>" target="_blank" title="<?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?>"><?php echo(isset($gdata['author']) ? $gdata['author'] : ''); ?></a></h4> 
              </div> 
             </div> 
             <div class="gtlol-754eb1 xshare"> 
              <span class="gtlol-c71166 xshare-title">分享到：</span> 
              <a href="http://connect.qq.com/widget/shareqq/index.html?url=<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>&amp;title=<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>" target="_blank" class="gtlol-4f375e share-qq"><i class="gtlol-c8185c fa fa-qq"></i></a> 
              <a href="http://service.weibo.com/share/share.php?url=<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>&amp;title=<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>&amp;appkey=&amp;pic=&amp;searchPic=true" target="_blank" class="gtlol-69fe87 share-weibo"><i class="gtlol-3993bf fa fa-weibo"></i></a> 
             </div> 
            </div> 
           </div> 
          </div> 
         </article>
         <?php if (isset($gdata['prev']['url']) || isset($gdata['next']['url'])) { ?> 		 
         <div class="gtlol-5bb67a entry-navigation"> 
          <nav class="gtlol-41fc03 article-nav"> 
           <span class="gtlol-206fcb article-nav-prev">上一篇<br /><?php if (isset($gdata['prev']['url'])) { ?><a href="<?php echo(isset($gdata['prev']['url']) ? $gdata['prev']['url'] : ''); ?>" rel="prev"><?php echo(isset($gdata['prev']['title']) ? $gdata['prev']['title'] : ''); ?></a><?php }else{ ?>没有了<?php } ?></span> 
           <span class="gtlol-292640 article-nav-next">下一篇<br /><?php if (isset($gdata['next']['url'])) { ?><a href="<?php echo(isset($gdata['next']['url']) ? $gdata['next']['url'] : ''); ?>" rel="next"> <?php echo(isset($gdata['next']['title']) ? $gdata['next']['title'] : ''); ?></a><?php }else{ ?>没有了<?php } ?></span> 
          </nav> 
         </div> 
		 <?php } ?> 
         
         <?php $data = block_taglike(array (
  'type' => '1',
  'limit' => '8',
  'dateformat' => 'm-d',
)); ?>  
         <div class="gtlol-e9894a related-posts"> 
          <h3>相关推荐</h3> 
          <div class="gtlol-a2c59a row"> 
		   <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?> 
           <div class="gtlol-6a6b5d col-lg-6"> 
            <article class="gtlol-1a4e1b post"> 
             <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li> 
            </article> 
           </div> 
		   <?php }} ?> 
          </div> 
         </div> 
		 <?php unset($data); ?>
		 <?php if ($_uid) { ?>
		 

		 <?php }else{ ?>		 
         <div class="gtlol-4d804d bottom-area"> 
          <div id="comments" class="gtlol-0a1487 comments-area"> 
           <ol class="gtlol-5d3761 comment-list"> 
            <label id="AjaxCommentEnd"></label> 
            <label id="AjaxCommentBegin"></label> 
           </ol> 
           <div id="respond" class="gtlol-3abdb8 comment-respond"> 
            <h3 id="reply-title" class="gtlol-38f716 comment-reply-title">发表评论</h3> 
             <p class="gtlol-0c3fcf nice-tips">您需要<a href="/user-login.html" target="_blank" rel="nofollow" class="gtlol-b15c15 login-btn nice-color"><u>登录</u></a>后才能发表评论</p> 
           </div> 
          </div> 
         </div> 
		 <?php } ?>
        </main> 
       </div> 
      </div> 
	         <div class="gtlol-97a1ce sidebar-column col-lg-3"> 
       <aside class="gtlol-37ce8a widget-area"> 	 
        <!--猜你喜欢--> 
        <div id="relatedpost" class="gtlol-9e7e0d widget cao-widget-posts"> 
         <h5 class="gtlol-974808 widget-title">猜你喜欢</h5> 
         <div class="gtlol-94e4be posts"> 
		 <?php $data = block_list_rand(array (
  'limit' => '6',
  'titlenum' => '35',
)); ?> 
	     <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
          <div class="gtlol-d2576c left"> 
           <div class="gtlol-65f3f7 entry-media"> 
            <div class="gtlol-62e057 placeholder" style="padding-bottom: 66.666666666667%;"> 
             <a target="_blank" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <img class="lazyload" data-src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" /> </a> 
            </div> 
           </div> 
           <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" rel="bookmark"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a> 
          </div> 
         <?php }} ?> 
	     <?php unset($data); ?>		  
         </div> 
        </div> 
		<!--广告招租--> 
		<div id="adforsale" class="gtlol-9e7e0d widget cao-widget-posts"> 
		<img class="lazyload" data-src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/adforsale.png" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="广告招租" width="100%"/> 
		</div> 
        <!--热门文章--> 
        <div id="hotposts" class="gtlol-9e7e0d widget cao-widget-posts"> 
         <h5 class="gtlol-974808 widget-title">热门文章</h5> 
         <div class="gtlol-94e4be posts"> 
		 <?php $data = block_list_top(array (
  'mid' => '2',
  'cid' => '0',
  'orderby' => 'views',
  'limit' => '6',
  'titlenum' => '35',
)); ?>
	      <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
          <div class="gtlol-d2576c left"> 
           <div class="gtlol-65f3f7 entry-media"> 
            <div class="gtlol-62e057 placeholder" style="padding-bottom: 66.666666666667%;"> 
             <a target="_blank" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"> <img class="lazyload" data-src="<?php echo(isset($v['pic']) ? $v['pic'] : ''); ?>" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" /> </a> 
            </div> 
           </div> 
           <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" rel="bookmark"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a> 
          </div> 
         <?php }} ?> 
	     <?php unset($data); ?>
         </div> 
        </div> 
       </aside> 
      </div>  
     </div> 
    </div> 
   </div> 
   <!--登录留言 开始-->   
   <div class="gtlol-da3271 cd-user-modal"> 
    <div class="gtlol-5309de cd-user-modal-container"> 
    <ul class="gtlol-db0ea6 cd-switcher"> 
	 <li><a href="#login">会员登录</a></li> 
     <li><a href="#message">在线留言</a></li> 
    </ul> 
    <!-- 登录表单 --> 
    <div id="cd-login"> 
	<?php if (empty($_uid)) { ?>
    <form  class="gtlol-b892cc cd-form" id="login-form" action="<?php echo(isset($login_url) ? $login_url : ''); ?>" method="post">
    <input type="hidden" name="FORM_HASH" value="<?php echo(isset($form_hash) ? $form_hash : ''); ?>" />
    <p class="gtlol-5c46da fieldset"><input class="gtlol-541b1b full-width has-padding has-border" id="username" type="text" name="username" value="" placeholder="* 请输入 用户名" autocomplete="off"></p>
    <p class="gtlol-5c46da fieldset"><input class="gtlol-d0854f full-width has-padding has-border" id="password" type="password" name="password" value="" placeholder="* 请输入 密码" autocomplete="off"></p>
    <?php if ($cfg['open_user_login_vcode']) { ?>
    <p class="gtlol-5c46da fieldset"><input class="gtlol-5e4755 full-width3 has-padding has-border" id="vcode" type="text" name="vcode" value="" placeholder="* 请输入 验证码" autocomplete="off">&nbsp;&nbsp;<img src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?user-vcode-name-loginvcode" alt="验证码" onclick="this.src='index.php?user-vcode-loginvcode-r-'+Math.random();" id="vcodeimg" style="width: 40%;" />
    </p>
    <?php } ?>
    <p class="gtlol-5c46da fieldset"><button type="submit" class="gtlol-20ec98 full-width2"  lay-submit lay-filter="form">登 录</button>
      <?php if ($cfg['open_user_register']) { ?><a href="/user-register.html" target="_blank" title="用户注册" class="gtlol-e62455 btn btn-default btn-lg btn-block mt-3 no-border">还没有账号？点击注册</a><?php } ?>
	  <?php if ($cfg['open_user_reset_password']) { ?><a href="/user-forget.html" target="_blank" class="gtlol-e62455 btn btn-default btn-lg btn-block mt-3 no-border">忘记密码?</a><?php } ?>
    </p>
    

    </form>
    <?php }else{ ?>
     <div class="gtlol-29ff95 cd-form"> 
      <p class="gtlol-5c46da fieldset">即将进入会员中心，请稍等......</p> 
     </div>
	<?php } ?>
    </div>  	
     <!-- 留言提交 --> 
    <div id="cd-message"> 
     <form class="gtlol-cd1410 cd-form" id="ctf_message_form" action="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-domessage-ajax-1.html" method="post"> 
      <p class="gtlol-5c46da fieldset"><input type="text" class="full-width has-padding has-border" name="message[title]" value="" placeholder="* 请输入 留言标题" required="required" /></p> 
      <p class="gtlol-5c46da fieldset"><input type="text" class="full-width has-padding has-border" name="message[author]" value="" placeholder="* 请输入 您的称呼" required="required" /></p> 
      <p class="gtlol-5c46da fieldset"><input type="text" class="full-width has-padding has-border" name="message[contact]" value="" placeholder="* 请输入 联系电话" required="required" /></p> 
      <p class="gtlol-5c46da fieldset"><textarea type="text"  class="gtlol-15d79f full-width has-padding has-border" name="message[content]" placeholder="* 请输入 留言内容，" required="required"></textarea></p> 
      <p class="gtlol-5c46da fieldset"><input class="full-width3 has-padding has-border" name="message[vcode]" placeholder="* 请输入 验证码" type="text" required="required" />&nbsp;&nbsp;<img id="captchaPic" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode-name-message" onclick="this.src='<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>index.php?comment-vcode-name-message-r-'+Math.random();" alt="验证码" style="width: 40%;" /></p> 
      <p class="gtlol-5c46da fieldset"><input class="full-width2" type="submit" id="ctf_submit" tabindex="6" value="提交" /></p> 
      <p id="ctf_message_tips"></p> 
     </form> 
    </div> 
    <a href="#0" class="gtlol-b7f3dd cd-close-form">关闭</a> 
    </div> 
  </div>
  <!-- 留言提交 --> 
  <script type="text/javascript" src="<?php echo(isset($cfg['webdir']) ? $cfg['webdir'] : ''); ?>static/js/pxmu.min.js"></script>
  <script>
    // 留言提交
    window.ctf_message_form_one = false;
    $("#ctf_message_form").submit(function() {
        if (window.ctf_message_form_one) return false;
        window.ctf_message_form_one = true;
        var content = $("textarea[name='message[content]']").val();
        var title = $("input[name='message[title]']").val();
        var contact = $("input[name='message[contact]']").val();
        var author = $("input[name='message[author]']").val();
        var vcode = $("input[name='message[vcode]']").val();
        setTimeout(function(){
            window.ctf_message_form_one = false;
        }, 2000);
        if( content == '' || title == '' || contact == '' || author == '' || vcode == '' ){
            pxmu.fail('各项信息都不能为空哦！');
        }else{
            var _this = $(this);
            $.post(_this.attr("action"), _this.serialize(), function(data){
                try{
                    var json = eval("("+data+")");
                    if(json.kong_status) {
                        pxmu.success({msg: json.message, time: 1000});
                        setTimeout(function(){
                            window.location.reload();
                        }, 2000);
						$("#ctf_message_form").find("input").val("");//递交成功清空input
						$("#ctf_message_form").find("textarea").val("");//递交成功清空textarea
                    }else{
                        pxmu.fail(json.message);
                    }
                }catch(e){
                    alert(data);
                }
            });
        }
        return false;
    });
  </script>
  <!--登录留言 END--> 
<!--边栏客服-->
<link rel="stylesheet" type="text/css" href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/supermenu.css"/>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/jquery.supermenu.js"></script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/share.js"></script>
<div class="gtlol-fc4229 hovermenu">
   <div class="gtlol-9a3c9b hovermenu-box">
    <a href="javascript:;" class="gtlol-8e9bdb a-show"><span class="gtlol-4aa605 i"></span></a> 
    <a href="javascript:;" class="gtlol-fc0111 a a-hide"><span class="gtlol-4aa605 i"></span></a> 
	<?php if (($control=='index' || $control=='cate' || $control=='show' || $control=='tag') && $action == 'index') { ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-e679a3 i a-share" title="点击分享"></span> 
     <div class="gtlol-adb0e9 d-share" style="display: none;"> 
      <h3>分享<span class="gtlol-a4dc3a d-close iconfont"></span></h3>
      <div class="gtlol-9d4da6 Hcopyurl">
       <p id="Hcopyurl" onclick="copyurl()">
	    <?php if ($control=='index' && $action == 'index') { ?>
		<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>
		<?php }elseif($control=='show' && $action == 'index') { ?>
		<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>
		<?php }else{ ?>
		<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($cfg_var['url']) ? $cfg_var['url'] : ''); ?>
		<?php } ?>
	   </p> 
       <span onclick="copyurl()">复制链接</span> 
      </div>
       <div class="gtlol-9ebab9 social-share" data-initialized="true" style="text-align: center;"> 
          <a href="#" class="gtlol-bdd198 social-share-icon icon-weibo"></a> 
          <a href="#" class="gtlol-f34d18 social-share-icon icon-qq"></a> 
          <a href="#" class="gtlol-033365 social-share-icon icon-wechat"></a> 
          <a href="#" class="gtlol-40b3be social-share-icon icon-qzone"></a> 
          <a href="#" class="gtlol-bfcafd social-share-icon icon-facebook"></a> 
          <a href="#" class="gtlol-993b8a social-share-icon icon-twitter"></a> 
       </div> 
     </div> 
    </div> 
	<?php } ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-79f668 i a-qq"></span> 
     <div class="gtlol-ae234f d d-qq" style="display: none;">
	  <span class="gtlol-e95b77 arrow"></span>
      <div class="gtlol-009344 hqq">售前咨询<a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="咨询网站客服QQ"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/qq.png" />QQ在线</a></div> 
	  <div class="gtlol-009344 hqq">售后服务<a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="咨询网站客服QQ"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/qq.png" />QQ在线</a></div> 
      <div class="gtlol-8a63d6 worktime">
       上班时间：9：00-22：00
       <br />周六、周日：14：00-22：00
      </div>
     </div>
    </div> 
	<?php if ($control=='show' && $action == 'index'  &&  $gdata['payprice']>0) { ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-0c5c08 i a-buy"></span> 
     <div class="gtlol-01bacd d d-buy" style="display: none;">
      <span class="gtlol-e95b77 arrow"></span> 
      <div class="gtlol-95d97e Hbuy">
       <span class="gtlol-e4e7de hprice"><?php if ($gdata['payprice']>0) { echo(isset($gdata['payprice']) ? $gdata['payprice'] : ''); }else{ ?>免费<?php } ?></span>
       <a href="#pay" title="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"><img src="<?php if ($gdata['haspic']) { echo(isset($gdata['pic']) ? $gdata['pic'] : ''); }else{ echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); } ?>" alt="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>" class="buycover" /></a>
       <a href="#pay" title="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>"><h3>购买主题</h3></a>
       <div class="gtlol-bf6e63 htips">
        <?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>
       </div>
      </div>
     </div>
    </div> 	
	<?php }else{ ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-0c5c08 i a-buy"></span> 
     <div class="gtlol-01bacd d d-buy" style="display: none;">
      <span class="gtlol-e95b77 arrow"></span> 
      <div class="gtlol-95d97e Hbuy">
       <span class="gtlol-e4e7de hprice">399</span>
       <a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="购买本站同款主题"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/images/theme.jpg" alt="购买本站同款主题" class="buycover" /></a>
       <a target="_blank" rel="nofollow"  href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" title="购买本站同款主题"><h3>购买本站同款主题</h3></a>
       <div class="gtlol-bf6e63 htips">
        本站LECMS付费资源主题
       </div>
      </div>
     </div>
    </div> 	
	<?php } ?>
	<?php if ($control=='show' && $action == 'index'  &&  $gdata['payprice']>0) { ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-aba934 i a-down"></span> 
     <div class="gtlol-f0db0c d d-down" style="display: none;">
      <i class="gtlol-e95b77 arrow"></i>
      <h3><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h3>
      <a href="#download" class="gtlol-c1e71e Hdown"><span class="gtlol-d99724 iconfont"></span>点击下载</a>
      <div class="gtlol-bf6e63 htips">
		<?php if (isset($gdata['tag_arr'])) { if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?>
         <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" rel="tag"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> 			 
        <?php }} } ?>
      </div>
     </div>
    </div>	
	<?php }else{ ?>
    <div class="gtlol-b9cea8 a">
     <span class="gtlol-aba934 i a-down"></span> 
     <div class="gtlol-f0db0c d d-down" style="display: none;">
      <i class="gtlol-e95b77 arrow"></i>
      <h3>LECMS网站程序 LECMS 3.0 正式版</h3>
      <a target="_blank" rel="nofollow"  href="https://www.lecms.cc/?thread-205.htm" class="gtlol-c1e71e Hdown"><span class="gtlol-d99724 iconfont"></span>点击下载</a>
      <div class="gtlol-bf6e63 htips">
       LECMS是一款高负载、轻量级、可扩展建站程序。
      </div>
     </div>
    </div>
    <?php } ?>	
	<a href="#" class="gtlol-b4b300 a" title="VIP会员"><span class="gtlol-cfc782 i a-vip note-open"></span></a>
    <a href="javascript:;" class="gtlol-e2893c a-top" style="display: block;" title="返回顶部"><span class="gtlol-4aa605 i"></span></a> 
   </div>
</div>   
 <script>
function copyurl(){
const range = document.createRange();
range.selectNode(document.getElementById("Hcopyurl"));
const selection = window.getSelection();
if(selection.rangeCount > 0) selection.removeAllRanges();
selection.addRange(range);
document.execCommand("copy");
alert("复制链接成功，感谢您的关注！");
};
function copytel(){
const range = document.createRange();
range.selectNode(document.getElementById("copytel"));
const selection = window.getSelection();
if(selection.rangeCount > 0) selection.removeAllRanges();
selection.addRange(range);
document.execCommand("copy");
alert("已经复制电话号码！期待您的来电！");
};
</script>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>supermenu/script/chinese-s.js"></script>  

<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/js/jquery.js"></script>
<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/layer/layer.js"></script>
<script>
    $('#sj_kami_buy').on('click',function (e){
        var aid=$(this).attr('data-id');
        var golds=$(this).attr('data-golds');
        $.get('/my-userinfo.html','',function(data){
            if(!data.err){
                layer.confirm('您当前有 <span style="color: red">'+data.data.golds+' </span>金币。本次购买需消耗 <span style="color: red">'+golds+' </span>金币!<br/>确认进行购买操作？', {
                  btn: ['确定','取消'],icon:3
                }, function(){
                    $.post('/my-userinfo.html',{id:aid},function(data){
                        if(!data.err){
                            layer.alert(data.msg,{icon:1},function (e){
                                window.location.reload()
                            })
                        }else{
                            layer.alert(data.msg,{icon:2})
                        }
                    },'json');
                });
            }else{
                layer.msg('余额获取失败！')
            }
        },'json');
    })
</script>
   <div class="gtlol-dc1f68 module parallax"> 
    <img class="jarallax-img lazyload" data-srcset="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/footbanner.jpg" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="" /> 
    <div class="gtlol-e849bd container"> 
     <h4 class="gtlol-66873e entry-title" data-aos="fade-up">哥特动漫王国-哥特萝莉社</h4> 
     <a target="_blank" class="gtlol-bde8de button" data-aos="fade-right" href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" rel="nofollow" title="全站免费"><i class="gtlol-950342 fa fa-heartbeat"></i> 模板定制</a> 
     <a target="_blank" class="gtlol-aa41a9 button transparent" data-aos="fade-left" href="https://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo(isset($cfg['webqq']) ? $cfg['webqq'] : ''); ?>&amp;site=qq&amp;menu=yes" rel="nofollow" title="欢迎加入"><i class="gtlol-c8185c fa fa-qq"></i> 联系我们</a> 
    </div> 
   </div>
   <footer class="gtlol-95250f site-footer"> 
    <div class="gtlol-e849bd container"> 
     <div class="gtlol-a9d3b9 footer-widget"> 
      <div class="gtlol-a2c59a row"> 
       <div class="gtlol-6a2773 col-xs-12 col-sm-6 col-md-3 widget--about"> 
        <div class="gtlol-35a7f5 widget--content"> 
         <div class="gtlol-3f43dd footer--logo mb-20"> 
          <img class="tap-logo" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/logofoot.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> 
         </div> 
         <p class="gtlol-ccf69d mb-10"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></p> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="gtlol-268104 col-xs-12 col-sm-3 col-md-2 col-md-offset-1 widget--links"> 
        <div class="gtlol-198027 widget--title"> 
         <h5>本站导航</h5> 
        </div> 
        <div class="gtlol-35a7f5 widget--content"> 
         <ul class="gtlol-566c2b list-unstyled mb-0"> 
          <li><a href="/about">关于我们</a></li> 
          <li><a href="/contact">联系我们</a></li> 
          <li><a href="/aftersale">售后保障</a></li> 
         </ul> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="gtlol-b5b18c col-xs-12 col-sm-3 col-md-2 widget--links"> 
        <div class="gtlol-198027 widget--title"> 
         <h5>更多介绍</h5> 
        </div> 
        <div class="gtlol-35a7f5 widget--content"> 
         <ul class="gtlol-566c2b list-unstyled mb-0"> 
          <li><a href="/process">购买流程</a></li> 
          <li><a href="/clause">交易条款</a></li> 
          <li><a href="/problem">常见问题</a></li> 
         </ul> 
        </div> 
       </div> 
       <!-- .col-md-2 end --> 
       <div class="gtlol-8f3c7d col-xs-12 col-sm-12 col-md-4 widget--newsletter"> 
        <div class="gtlol-198027 widget--title"> 
         <h5>快速搜索</h5> 
        </div> 
        <div class="gtlol-35a7f5 widget--content"> 
         <form id="search_form3" class="gtlol-da4d98 newsletter--form mb-30" method="get" target="_blank"> 
		  <input type="hidden" name="u" value="search-index" /> 
          <input type="text" class="full-width has-padding has-border" name="keyword" placeholder="关键词" /> 
          <button type="submit"><i class="gtlol-417359 fa fa-arrow-right"></i></button> 
         </form> 
         <h6><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>提供的大部分资源来于网络收集整理，仅供学习交流之用。请勿非法使用，否则产生的一切后果自行承担，本站概不负责。</h6>
        </div> 
       </div> 
      </div> 
     </div> 
    </div> 
	<?php if ($control=='index' && $action == 'index') { ?>
    <div class="gtlol-389c54 links"> 
     <div class="gtlol-e849bd container"> 
      <ul>
       <li>友情链接：</li>
	   <?php $data = block_links(array (
)); if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
       <li><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></li>
       <?php }} unset($data); ?> 
      </ul> 
     </div> 
    </div>  
	<?php } ?>
    <div class="gtlol-72cc7c site-info">
	 本站所发布的大部分内容来源于互联网，仅限于小范围内传播学习和文献参考，请在下载后24小时内删除！如果有侵权之处请第一时间联系我们删除，敬请谅解！
     <br />任何人不得对本站资源进行倒卖、行骗、传播。严禁用于商业用途，请遵循相关法律法规，本站一切资源不代表本站立场！
     <br />Copyright&copy;2015-2021 <a href="<?php echo(isset($cfg['weburl']) ? $cfg['weburl'] : ''); ?>" rel="home"><?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?></a>
     <a href="/sitemap.xml" target="_blank">网站地图</a> 
     <a href="/tags" target="_blank">TAGS</a>
     <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a> 
    </div> 	
   </footer> 
   <!--<div class="gtlol-606b14 rollbar">
   <div class="gtlol-f4bce9 rollbar-item note-open"><i class="gtlol-c26a81 fa fa-bell-o"></i></div><!--点击弹窗-->
   <!--<div class="gtlol-238be5 rollbar-item" etap="to_top"><i class="gtlol-43414a fa fa-angle-up"></i></div><!--返回顶部--> 
   <!--</div>-->  
   <!--移动端logo-->
   <div class="gtlol-f54efd off-canvas"> 
    <div class="gtlol-7fc92c canvas-close">
     <i class="gtlol-963018 mdi mdi-close"></i> 
    </div> 
    <div class="gtlol-cfa30a logo-wrapper"> 
     <a href="/"><img class="logo regular" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>images/logom.jpg" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>" /> </a> 
    </div> 
    <div class="gtlol-9d2b4c mobile-menu hidden-lg hidden-xl"></div> 
    <aside class="gtlol-37ce8a widget-area"></aside> 
   </div> 
   <!--js-->
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/main.js'></script>
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/plugins.js'></script>
   <script language="javascript" src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/server.js"></script>
   <?php if ($control=='show' && $action == 'index') { ?>
   <script type='text/javascript' src='<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>js/jquery.fancybox.min.js'></script>
   <script type="application/ld+json">
   {
    "@content": "https://ziyuan.baidu.com/contexts/cambrian.jsonld",
    "@id": "<?php echo(isset($gdata['absolute_url']) ? $gdata['absolute_url'] : ''); ?>",
    "appid": "",
    "title": "<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>",
    "images": ["<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>"],
    "description": "<?php echo(isset($gdata['intro']) ? $gdata['intro'] : ''); ?>",
    "pubDate": "<?php  echo date('Y-m-d', $gdata['dateline']).'T'.date('H:i:s', $gdata['dateline']); ?>",
    "upDate": "<?php  echo date('Y-m-d', $gdata['lasttime']).'T'.date('H:i:s', $gdata['lasttime']); ?>",
    "lrDate": "<?php  echo date('Y-m-d', $gdata['lasttime']).'T'.date('H:i:s', $gdata['lasttime']); ?>"
   }
   </script>  
   <script>
    $(".entry-content a").each(function(){
    var articleHref = $(this).attr("href").split('/')[2];
    if(articleHref != window.location.host){
    $(this).attr("rel","external nofollow");
    };
    })
   </script>
   <?php } ?>
   </div>
   <?php if (($control=='tag' || $control=='search') && $action == 'index') { ?>
   <script>$(".moretag").readmore({moreLink: '<a href="#" class="gtlol-59b066 more"><i class="gtlol-10dda2 fa fa-plus-square"></i>更多</a>',lessLink: '<a href="#" class="gtlol-59b066 more"><i class="gtlol-6856b1 fa fa-minus-square"></i>收起</a>',speed: 100,collapsedHeight: 30});</script>
   <?php } ?>
   <script>   
	(function(){
		$("#search_form,#search_form2,#search_form3").submit(function(){
			var mid = $(this).find("[name='mid']").val();
			var keyword = $(this).find("[name='keyword']").val();
			window.location.href = "/search/"+encodeURIComponent(keyword)+"/";
			return false;
		});	
	})();  
   </script>    
  </div>
 </body>
</html>  