<?php defined('APP_NAME') || exit('Access Denied');  function block_navigate($conf) { global $run; $alias = isset($conf['alias']) ? $conf['alias'] : 0; $showcate = _int($conf, 'showcate', 0); $nocids = empty($conf['nocids']) ? '' : $conf['nocids']; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $cache_key = $life ? md5('navigate'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if($alias){ $field = 'navigate_'.$alias; }else{ $field = 'navigate'; } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($nocids){ $nocids_arr = explode(',', $nocids); }else{ $nocids_arr = array(); } $nav_arr = $run->kv->xget($field); foreach($nav_arr as &$v) { if($nocids_arr && in_array($v['cid'], $nocids_arr)){ continue; } if($v['cid'] > 0) { $v['url'] = $run->category->category_url($v); } if($v['cid'] > 0 && $allcategorys){ $v['category'] = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->format($v['category']); }else{ $v['category'] = array(); } if(!empty($v['son'])) { foreach($v['son'] as &$v2) { if($nocids_arr && in_array($v2['cid'], $nocids_arr)){ continue; } if($v2['cid'] > 0) { $v2['url'] = $run->category->category_url($v2); } if($v2['cid'] > 0 && $allcategorys){ $v2['category'] = isset($allcategorys[$v2['cid']]) ? $allcategorys[$v2['cid']] : array(); $run->category->format($v2['category']); }else{ $v2['category'] = array(); } } } } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $nav_arr, $life); } return $nav_arr; }
 function block_content_total_by_date($conf) { global $run; $mid = _int($conf, 'mid', 2); $type = isset($conf['type']) ? $conf['type'] : 'all'; $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : ''; if( empty($table) ){ return 0; } $cache_key = $life ? md5('content_total_by_date'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return (int)$cache_data; } } $where = array(); $run->cms_content->table = 'cms_'.$table; $total = 0; switch($type) { case 'all': $where = array(); break; case 'today': $starttime = mktime(0,0,0,date('m'),date('d'),date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'yesterday': $starttime = mktime(0,0,0,date('m'),date('d')-1,date('Y')); $endtime = mktime(0,0,0,date('m'),date('d'),date('Y'))-1; $where = array('dateline'=>array('>'=>$starttime, '<='=>$endtime)); break; case 'week': $starttime = mktime(0, 0, 0, date('m'), (date('d') - (date('w')>0 ? date('w') : 7) + 1), date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'month': $starttime = mktime(0,0,0,date('m'),1,date('Y')); $where = array('dateline'=>array('>'=>$starttime)); break; case 'year': $starttime = strtotime(date('Y',time())."-1"."-1"); $where = array('dateline'=>array('>'=>$starttime)); break; } if($where){ $total = $run->cms_content->find_count($where); }else{ $total = $run->cms_content->count(); } if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $total, $life); } return $total; }
 function block_list_rand($conf) { global $run; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $limit = _int($conf, 'limit', 10); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); if(!isset($conf['cid']) && isset($_GET['cid'])){ $conf['cid'] = intval($_GET['cid']); } $cache_key = $life ? md5('list_rand'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; if($table == 'page'){ return array(); } $table_prefix = $_ENV['_config']['db']['master']['tablepre']; $table_full = $table_prefix.'cms_'.$table; $run->cms_content->table = 'cms_'.$table; $total = $run->cms_content->count(); $beishu = $limit > 10 ? 10 : 5; if($total > 1000 && $total > $limit*$beishu){ $keys = array(); $i = 0; while ($i<$limit){ $sql = "SELECT t1.id FROM {$table_full} AS t1 JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM {$table_full})) AS id) AS t2 WHERE t1.id >= t2.id LIMIT 1"; $arr = $run->db->fetch_first($sql); if($arr && !in_array($arr['id'], $keys)){ $keys[] = $arr['id']; $i++; } } $list_arr = $run->cms_content->mget($keys); }else{ $keys = array(); $sql = "SELECT id FROM {$table_full} ORDER BY RAND() LIMIT {$limit}"; $arr = $run->db->fetch_all($sql); foreach ($arr as $v){ $keys[] = $v['id']; } $list_arr = $run->cms_content->mget($keys); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; if(empty($keys)){ foreach($list_arr as $v) { $keys[] = $v['id']; } } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_list($conf) { global $run; $cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0); $cids = empty($conf['cids']) ? '' : $conf['cids']; $mid = isset($conf['mid']) ? max(2, intval($conf['mid'])) : (isset($_GET['mid']) ? max(2, intval($_GET['mid'])) : 2); $dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat']; $titlenum = _int($conf, 'titlenum'); $intronum = _int($conf, 'intronum'); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline', 'lasttime', 'comments')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start', 0); $limit = _int($conf, 'limit', 10); $showcate = _int($conf, 'showcate', 0); $showviews = _int($conf, 'showviews', 0); $field_format = _int($conf, 'field_format', 0); $life = isset($conf['life']) ? (int)$conf['life'] : (isset($run->_cfg['life']) ? (int)$run->_cfg['life'] : 0); $extra = array('block_name'=>'block_list'); $cache_key = $life ? md5('list'.serialize($conf)) : ''; if($cache_key){ $cache_data = $run->runtime->get_block_data_cache($cache_key); if($cache_data){ return $cache_data; } } if ($cids){ $cid_arr = explode(',', $cids); $where = array('cid' => array("IN" => $cid_arr)); $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; }else{ if($cid == 0) { $cate_name = 'No Title'; $cate_url = 'javascript:;'; $table_arr = &$run->_cfg['table_arr']; $table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article'; $where = array(); }else{ $cate_arr = $run->category->get_cache($cid); if(empty($cate_arr)) return; $cate_name = $cate_arr['name']; $cate_url = $run->category->category_url($cate_arr); $table = &$cate_arr['table']; $mid = $cate_arr['mid']; if(!empty($cate_arr['son_cids']) && is_array($cate_arr['son_cids'])) { $where = array('cid' => array("IN" => $cate_arr['son_cids'])); }else{ $where = array('cid' => $cid); } } } if($table == 'page'){ return array(); } if($showcate){ $allcategorys = $run->category->get_category_db(); }else{ $allcategorys = array(); } $run->cms_content->table = 'cms_'.$table; $list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, $start, $limit, $limit, $extra); if($showviews && $list_arr){ $run->cms_content_views->table = 'cms_'.$table.'_views'; $keys = array(); foreach($list_arr as $v) { $keys[] = $v['id']; } $views_list_arr = $run->cms_content_views->mget($keys); $views_key = 'cms_'.$table.'_views-id-'; }else{ $views_key = ''; $views_list_arr = array(); } $xuhao = 1; foreach($list_arr as &$v) { $run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum, $field_format); if($showcate && $allcategorys){ $cate = isset($allcategorys[$v['cid']]) ? $allcategorys[$v['cid']] : array(); $run->category->getCategoryInfoByList($v, $cate); } if($showviews && $views_list_arr){ $v['views'] = isset($views_list_arr[$views_key.$v['id']]['views']) ? (int)$views_list_arr[$views_key.$v['id']]['views'] : 0; } $v['xuhao'] = $xuhao; $xuhao++; } $ret = array('cate_name'=> $cate_name, 'cate_url'=> $cate_url, 'list'=> $list_arr); if($cache_key){ $run->runtime->set_block_data_cache($cache_key, $ret, $life); } return $ret; }
 function block_links($conf) { global $run; $where = array(); $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'orderby')) ? $conf['orderby'] : 'id'; $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1; $start = _int($conf, 'start'); $limit = _int($conf, 'limit', 10); $arr = $run->links->find_fetch($where, array($orderby => $orderway), $start, $limit); return $arr; }
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>快萌ACG - 南+社区</title>
<meta name="keyword" content="快萌ACG,南+社区,快萌论坛,游戏,百合,acg,和谐,同人,C94,韩漫,游戏,百合,萝莉,魔法少女,初音,东方,伪娘,末途,校园奴隶契约,驱灵师,小黄游,HS2,游戏合集包,漫画合集包,写真,套图">
<meta name="description" content="<?php echo(isset($cfg['seo_description']) ? $cfg['seo_description'] : ''); ?>">
<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1"/>
<meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width">
<meta name="apple-mobile-web-app-title" content="">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="green">
<meta name="format-detection" content="telphone=no, email=no">
<meta name="HandheldFriendly" content="true">
<meta name="screen-orientation" content="portrait">
<meta name="x5-orientation" content="portrait">
<meta name="full-screen" content="yes">
<meta name="x5-fullscreen" content="true">
<meta name="browsermode" content="application">
<meta name="x5-page-mode" content="app">
<meta name="renderer" content="webkit">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/font_3913661_iikaqjykdll.js"></script>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font_3913661_iikaqjykdll.css" rel="stylesheet">
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/font-awesome.min.css" rel="stylesheet">
<style type="text/css">
.iconfont {
    font-size: inherit;
}
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
.th_padding, .left_padding {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 992px) {
  .col-md-12 {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  @media only screen and (min-width: 320px) and (max-width: 992px) {
    .th_padding {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}
@media only screen and (min-width: 320px) and (max-width: 992px) {
  .pagebar {
    text-align: center;
    margin-top: 15px;
  }
 }
.pagebar {
  text-align: center;
  margin-top: 15px;
}
  
  .pagebar a, .pagebar b {
  -webkit-transition: all .3s ease-in-out;
  -moz-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
  font-size: 16px;
  margin: 0px 5px;
  border: 1px solid #f0f0f5;
  color: #666;
  padding: 0px 8px;
  border-radius: 2px;
  box-sizing: border-box;
}
  .pagebar b {
  background: #7cbeff;
  border: 1px solid #7cbeff;
  color: #fff;
  font-weight: normal;
}
</style>
<link href="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/style/style.css?2024" rel="stylesheet">
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/jquery.min.js"></script>
<div class="tdacg-0cd2f1 ui progress" style="position: fixed; top: 0; left: 0; z-index: 999; width: 100vw;">
  <div id="page-reading-percent" class="tdacg-0b16ae percent" style="width: 0;"></div>
</div>
<script>
    $(function (){
         $(document).scroll(function (){
             let height = $(this).height() - $(window).height();
             let top = $(this).scrollTop();
             let percent = top / height * 100;
             $("#page-reading-percent").width(percent.toFixed(2) + "%");
        });
    });
     function scrollToTop(){
        $("html, body").animate({ scrollTop:0 }, 500);
    }
     function showModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("show");
        setTimeout(() => {
            modalPageDiv.addClass("open");
        }, 50);
    }
     function hideModal(){
        const modalPageDiv = $("#modal-page");
        modalPageDiv.addClass("close");
        setTimeout(() => {
            modalPageDiv.removeClass("show");
            modalPageDiv.removeClass("open");
            modalPageDiv.removeClass("close");
        }, 500);
    }
</script>
<div class="tdacg-e65031 ui modal-page right" id="modal-page"> 
   <div class="tdacg-39ae83 modal-background" onclick="hideModal()"></div>
   <div class="tdacg-0f04ec modal-container padding">
    <div class="tdacg-d0bab5 ui flex-item padding">
      <div class="tdacg-2df666 center"></div>
      <div class="tdacg-59f765 end">
        <div class="tdacg-14d889 ui button circle outline" onclick="hideModal()"><i class="tdacg-07322e iconfont icon-close"></i></div>
      </div>
    </div>
     <div class="tdacg-1ee7ec panel-block">
      <div class="tdacg-4209da title">导航菜单</div>
      <div class="tdacg-b77a93 ui tree linear-split" style="padding: 20px 0;"> <?php $data = block_navigate(array (
)); ?>
        <ul>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold"><a href="/" class="tdacg-fbdb65 padding-half">首页</a></div>
            </div>
          </li>
          <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="tdacg-fbdb65 padding-half"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a></div>
            </div>
          </li>
          <?php }} ?>
          <li>
            <div class="tdacg-82ee99 ui flex-item item">
              <div class="tdacg-1c0dc5 start justify-center">
                <div class="tdacg-69d758 ui button clear waiting tiny circle"><i class="tdacg-bbc127 fa fa-"></i></div>
              </div>
              <div class="tdacg-cff74c center justify-center text-bold">
               <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="tdacg-fbdb65 padding-half">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="tdacg-fbdb65 padding-half">登录/注册</a>
                <?php } ?></div>
            </div>
          </li>
        </ul>
        <?php unset($data); ?> </div>
    </div>
  </div>
</div>
<header>
  <div class="tdacg-8dca09 container auto-margin ui flex-item"> <a class="tdacg-2f82ca start web-logo" href="<?php echo(isset($cfg['webroot']) ? $cfg['webroot'] : ''); ?>"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/logo.png" alt="<?php echo(isset($cfg['webname']) ? $cfg['webname'] : ''); ?>"></a> <a class="tdacg-27ac43 start web-name padding-left" href="/">快萌ACG</a>
<div class="tdacg-e47538 search center padding-left justify-center">
      <div class="tdacg-7f0202 ui input radius">
        <input type="text" placeholder="前往acgk.cc搜索,本站待修复">
        <div class="tdacg-36a14a ">
          <div class="tdacg-09d874 ui button clear" ><i class="iconfont icon-sousuo"></i></div>
        </div>
      </div>
    </div>
    <?php $data = block_navigate(array (
)); ?>
    <div class="tdacg-43735b end justify-center">
      <nav> <a class="tdacg-96319d item" href="/">首页</a> <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> <a class="tdacg-0a824c item" href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} ?> 
      <?php if ($_uid) { ?>
                    <a href="/my-index.html" class="tdacg-1f54da item">个人中心</a>
                <?php }else{ ?>
                    <a href="/user-login.html" class="tdacg-1f54da item">登录/注册</a>
      <?php } ?>
      </nav>
    </div>
    <?php unset($data); ?>
    <div class="tdacg-614bc4 end ext-menu justify-center">
      <div class="tdacg-a2b9b7 ui button circle" onclick="showModal()"><i class="tdacg-3805f5 fa fa-list"></i></div>
    </div>
  </div>
</header>

<div class="tdacg-9869ca main-warp">
  <div class="tdacg-ea1388 container auto-margin">
    <?php $data = block_content_total_by_date(array (
  'mid' => '2',
  'type' => 'today',
)); ?><div class="tdacg-913fe7 today-posts" style="background-color: #fff;color: #343a40;border-radius: 5px;text-align: center;font-size: 1.9rem;transition: background-color 0.3s;font-weight: 900;">今日发布：<m style="font-size: 2.8rem;color: red;"> <?php echo(isset($data) ? $data : ''); ?> </m>篇</div><?php unset($data); ?>
    <div class="tdacg-9f4386 slide-wrap">
      <div class="tdacg-5837dc slide">
        <div class="tdacg-728195 text">二次元社区</div>
        <div class="tdacg-58d3a7 image"><img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/bg1.png" alt=""></div>
      </div>
      <div class="tdacg-c9b828 panel-block notice">
        <div class="tdacg-4209da title">Top</div>
        <div class="tdacg-fbdb65 padding-half"></div>
        <ul class="tdacg-9912b9 text-list">
          <?php $data = block_list_rand(array (
  'orderby' => 'views',
  'limit' => '8',
  'titlenum' => '24',
)); ?>
          <?php $rank = 1; ?>
          
          <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
          <li><span class="tdacg-6c0dcf tag"><?php echo(isset($rank) ? $rank : ''); ?></span> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title=""><?php echo(isset($v['title']) ? $v['title'] : ''); ?></a></li>
          <?php $rank++; ?>
          <?php }} ?>
          <?php unset($data); ?>
        </ul>
      </div>
    </div>
    <div class="tdacg-c5f0ed tab-list margin-top padding-top" id="tab-list">
      <div class="tdacg-7068f7 tab-row">
          <div class="tdacg-62d26c col active" data-tab="tab-wealth">
              <div class="tdacg-5540ce name">game</div>
              <div class="tdacg-771e00 icon">
                  游戏 
              </div>
          </div>
          <div class="tdacg-a56e7d col" data-tab="tab-loan">
              <div class="tdacg-5540ce name">anime</div>
              <div class="tdacg-771e00 icon">
                  动漫 
              </div>
          </div>
          <div class="tdacg-cc0b36 col" data-tab="tab-stock">
              <div class="tdacg-5540ce name">comics</div>
              <div class="tdacg-771e00 icon">
                  漫画
              </div>
          </div>
          <div class="tdacg-7d10d4 col" data-tab="tab-insurance">
              <div class="tdacg-5540ce name">tutorial</div>
              <div class="tdacg-771e00 icon">
                  教程
              </div>
          </div>
      </div>
      <div class="tdacg-d2976a tab-content" id="tab-wealth">
        <ul class="tdacg-9912b9 text-list">
          <!-- orderby="dateline" 最新日期，ID2内容 内容数量16 标题长度50--> 
          <?php $data = block_list(array (
  'orderby' => 'dateline',
  'cid' => '1',
  'limit' => '16',
  'titlenum' => '80',
)); ?>
          <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
          <li><span class="tdacg-b74196 time"><i class="tdacg-1fd73e iconfont icon-receipt"></i></span> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title=""><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
          <?php }} ?>
          <?php unset($data); ?>
        </ul>
      </div>
      <div class="tdacg-91fce4 tab-content" id="tab-loan" style="display:none;">
        <ul class="tdacg-9912b9 text-list">
          <?php $data = block_list(array (
  'orderby' => 'dateline',
  'cid' => '2',
  'limit' => '16',
  'titlenum' => '80',
)); ?>
          <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
          <li><span class="tdacg-b74196 time"><i class="tdacg-1fd73e iconfont icon-receipt"></i></span> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title=""><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
          <?php }} ?>
          <?php unset($data); ?>
        </ul>
      </div>
      <div class="tdacg-8a301e tab-content" id="tab-stock" style="display:none;">
        <ul class="tdacg-9912b9 text-list">
          <?php $data = block_list(array (
  'orderby' => 'dateline',
  'cid' => '3',
  'limit' => '16',
  'titlenum' => '80',
)); ?>
          <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
          <li><span class="tdacg-b74196 time"><i class="tdacg-1fd73e iconfont icon-receipt"></i></span> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title=""><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
          <?php }} ?>
          <?php unset($data); ?>
        </ul>
      </div>
      <div class="tdacg-b767cc tab-content" id="tab-insurance" style="display:none;">
        <ul class="tdacg-9912b9 text-list">
          <?php $data = block_list(array (
  'orderby' => 'dateline',
  'cid' => '6',
  'limit' => '16',
  'titlenum' => '80',
)); ?>
          <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
          <li><span class="tdacg-b74196 time"><i class="tdacg-1fd73e iconfont icon-receipt"></i></span> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title=""><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></li>
          <?php }} ?>
          <?php unset($data); ?>
        </ul>
      </div>
    </div>
    <script>
document.addEventListener('DOMContentLoaded', function() {
  const tabs = document.querySelectorAll('.tab-row .col');
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      tabs.forEach(t => t.classList.remove('active'));
      const contents = document.querySelectorAll('.tab-content');
      contents.forEach(c => c.style.display = 'none');
      this.classList.add('active');
      const activeTab = this.getAttribute('data-tab');
      document.getElementById(activeTab).style.display = 'block';
    });
  });
});
</script>
    <div class="tdacg-625176 panel-block margin-top">
      <div class="tdacg-4209da title">友情链接</div>
      <div class="tdacg-fbdb65 padding-half"></div>
      <div class="tdacg-389c54 links"> <?php $data = block_links(array (
)); ?>
        <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a> <?php }} ?>
        <?php unset($data); ?> </div>
    </div>
  </div>
</div>
<footer>
  <div class="tdacg-8dca09 container auto-margin ui flex-item">
    <div class="tdacg-2df666 center">
      <?php echo(isset($cfg['copyright']) ? $cfg['copyright'] : ''); ?> <a rel="nofollow" href="https://beian.miit.gov.cn"><?php echo(isset($cfg['beian']) ? $cfg['beian'] : ''); ?></a> <?php echo(isset($cfg['tongji']) ? $cfg['tongji'] : ''); ?></div>
  </div>
</footer>
<!-- 随航组件 -->
<div class="tdacg-bb9d5e fixed-menu">
  <div class="tdacg-1f54da item"> 
    <!-- 此处的二维码为自动生成 -->
    <div class="tdacg-cd14a0 hover-display url-qrcode" id="pageUrlQrcode"></div>
    <i class="tdacg-75841e fa fa-qrcode"></i> </div>
  <div class="tdacg-1f54da item">
    <div class="tdacg-04200a hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="QQ"> </div>
    <i class="tdacg-c8185c fa fa-qq"></i> </div>
  <div class="tdacg-1f54da item">
    <div class="tdacg-04200a hover-display"> <img src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/images/qrcode.png" alt="微信"> </div>
    <i class="tdacg-65d1a0 fa fa-weixin"></i> </div>
  <div class="tdacg-b7188f item" onclick="scrollToTop()"><i class="tdacg-1fa263 fa fa-arrow-up"></i></div>
</div>
<script src="<?php echo(isset($cfg['tpl']) ? $cfg['tpl'] : ''); ?>assets/script/qrcode.js"></script> 
<script>
    const qrcode = new QRCode("pageUrlQrcode", {
        text: window.location.href,
        width: 150,
        height: 150,
        correctLevel : QRCode.CorrectLevel.H
    });
</script>
<footer style="background-color: #f1f1f1;padding: 1px;text-align: center;">
  <h2><a href="/sitemap.xml">网站地图</a></h2>
</footer>
</body></html> 