<?php
defined('ROOT_PATH') or exit;

class runtime extends model {
	private $data = array();		// 保证唯一性
	private $changed = array();		// 表示修改过的key

	function __construct() {
		$this->table = 'runtime';	// 表名
		$this->pri = array('k');	// 主键



	}

	// 读取缓存
	public function get($k) {
        strlen($k) > 32 AND $k = md5($k);
		$arr = parent::get($k);
		return !empty($arr) && (empty($arr['expiry']) || $arr['expiry'] > $_ENV['_time']) ? _json_decode($arr['v']) : array();
	}

	// 写入缓存
	public function set($k, $s, $life = 0) {
		$s = _json_encode($s);
		$arr = array();
        strlen($k) > 32 AND $k = md5($k);
		$arr['k'] = $k;
		$arr['v'] = $s;
		$arr['expiry'] = $life ? $_ENV['_time'] + $life : 0;
		return parent::set($k, $arr);
	}

	// 读取
	public function xget($key = 'cfg') {
		if(!isset($this->data[$key])) {
			$this->data[$key] = $this->get($key);
			if($key == 'cfg' && empty($this->data[$key])) {
				$cfg = (array)$this->kv->get('cfg');

				empty($cfg['theme']) && $cfg['theme'] = 'default';  //主题

				$cfg['tpl'] = $cfg['webdir'].'view/'.$cfg['theme'].'/'; //模板路径
				$cfg['webroot'] = HTTP.$cfg['webdomain']; //完整域名，不带安装目录
				$cfg['weburl'] = HTTP.$cfg['webdomain'].$cfg['webdir']; //完整域名，带安装目录

				//模型
                $models = $this->models->get_models();
                $table_arr = $mod_name = $mod_url = array();
                foreach ($models as $md){
                    $table_arr[$md['mid']] = $md['tablename'];
                    if($md['mid'] > 1){
                        $mod_name[$md['mid']] = $md['name'];
                        $mod_url[$md['tablename']] = $this->urls->model_url($md['tablename'], $md['mid'], false, array('cfg'=>$cfg));
                    }
                }
				$cfg['table_arr'] = $table_arr;
				$cfg['mod_name'] = $mod_name;
                $cfg['mod_url'] = $mod_url;

				//用户组
				if(isset($cfg['open_user']) && $cfg['open_user']){
                    $cfg['group_name'] = $this->user_group->get_name();
                }

				//分类
				$categorys = $this->category->get_category_db();
				$cate_arr = array();
				foreach($categorys as $row) {
					$cate_arr[$row['cid']] = $row['alias'];
				}
				$cfg['cate_arr'] = $cate_arr;
                

				$this->data[$key] = &$cfg;
				$this->set('cfg', $this->data[$key]);
			}
		}
        //移动端模板分离
        if($key == 'cfg' && !empty($this->data['cfg']['open_mobile_view']) && is_mobile()==1){
            $this->data['cfg']['theme'] =  isset($this->data['cfg']['mobile_view']) ? $this->data['cfg']['mobile_view'] : 'mobile';
            $this->data['cfg']['tpl'] = $this->data['cfg']['webdir'].'view/'.$this->data['cfg']['theme'].'/';
            $this->data['cfg']['weburl'] = HTTP.$_SERVER['HTTP_HOST'].$this->data['cfg']['webdir'];
        }
        
//获取域名
$httphost = strtolower(R('HTTP_HOST', 'S'));
if(strpos($httphost, $this->data['cfg']['webdomain']) === false){   //不是主站
    $httphost_arr = explode('.', $httphost);
    $www = '';
    if(count($httphost_arr) > 2){
        $httphost_arr = array_slice($httphost_arr, -2);
        $httphost = implode('.', $httphost_arr);
        if($httphost_arr[0] == 'www'){
            $www = 'www';
        }
    }

    //优先从缓存里面读取
    $domain_pre = substr($httphost, 0, strpos($httphost, '.'));
    $cache_key = 'website_group_'.$domain_pre;
    $cache_key = str_replace('-','_',$cache_key);   //没有这句，如果域名带 - ，会导致报错
    $website_group = $this->get($cache_key);
    if(empty($website_group)){
        $website_group = $this->website_group->find_fetch(array('webdomain'=>$httphost), array('id' => 1), 0, 1);
        if($website_group){
            $website_group = current($website_group);
            $this->set($cache_key, $website_group, 7*86400);
        }
    }

    if($website_group){
        $website_group_content = _json_decode($website_group['content']);

        //主题和主题路径
        if(isset($website_group_content['theme']) && !empty($website_group_content['theme'])){
            $this->data['cfg']['theme'] = $website_group_content['theme'];
            $this->data['cfg']['tpl'] = $this->data['cfg']['webdir'].'view/'.$this->data['cfg']['theme'].'/';
        }

        //移动端主题和主题路径
        if(isset($website_group_content['theme_mobile']) && !empty($website_group_content['theme_mobile']) && $key == 'cfg' && !empty($this->data['cfg']['open_mobile_view']) && is_mobile()==1){
            $this->data['cfg']['theme'] = $website_group_content['theme_mobile'];
            $this->data['cfg']['mobile_view'] = $website_group_content['theme_mobile'];  //不加这个，多次调用后移动端模版会复原20241118
            $this->data['cfg']['tpl'] = $this->data['cfg']['webdir'].'view/'.$this->data['cfg']['theme'].'/';
        }

        //域名 ， 完整域名
        $this->data['cfg']['webdomain'] = $www.$website_group['webdomain'];
        $this->data['cfg']['webroot'] = http().$this->data['cfg']['webdomain']; //完整域名，不带安装目录
        $this->data['cfg']['weburl'] = http().$this->data['cfg']['webdomain'].$this->data['cfg']['webdir']; //完整域名，带安装目录

        //站点SEO信息
        if(isset($website_group_content['webname']) && !empty($website_group_content['webname'])){
            $this->data['cfg']['webname'] = $website_group_content['webname'];
        }
        if(isset($website_group_content['seo_title']) && !empty($website_group_content['seo_title'])){
            $this->data['cfg']['seo_title'] = $website_group_content['seo_title'];
        }
        if(isset($website_group_content['seo_keywords']) && !empty($website_group_content['seo_keywords'])){
            $this->data['cfg']['seo_keywords'] = $website_group_content['seo_keywords'];
        }
        if(isset($website_group_content['seo_description']) && !empty($website_group_content['seo_description'])){
            $this->data['cfg']['seo_description'] = $website_group_content['seo_description'];
        }

        //内容URL格式
        if(isset($website_group_content['link_show_type']) && !empty($website_group_content['link_show_type'])){
            //主站是灵活型（link_show_end = ''），分站不是灵活型
            if($this->data['cfg']['link_show_type'] == 7 && $website_group_content['link_show_type'] != 7){
                $this->data['cfg']['link_show_end'] = $_ENV['_config']['url_suffix'];
            }
            $this->data['cfg']['link_show_type'] = (int)$website_group_content['link_show_type'];
        }

        //模板伪静态标识
        if(isset($website_group_content['theme_bj']) && !empty($website_group_content['theme_bj'])){
            $this->data['cfg']['theme_bj'] = $website_group_content['theme_bj'];
        }

        //备案信息
        if(isset($website_group_content['beian']) && !empty($website_group_content['beian'])){
            $this->data['cfg']['beian'] = $website_group_content['beian'];
        }

        //统计代码
        if(isset($website_group_content['tongji'])){
            $this->data['cfg']['tongji'] = $website_group_content['tongji'];
        }

        //模板logo名，需要模板支持
        if(isset($website_group_content['logoname']) && !empty($website_group_content['logoname'])){
            $this->data['cfg']['logoname'] = $website_group_content['logoname'];
        }

        if(isset($website_group_content['copyright']) && !empty($website_group_content['copyright'])){
            $this->data['cfg']['copyright'] = $website_group_content['copyright'];
        }

        //关闭网站
        if(isset($website_group_content['close_website'])){
            $this->data['cfg']['close_website'] = (int)$website_group_content['close_website'];
        }

    }
    unset($website_group);
    unset($www);
    unset($cache_key);
}
		return $this->data[$key];
	}

	// 修改
	public function xset($k, $v, $key = 'cfg') {
		if(!isset($this->data[$key])) {
			$this->data[$key] = $this->get($key);
		}
		if($v && is_string($v) && ($v[0] == '+' || $v[0] == '-')) {
			$v = intval($v);
			$this->data[$key][$k] += $v;
		}else{
			$this->data[$key][$k] = $v;
		}
		$this->changed[$key] = 1;
	}

	// 保存
	public function xsave($key = 'cfg') {
		$this->set($key, $this->data[$key]);
		$this->changed[$key] = 0;
	}

	// 保存所有修改过的key
	public function save_changed() {
		foreach($this->changed as $key=>$v) {
			$v && $this->xsave($key);
		}
	}

	//删除
    public function xdelete($k = ''){
        strlen($k) > 32 AND $k = md5($k);
        $r = $this->delete($k);
        if($r){
            

            return $r;
        }else{
            return false;
        }
    }

    //block设置数据表缓存
    public function set_block_data_cache($k, $v, $life = 60){
        

        $r = $this->set($k, $v, $life);
        if($r){
            

            return $r;
        }else{
            return false;
        }
    }

    //block获取数据表缓存
    public function get_block_data_cache($k = ''){
        

	    $r = $this->get($k);
	    if($r){
            

	        return $r;
        }else{
	        return false;
        }
    }

    //设置文件缓存
    public function setFileCache($key = '', $data = array(), $life = 600){
        

        $datas = array(
            'datas' => serialize($data),
            'time' => $_ENV['_time']+$life
        );
        $cacheFile = RUNTIME_PATH.'filecache/' . $key . '.cache';

        

        return FW($cacheFile, _json_encode($datas));
    }

    //读取文件缓存
    public function getFileCache($key = ''){
        

        $cacheFile = RUNTIME_PATH.'filecache/'. $key . '.cache';
        if ( !is_file($cacheFile) ) {
            return array();
        }

        $cacheFile_str = file_get_contents($cacheFile);
        $cache_datas_arr = _json_decode($cacheFile_str);

        if( empty($cache_datas_arr) || $cache_datas_arr['time'] < $_ENV['_time'] ){
            @unlink($cacheFile);
            return array();
        }

        if( isset($cache_datas_arr['datas']) ){
            $datas = unserialize($cache_datas_arr['datas']);
        }else{
            $datas = array();
        }
        

        return $datas;
    }

    

}
