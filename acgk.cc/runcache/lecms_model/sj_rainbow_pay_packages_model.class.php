<?php
defined('ROOT_PATH') or exit;

class sj_rainbow_pay_packages extends model {

    function __construct() {
        $this->table = 'sj_rainbow_pay_packages';
        $this->pri = array('id');
        $this->maxid = 'id';
    }

    // 获取套餐列表
    public function list_arr($where, $orderby, $orderway, $start, $limit, $total) {
        try {
            // 优化大数据量翻页
            if($start > 1000 && $total > 2000 && $start > $total/2) {
                $orderway = -$orderway;
                $newstart = $total-$start-$limit;
                if($newstart < 0) {
                    $limit += $newstart;
                    $newstart = 0;
                }
                $list_arr = $this->find_fetch($where, array($orderby => $orderway), $newstart, $limit);
                $data = array_reverse($list_arr, TRUE);
            } else {
                $data = $this->find_fetch($where, array($orderby => $orderway), $start, $limit);
            }

            $statusArr = array(0=>'禁用', 1=>'启用');
            $typeArr = array(1=>'金币充值', 2=>'VIP开通');

            $rsData = array();
            if(!empty($data)){
                foreach($data as $k=>$v){
                    $v['status_text'] = isset($statusArr[$v['status']]) ? $statusArr[$v['status']] : '-';
                    $v['type_text'] = isset($typeArr[$v['type']]) ? $typeArr[$v['type']] : '-';
                    $v['create_time_text'] = empty($v['create_time']) ? '-' : date('Y-m-d H:i:s', $v['create_time']);
                    $v['amount_text'] = '￥'.$v['amount'];

                    if($v['type'] == 1) {
                        $v['value_text'] = $v['value'] . ' 金币';
                    } else {
                        $v['value_text'] = $v['value'] . ' 天VIP';
                    }

                    $rsData[] = $v;
                }
            }
            return $rsData;
        } catch (Exception $e) {
            // 如果出现异常，返回空数组
            return array();
        }
    }

    // 获取前台显示的套餐列表
    public function get_frontend_packages($type = 0) {
        $where = array('status' => 1);
        if ($type > 0) {
            $where['type'] = $type;
        }
        
        $data = $this->find_fetch($where, array('sort' => 1, 'id' => 1), 0, 0);
        
        $rsData = array();
        if(!empty($data)){
            foreach($data as $k=>$v){
                if($v['type'] == 1) {
                    $v['value_text'] = $v['value'] . ' 金币';
                    $v['desc'] = '充值 ' . $v['value'] . ' 金币';
                } else {
                    $v['value_text'] = $v['value'] . ' 天VIP';
                    $v['desc'] = '开通 ' . $v['value'] . ' 天VIP会员';
                }
                $v['amount_text'] = '￥' . $v['amount'];
                $rsData[] = $v;
            }
        }
        return $rsData;
    }

    // 根据ID获取单个套餐
    public function get($id) {
        $id = intval($id);
        if ($id <= 0) {
            return false;
        }

        $data = $this->find_fetch(array('id' => $id), array(), 0, 1);
        return $data ? current($data) : false;
    }

    // 根据ID获取套餐（别名方法，兼容不同调用方式）
    public function get_by_id($id) {
        return $this->get($id);
    }
}
