<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-10-09
 * Time: 15:21
 * Description: 后台首页控制器
 */

defined('ROOT_PATH') or exit;

include RUNTIME_CONTROL.'admin_control.class.php'; class index_control extends admin_control{

	// 后台首页
	public function index() {
        

        $cfg = $this->kv->xget('cfg');

        

        $this->assign('cfg', $cfg);
        $this->display();
	}

	// 后台登录
	public function login() {
		if(empty($_POST)) {


            $cfg = $this->kv->xget('cfg');
            !isset($cfg['admin_vcode']) && $cfg['admin_vcode'] = 0;

            //判断是否启用安全入口和验证安全密钥
            if(isset($cfg['admin_safe_entrance']) && $cfg['admin_safe_entrance'] && $cfg['admin_safe_auth'] && R('auth', 'G') != $cfg['admin_safe_auth']){


                exit;
            }

            //输出，登录页面需要判断是否要登录验证码
            $this->assign('cfg', $cfg);

            


			$this->display();
		}elseif(form_submit()) {
            


			$user = &$this->user;
			$username = R('username', 'P');
			$password = R('password', 'P');

			if($message = $user->check_username($username)) {
			    E(1, $message);
			}elseif($message = $user->check_password($password)){
                E(1, $message);
			}

			//登录验证码
            $cfg = $this->kv->xget('cfg');
			if(isset($cfg['admin_vcode']) && $cfg['admin_vcode']){
                $vcode = R('vcode', 'P');
                if(empty($vcode) || strtoupper($vcode) != _SESSION('adminlogin')){
                    E(1, lang('vcode_error'));
                }
            }

            


			// 防IP暴力破解
			$ip = &$_ENV['_ip'];
			if($user->anti_ip_brute($ip)) {
                E(1, lang('please_try_15_min'));
			}

			$data = $user->get_user_by_username($username);
            

			if($data && $user->verify_password($password, $data['salt'], $data['password'])) {
                


                //保存登录信息（cookie或session）
                $this->user->user_token_login(1, $data);

				// 更新登录信息
				$data['lastip'] = $data['loginip'];
				$data['lastdate'] = $data['logindate'];
				$data['loginip'] = ip2long($ip);
				$data['logindate'] = $_ENV['_time'];
				$data['logins']++;
				$user->update($data);

				// 删除密码错误记录
				$this->runtime->delete('password_error_'.$ip);

                

                E(0, lang('login_successfully'));
			}else{
                


				// 记录密码错误日志
				$log_password = '******'.substr($password, 6);
				log::write(lang('password_error')."：$username - $log_password", 'login_log.php');

				// 记录密码错误次数
				$user->password_error($ip);

                

                E(1, lang('username_password_error'));
			}
		}else{
            E(1, lang('form_invalid'));
		}
	}

	// 后台退出登录
	public function logout(){
        if($_POST){
            

            $this->user->user_token_logout(1);

            $res = array(
                'code'=>1, 'msg'=>lang('logout_successfully')
            );

            $res['login_url'] = $this->admin_safe_login_url();

            

            exit( json_encode($res) );
        }else{
            $res = array(
                'code'=>0, 'msg'=>lang('data_error')
            );
            exit( json_encode($res) );
        }
	}

    //生成验证码
    public function vcode(){
        $vcode = new vcode();
        $name = R('name','G');
        empty($name) && $name = 'adminlogin';

        $width = isset($_GET['width']) ? (int)$_GET['width'] : 115;
        $height = isset($_GET['height']) ? (int)$_GET['height'] : 44;
        


        return $vcode->get_vcode($name, $width, $height);
    }



}
