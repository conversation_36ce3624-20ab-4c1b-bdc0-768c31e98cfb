<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title><?php echo '后台管理'; ?></title>
	<meta name="keywords" content="<?php echo '后台管理'; ?> Power <NAME_EMAIL>">
	<meta name="description" content="<?php echo '后台管理'; ?>">
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta http-equiv="Access-Control-Allow-Origin" content="*">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="format-detection" content="telephone=no">
	<link rel="stylesheet" href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/lib/layui-v2.8.15/css/layui.css" media="all">
	<link rel="stylesheet" href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/css/layuimini.css?v=*******" media="all">
	<link rel="stylesheet" href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/css/themes/default.css" media="all">
	<link rel="stylesheet" href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
	<!--[if lt IE 9]>
	<script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
	<script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
	<![endif]-->
	<style id="layuimini-bg-color"></style>
</head>
<body class="layui-layout-body layuimini-all">

<div class="layui-layout layui-layout-admin">

	<div class="layui-header header">
		<div class="layui-logo layuimini-logo"></div>

		<div class="layuimini-header-content">
			<a>
				<div class="layuimini-tool"><i title="展开" class="fa fa-outdent" data-side-fold="1"></i></div>
			</a>

			<!--电脑端头部菜单-->
			<ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-menu-header-pc layuimini-pc-show">
			</ul>

			<!--手机端头部菜单-->
			<ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-mobile-show">
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-list-ul"></i> <?php echo '选择模块'; ?></a>
					<dl class="layui-nav-child layuimini-menu-header-mobile">
					</dl>
				</li>
			</ul>

			<ul class="layui-nav layui-layout-right">
				<li class="layui-nav-item" lay-unselect>
					<a href="../" target="_blank" title="<?php echo '网站首页'; ?>"><i class="fa fa-home"></i></a>
				</li>
				<li class="layui-nav-item" lay-unselect>
					<a href="javascript:;" data-refresh="刷新" data_title="<?php echo '刷新成功'; ?>" title="<?php echo '刷新'; ?>"><i class="fa fa-refresh"></i></a>
				</li>
				<li class="layui-nav-item mobile layui-hide-xs" lay-unselect>
					<a href="javascript:;" data-check-screen="full" title="<?php echo '全屏'; ?>"><i class="fa fa-arrows-alt"></i></a>
				</li>
				<li class="layui-nav-item layuimini-setting">
					<a href="javascript:;"><?php echo(isset($_user['username']) ? $_user['username'] : ''); ?></a>
					<dl class="layui-nav-child">
						<dd>
							<a href="javascript:;" layuimini-content-href="index.php?my-info" data-title="<?php echo '修改资料'; ?>" data-icon="fa fa-gears"><?php echo '修改资料'; ?></a>
						</dd>
						<dd>
							<a href="javascript:;" layuimini-content-href="index.php?my-password" data-title="<?php echo '修改密码'; ?>" data-icon="fa fa-gears"><?php echo '修改密码'; ?></a>
						</dd>
						<dd>
							<hr>
						</dd>
						<dd>
							<a href="javascript:;" class="login-out"><?php echo '退出'; ?></a>
						</dd>
					</dl>
				</li>
				<li class="layui-nav-item layuimini-select-bgcolor" lay-unselect>
					<a href="javascript:;" data-bgcolor="<?php echo '配色方案'; ?>" data-title="<?php echo '配色方案'; ?>"><i class="fa fa-ellipsis-v"></i></a>
				</li>
			</ul>
		</div>
	</div>

	<!--无限极左侧菜单-->
	<div class="layui-side layui-bg-black layuimini-menu-left">
	</div>

	<!--初始化加载层-->
	<div class="layuimini-loader">
		<div class="layuimini-loader-inner"></div>
	</div>

	<!--手机端遮罩层-->
	<div class="layuimini-make"></div>

	<!-- 移动导航 -->
	<div class="layuimini-site-mobile"><i class="layui-icon"></i></div>

	<div class="layui-body">

		<div class="layuimini-tab layui-tab-rollTool layui-tab" lay-filter="layuiminiTab" lay-allowclose="true">
			<ul class="layui-tab-title">
				<li class="layui-this" id="layuiminiHomeTabId" lay-id=""></li>
			</ul>
			<div class="layui-tab-control">
				<li class="layuimini-tab-roll-left layui-icon layui-icon-left"></li>
				<li class="layuimini-tab-roll-right layui-icon layui-icon-right"></li>
				<li class="layui-tab-tool layui-icon layui-icon-down">
					<ul class="layui-nav close-box">
						<li class="layui-nav-item">
							<a href="javascript:;"><span class="layui-nav-more"></span></a>
							<dl class="layui-nav-child">
								<dd><a href="javascript:;" layuimini-tab-close="current"><?php echo '关闭当前'; ?></a></dd>
								<dd><a href="javascript:;" layuimini-tab-close="other"><?php echo '关闭其他'; ?></a></dd>
								<dd><a href="javascript:;" layuimini-tab-close="all"><?php echo '关闭所有'; ?></a></dd>
							</dl>
						</li>
					</ul>
				</li>
			</div>
			<div class="layui-tab-content">
				<div id="layuiminiHomeTabIframe" class="layui-tab-item layui-show"></div>
			</div>
		</div>

	</div>
    <div class="layui-footer footer">
        <?php echo base64_decode('wqkgTGVjbXMu'); ?>
    </div>
</div>

<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/js/lay-config.js?v=2.0.0" charset="utf-8"></script>
<script type="text/javascript">
	layui.use(['jquery', 'layer', 'miniAdmin'], function () {
		var $ = layui.jquery,layer = layui.layer,miniAdmin = layui.miniAdmin;
		var admin_layout = "<?php echo(isset($cfg['admin_layout']) ? $cfg['admin_layout'] : ''); ?>";
		if(admin_layout == 1){
			var multiModule = true;
		}else{
			var multiModule = false;
		}
		var options = {
			iniUrl: "index.php?admin-init_navigation",
			urlHashLocation: false,
			bgColorDefault: false,
			multiModule: multiModule,
			menuChildOpen: false,
			loadingTime: 0,
			pageAnim: false,
			maxTabNum: 20
		};
		miniAdmin.render(options);

		$('.login-out').on("click", function () {
			$.post("index.php?index-logout-ajax-1",{do: 1},function(res){
				var login_url = res.login_url;
				layer.msg('<?php echo '退出成功'; ?>',{icon: 1}, function () {
					window.location = login_url;
				});
			},'json');
		});
	});
</script>
</body>
</html>
