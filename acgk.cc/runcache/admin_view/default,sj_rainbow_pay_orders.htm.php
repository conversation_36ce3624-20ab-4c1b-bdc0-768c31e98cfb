<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
            <legend>搜索</legend>
            <div>
                <form class="layui-form layui-form-pane">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">订单状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value="" selected="selected">全部</option>
                                    <option value="0">未支付</option>
                                    <option value="1">已支付</option>
                                    <option value="2">已取消</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">订单类型</label>
                            <div class="layui-input-inline">
                                <select name="type">
                                    <option value="" selected="selected">全部</option>
                                    <option value="1">金币充值</option>
                                    <option value="2">VIP开通</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">订单号</label>
                            <div class="layui-input-inline">
                                <input placeholder="订单号" autocomplete="off" type="text" class="layui-input" name="order_no" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">用户名</label>
                            <div class="layui-input-inline">
                                <input placeholder="用户名" autocomplete="off" type="text" class="layui-input" name="username" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn">
                                <i class="layui-icon"></i> 搜索
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>

        <script type="text/html" id="toolbar">
            <div class="layui-btn-container">
                <a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="refresh">刷新</a>
            </div>
        </script>

        <table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table>

        <script type="text/html" id="currentTableBar">
            <div class="layui-btn-group">
                {{# if(d.status == 0) { }}
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="repair">补单</a>
                {{# } }}
                <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="detail">详情</a>
            </div>
        </script>

        <script type="text/html" id="statusTpl">
            {{# if(d.status == 0) { }}
            <span class="layui-badge layui-bg-orange">{{d.status_text}}</span>
            {{# } else if(d.status == 1) { }}
            <span class="layui-badge layui-bg-green">{{d.status_text}}</span>
            {{# } else { }}
            <span class="layui-badge">{{d.status_text}}</span>
            {{# } }}
        </script>
    </div>
</div>

<script type="text/javascript">
layui.use(['form', 'layer', 'table'], function () {
    var layer = layui.layer, form = layui.form, table = layui.table;
    
    table.render({
        elem: '#data-table',
        url: 'index.php?sj_rainbow_pay-getorderslist-',
        height: 'full-145',
        toolbar: '#toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cellMinWidth: 50,
        escape: false,
        cols: [[
            {type: "checkbox", width: 50, fixed: 'left'},
            {field: 'order_no', width: 180, title: '订单号', align: 'center'},
            {field: 'username', width: 120, title: '用户名', align: 'center'},
            {field: 'type_text', width: 100, title: '类型', align: 'center'},
            {field: 'amount_text', width: 100, title: '金额', align: 'center'},
            {field: 'value', width: 100, title: '奖励', align: 'center'},
            {field: 'pay_type_text', width: 100, title: '支付方式', align: 'center'},
            {field: 'status', width: 100, title: '状态', align: 'center', templet: '#statusTpl'},
            {field: 'create_time_text', width: 170, title: '创建时间', align: 'center'},
            {field: 'pay_time_text', width: 170, title: '支付时间', align: 'center'},
            {title: '操作', width: 140, toolbar: '#currentTableBar', align: "center"}
        ]],
        limits: [20, 50, 100, 500, 1000],
        limit: 20,
        page: true
    });

    // 监听搜索操作
    form.on('submit(data-search-btn)', function (data) {
        table.reload('data-table', {
            page: {curr: 1},
            where: {
                status: data.field.status,
                type: data.field.type,
                order_no: data.field.order_no,
                username: data.field.username,
            }
        }, 'data');
        return false;
    });

    // 监听工具栏事件
    table.on('toolbar(data-table-filter)', function (obj) {
        if (obj.event === 'refresh') {
            table.reload('data-table');
        }
    });

    // 监听行工具事件
    table.on('tool(data-table-filter)', function (obj) {
        var data = obj.data;
        if (obj.event === 'repair') {
            layer.confirm('确定要补单吗？补单后将立即为用户到账相应的金币或VIP时长。', function (index) {
                var loadIndex = layer.load(2, {shade: [0.3, '#000']});
                $.ajax({
                    url: "index.php?sj_rainbow_pay-repairorder",
                    type: "POST",
                    data: {"order_no": data.order_no},
                    dataType: 'json',
                    success: function(result) {
                        layer.close(loadIndex);
                        if (!result.err) {
                            layer.msg('补单成功！', {icon: 1}, function() {
                                // 刷新表格数据
                                table.reload('data-table');
                            });
                        } else {
                            layer.alert('补单失败：' + result.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.alert('网络错误，请重试！', {icon: 2});
                    }
                });
                layer.close(index);
            });
        } else if (obj.event === 'detail') {
            layer.open({
                type: 1,
                title: '订单详情',
                area: ['600px', '400px'],
                content: '<div style="padding: 20px;">' +
                    '<p><strong>订单号：</strong>' + data.order_no + '</p>' +
                    '<p><strong>用户：</strong>' + data.username + '</p>' +
                    '<p><strong>类型：</strong>' + data.type_text + '</p>' +
                    '<p><strong>金额：</strong>' + data.amount_text + '</p>' +
                    '<p><strong>奖励：</strong>' + data.value + (data.type == 1 ? ' 金币' : ' 天VIP') + '</p>' +
                    '<p><strong>支付方式：</strong>' + data.pay_type_text + '</p>' +
                    '<p><strong>状态：</strong>' + data.status_text + '</p>' +
                    '<p><strong>创建时间：</strong>' + data.create_time_text + '</p>' +
                    '<p><strong>支付时间：</strong>' + data.pay_time_text + '</p>' +
                    (data.trade_no ? '<p><strong>交易号：</strong>' + data.trade_no + '</p>' : '') +
                    '</div>'
            });
        }
    });
});
</script>
</body>
</html>
