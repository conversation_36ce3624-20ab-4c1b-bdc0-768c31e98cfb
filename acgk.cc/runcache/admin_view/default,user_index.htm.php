<?php defined('APP_NAME') || exit('Access Denied'); 
?><!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo '后台管理'; ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/css/public.css" media="all">
    <link rel="stylesheet" href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>js/jquery.js" type="text/javascript"></script>
    <script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>layui/js/lay-config.js?v=1.0.4" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "<?php echo C('admin_lang'); ?>";
    </script>
    <script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>admin/js/admin.js" charset="utf-8"></script>
</head>
<div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
            <legend><?php echo '搜索'; ?></legend>
            <div>
                <form class="layui-form layui-form-pane">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"><?php echo '用户组'; ?></label>
                            <div class="layui-input-inline">
                                <?php echo(isset($groupidhtml) ? $groupidhtml : ''); ?>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label"><?php echo '用户名'; ?></label>
                            <div class="layui-input-inline">
                                <input placeholder="<?php echo '用户名'; ?>" autocomplete="off" type="text" class="layui-input" name="username" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label"><?php echo '邮箱'; ?></label>
                            <div class="layui-input-inline">
                                <input placeholder="<?php echo '邮箱'; ?>" autocomplete="off" type="text" class="layui-input" name="email" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label"><?php echo '用户ID'; ?></label>
                            <div class="layui-input-inline">
                                <input placeholder="<?php echo '用户ID'; ?>" autocomplete="off" type="number" class="layui-input" name="uid" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary"  lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> <?php echo '搜索'; ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>
        <script type="text/html" id="toolbar">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="add"><?php echo '添加'; ?></button>
                <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete"><?php echo '批量删除'; ?></button>
            </div>
        </script>
        <table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table>

        <script type="text/html" id="currentTableBar">
            <div class="layui-btn-group">
                <a class="layui-btn layui-btn-xs" lay-event="edit"><?php echo '编辑'; ?></a>
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="info"><?php echo '查看'; ?></a>
                <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="pwd"><?php echo '改密'; ?></a>
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete"><?php echo '删除'; ?></a>
            </div>
        </script>
    </div>
</div>
<script id="edit_code" type="text/html">
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo '新密码'; ?></label>
            <div class="layui-input-block">
                <input id="t_newpw" name="newpw" type="text" required="required" value="123456" class="layui-input" />
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    layui.use(['form','layer', 'table', 'miniTab'], function () {
        var layer = layui.layer, form = layui.form, table = layui.table, miniTab = layui.miniTab;

        table.render({
            elem: '#data-table',
            url: 'index.php?user-get_list-',
            height: 'full-145',
            toolbar: '#toolbar',
            defaultToolbar: ['filter', 'exports', 'print'],
            cellMinWidth: 50,
            escape: false,
            cols: [[<?php echo(isset($cols) ? $cols : ''); ?>]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 15,
            page: true
        });
        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('data-table', {
                page: {
                    curr: 1
                }
                , where: {
                    groupid: data.field.groupid,
                    uid: data.field.uid,
                    email: data.field.email,
                    username: data.field.username
                }
            }, 'data');

            return false;
        });
        /**
         * toolbar监听事件 table列表 头部的操作
         */
        table.on('toolbar(data-table-filter)', function (obj) {
            if (obj.event === 'add') {  // 监听添加操作
                miniTab.openNewTabByIframe({
                    href:"index.php?user-add",
                    title:"<?php echo '添加用户'; ?>",
                });
            } else if (obj.event === 'delete') {  // 监听删除操作
                var checkStatus = table.checkStatus('data-table')
                    , data = checkStatus.data;
                var len = data.length;
                if(len == 0){
                    layer.msg('<?php echo '请选择数据'; ?>',{icon:5});
                    return false;
                }else{
                    adminAjax.confirm('<?php echo '确定删除？'; ?>', function () {
                        var id_arr = [];
                        for (var i in data) {
                            id_arr[i] = data[i]['uid'];
                        }
                        adminAjax.postd("index.php?user-batch_del-ajax-1", {"id_arr": id_arr});
                    });
                }
            }
        });
        //监听单元格编辑
        table.on('edit(data-table-filter)', function(obj){
            var value = obj.value //得到修改后的值
                ,data = obj.data //得到所在行所有键值
                ,field = obj.field; //得到字段
            adminAjax.postd("index.php?user-set-ajax-1", {"uid":data.uid, "field":field, "value":value});
        });
        //监听每一行的操作
        table.on('tool(data-table-filter)', function (obj) {
            var data = obj.data;

            if (obj.event === 'edit') {
                miniTab.openNewTabByIframe({
                    href:"index.php?user-edit-uid-"+data.uid,
                    title:"<?php echo '编辑用户'; ?>",
                });
            } else if (obj.event === 'info') {
                var url = 'index.php?user-info-uid-'+data.uid;
                adminAjax.open('<?php echo '查看'; ?>',url,'95%','90%', function () {});
            } else if (obj.event === 'pwd') {
                adminAjax.confirm($('#edit_code').html(),function () {
                    var newpw = $("#t_newpw").val();
                    if(newpw == ''){
                        layer.msg('<?php echo '新密码不能为空'; ?>', {icon:5});
                    }else{
                        adminAjax.postd("index.php?user-pwd-ajax-1", {"newpw":newpw,"uid":data.uid},function (json) {
                            if( json.err == 0 ){
                                var icon = 1;
                            }else{
                                var icon = 5;
                            }
                            layer.msg(json.msg, {icon: icon});
                            if(json.err==0 && json.name != '') {
                                setTimeout(function(){
                                    window.location.href = json.name;
                                }, 1000);
                            }
                        });
                    }
                },function () {

                },'<?php echo '改密'; ?>');
            } else if (obj.event === 'delete') {
                adminAjax.confirm('<?php echo '确定删除？'; ?>', function () {
                    adminAjax.postd("index.php?user-del-ajax-1", {"uid":data.uid});
                });
            }
        });
    });
</script>
</body>
</html>
