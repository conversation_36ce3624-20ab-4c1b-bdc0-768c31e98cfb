/* AB模版网 做最好的织梦整站模板下载网站 Www.AdminBuy.Cn */

/* QQ：9490489 */

/* 仿站：Fang.AdminBuy.Cn */

/* 素材：Sc.AdminBuy.Cn */

@import url('iconfont.css');

* {

	margin: 0;

	padding: 0;

	font-family: yilong, Helvetica, Arial, "Open Sans", <PERSON><PERSON>, 'PingFang SC', 'Hiragino Sans GB', 'Source <PERSON>', <PERSON><PERSON>, 'Microsoft Yahei', sans-serif;

	-webkit-box-sizing: border-box;

	-moz-box-sizing: border-box;

	box-sizing: border-box;

}

body {

	font-size: 14px;

	background: #F6F6F6;

}

a {

	color: inherit;

	text-decoration: none;

	-webkit-transition: all .3s ease;

	-moz-transition: all .3s ease;

	-ms-transition: all .3s ease;

	-o-transition: all .3s ease;

	transition: all .3s ease

}

a:hover {

	color: #00A7EB

}

ul, li, p, h1, h2, h3 {

	list-style-type: none;

	margin: 0;

	padding: 0;

}

h1, h2, h3, h4, h5, h6 {

	font-weight: 400;

	line-height: 1.4;

	color: #273746;

	margin: 0;

}

.mask {

	width: 100%;

	height: 100%;

	position: fixed;

	top: 0;

	left: 0;

	display: none;

	background: #000;

	opacity: 0.5;

	-webkit-opacity: 0.5;

	z-index: 100;

}

@font-face {

	font-family: yilong;

	font-weight: 400

}

.red {

	color: #f54335

}

 @keyframes mymove {

from {

-webkit-transform: rotate(45deg);

transform:rotate(45deg);

}

to {

	-webkit-transform: rotate(225deg);

	transform: rotate(225deg);

}

}

@-webkit-keyframes mymove {

from {

-webkit-transform: rotate(45deg);

transform:rotate(45deg);

}

to {

	-webkit-transform: rotate(225deg);

	transform: rotate(225deg);

}

}

 @keyframes mymovex {

from {

-webkit-transform: rotate(0deg);

transform:rotate(0deg);

}

to {

	-webkit-transform: rotate(360deg);

	transform: rotate(360deg);

}

}

@-webkit-keyframes mymovex {

from {

-webkit-transform: rotate(0deg);

transform:rotate(0deg);

}

to {

	-webkit-transform: rotate(360deg);

	transform: rotate(360deg);

}

}

@-webkit-keyframes fade-zoom-in {

0% {

opacity:0;

-webkit-transform:scale(1.1);

transform:scale(1.1)

}

100% {

opacity:1;

-webkit-transform:scale(1);

transform:scale(1)

}

}

@-o-keyframes fade-zoom-in {

0% {

opacity:0;

-o-transform:scale(1.1);

transform:scale(1.1)

}

100% {

opacity:1;

-o-transform:scale(1);

transform:scale(1)

}

}

@keyframes fade-zoom-in {

0% {

opacity:0;

-webkit-transform:scale(1.1);

-o-transform:scale(1.1);

transform:scale(1.1)

}

100% {

opacity:1;

-webkit-transform:scale(1);

-o-transform:scale(1);

transform:scale(1)

}

}

.wrap {

	max-width: 1200px;

	width: 100%;

	margin: 0 auto;

	padding: 0 15px;

	position: relative;

}

.row {

	margin-left: -15px;

	margin-right: -15px;

}

.backtop {

	width: 50px;

	height: 50px;

	text-align: center;

	line-height: 50px;

	background: #282828;

	opacity: 0.6;

	position: fixed;

	right: 20px;

	bottom: 50px;

	cursor: pointer;

}

.backtop i {

	color: #fff;

}

.backtop.cd-is-visible {

	/* the button becomes visible */

	visibility: visible;

	opacity: 1;

}

.backtop.cd-fade-out {

	/* 如果用户继续向下滚动,这个按钮的透明度会变得更低 */

	opacity: .5;

}

.no-touch .backtop:hover {

	background-color: #e86256;

	opacity: 1;

}

.subbody {

	padding: 110px 0 0 0;

}

.topmenu {

	height: 91px;

	background: #fff;

	position: fixed;

	width: 100%;

	z-index: 11;

	top: 0;

	left: 0;

	border-bottom: 1px solid #dedede;

}

.logo {

	float: left;

	padding: 10px 0;

	width: 25%;

}

.logo img {

	height: 70px;

	max-width: 100%;

}

.menu {

	float: left;

	width: 75%;

}

li.closex {

	display: none;

}

.search {

	width: 50px;

	position: absolute;

	right: 0;

	top: 35px;

}

.search i {

	font-weight: bold;

	font-size: 20px;

	cursor: pointer;

}

.search-box {

	width: 100%;

	height: 100%;

	background: #fff;

	position: fixed;

	z-index: 100;

	top: 0;

	left: 0;

	display: none;

	font-size: 16px;

	-webkit-animation: fade-zoom-in .3s forwards;

	-o-animation: fade-zoom-in .3s forwards;

	animation: fade-zoom-in .3s forwards;

}

.search-close {

	width: 1000px;

	height: 60px;

	margin: 0 auto;

	padding: 100px 0 0 0;

}

.search-close i {

	float: right;

	font-size: 40px;

	color: #666;

	cursor: pointer;

}

.search-close i:hover {

	animation: mymovex 0.5s 1;

	-webkit-animation: mymovex 0.5s 1;

}

dl.se {

	overflow: hidden;

}

dl.se dt {

	width: 500px;

	float: left;

}

dl.se dt input {

	width: 100%;

	height: 54px;

	padding: 0 10px;

	border: 1px solid #dedede;

	border-radius: 5px;

}

dl.se dd button {

	width: 100%;

	border: 0;

	background: #000;

	color: #fff;

	height: 56px;

	border-radius: 0 5px 5px 0;

}

dl.se dd {

	width: 100px;

	float: right;

}

.search-con {

	width: 600px;

	margin: 0 auto;

	padding-top: 20px;

}

.search-tips {

	line-height: 50px;

	padding-top: 30px;

}

.search-as {

	line-height: 34px;

}

.search-as a {

	padding: 0 20px;

	border: 1px solid #dedede;

	display: inline-block;

	margin-right: 10px;

	margin-bottom: 10px;

	white-space: nowrap;

	border-radius: 5px;

	color: #ccc;

}

.search-as a:hover {

	color: #666;

	border: 1px solid #666;

}

/*menu*/

#nav {

	width: 800px;

	display: block;

	margin-top: 25px;

}

#nav .mainlevel {

	float: left;

	border-right: 1px solid #fff;

	position: relative;

	z-index: 10;

}

#nav .mainlevel a {

	color: #000;

	text-decoration: none;

	line-height: 40px;

	display: block;

	padding: 0 20px;

	font-size: 17px;

}

#nav .mainlevel a i {

	width: 6px;

	height: 6px;

	border-bottom: 2px solid #666;

	border-right: 2px solid #666;

	display: block;

	-webkit-transform: rotate(45deg);

	transform: rotate(45deg);

	float: right;

	margin-top: 16px;

	margin-left: 10px;

}

#nav .mainlevel a:hover, #nav .mainlevel a.hover {

	color: #19B5FE;

	text-decoration: none;

}

#nav .mainlevel a:hover i {

	animation: mymove 0.2s 1;

	-webkit-animation: mymove 0.2s 1;

	transform: rotate(225deg);

	margin-top: 19px;

	border-bottom: 2px solid #19B5FE;

	border-right: 2px solid #19B5FE;

}

#nav .mainlevel ul {

	display: none;

	position: absolute;

	background: #fff;

	box-shadow: 1px 3px 5px #888888;

	border-top: 0;

}

#nav .mainlevel li {

	background: #fff;

	color: #19B5FE;

	font-size: 15px;

	width: 140px;/*IE6 only*/

}

.main {

	clear: both;

	padding: 120px 0 0px 0;

	overflow: hidden;

}

.banner {

	width: 75%;

	float: left;

	padding: 0 15px;

}

.banner-info {

	position: absolute;

	z-index: 12;

}

.swiper-pagination-bullet {

	width: 25px;

	height: 3px;

	border-radius: 0;

	background: #fff;

}

.swiper-slide {

	float: left;

	width: 100%;

	height: 430px;

	background-repeat: no-repeat;

	background-position: center 0;

	background-size: auto 430px;

}

.swiper-button-prev, .swiper-button-next {

	display: none;

	opacity: 0.6;

	width: 36px;

	height: 60px;

}

.swiper-button-prev {

	background: url(../images/l1.png) no-repeat;

}

.swiper-button-next {

	background: url(../images/r1.png) no-repeat;

}

.top-news {

	width: 25%;

	float: left;


}

.top-news2 {

	width: 100%;

	float: left;

	margin-bottom: 30px;

}

.top-news-box {

	background: #FFF;

	-webkit-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	-moz-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

}

.top-news h2, .top-news2 h2 {

	font-size: 20px;

	padding: 0 10px;

	font-weight: normal;

	line-height: 54px;

	border-top: 2px solid #000;

}

.top-img {

	padding: 0;

}

.top-img img {

	max-width: 100%;

}

ul.topnews li a {

	line-height: 39px;

	display: block;

	height: 39px;

	overflow: hidden;

	padding: 0 10px;

	font-size: 15px;

}

ul.topnews li a:hover {

	background: #333;

	color: #fff;

}

ul.topnews li a i {

	font-size: 10px;

	margin-right: 15px;

	float: left;

	font-weight: bold;

}

.hotnews {

	clear: both;

	padding-top: 15px;

	overflow: hidden;

}

.hotnewlist {

	width: 33.333333%;

	float: left;

	height: 200px;

	padding: 0 15px;

}

.hotdiv {

	width: 100%;

	height: 100%;

	position: relative;

	background-size: cover;

	background-repeat: no-repeat;

	background-position: center 0;

	-moz-box-sizing: border-box;

	-webkit-box-sizing: border-box;

	box-sizing: border-box;

	display: inline-table;

	overflow: hidden;

}

.overlay {

	width: 100%;

	height: 100%;

	background: -webkit-linear-gradient(270deg, rgba(0,0,0,.01) 2%, rgba(0,0,0,.95) 100%);

	background: linear-gradient(180deg, rgba(0,0,0,.01) 2%, rgba(0,0,0,.95) 100%);

	-webkit-transition: opacity .4s ease-in-out;

	transition: opacity .4s ease-in-out;

	position: absolute;

}

.hotnewlist:hover .overlay {

	opacity: 0.2;

}

.title {

	position: absolute;

	text-shadow: 1px 1px 2px #000;

	bottom: 0;

	padding: 20px 18px;

	left: 0;

	right: 0;

}

.title span {

	padding: 4px 10px;

	background-color: #19B5FE;

	color: #fff;

	font-size: 13px;

	margin: 0 5px 5px 0;

	border-radius: 2px;

	display: inline-block;

	text-shadow: none;

	line-height: 1;

}

.title h3 {

	color: #fff;

	display: block;

	font-size: 16px;

	max-height: 48px;

	overflow: Hidden;

	font-weight: normal;

}

.swiper-slide .title h3.f20 {

	font-size: 25px;

}

.swiper-slide .title {

	padding: 0 10px 30px 18px;

}

.mainbody {

	clear: both;

	padding-top: 20px;

	overflow: hidden;

}

.left {

 width: 75%;

 float: left;

 padding: 0 15px;

}

.right {

 width: 25%;

 float: right;

}

.post-nav {

	padding: 0 10px;

	margin-bottom: 15px

}

.post-nav span {

	cursor: pointer;

	position: relative;

	background: #FFF;

	display: inline-block;

	border-radius: 30px;

	padding: 11px 25px;

	margin-top: 10px;

	margin-right: 25px;

	margin-bottom: 10px;

	border: 1px solid #eae9e9;

	-moz-transition: all .3s ease-in-out;

	-webkit-transition: all .3s ease-in-out;

	transition: all .3s ease-in-out;

	-webkit-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	-moz-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	box-shadow: 0 0 10px 0 rgba(146,146,146,.1)

}

.post-nav span.current, .post-nav span:hover {

	background: #282828;

	color: #FFF;

	border: 1px solid #282828

}

.morebtn {

	text-align: center;

	clear: both;

	padding: 30px 0;

}

.morebtn button {

	cursor: pointer;

	border: 0;

	width: 150px;

	background: #00a7eb;

	border-radius: 2px;

	font-size: 16px;

	color: #fff;

	height: 46px;

	line-height: 46px;

	display: inline-block;

	margin: 0 8px;

	-moz-transition: all .3s ease-in-out;

	-webkit-transition: all .3s ease-in-out;

	transition: all .3s ease-in-out;

}

.morebtn button:hover {

	background: #0298d4

}

.footer {

	width: 100%;

	position: relative;

	clear: both;

	background-color: #282828;

	margin-top: 30px;

	padding: 30px 0 30px 0;

}

.footer .footer-copyright {

	float: left;

	font-size: 15px;

	line-height: 1.6

}

.footer .footer-copyright a {

	color: #ddd

}

.footer .footer-copyright a:hover {

	color: #FFF;

	color: #ddd

}

.footer .footer-social {

	float: right;

	margin-top: 0;

	position: relative

}

.footer .footer-social a {

	font-size: 20px;

	margin-left: 10px

}

.footer .footer-social a:first-child {

	margin-left: 0

}

.footer .copyright-footer p {

	color: #828282

}

.footer .copyright-footer a {

	color: #929292

}

.footer .links-footer {

	font-size: 10px;

	color: #353e4a;

	padding: 18px 0 0;

	margin-top: 20px;

}

.footer .links-footer a, .footer .links-footer span {

	color: #696969;

	line-height: 1.2;

	margin: 0 5px 0 0;

	font-size: 12px

}

.footer .nav-footer {

	margin-bottom: 20px

}

.footer .nav-footer a {

	color: #e0e0e0;

	margin-right: 10px;

	font-size: 15px

}

.footer .footer-box .nav-footer a:hover {

	color: #FFF

}

.footer .footer-box .nav-footer span {

	margin: 0 10px

}

.social-footer a i {

	font-size: 20px;

	color: #fff;

}

.social-footer {

	float: right

}

.social-footer a {

	position: relative;

	float: left;

	width: 40px;

	height: 40px;

	line-height: 40px;

	background-color: #343a40;

	text-align: center;

	border-radius: 20px;

	margin: 0 5px;

	color: #FFF;

	font-size: 16px

}

.social-footer a i {

	color: #c5c5c5

}

.social-footer a:hover i {

	color: #FFF

}

.social-footer a.ketangdibu:hover {

	background-color: #f74864

}

.social-footer a.taobaodibu:hover {

	background-color: #FF4200

}

.social-footer a.mailii i {

	font-size: 16px;

}

.social-footer a.mailii:hover {

	background-color: #e64c4c

}

.social-footer a.wangxiaodibu:hover {

	background-color: #00C5FF

}

.social-footer a.wxii:hover {

	background-color: #35a999

}

.items {

	position: relative;

	overflow: hidden;

	display: block;

	margin-bottom: 15px;

	background: #FFF;

	-webkit-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	-moz-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

}

.content-box {

	overflow: hidden;

	padding: 32px 27px;

}

.posts-gallery-img {

	position: relative;

	float: left;

	width: 231.98px;

	max-height: 173.98px;

	height: auto;

	overflow: hidden

}

.posts-gallery-img img {

	width: 100%;

	min-height: 173.98px;

	transition: 0.5s;

}

.posts-gallery-img img:hover:hover {

	transform: scale(1.1);

	-webkit-transform: scale(1.1);

	-moz-transform: scale(1.1);

	-ms-transform: scale(1.1);

	-o-transform: scale(1.1);

}

.posts-gallery-content {

	margin-left: 245px

}

.posts-gallery-content h2 {

	position: relative;

	margin: 0 0 20px 0;/*!  */

	font-size: 22px;

	max-height: 61.6px;

	overflow: hidden

}

.posts-gallery-content .posts-gallery-text {

	line-height: 1.6;

	margin-bottom: 20px;

	color: #566573

}

.post-style-tips {

	position: absolute;

	bottom: 32px;

	right: 28px

}

.post-style-tips a {

	padding: 6px 10px;

	line-height: 1;

	color: #FFF;

	background: #378DF7;

	display: inline-block;

	font-size: 13px

}

.post-style-tips span a:hover {

	background: #273746

}

.post-images-item {

	margin-bottom: 20px

}

.post-images-item ul {

	overflow: hidden;

	margin-left: -10px

}

.post-images-item ul li {

	float: left;

	width: 33.3333%

}

.post-images-item ul li .overlay {

	opacity: 0

}

.post-images-item ul li a:hover .overlay {

	opacity: .3

}

.post-images-item ul li .image-item {

	margin-left: 10px;

	max-height: 174px;

	position: relative;

	overflow: hidden

}

.post-images-item ul li a img {

	width: 100%;

	height: auto;

	min-height: 174px

}

.posts-gallery-text {

}

.posts-default-info {

	position: relative;

	display: inline-block

}

.posts-gallery-info {

	position: absolute;

	bottom: 32px

}

.posts-default-info ul li {

	font-size: 12px;

	letter-spacing: -.2px;

	float: left;

	padding: 0;

	margin: 0 10px 0 0;

	color: #748594;

	position: relative;

	line-height: 1.5

}

.posts-default-info ul li a {

	color: #748594

}

.posts-default-info ul li.post-author {

	padding-left: 30px;

	padding-top: 2px;

}

.posts-default-info ul li .avatar {

	position: absolute;

	top: -2px;

	left: 0;

	width: 25px

}

.posts-default-info ul li .avatar img {

	border: 1px solid #e5e5e5;

	border-radius: 50%;

	padding: 1px;

	width: 25px;

	height: 25px

}

.posts-default-info ul li.ico-cat i {

	font-size: 14px;

}

.posts-default-info ul li.ico-time i {

	font-size: 14px;

}

.posts-default-info ul li.ico-eye i {

	font-size: 14px;

}

.posts-default-info ul li.ico-like i {

	font-size: 12px;

}

.posts-default-info ul li.ico-like {

	padding-top: 2px;

}

.post-entry-categories {

	margin-bottom: 15px

}

.post-entry-categories a {

	padding: 4px 10px;

	background-color: #19B5FE;

	color: #fff;

	font-size: 12px;

	line-height: 1.4;

	font-weight: 400;

	margin: 0 5px 5px 0;

	border-radius: 2px;

	display: inline-block

}

.post-entry-categories a:nth-child(5n) {

	background-color: #4A4A4A

}

.post-entry-categories a:nth-child(5n+1) {

	background-color: #ff5e5c

}

.post-entry-categories a:nth-child(5n+2) {

	background-color: #ffbb50

}

.post-entry-categories a:nth-child(5n+3) {

	background-color: #1ac756

}

.post-entry-categories a:nth-child(5n+4) {

	background-color: #19B5FE

}

.post-entry-categories a:hover {

	background-color: #1B1B1B;

	color: #FFF

}

.posts-default-title {

	position: relative;

	margin: 0 0 30px

}

.posts-default-title h2 {

	position: relative;/*!  */

	font-size: 22px;

	margin: 0 0 25px;

	padding: 0 0 20px

}

.posts-default-title h2:after {

	content: "";

	background-color: #19B5FE;

	left: 0;

	width: 50px;

	height: 2px;

	bottom: -2px;

	position: absolute

}

.content-box {

	position: relative;

	line-height: normal

}

.content-box .posts-text {

	line-height: 1.6;

	margin-bottom: 20px;

	color: #566573

}

.widget {

	margin-bottom: 15px;

	clear: both;

	position: relative;

	overflow: hidden;

	background: #FFF;

	-webkit-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	-moz-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	padding: 27px

}

.widget widget_cjtz_img img {

	max-width: 100%;

	height: auto;

	border-radius: 0

}

.widget h3 {

	font-size: 18px;

	color: #282828;

	font-weight: 400;

	margin: 0;

	text-transform: uppercase;

	padding-bottom: 18px;

	margin-bottom: 28px;

	position: relative

}

.widget h3:after {

	content: "";

	background-color: #19B5FE;

	left: 0;

	width: 50px;

	height: 2px;

	bottom: -2px;

	position: absolute

}

.widget_ad {

	margin-bottom: 30px;

	clear: both;

	position: relative;

	overflow: hidden;

	background: #FFF;

	-webkit-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	-moz-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

}

.recent-posts-widget li {

	position: relative;

	margin-top: 20px;

	overflow: hidden

}

.recent-posts-widget li .recent-posts-img {

	float: left;

	width: 100px;

	height: auto;

	max-height: 75px;

	position: relative/*!  */

}

.recent-posts-widget li .recent-posts-img img {

	width: 100px;

	height: auto

}

.recent-posts-widget li .recent-posts-title {

	margin-left: 115px

}

.recent-posts-widget li .recent-posts-title h4 {

	font-size: 15px;

	height: 40px;

	overflow: hidden;

	margin-bottom: 10px

}

.recent-posts-widget li .recent-posts-title span {

	font-size: 12px;

	color: #9A9A9A

}

.widge_tags a {

	text-transform: uppercase;

	-webkit-transition: all .3s ease;

	-o-transition: all .3s ease;

	transition: all .3s ease;

	display: inline-block;

	vertical-align: top;

	font-size: 14px;

	line-height: 20px;

	padding: 4px 17px;

	margin: 0 10px 7px 0;

	border: 1px solid #e0e0e0;

	border-radius: 2px;

	color: rgba(0,0,0,.66);

	background-color: rgba(255,255,255,.97)

}

.widge_tags a:nth-child(8), .widge_tags a:nth-child(15), .widge_tags a:nth-child(35) {

	border-color: #fdb2b2;

	color: #fd6161

}

.widge_tags a:nth-child(8) i, .widge_tags a:nth-child(15) i, .widge_tags a:nth-child(35) i {

	position: relative;

	top: -1px

}

.widge_tags a:hover {

	color: #FFF;

	background: #1B1B1B;

	border-color: #1B1B1B

}

.social-widget-link {

	position: relative;

	margin-bottom: 10px;

	position: relative;

	display: block;

	font-size: 13px;

	background: #fff;

	color: #525252;

	line-height: 40px;

	padding: 0 14px;

	border: 1px solid #DDD;

	border-radius: 2px

}

.social-widget-link-count i {

	margin-right: 9px;

	font-size: 17px;

	vertical-align: middle

}

.social-widget-link-title {

	position: absolute;

	top: -1px;

	right: -1px;

	bottom: -1px;

	width: 100px;

	text-align: center;

	background: rgba(255,255,255,.08);

	transition: width .3s;

	border-radius: 0 3px 3px 0

}

.social-widget-link:hover .social-widget-link-title {

	width: 116px

}

.social-widget-link a {

	position: absolute;

	top: 0;

	left: 0;

	right: 0;

	bottom: 0

}

.social-link-ketang {

	border-color: rgba(236,61,81,.39)

}

.social-link-ketang i {

	color: #ec3d51;

	font-size: 22px;

}

.social-link-ketang .social-widget-link-title {

	background-color: #ec3d51;

	color: #fff

}

.social-link-taobao-wangxiao {

	border-color: rgba(255,66,0,.39)

}

.social-link-taobao-wangxiao i {

	color: #FF4200;

	font-size: 22px;

}

.social-link-taobao-wangxiao .social-widget-link-title {

	background-color: #FF4200;

	color: #fff

}

.social-link-email {

	border-color: rgba(42,179,154,.4)

}

.social-link-email i {

	color: #2ab39a

}

.social-link-email .social-widget-link-title {

	background-color: #2ab39a;

	color: #fff

}

.social-link-wangxiao {

	border-color: rgba(18,170,232,.39)

}

.social-link-wangxiao i {

	color: #12aae8;

	font-size: 22px;

}

.social-link-wangxiao .social-widget-link-title {

	background-color: #12aae8;

	color: #fff

}

.social-link-wechat {

	border-color: rgba(25,152,114,.4)

}

.social-link-wechat i {

	color: #199872;

	font-size: 22px;

}

.social-link-wechat .social-widget-link-title {

	background-color: #199872;

	color: #fff

}

ul.hot-article li {

	position: relative;

	height: 175px;

	margin-bottom: 30px;

}

ul.hot-article li img {

	width: 100%;

	height: 175px;

}

ul.hot-article li img:hover {

	opacity: 0.6;

}

ul.hot-article li .tits {

	position: absolute;

	bottom: 0;

	left: 0;

	right: 0;

	background: rgba(0,0,0,.5);

	padding: 10px 15px;

}

ul.hot-article li .tits h4 a {

	color: #f4f4f4;

	line-height: 24px;

}

ul.hot-article li a.img .icon-fenxiang {

	font-size: 50px;

	position: absolute;

	z-index: 600;

	top: 35%;

	left: 45%;

	opacity: 0;

	color: #fff;

	-webkit-transition: opacity .35s, -webkit-transform .35s;

	transition: opacity .35s, transform .35s;

	-webkit-transform: translate3d(-60px, 60px, 0);

	transform: translate3d(-60px, 60px, 0)

}

ul.hot-article li:hover a.img .icon-fenxiang {

	opacity: 1;

	-webkit-transform: translate3d(0, 0, 0);

	transform: translate3d(0, 0, 0)

}

ul.hot-article li:hover .tits {

	display: none;

}

.f-weixin-dropdown {

	position: fixed;

	height: 100%;

	width: 100%;

	top: 0;

	left: 0;

	background: rgba(0,0,0,.9);

	z-index: 9998;

	display: none;

	-webkit-animation: fade-zoom-in .3s forwards;

	-o-animation: fade-zoom-in .3s forwards;

	animation: fade-zoom-in .3s forwards;

	display: none;

	-webkit-backface-visibility: hidden

}

.f-weixin-dropdown .qcode img {

	width: 180px;

	height: auto

}

.f-weixin-dropdown.is-visible {

	display: block

}

.f-weixin-dropdown .tooltip-weixin-inner {

	max-width: 200px;

	padding: 0 20px;

	margin: auto;

	text-align: center;

	position: absolute;

	width: 100%;

	left: 0;

	right: 0;

	height: 260px;

	top: -150px;

	bottom: 0

}

.f-weixin-dropdown .tooltip-weixin-inner h3 {

	color: #FFF;

	font-size: 32px;

	font-weight: 300;

	margin-bottom: 10px;

	line-height: 1.3

}

.f-weixin-dropdown .close_tip {

	position: fixed;

	z-index: 99999;

	top: 80px;

	right: 80px;

	color: #BDBDBD;

	font-size: 36px;

	cursor: pointer;

	-webkit-transition: all .4s ease;

	transition: all .4s ease

}

.topad {

	padding: 0 15px;

	margin-bottom: 15px;

}

.topad img {

	width: 100%;

	height: auto;

}

.left-ad {

	margin-bottom: 15px;

}

.left-ad img {

	width: 100%;

	height: auto;

}

.article-con {

	padding: 25px;

	background: #fff;

	-webkit-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	-moz-box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	box-shadow: 0 5px 10px 0 rgba(146,146,146,.1);

	margin-bottom: 2px;

}

.postion {

	line-height: 30px;

	margin-bottom: 30px;

}

.postion i {

	font-size: 10px;

	padding: 0 2px;

	color: #000;

}

.art-con h1 {

	position: relative;

	margin-bottom: 15px;

	font-size: 22px;

	line-height: 2;

	display: block;

	font-weight: 400;

	margin: 0 0 25px;

	padding: 0 0 25px;

	border-bottom: 1px solid #e7e7e7

}

.art-con h1:after {

	content: "";

	background-color: #19B5FE;

	left: 0;

	width: 50px;

	height: 2px;

	bottom: -2px;

	position: absolute

}

.art-txt p a {

	text-decoration: none;

	border-bottom: 1px solid #3d464d;

	padding-bottom: 2px;

}

.art-txt {

	padding: 10px 0;

	/*line-height: 28px;*/

	font-size: 16px;

}

.art-txt p {

	

}

.art-txt img {

	max-width: 100% !important;

	height: auto !important;

}

.yinyong {

	margin: 15px 0;

	padding: 10px 0;

	border-bottom: 1px solid #eaeaea

}

.yinyong quote {

	font-size: 21px;

	color: #BABABA;

	display: inline-block

}

.yinyong quote p {

	margin-bottom: 0!important

}

.art-txt p a.download-button {

	color: #FFF;

	background-color: #32a5e7;

	border: 2px solid #32a5e7;

	margin-right: 20px;

	text-indent: 0

}

.art-txt p a.download-button:hover {

	color: #32a5e7;

	background-color: #FFF;

	border-color: #32a5e7

}

.btn {

	display: inline-block;

	margin-bottom: 0;

	font-weight: 400;

	text-align: center;

	vertical-align: middle;

	-ms-touch-action: manipulation;

	touch-action: manipulation;

	cursor: pointer;

	border: 1px solid transparent;

	white-space: nowrap;

	padding: 6px 12px;

	font-size: 13px;

	line-height: 1.42857143;

	-moz-user-select: none;

	-ms-user-select: none;

	user-select: none;

	border-radius: 2px;

	-webkit-transition: all .3s ease;

	-o-transition: all .3s ease;

	transition: all .3s ease

}

.art-txt a.btn {

	display: inline-block;

	margin-bottom: 0;

	font-weight: 400;

	text-align: center;

	vertical-align: middle;

	cursor: pointer;

	background-image: none;

	border: 1px solid transparent;

	white-space: nowrap;

	padding: 8px 25px 9px 25px;

	font-size: 14px;

	line-height: 1.42857143;

	-webkit-user-select: none;

	-moz-user-select: none;

	-ms-user-select: none;

	user-select: none;

	margin: 10px 0 20px;

	margin-right: 20px

}

.art-txt a.btn i {

	margin-right: 8px

}

.ad01 {

	clear: both;

	padding: 10px 0px 0px 0px;

	overflow: hidden;

}

.ad01_1 {

	float: left;

	width: 336px;

}

.ad01_2 {

	float: right;

	width: 336px;

}

.shareBox {

	text-align: center;

	padding: 30px 0 35px;

	position: relative

}

.shareBox p {

	padding-bottom: 0

}

.shareBox .sharebtn {

	width: 130px;

	height: 40px;

	line-height: 40px;

	border: 1px solid #F74840;

	box-shadow: none;

	background: #fff;

	display: inline-block;

	text-align: center;

	margin: 0 2px;

	color: #F74840;

	font-size: 14px;

	text-decoration: none;

	border-radius: 0;

	-webkit-transition: all .3s ease;

	-o-transition: all .3s ease;

	transition: all .3s ease;

	border-radius: 2px;

	-moz-border-radius: 2px;

	-webkit-border-radius: 2px

}

.shareBox .like .icon-heart-filled {

	display: none;

	margin-top: -3px

}

.shareBox .like.current {

	background-color: #F74840;

	color: #fff

}

.shareBox .like i {

	vertical-align: middle;

	display: inline-block;

	margin-top: -3px;

	display: inline-block

}

.shareBox .like.current .icon-heart-filled {

	display: inline-block;

	-webkit-animation: waver .3s linear 1;

	-moz-animation: waver .3s linear 1;

	-o-animation: waver .3s linear 1;

	animation: waver .3s linear 1

}

.shareBox .like.current .icon-heart {

	display: none

}

.panel-reward {

	position: relative;

	padding: 30px 0 0;

	display: none

}

.panel-reward ul li {

	display: inline-block;

	text-align: center;

	margin: 0 10px;

	padding: 5px;

	border: 1px solid #87ddff;

	border-radius: 2px;

	-moz-border-radius: 2px;

	-webkit-border-radius: 2px

}

.panel-reward ul .weixinpay {

	border-color: #51C332

}

.panel-reward ul li img {

	width: 120px;

	height: auto

}

.panel-reward ul li b {

	display: block;

	font-weight: 400;

	margin-top: 3px

}

.action-share {

	padding: 30px 0 0;

	display: none;

}

.bdsharebuttonbox {

	margin: 0 auto;

	width: 190px

}

.shareBox .J_showAllShareBtn {

	color: #3496E6;

	border-color: #3496E6

}

.shareBox .sharebtn:hover {

	background-color: #F74840;

	color: #fff

}

.shareBox .publicity-btn {

	background-color: #378DF7;

	color: #FFF;

	border-color: #378DF7

}

.shareBox .publicity-btn:hover {

	background-color: #2c6fc1;

	border-color: #2c6fc1

}

.shareBox .J_showAllShareBtn:hover {

	background-color: #3496E6;

	color: #fff

}

.shareBox .pay-author {

	border-color: #51C332;

	color: #51C332

}

.shareBox .pay-author:hover {

	background: #51C332;

	color: #FFF

}

.pronext {

	clear: both;

	padding: 2px 0;

	overflow: hidden;

}

.propage, .nextpage {

	width: 50%;

	float: left;

	line-height: 26px;

}

.propage span, .nextpage span {

	color: #999;

}

.nextpage {

	text-align: right;

	border-left: 1px solid #efefef;

}

h3.subtitle {

	font-size: 25px;

	color: #282828;

	font-weight: 400;

	margin: 0;

	/*margin-bottom: 26px;*/

	text-transform: uppercase;

	padding-bottom: 15px;

	position: relative

}

h3.subtitle:after {

	content: "";

	background-color: #19B5FE;

	left: 0;

	width: 50px;

	height: 3px;

	bottom: -2px;

	position: absolute

}

h3.subtitle em {

	font-style: normal;

	color: #666;

}

ul.sub-news {

	overflow: hidden;

	margin-left: -10px;

}

ul.sub-news li {

	width: 50%;

	float: left;

	padding-right: 10px;

}

ul.sub-news li a {

	line-height: 40px;

	display: block;

	height: 40px;

	overflow: hidden;

	padding: 0 10px;

	font-size: 15px;

}

ul.sub-news li a:hover {

	background: #333;

	color: #fff;

}

ul.sub-news li a i {

	font-size: 10px;

	margin-right: 15px;

	float: left;

	font-weight: bold;

}

/*下拉列表缩略图文章样式自适应small start
.like-posts li {
	position: relative;
	margin-top: 20px;
	overflow: hidden;
	display: flex;
}
.like-posts li .recent-posts-img {
	
	flex: 0 0 30%;
	width: 120px;
	height: auto;
	max-height: 175px;
	
}
.like-posts li .recent-posts-img img {
	width: 100%;
	height: 80px
}
.like-posts li .recent-posts-title {
	height:100%;
	padding-left: 20px;
}
.like-posts li .recent-posts-title h4 {
	font-size: 15px;
	height: 40px;
	margin-top: 5px;
	margin-bottom: 10px;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
}
.like-posts li .recent-posts-title .info {
	font-size: 12px;
	color: #9A9A9A;
	position: absolute;
	bottom: 10px;
}
.like-posts li .recent-posts-title .info .date{
	padding-right: 10px;
}
.like-posts li .recent-posts-title .info i {
    font-size: 14px;
}
@media (max-width:768px) {
	.like-posts li .recent-posts-img{
		flex: 0 0 40%;
		width: 40%;
	}
	.like-posts li .recent-posts-title {
		padding-left: 10px;
	}
	.like-posts li .recent-posts-title h4{
		margin-top: 0;
		margin-bottom: 30px;
		font-size: 14px;
	}
	.like-posts li .recent-posts-img{
		width: 100px;
	}
}
下拉列表缩略图文章样式自适应small end*/
/*small start*/
.like-posts li {
	position: relative;
	margin-top: 20px;
	overflow: hidden;
	display: flex;
}
.like-posts li .recent-posts-img {
	flex: 0 0 250px;
	width: 250px;
	height: auto;
	max-height: 175px;
}
.like-posts li .recent-posts-img img {
	width: 100%;
	height: 175px;
}
.like-posts li .recent-posts-title {
	height:100%;
	padding-left: 20px;
}
.like-posts li .recent-posts-title h4 {
	font-size: 22px;
	height: 40px;
	margin-top: 5px;
	margin-bottom: 10px;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
}
.like-posts li .recent-posts-title .info {
	font-size: 12px;
	color: #9A9A9A;
	position: absolute;
	bottom: 10px;
}
.like-posts li .recent-posts-title .info .date{
	padding-right: 10px;
}
.like-posts li .recent-posts-title .info i {
    font-size: 14px;
}
@media (max-width:768px) {
	.like-posts li .recent-posts-img{
		flex: 0 0 120px;
		width: 120px;
	}
	.like-posts li .recent-posts-title {
		padding-left: 10px;
	}
	.like-posts li .recent-posts-title h4{
		margin-top: 0;
		margin-bottom: 30px;
		font-size: 14px;
	}
	.like-posts li .recent-posts-img  img{
		height: 80px;
	}
}
/*small end*/

/*网分页样式*/

.pages {

	clear: both;

	margin: 20px auto 20px auto;

	padding-top: 10px;

	overflow: hidden;

	text-align: center;

	font-size: 14px;

}

.pages ul li {

	display: inline-block;

	border: 1px solid #ccc;

	padding: 6px 15px;

	margin: 0 1px;

	line-height: 24px;

	background: #fff;

	color: #999;

	border-radius: 2px

}

.pages ul li:hover {

	background: #00A7EB;

	color: #fff;

	border: 1px solid #00A7EB

}

.pages ul li:hover a {

	color: #fff;

}

.pages ul li.thisclass {

	display: inline-block;

	border: 1px solid #00A7EB;

	padding: 6px 15px;

	margin: 0 1px;

	background: #00A7EB;

	color: #fff;

}

.pages ul li.thisclass a {

	color: #fff;

}

.pages ul li a {

	display: block;

	color: #999

}

.pages ul li a:hover {

	color: #fff;

}

.clearfix {

	clear: both;

}

.tags .tags-title {

	height: 40px;

	line-height: 40px;

}

.tags .tags-title h2 {

	color: #2a363c;

	font-weight: bold;

	font-size: 16px;

}

.tags ul {

	padding: 10px 0 20px 0;

	overflow: hidden;

}

.tags ul li {

	float: left;

	padding: 0px 5px 0px 0px;

	list-style: none

}
.banner a{
	display: block;width: 100%;height: 100%
}
@media screen and (max-width: 600px) {
	.banner .swiper-slide{
		background-size: 100% 100%
	}
}

.tags ul li a {

	text-transform: uppercase;

	-webkit-transition: all .3s ease;

	-o-transition: all .3s ease;

	transition: all .3s ease;

	display: inline-block;

	vertical-align: top;

	font-size: 14px;

	line-height: 22px;

	padding: 5px 15px;

	margin: 0 6px 8px 0;

	border: 1px solid #e0e0e0;

	border-radius: 2px;

	color: rgba(0,0,0,.88);

	background-color: rgba(255,255,255,.97)

}

.tags ul li a:nth-child(8), .tags ul li a:nth-child(15), .tags ul li a:nth-child(35) {

	border-color: #fdb2b2;

	color: #fd6161

}

.tags ul li a:nth-child(8) i, .tags ul li a:nth-child(15) i, .tags ul li a:nth-child(35) i {

	position: relative;

	top: -1px

}

.tags ul li a:hover {

	color: #FFF;

	background: #1B1B1B;

	border-color: #1B1B1B

}

 @media screen and (max-width:1100px) {

.wrap {

	max-width: 760px;

}

.right, .top-news {

	display: none;

}

.left, .banner {

	width: 100%;

}

.left {

	padding: 0 5px;

}

.mainbody {

	padding-top: 10px;

}

.topmenu {

	height: 122px;

	background: #fff;

	position: fixed;

	width: 100%;

	z-index: 11;

	top: 0;

	left: 0;

	border-bottom: 1px solid #dedede;

}

.logo {

	padding: 15px 0;

	width: 700px;

	height: 50px;

	margin: 0 auto;

	text-align: center;

}

.logo img {

	height: 50px;

	width: auto;

}

.search {

	top: 25px;

}

.menu {

	width: 100%;

	margin-top: 0px;

}

.banner {

	padding: 0 5px;

}

.swiper-slide {

	height: 250px;

}

.hotnews {

	padding-top: 0px;

}

.hotnewlist {

	height: 160px;

	padding: 0 5px;

}

.items {

	margin-bottom: 2px;

}

.content-box {

	padding: 20px 15px;

}

.post-nav {

	position: relative;

	margin-bottom: 0;

	box-shadow: none;

	background: #FFF;

	height: 45px;

	overflow: hidden;

	overflow-x: auto;

	white-space: nowrap;

	width: 100%;

	-webkit-box-pack: justify;

	padding: 0;

}

.post-nav:after {

	content: "";

	background-color: #f5f4f4;

	left: 0;

	width: 100%;

	height: 2px;

	bottom: 0;

	position: absolute

}

.post-nav span {

	position: relative;

	padding: 0;

	height: 45px;

	line-height: 45px;

	font-size: 14px;

	border-radius: 0;

	box-shadow: none;

	margin: 0 20px 0 15px;

	color: #748594;

	border-width: 0

}

.post-nav span.current, .post-nav span:hover {

	background: 0 0;

	color: #273746;

	border-width: 0

}

.post-nav span.current:after {

	content: "";

	background-color: #273746;

	left: 0;

	width: 100%;

	height: 2px;

	bottom: 0;

	position: absolute;

	z-index: 1

}

.footer {

	width: 100%;

	position: relative;

	clear: both;

	background-color: #282828;

	margin-top: 30px;

	padding: 30px 0 30px 0;

}

.social-footer {

	float: none;

	text-align: center;

	margin-bottom: 20px;

}

.social-footer a {

	float: none;

	display: inline-block;

}

.links-footer, .copyright-footer {

	text-align: center;

}

.nav-footer {

	clear: both;

	text-align: center;

}

.search-close {

	width: 100%;

	height: 60px;

	margin: 0 auto;

	padding: 30px 50px 0 0;

}

.postion, .topad {

	display: none;

}

.subbody {

	padding-top: 130px;

}

}

 @media screen and (max-width:768px) {

.wrap {

	max-width: 100%;

}

.right, .top-news {

	display: none;

}

.left, .banner {

	width: 100%;

}

.left {

	padding: 0px;

}

.mainbody {

	padding-top: 10px;

}

.main {

	padding-top: 70px;

}

.topmenu {

	height: 70px;

	background: #fff;

	position: fixed;

	width: 100%;

	z-index: 11;

	top: 0;

	left: 0;

	border-bottom: 1px solid #dedede;

}

.logo {

	padding: 15px 0;

	width: 100%;

	height: 40px;

	margin: 0 auto;

	text-align: center;

}

.logo img {

	height: 40px;

	width: auto;

}

.search {

	top: 20px;

}

#mobilemenu {

	position: absolute;

	width: 25px;

	top: 28px;

	height: 15px;

	left: 20px;

	border-bottom: 2px solid #333;

	border-top: 2px solid #333;

}

.banner {

	padding: 0px;

}

.swiper-slide {

	height: 200px;

}

.hotnews {

	display: none

}

.hotnewlist {

	height: 160px;

	padding: 0 5px;

}

.items {

	margin-bottom: 2px;

}

.content-box {

	padding: 20px 15px;

}

.post-nav {

	position: relative;

	margin-bottom: 0;

	box-shadow: none;

	background: #FFF;

	height: 45px;

	overflow: hidden;

	overflow-x: auto;

	white-space: nowrap;

	width: 100%;

	-webkit-box-pack: justify;

	padding: 0;

}

.post-nav:after {

	content: "";

	background-color: #f5f4f4;

	left: 0;

	width: 100%;

	height: 2px;

	bottom: 0;

	position: absolute

}

.post-nav span {

	position: relative;

	padding: 0;

	height: 45px;

	line-height: 45px;

	font-size: 14px;

	border-radius: 0;

	box-shadow: none;

	margin: 0 20px 0 15px;

	color: #748594;

	border-width: 0

}

.post-nav span.current, .post-nav span:hover {

	background: 0 0;

	color: #273746;

	border-width: 0

}

.post-nav span.current:after {

	content: "";

	background-color: #273746;

	left: 0;

	width: 100%;

	height: 2px;

	bottom: 0;

	position: absolute;

	z-index: 1

}

.footer {

	width: 100%;

	position: relative;

	clear: both;

	background-color: #282828;

	margin-top: 30px;

	padding: 30px 0 30px 0;

}

.social-footer {

	float: none;

	text-align: center;

	margin-bottom: 20px;

}

.social-footer a {

	float: none;

	display: inline-block;

}

.links-footer, .copyright-footer {

	text-align: center;

}

.nav-footer {

	clear: both;

	text-align: center;

}

.footer .nav-footer a {

	font-size: 14px;

}

.search-close {

	width: 100%;

	height: 60px;

	margin: 0 auto;

	padding: 30px 50px 0 0;

}

.search-close i {

	font-size: 25px;

	font-weight: bold;

}

.ad01, .postion, .topad, .left-ad {

	display: none;

}

.subbody {

	padding-top: 70px;

}

.posts-gallery-img {

	width: 33.3333%;

}

.posts-gallery-img img {

	max-height: 130px;

	min-height: 0;

}

.posts-gallery-content {

	margin-left: 36%;

}

.swiper-slide .title h3.f20 {

	font-size: 16px;

}

.posts-gallery-text {

	display: none;

}

.posts-gallery-info {

	bottom: 15px;

	position: absolute;

}

.posts-gallery-content h2, .posts-default-title h2 {

	font-size: 16px;

}

li.post-author, li.ico-eye, li.ico-like, .posts-text {

	display: none;

}

.art-con li.ico-like, .art-con li.ico-eye, .art-con li.post-author {

	display: block;

}

.shareBox .sharebtn {

	width: auto;

	padding: 0 15px;

	margin: 5px;

	height: 36px;

	line-height: 35px;

	font-size: 13px;

}

.propage, .nextpage {

	width: 100%;

}

.nextpage {

	text-align: left;

}

.pronext {

	margin-bottom: 0px;

}

.article-con {

	padding: 10px;

}

.art-con h1 {

	font-size: 18px;

	margin-bottom: 10px;

	padding-bottom: 15px;

}

ul.sub-news li {

	width: 100%;

}

/*menu*/

.menu {

	left: -100%;

	width: 70%;

	background: #fff;

	position: fixed;

	top: 0;

	height: 100%;

	margin: 0;

	padding: 10px;

	z-index: 101;

}

#nav li.closex {

	display: block;

	padding: 0 20px;

	line-height: 60px;

}

#nav li.closex i {

	font-size: 20px;

	font-weight: bold;

}

#nav {

	display: none;

	width: 100%;

}

#nav .mainlevel {

	border-right: 1px solid #fff;

	position: relative;

	z-index: 10;

	float: none;

}

#nav .mainlevel a {

	color: #000;

	text-decoration: none;

	line-height: 50px;

	display: block;

	padding: 0 20px;

	font-size: 14px;

}

#nav .mainlevel a i {

	width: 6px;

	height: 6px;

	border-bottom: 2px solid #666;

	border-right: 2px solid #666;

	display: block;

	-webkit-transform: rotate(45deg);

	transform: rotate(45deg);

	float: right;

	margin-top: 26px;

	margin-left: 10px;

}

#nav .mainlevel ul {

	display: none;

	position: relative;

	background: #fff;

	box-shadow: 0px 0px 0px #888888;

	border: 1px solid #dedede;

	border-top: 2px solid #333;

}

#nav .mainlevel li {

	background: #fff;

	color: #19B5FE;

	font-size: 15px;

	width: 100%;

}

#nav .mainlevel li a {

	line-height: 35px;

}

.search-con {

	width: 90%;

}

dl.se dt {

	width: 75%;

	float: left;

}

dl.se dd {

	width: 25%;

	float: right;

}

.search-tips {

	line-height: 40px;

	padding-top: 10px;

	font-size: 15px;

}

.search-as {

	line-height: 34px;

}

.search-as a {

	padding: 0 20px;

	border: 1px solid #dedede;

	display: inline-block;

	margin-right: 10px;

	margin-bottom: 10px;

	white-space: nowrap;

	border-radius: 5px;

	color: #ccc;

	font-size: 12px;

	line-height: 30px;

}

.search-as a:hover {

	color: #666;

	border: 1px solid #666;

}

}

/*方法步骤*/
.tutorial-detail dd { position: relative; padding-left: 66px; padding-bottom: 20px; color: #333;line-height: 25px;font-size: 16px;}
.tutorial-detail dd a{ color: #00a0e9;}
.tutorial-detail dd:before { content: ''; position: absolute; left: 33px; top: 0; bottom: 0; border-left: 2px dotted #e4e4e4 }
/*.tutorial-detail dd:first-of-type i { color: #989898; width: auto; left: 14px; line-height: 25px; background: #fff }*/
.tutorial-detail dd i { position: absolute; left: 19px; width: 30px; text-align: center; background-color: #00a0e9; font-size: 14px; color: #fff; line-height: 30px; border-radius: 50%;font-style: normal; }
.tutorial-detail dd strong { font-size: 17px; font-weight: 700; margin-bottom: 10px; line-height: 30px }
.tutorial-detail dd b { display: block; font-size: 17px; font-weight: 700; margin-bottom: 10px; line-height: 30px }
.tutorial-detail dd p { margin-bottom: 1em; font-size: 16px; line-height: 1.5 }
.tutorial-detail dd img { display: block; max-width: 80%; height: auto !important; margin: 20px auto 0 }

/*阅读全文*/
.show_more_btn{width:100%;height:70px;display:block;clear:both;font-size:18px;font-weight:400!important;text-align:center;position:relative;margin:0;padding-top:4px;z-index:5}
.show_more_btn i{width:15px;height:10px;background:url(../images/ndarrow.png) no-repeat 50%;background-size:20px;display:inline-block;vertical-align:baseline;padding-bottom:4px}
.shadow{width:100%;height:111px;background:linear-gradient(180deg,hsla(0,0%,100%,0),#fff);background:-ms-linear-gradient(to bottom,hsla(0,0%,100%,0),#fff 100%);position:absolute;top:-111px;left:0}
.show_more_text{font-size:18px;width:170px;height:40px;border:1px solid #e93274;color:#e93274;line-height:40px;padding:10px 40px;border-radius:5px}
.show_more_text img{display:inline-block}
.show_more_text:hover{background-color:rgba(233,50,116,.1);cursor:pointer}
.smalltext{background: #f5f5f5;padding:5px 10px;margin-bottom: 10px;font-size: 17px;color: #333;border-radius: 5px;font-size:13px;}

/*底部样式*/
.footer .copyright-footer{
    margin-top:20px;
}
.footer .copyright-footer p {

	color: #828282;
	text-align:center;

}

.footer .copyright-footer a {

	color: #929292

}

.footer .links-footer {

	font-size: 10px;

	color: #353e4a;

	padding: 0 0 18px;

	border-bottom: 1px solid rgba(255,255,255,.05);

}
/*新增短文样式，优化短文间距等样式*/
.art-txt p{line-height:30px;margin-bottom:20px;}