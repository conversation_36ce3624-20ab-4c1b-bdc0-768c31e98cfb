{inc:header.htm}
<style>
    .layui-elem-field legend{font-size: 14px;}
</style>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-card">
            <div class="layui-card-body">
            <form class="layui-form layuimini-form" id="form" action="index.php?cms_page-{$_GET['action']}-ajax-1" method="post">
                <input name="mid" type="hidden" value="1" />
                <input name="cid" type="hidden" value="{$data[cid]}" />
                <fieldset class="layui-elem-field">
                    <legend>{lang:basic_info}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item" id="i_upid">
                            <label class="layui-form-label required">{lang:cms_page_upid}</label>
                            <div class="layui-input-inline">
                                <select name="upid" id="upid"><option value="0">{lang:none}</option></select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label required">{lang:category_name}</label>
                                <div class="layui-input-inline">
                                    <input name="name" value="{$data[name]}" placeholder="{lang:category_name}" maxlength="30" type="text" class="layui-input" required="required" lay-verify="required"/>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label required">{lang:alias}</label>
                                <div class="layui-input-inline">
                                    <input name="alias" value="{$data[alias]}" placeholder="{lang:alias}" maxlength="50" type="text" class="layui-input" required="required" lay-verify="required"/>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:intro}</label>
                            <div class="layui-input-block">
                                <input name="intro" value="{$data[intro]}" placeholder="{lang:intro}" maxlength="255" type="text" class="layui-input" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:thumb}</label>
                            <div class="layui-input-block">
                                <div class="layui-input-inline"><input name="pic" value="{$data[pic]}" id="pic_val" placeholder="{lang:thumb}" type="text" class="layui-input" /></div>
                                <div class="layui-input-inline">
                                    <button type="button" class="layui-btn" id="pic">
                                        <i class="layui-icon">&#xe67c;</i>{lang:upload_pic}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">{lang:orderby}</label>
                                <div class="layui-input-inline">
                                    <input name="orderby" value="{$data[orderby]}" type="number" class="layui-input" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label required">{lang:cate_tpl}</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" type="text" name="cate_tpl" id="cate_tpl" value="{$data[cate_tpl]}" maxlength="80" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:page_content}</label>
                            <div class="layui-input-block">
                                <textarea id="page_content" name="page_content" class="layui-textarea">{$data[page_content]}</textarea>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <fieldset class="layui-elem-field">
                    <legend>{lang:seo}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:seo_title}</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="{lang:seo_title}" maxlength="100" type="text" name="seo_title" value="{$data[seo_title]}" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:seo_keywords}</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="{lang:seo_keywords}" maxlength="200" type="text" name="seo_keywords" value="{$data[seo_keywords]}" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:seo_description}</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="{lang:seo_description}" maxlength="255" type="text" name="seo_description" value="{$data[seo_description]}" />
                            </div>
                        </div>
                    </div>
                </fieldset>

                {hook:admin_cms_page_content_set_after.htm}
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    window.editor_init = function(){
        // 编辑器API
        window.editor_api = {
            // 单页内容
            page_content : {
                obj : $('#page_content'),
                get : function() {
                    return this.obj.val();
                },
                set : function(s) {
                    return this.obj.val(s);
                },
                focus : function() {
                    return this.obj.focus();
                }
            }
        }
    };

    // 加载所属频道
    function loadCategoryUpid(mid, upid, noid) {
        adminAjax.get("index.php?category-get_category_upid-ajax-1-mid-"+mid+"-upid-"+Math.max(0, upid)+"-noid-"+Math.max(0, noid)+"&r="+time(), function(data){
            data = toJson(data);
            $("#i_upid>.layui-input-inline").html(data.upid);
            layui.use('form', function(){  //此段代码必不可少，不然select不可见
                var form = layui.form;
                form.render();
            });
        });
    }

    layui.use(['form','layer', 'upload', 'miniTab'], function () {
        var layer = layui.layer, upload = layui.upload, form = layui.form, miniTab = layui.miniTab;

        //编辑器
        editor_init();

        loadCategoryUpid(1,"{$data[upid]}","{$data[cid]}");

        //缩略图上传
        upload.render({
        	elem: '#pic' //绑定元素
        	,url: 'index.php?attach-upload_pic-table-category' //上传接口
        	,field:'upfile'
        	,accept: 'images'
        	,acceptMime:'image/*'
        	,done: function(res){
        		if(res.err == 1){
        			layer.msg(res.msg, {icon: 5});
        		}else{
        			$("#pic_val").val(res.data.src);
        			layer.msg('{lang:upload_successfully}', {icon: 1});
        		}
        	}
        	,error: function(){
        		//请求异常回调
        		layer.msg('{lang:request_exception}',{icon: 5});
        	}
        });

        //监听提交
        form.on('submit(form)', function () {
            if (window.hasOwnProperty('editor')) {
                window.editor.async();
            }
            adminAjax.postform("#form",function (data) {
                var json = toJson(data);
                if( json.err == 0 ){
                    var icon = 1;
                }else{
                    var icon = 5;
                }
                layer.msg(json.msg, {icon: icon});
                if(json.err==0) {
                    setTimeout(function(){
                        miniTab.reloadIframe('index.php?cms_page-index');
                        miniTab.deleteCurrentByIframe();
                    }, 1500);
                }
            });
            return false;
        });
    });
</script>
{hook:admin_category_set_after.htm}
{hook:admin_cms_page_set_after.htm}
</body>
</html>
