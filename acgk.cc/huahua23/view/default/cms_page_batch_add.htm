{inc:header.htm}
<style>
    .layui-elem-field legend{font-size: 14px;}
</style>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-card">
            <div class="layui-card-body">
            <form class="layui-form layuimini-form" id="form" action="index.php?cms_page-batch_add-ajax-1" method="post">
                <fieldset class="layui-elem-field">
                    <legend>{lang:basic_info}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item" id="i_upid">
                            <label class="layui-form-label required">{lang:cms_page_upid}</label>
                            <div class="layui-input-inline">
                                <select name="upid" id="upid"><option value="0">{lang:none}</option></select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label required">{lang:category}</label>
                            <div class="layui-input-block">
                                <textarea name="categorys" placeholder="{lang:category_name}#{lang:alias}" class="layui-textarea" required="required" lay-verify="required"></textarea>
                                <div class="layui-form-mid layui-word-aux">{lang:one_line_one}</div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">{lang:orderby}</label>
                                <div class="layui-input-inline">
                                    <input name="orderby" value="0" type="number" class="layui-input" />
                                </div>
                            </div>
                            <div class="layui-inline" id="i_cate_tpl">
                                <label class="layui-form-label required">{lang:cate_tpl}</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" type="text" name="cate_tpl" id="cate_tpl" value="{$data[cate_tpl]}" />
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                {hook:admin_cms_page_batch_add_after.htm}
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">

    // 加载所属频道
    function loadCategoryUpid(mid, upid, noid) {
        adminAjax.get("index.php?category-get_category_upid-ajax-1-mid-"+mid+"-upid-"+Math.max(0, upid)+"-noid-"+Math.max(0, noid)+"&r="+time(), function(data){
            data = toJson(data);
            $("#i_upid>.layui-input-inline").html(data.upid);
            layui.use('form', function(){  //此段代码必不可少，不然select不可见
                var form = layui.form;
                form.render();
            });
        });
    }

    layui.use(['form','layer', 'miniTab'], function () {
        var layer = layui.layer, form = layui.form, miniTab = layui.miniTab;
        //编辑器
        editor_init();

        loadCategoryUpid(1,0,0);

        //监听提交
        form.on('submit(form)', function () {
            if (window.hasOwnProperty('editor')) {
                window.editor.async();
            }
            adminAjax.postform("#form",function (data) {
                var json = toJson(data);
                if( json.err == 0 ){
                    var icon = 1;
                }else{
                    var icon = 5;
                }
                layer.msg(json.msg, {icon: icon});
                if(json.err==0) {
                    setTimeout(function(){
                        miniTab.reloadIframe('index.php?cms_page-index');
                        miniTab.deleteCurrentByIframe();
                    }, 1500);
                }
            });
            return false;
        });
    });
</script>
{hook:admin_category_set_after.htm}
</body>
</html>
