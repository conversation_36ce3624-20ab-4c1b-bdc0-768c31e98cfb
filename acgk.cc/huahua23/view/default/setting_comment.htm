{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">{lang:comment_setting}</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?setting-comment-ajax-1" method="post">
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:open_comment}</label>
				<div class="layui-input-block">
					{$input[open_comment]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:comment_vcode}</label>
				<div class="layui-input-block">
					{$input[open_comment_vcode]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:no_login_comment}</label>
				<div class="layui-input-block">
					{$input[open_no_login_comment]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label required">{lang:default_author}</label>
				<div class="layui-input-inline">
					{$input[comment_default_author]}
				</div>
			</div>
			{hook:admin_setting_comment_after.htm}
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
layui.use(['form','layer'], function(){
	var layer = layui.layer, form = layui.form;
	//监听提交
	form.on('submit(form)', function(){
		adminAjax.submit("#form");
	});
});
</script>
</body>
</html>
