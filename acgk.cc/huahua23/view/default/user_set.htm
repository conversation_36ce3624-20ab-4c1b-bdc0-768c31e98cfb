{inc:header.htm}

<div class="layui-card">
    <div class="layui-card-header">{if:$_GET['action'] == 'add'}{lang:add_user}{else}{lang:edit_user}{/if}</div>
    <div class="layui-card-body">
        <form class="layui-form layuimini-form" id="form" action="index.php?user-{$_GET['action']}-ajax-1" method="post">
            <input name="uid" type="hidden" value="{$data[uid]}" />
            <div class="layui-form-item">
                <label class="layui-form-label required">{lang:user_group}</label>
                <div class="layui-input-inline">
                    {$groupidhtml}
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">{lang:username}</label>
                <div class="layui-input-inline">
                    <input type="text" name="username" value="{$data[username]}" maxlength="16" lay-verify="required" lay-reqtext="{lang:username_dis_empty}" placeholder="{lang:username}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            {if:$_GET['action'] == 'add'}
            <div class="layui-form-item">
                <label class="layui-form-label required">{lang:password}</label>
                <div class="layui-input-inline">
                    <input type="text" name="password" value="{$data[password]}" maxlength="32" minlength="6" lay-verify="required" lay-reqtext="{lang:password_dis_empty}" placeholder="{lang:password}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            {/if}
            <div class="layui-form-item">
                <label class="layui-form-label">{lang:author}</label>
                <div class="layui-input-inline">
                    <input type="text" name="author" value="{$data[author]}" maxlength="20" placeholder="{lang:author}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">{lang:email}</label>
                <div class="layui-input-inline">
                    <input type="email" name="email" value="{$data[email]}" maxlength="40" placeholder="{lang:email}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">{lang:mobile}</label>
                <div class="layui-input-inline">
                    <input type="text" name="mobile" value="{$data[mobile]}" maxlength="20" placeholder="{lang:mobile}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">{lang:credits}</label>
                <div class="layui-input-inline">
                    <input type="number" name="credits" value="{$data[credits]}" placeholder="{lang:credits}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">{lang:golds}</label>
                <div class="layui-input-inline">
                    <input type="number" name="golds" value="{$data[golds]}" placeholder="{lang:golds}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">{lang:homepage}</label>
                <div class="layui-input-block">
                    <input type="text" name="homepage" value="{$data[homepage]}" maxlength="255" placeholder="{lang:homepage}" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">{lang:intro}</label>
                <div class="layui-input-block">
                    <textarea name="intro" maxlength="255" placeholder="{lang:intro}" class="layui-textarea">{$data[intro]}</textarea>
                </div>
            </div>
            {hook:admin_user_set_after.htm}
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    layui.use(['form','layer', 'miniTab'], function () {
        var layer = layui.layer, form = layui.form, miniTab = layui.miniTab;
        //监听提交
        form.on('submit(form)', function () {
            adminAjax.postform('#form',function (data) {
                var json = toJson(data);
                if( json.err == 0 ){
                    var icon = 1;
                }else{
                    var icon = 5;
                }
                layer.msg(json.msg, {icon: icon});
                if(json.err==0) {
                    setTimeout(function(){
                        miniTab.reloadIframe('index.php?user-index');
                        miniTab.deleteCurrentByIframe();
                    }, 1500);
                }
            });
            return false;
        });
    });
</script>
</body>
</html>
