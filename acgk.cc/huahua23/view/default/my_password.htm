{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">{lang:edit_password}</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?my-password-ajax-1" method="post">
			<div class="layui-form-item">
				<label class="layui-form-label required">{lang:old_password}</label>
				<div class="layui-input-inline">
					{$input[oldpw]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label required">{lang:new_password}</label>
				<div class="layui-input-inline">
					{$input[newpw]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label required">{lang:confirm_new_password}</label>
				<div class="layui-input-inline">
					{$input[confirm_newpw]}
				</div>
			</div>
			{hook:admin_my_password_after.htm}
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
	layui.use('form', function(){
		var form = layui.form;

		//监听提交
		form.on('submit(form)', function () {
			adminAjax.postform('#form',function (data) {
				var json = toJson(data);
				if( json.err == 0 ){
					var icon = 1;
				}else{
					var icon = 5;
				}
				layer.msg(json.msg, {icon: icon});
				if(json.err==0 && json.name != '') {
					setTimeout(function(){
						window.location.href = json.name;
					}, 1000);
				}
			});
			return false;
		});
	});
</script>
</body>
</html>
