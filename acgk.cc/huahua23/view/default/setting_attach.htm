{inc:header.htm}
<div class="layuimini-container">
	<div class="layuimini-main">
		<div class="layui-card">
			<div class="layui-card-header">{lang:attach_setting}</div>
			<div class="layui-card-body">
				<form id="form" class="layui-form" action="index.php?setting-attach-ajax-1" method="post">
					<div class="layui-tab">
						<ul class="layui-tab-title">
							<li class="layui-this">{lang:image}</li>
							<li>{lang:attach}</li>
						</ul>
						<div class="layui-tab-content">
							<div class="layui-tab-item layui-show">
								<div class="layui-form-item">
									<label class="layui-form-label required">{lang:allow_ext}</label>
									<div class="layui-input-block">
										{$input[up_img_ext]}
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label required">{lang:max_size}</label>
									<div class="layui-input-inline">
										{$input[up_img_max_size]}
									</div>
									<div class="layui-form-mid layui-word-aux">(KB)</div>
								</div>
							</div>
							<div class="layui-tab-item">
								<div class="layui-form-item">
									<label class="layui-form-label required">{lang:allow_ext}</label>
									<div class="layui-input-block">
										{$input[up_file_ext]}
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label required">{lang:max_size}</label>
									<div class="layui-input-inline">
										{$input[up_file_max_size]}
									</div>
									<div class="layui-form-mid layui-word-aux">(KB)</div>
								</div>
							</div>
						</div>
					</div>
					<div class="layui-form-item">
						<div class="layui-input-block">
							<button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript">
	layui.use('form', function(){
		var form = layui.form;

		//监听提交
		form.on('submit(form)', function(){
			adminAjax.submit("#form");
		});
	});
</script>
</body>
</html>
