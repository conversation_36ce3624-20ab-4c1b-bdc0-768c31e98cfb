{inc:header.htm}
<style>
    .layui-elem-field legend{font-size: 14px;}
</style>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-card">
            <div class="layui-card-body">
            <form class="layui-form layuimini-form" id="form" action="index.php?category-batch_add-ajax-1" method="post">
                <fieldset class="layui-elem-field">
                    <legend>{lang:basic_info}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label required">{lang:model}</label>
                            <div class="layui-input-block">
                                {loop:$mod_name $v $k}<input lay-filter="mid" name="mid" type="radio" value="{$k}" title="{$v}" {if:$data[mid] == $k}checked{/if}>{/loop}
                            </div>
                        </div>

                        <div class="layui-form-item" id="i_type">
                            <label class="layui-form-label required">{lang:type}</label>
                            <div class="layui-input-block">
                                <input lay-filter="type" name="type" type="radio" value="0" title="{lang:cate_type_0}" checked>
                                <input lay-filter="type" name="type" type="radio" value="1" title="{lang:cate_type_1}">
                            </div>
                        </div>
                        <div class="layui-form-item" id="i_upid">
                            <label class="layui-form-label required">{lang:select_upid}</label>
                            <div class="layui-input-inline">
                                <select name="upid" id="upid"><option value="0">{lang:none}</option></select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label required">{lang:category}</label>
                            <div class="layui-input-block">
                                <textarea name="categorys" placeholder="{lang:category_name}#{lang:alias}" class="layui-textarea" required="required" lay-verify="required"></textarea>
                                <div class="layui-form-mid layui-word-aux">{lang:one_line_one}</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">{lang:orderby}</label>
                                <div class="layui-input-inline">
                                    <input name="orderby" value="0" type="number" class="layui-input" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">{lang:contribute}</label>
                                <div class="layui-input-inline">
                                    <input title="{lang:disable}" name="contribute" type="radio" value="0" checked>
                                    <input title="{lang:enable}" name="contribute" type="radio" value="1">
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <fieldset class="layui-elem-field">
                    <legend>{lang:tpl_info}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline" id="i_cate_tpl">
                                <label class="layui-form-label required">{lang:cate_tpl}</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" type="text" name="cate_tpl" id="cate_tpl" value="{$data[cate_tpl]}" />
                                </div>
                            </div>
                            <div class="layui-inline" id="i_show_tpl">
                                <label class="layui-form-label required">{lang:show_tpl}</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" type="text" name="show_tpl" id="show_tpl" value="{$data[show_tpl]}" />
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                {hook:admin_category_batch_add_after.htm}
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var models = {$models};

    // 设置表单内的值
    function setFormVal(mid, type) {

        $("#i_cate_tpl>.layui-form-label").html(type == 1 ? "{lang:channel_tpl}" : "{lang:cate_tpl}");

        // 默认模板设置
        var edit_mid = "{$data[mid]}";
        var edit_type = "{$data[type]}";
        if(!!edit_mid && edit_mid == mid && edit_type == type) {
            $("#cate_tpl").val("{$data[cate_tpl]}");
            $("#show_tpl").val("{$data[show_tpl]}");
        }else{
            var k = "models-mid-"+mid;

            $("#cate_tpl").val(type == 1 ? models[k]["index_tpl"] : models[k]["cate_tpl"]);
            $("#show_tpl").val(models[k]["show_tpl"]);
        }
    }

    // 加载所属频道
    function loadCategoryUpid(mid, upid, noid) {
        adminAjax.get("index.php?category-get_category_upid-ajax-1-mid-"+mid+"-upid-"+Math.max(0, upid)+"-noid-"+Math.max(0, noid)+"&r="+time(), function(data){
            data = toJson(data);
            $("#i_upid>.layui-input-inline").html(data.upid);
            layui.use('form', function(){  //此段代码必不可少，不然select不可见
                var form = layui.form;
                form.render();
            });
        });
    }

    layui.use(['form','layer', 'miniTab'], function () {
        var layer = layui.layer, form = layui.form, miniTab = layui.miniTab;
        var mid = "{$data[mid]}";

        loadCategoryUpid(mid,0,0);

        var def_type = $('input[name="type"]:checked').val();
        var type = 0;
        if(def_type != type){
            type = def_type;
        }
        var def_mid = $('input[name="mid"]:checked').val();
        if(def_mid != mid){
            mid = def_mid;
        }
        setFormVal(mid,type);


        // 如果分类已发布了内容或有下级分类或者是单页就不允许改变模型和类型
        var count = 0;
        var son_cate = 0;

        if(count > 0 || son_cate > 0 || mid == 1) {
            $("input[name='mid']").each(function() {
                if($(this).val() != "{$data[mid]}") {
                    $(this).attr("disabled", "disabled");
                }
            });
            $("input[name='type']").each(function() {
                if($(this).val() != "{$data[type]}") {
                    $(this).attr("disabled", "disabled");
                }
            });
        }else{
            form.on('radio(mid)', function(data) {
                var type = $('input[name="type"]:checked').val();
                //设置表单的值
                setFormVal(data.value, type);
                // 加载所属频道
                loadCategoryUpid(data.value, "{$data[upid]}", "{$data[cid]}");
            });

            form.on('radio(type)', function(data) {
                setFormVal($('input[name="mid"]:checked').val(), data.value);
            });
        }

        //监听提交
        form.on('submit(form)', function () {
            if (window.hasOwnProperty('editor')) {
                window.editor.async();
            }
            adminAjax.postform("#form",function (data) {
                var json = toJson(data);
                if( json.err == 0 ){
                    var icon = 1;
                }else{
                    var icon = 5;
                }
                layer.msg(json.msg, {icon: icon});
                if(json.err==0) {
                    setTimeout(function(){
                        miniTab.reloadIframe('index.php?category-index');
                        miniTab.deleteCurrentByIframe();
                    }, 1500);
                }
            });
            return false;
        });
    });
</script>
{hook:admin_category_set_after.htm}
</body>
</html>
