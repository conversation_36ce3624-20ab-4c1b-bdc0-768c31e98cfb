<script type="text/javascript">
    (function(){
        if(typeof window.editor_once != 'undefined') return;

        // 获取后台路径
        window.admurl = (function() {
            var url = document.URL || location.href;
            return url.substr(0, url.lastIndexOf("/"));
        })();

        // 获取首页路径
        window.weburl = (function() {
            return admurl.substr(0, admurl.lastIndexOf("/"));
        })();

        window.UMEDITOR_HOME_URL = weburl+"/static/js/umeditor/";

        loadCss(UMEDITOR_HOME_URL+"themes/default/css/umeditor.min.css");
        loadJs(UMEDITOR_HOME_URL+"umeditor.config.js", UMEDITOR_HOME_URL+"umeditor.min.js", UMEDITOR_HOME_URL+"lang/zh-cn/zh-cn.js", function(){
            $("#content").removeAttr("class");

            var ue = UM.getEditor("content", {initialFrameWidth : '100%', imageUrl : UMEDITOR_CONFIG.imageUrl + '{$edit_cid_id}'});
            window.editor_api = {
                content : {
                    obj : ue,
                    get : function() {
                        return this.obj.getContent();
                    },
                    set : function(s) {
                        return this.obj.setContent(s);
                    },
                    focus : function() {
                        return this.obj.focus();
                    }
                }
            }
        });
    })();
</script>
