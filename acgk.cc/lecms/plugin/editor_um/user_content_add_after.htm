<script type="text/javascript">
    //加载CSS
    function loadCss(file) {
        // 不重复加载
        var tags = document.getElementsByTagName('link');
        for(var j=0; j<tags.length; j++) {
            if(tags[j].href.indexOf(file) != -1) {
                return false;
            }
        }

        var link = document.createElement("link");
        link.rel = "stylesheet";
        link.type = "text/css";
        link.href = file;
        document.getElementsByTagName('head')[0].appendChild(link);
    }
    //加载JS
    function loadJs() {
        var args = arguments;

        //循环加载JS
        var load = function(i) {
            if(typeof args[i] == 'string') {
                var file = args[i];

                // 不重复加载
                var tags = document.getElementsByTagName('script');
                for(var j=0; j<tags.length; j++) {
                    if(tags[j].src.indexOf(file) != -1) {
                        if(i < args.length) load(i+1);
                        return;
                    }
                }

                var script = document.createElement("script");
                script.type = "text/javascript";
                script.src = file;

                // callback next
                if(i < args.length) {
                    // Attach handlers for all browsers
                    script.onload = script.onreadystatechange = function() {
                        if(!script.readyState || /loaded|complete/.test(script.readyState)) {
                            // Handle memory leak in IE
                            script.onload = script.onreadystatechange = null;

                            // Remove the script (取消移除，判断重复加载时需要读 script 标签)
                            //if(script.parentNode) { script.parentNode.removeChild(script); }

                            // Dereference the script
                            script = null;

                            load(i+1);
                        }
                    };
                }
                document.getElementsByTagName('head')[0].appendChild(script);
            }else if(typeof args[i] == 'function') {
                args[i]();
                if(i < args.length) {
                    load(i+1);
                }
            }
        }

        load(0);
    }
    (function(){
        if(typeof window.editor_once != 'undefined') return;

        // 获取首页路径
        window.weburl = (function() {
            var url = document.URL || location.href;
            return url.substr(0, url.lastIndexOf("/"));
        })();

        window.UMEDITOR_HOME_URL = weburl+"/static/js/umeditor/";

        loadCss(UMEDITOR_HOME_URL+"themes/default/css/umeditor.min.css");
        loadJs(UMEDITOR_HOME_URL+"umeditor.config.js", UMEDITOR_HOME_URL+"umeditor.min.js", UMEDITOR_HOME_URL+"lang/zh-cn/zh-cn.js", function(){
            $("#content").removeAttr("class");

            var ue = UM.getEditor("content", {initialFrameWidth : '100%', imageUrl : window.weburl + "/index.php?u=my-up_image-ajax-1{$edit_cid_id}"});
            window.editor_api = {
                content : {
                    obj : ue,
                    get : function() {
                        return this.obj.getContent();
                    },
                    set : function(s) {
                        return this.obj.setContent(s);
                    },
                    focus : function() {
                        return this.obj.focus();
                    }
                }
            }
        });
    })();
</script>
