<?php
function  buy()
{
    $this->_cfg['titles'] = '我购买的内容';
    $mid = max(2, (int)R('mid'));
    $table = $this->models->get_table($mid);
    $this->cms_content->table = 'cms_'.$table;
    $where['uid'] = $this->_uid;
    // 初始分页
    $pagenum = 10;
    $total = $this->user_buy->find_count($where);
    $maxpage = max(1, ceil($total/$pagenum));
    $page = min($maxpage, max(1, intval(R('page'))));
    $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('kami', 'my', true)); //这里使用bootstrap风格
    $this->assign('pages', $pages);
    $this->assign('total', $total);
    $data = $this->user_buy->list_arr($where, 'create_time', -1, ($page-1)*$pagenum, $pagenum, $total);
    foreach ($data as $k=>$v){
        $item=$this->cms_content->get($v['aid']);
        $data[$k]['create_time']=empty($v['create_time'])?'-':date('Y-m-d H:i:s',$v['create_time']);
        $data[$k]['title']=$item['title'];
        $data[$k]['url']=$this->cms_content->content_url($item, $mid);
    }
    $this->assign('data', $data);
    $this->assign('cfg', $this->_cfg);
    $this->assign('cfg_var', $this->_var);
    $GLOBALS['run'] = &$this;
    $_ENV['_theme'] = &$this->_cfg['theme'];
    $this->display('templet/my_buy.htm');
}
function userinfo()
{
    if($_POST){
        if(empty($_POST['id']) || !is_numeric($_POST['id'])){
            E(1, '参数错误');
        }
        $arc=$this->cms_article->get($_POST['id']);
        $golds=$this->_user['golds']-$arc['golds'];
        if($golds<0){
            E(1, '您的金币余额不足，请<a href="/my-vip.html" target="_blank" style="color: #0a5ba6">充值</a>后再试！');
        }
        if($arc['golds']<=0){
            E(1, '该内容不支持金币购买！');
        }
        //写入购买记录
        $data = array(
            'aid' => $_POST['id'],
            'uid' => $this->_user['uid'],
            'fee' => $arc['golds'],
            'pay_type' =>2,
            'create_time'=>time()
        );
        $id = $this->user_buy->create($data);
        if(!$id) {
            E(1, '购买失败！');
        }
        $updateData=array(
            'uid'=>$this->_user['uid'],
            'golds'=>$golds
        );
        $this->user->update($updateData);
        //更新余额
        E(0, '购买成功，你账户的金币余额为：'.$golds);
    }
    $data=array(
        'golds'=>$this->_user['golds'],
        'credits'=>$this->_user['credits'],
    );
    E(0, 'ok','',['data'=>$data]);
}
