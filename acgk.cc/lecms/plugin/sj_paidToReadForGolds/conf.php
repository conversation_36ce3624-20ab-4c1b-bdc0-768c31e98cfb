<?php
return array(
	'name' => '付费阅读(金币版)',	// 插件名
	'brief' => '付费阅读(金币版)插件可以设置任意一篇文章内容(部分内容或全部内容)需要付费(金币)才能阅读！配合礼品卡(即卡密)插件，可以实现无需支付接口的情况下，获得营收(发卡平台出售卡密)！
<br>插件安装完毕，启用即可，无需设置！',
	'version' => '1.0.0',			// 插件版本
	'cms_version' => '3.0.3',		// 插件支持的程序版本
	'update' => '2024-09-28',		// 插件最近更新
	'author' => 'isunjie',				// 插件作者
	'authorurl' => 'https://www.isunjie.cn/plugins/208.html',	// 插件作者主页
	'setting' => '',		// 插件设置URL
);