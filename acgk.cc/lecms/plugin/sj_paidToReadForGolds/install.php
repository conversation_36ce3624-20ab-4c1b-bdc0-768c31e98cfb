<?php
defined('ROOT_PATH') || exit;
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];	//表前缀
$sql = "ALTER TABLE ".$tableprefix."cms_article ADD COLUMN `golds`  int(7) NULL DEFAULT 1 COMMENT '消耗金币' AFTER `jumpurl`;";
$sql2="CREATE TABLE IF NOT EXISTS ".$tableprefix."user_buy (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) DEFAULT NULL,
  `aid` int(11) DEFAULT NULL,
  `fee` varchar(20) DEFAULT NULL,
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  `pay_type` int(1) DEFAULT '1' COMMENT '支付方式：1：积分2：金币3：支付宝4:微信',
  `delete_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
";
$this->db->query($sql);
$this->db->query($sql2);
//ALTER TABLE `le_cms_article` ADD COLUMN `glods`  int(7) NULL DEFAULT 0 COMMENT '消耗金币' AFTER `credits`;
