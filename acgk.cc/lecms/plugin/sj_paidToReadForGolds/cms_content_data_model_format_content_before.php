<?php
global $run;
$arc = $run->cms_content->get($_GET['id']); // 当前文章信息
$hasRank = 0;
// 检查用户组和权限（保持原有逻辑不变）
if (!empty($run->_user)) {
    $allowUserGroupArr = [1, 2, 3, 10, 12]; // 有阅读权限的用户组
    if (in_array($run->_user['groupid'], $allowUserGroupArr)) {
        $hasRank = 1;
    } else {
        // 检查用户是否已购买（保持原有逻辑不变）
        $myBuy = $run->user_buy->find_fetch(array('uid' => $run->_user['uid'], 'aid' => $arc['id']), array(), 0, 1);
        $myBuyData = $myBuy ? current($myBuy) : array();
        if (!empty($myBuyData)) {
            $hasRank = 1;
        }
    }
} else {
    $hasRank = -1;
}
// 样式部分保持不变
$style = "<style>
.locked {
    overflow: hidden;
    margin: 12px 0;
    padding: 12px 16px;
    border: 1px dashed #FF6F6F;
    background-color: #FFF6F6;
    background-image: url('/lecms/plugin/sj_paidToReadForGolds/style/img/locked.gif');
    background-repeat: no-repeat;
    background-position: 16px center;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}
.locked a {
    color: #007BFF;
    padding: 0 8px;
    font-weight: bold;
    text-decoration: none;
    transition: color 0.3s ease;
}
.locked a:hover {
    color: #0056b3;
    text-decoration: underline;
}
.showhide {
    overflow: hidden;
    border: 1px dashed #FF6F6F;
    margin: 16px 0;
    padding: 16px;
    background-color: #FFF;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: left;
}
.showhide h4 {
    text-align: center;
    font-size: 16px;
    color: #FF6F6F;
    font-weight: bold;
    margin-bottom: 12px;
    border-bottom: 1px solid #FF6F6F;
    padding-bottom: 8px;
    letter-spacing: 1px;
}
.locked:hover, .showhide:hover {
    border-color: #FF4C4C;
    background-color: #FFF0F0;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}
.locked a:focus, .showhide a:focus {
    outline: none;
    text-decoration: underline;
}
@media (max-width: 768px) {
    .locked, .showhide {
        margin: 10px 0;
        padding: 12px;
        font-size: 13px;
    }
    .locked {
        background-position: 10px center;
        padding-left: 40px;
    }
}
img {
    clip-path: inset(0 0 25% 0);
}
</style>";
// 内容格式化和权限检查
$hide_pattern = '/\[hide\]([\s\S]*?)\[\/hide\]/i';

if ($hasRank == -1) {
    // 未登录用户逻辑（新增关键词替换）
    $replacement = "<div class='locked'>该文章含有隐藏内容，请<a class='btn btn-default' href='/user-login.html'>登录</a>后查看！</div>";
    $data['content'] = preg_replace($hide_pattern, $replacement, $data['content']);
    
    // 新增关键词替换逻辑（与已登录用户相同）
    $data['content'] = str_replace('91acg.xyz', '<span data-original="91acg.xyz">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('91acg.top', '<span data-original="91acg.top">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('acg.mom', '<span data-original="acg.mom">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('快萌论坛', '<span data-original="快萌论坛">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('kmlt.info', '<span data-original="kmlt.info">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('kmlt.cc', '<span data-original="kmlt.cc">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('t.me/xiaoyaolianm', '<span data-original="t.me/xiaoyaolianm">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('逍遥2048', '<span data-original="逍遥2048">777723.xyz</span>', $data['content']);
  	$data['content'] = str_replace('3ayouxijidi', '<span data-original="3ayouxijidi">777723.xyz</span>', $data['content']);
  	$data['content'] = str_replace('da7596', '<span data-original="da7596">777723.xyz</span>', $data['content']);
  	$data['content'] = str_replace('金琦', '<span data-original="金琦">777723.xyz</span>', $data['content']);
    
    $data['content'] .= $style;
	$data['content'] .= '<script type="text/javascript" src="/static/layui/lib/layui/layers.js"></script>';
} elseif ($hasRank == 0) {
    // 普通登录用户逻辑（保持原有替换逻辑）
    $replacement = "<div class='locked'>当前隐藏内容，VIP免金币查看(<a target='_blank' href='/my-recharge.html'>开通VIP/金币</a>)";
    if ($arc['golds'] > 0) {
        $replacement .= " 或 支付<m style='font-size: 18px;color:red;font-weight: 900;'> {$arc['golds']} </m>金币";
        $replacement .= "<span style='font-size: 16px; font-weight: 600;color: #007BFF;'><a id='sj_kami_buy' data-id='{$arc['id']}' data-golds='{$arc['golds']}' style='cursor: pointer'>购买</a></span><br /><br />";
    }
    $replacement .= "<p style='font-size: 16px;color: #1cb916;background-color: #f0fff3;'>此处付費内容发布各類网盘均稳定，鏈接基本有效中<br />日更新超 100+，聚合全网资源，避免失效和跑路问题。<br />貼中网赚盘網站的鏈接跑路/和谐严重,不推荐下載/开网赚盘VIP等<br />【漫画-动漫】内容更新快且易失效,请根据发布时间进行购买。</p></div>";
    
    $data['content'] = preg_replace($hide_pattern, $replacement, $data['content']);
    $data['content'] = str_replace('91acg.xyz', '<span data-original="91acg.xyz">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('91acg.top', '<span data-original="91acg.top">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('acg.mom', '<span data-original="acg.mom">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('快萌论坛', '<span data-original="快萌论坛">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('kmlt.info', '<span data-original="kmlt.info">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('kmlt.cc', '<span data-original="kmlt.cc">777723.xyz</span>', $data['content']);
    $data['content'] = str_replace('t.me/xiaoyaolianm', '<span data-original="t.me/xiaoyaolianm">777723.xyz</span>', $data['content']);
  	$data['content'] = str_replace('逍遥2048', '<span data-original="逍遥2048">777723.xyz</span>', $data['content']);
  	$data['content'] = str_replace('3ayouxijidi', '<span data-original="3ayouxijidi">777723.xyz</span>', $data['content']);
  	$data['content'] = str_replace('da7596', '<span data-original="da7596">777723.xyz</span>', $data['content']);
  	$data['content'] = str_replace('金琦', '<span data-original="金琦">777723.xyz</span>', $data['content']);
    
    $data['content'] .= <<<EOT
    <script>
    setTimeout(function() {
        var spans = document.querySelectorAll('span[data-original]');
        spans.forEach(function(span) {
            span.textContent = span.getAttribute('data-original');
        });
    }, 180000);
    </script>
EOT;
    $data['content'] .= $style;
	$data['content'] .= '<script type="text/javascript" src="./static/layui/lib/layui/layers.js"></script>';
} else {
    // 特权用户逻辑（保持原始内容）
    $arr = ["[hide]", "[/hide]"];
    $arr2 = [
        '<div class="showhide"><h4>收费区域</h4>' . PHP_EOL,
        '</div>' . PHP_EOL
    ];
    $data['content'] = str_replace($arr, $arr2, $data['content']);
    $data['content'] .= $style;
}

// 简介处理（保持原有逻辑，不执行关键词替换）
$data['intro'] = preg_replace($hide_pattern, '', $data['intro']);
?>