<!-- 移除重复的jQuery加载，使用页面已有的jQuery -->
<script type="text/javascript" src="/lecms/plugin/sj_paidToReadForGolds/style/layer/layer.js"></script>
<style>
/* 强制重置弹窗样式 - 防止被其他插件CSS影响 */
.layui-layer,
.layui-layer *,
.layui-layer-dialog,
.layui-layer-content,
.layui-layer-btn,
.layui-layer-title {
    clip-path: none !important;
    clip: none !important;
    transform: none !important;
}

/* 强制弹窗居中显示 */
.layui-layer {
    position: fixed !important;
    z-index: 99999999 !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .layui-layer {
        width: 90% !important;
        max-width: 350px !important;
    }
}
/* 修复layer弹窗居中显示问题 - 防止被其他CSS影响 */
.layui-layer {
    position: fixed !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
    z-index: 99999999 !important;
    /* 重置可能被其他样式影响的属性 */
    clip-path: none !important;
    clip: none !important;
    overflow: visible !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 确保弹窗内容不被裁剪 */
.layui-layer * {
    clip-path: none !important;
    clip: none !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .layui-layer {
        width: 90% !important;
        max-width: 350px !important;
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
    }

    .layui-layer-content {
        font-size: 14px !important;
        line-height: 1.5 !important;
    }
}

/* 修复可能的CSS冲突 */
.layui-layer-dialog,
.layui-layer-page,
.layui-layer-iframe,
.layui-layer-loading {
    clip-path: none !important;
    clip: none !important;
    position: fixed !important;
}
</style>
<script>
    $('#sj_kami_buy').on('click',function (e){
        var aid=$(this).attr('data-id');
        var golds=$(this).attr('data-golds');
        $.get('/my-userinfo.html','',function(data){
            if(!data.err){
                layer.confirm('您当前有 <span style="color: red">'+data.data.golds+' </span>金币。本次购买需消耗 <span style="color: red">'+golds+' </span>金币!<br/>确认进行购买操作？', {
                  btn: ['确定','取消'],
                  icon: 3,
                  offset: 'auto', // 自动居中
                  area: ['400px', 'auto'], // 设置宽度，高度自适应
                  maxWidth: '90%', // 最大宽度
                  resize: false, // 禁止拖拽调整大小
                  move: '.layui-layer-title', // 只允许标题栏拖拽
                  success: function(layero, index){
                      // 弹窗成功后强制居中
                      var winWidth = $(window).width();
                      var winHeight = $(window).height();
                      var layerWidth = layero.outerWidth();
                      var layerHeight = layero.outerHeight();

                      layero.css({
                          'left': (winWidth - layerWidth) / 2 + 'px',
                          'top': (winHeight - layerHeight) / 2 + 'px'
                      });
                  }
                }, function(){
                    $.post('/my-userinfo.html',{id:aid},function(data){
                        if(!data.err){
                            layer.alert(data.msg,{
                                icon:1,
                                offset: 'auto',
                                area: ['300px', 'auto'],
                                success: function(layero, index){
                                    // 成功提示也居中
                                    var winWidth = $(window).width();
                                    var winHeight = $(window).height();
                                    var layerWidth = layero.outerWidth();
                                    var layerHeight = layero.outerHeight();

                                    layero.css({
                                        'left': (winWidth - layerWidth) / 2 + 'px',
                                        'top': (winHeight - layerHeight) / 2 + 'px'
                                    });
                                }
                            },function (e){
                                window.location.reload()
                            })
                        }else{
                            layer.alert(data.msg,{
                                icon:2,
                                offset: 'auto',
                                area: ['300px', 'auto'],
                                success: function(layero, index){
                                    // 错误提示也居中
                                    var winWidth = $(window).width();
                                    var winHeight = $(window).height();
                                    var layerWidth = layero.outerWidth();
                                    var layerHeight = layero.outerHeight();

                                    layero.css({
                                        'left': (winWidth - layerWidth) / 2 + 'px',
                                        'top': (winHeight - layerHeight) / 2 + 'px'
                                    });
                                }
                            })
                        }
                    },'json');
                });
            }else{
                layer.msg('余额获取失败！', {
                    offset: 'auto',
                    success: function(layero, index){
                        // 消息提示也居中
                        var winWidth = $(window).width();
                        var winHeight = $(window).height();
                        var layerWidth = layero.outerWidth();
                        var layerHeight = layero.outerHeight();

                        layero.css({
                            'left': (winWidth - layerWidth) / 2 + 'px',
                            'top': (winHeight - layerHeight) / 2 + 'px'
                        });
                    }
                })
            }
        },'json');
    })

    // 窗口大小改变时重新居中所有layer弹窗
    $(window).resize(function(){
        $('.layui-layer').each(function(){
            var $this = $(this);
            var winWidth = $(window).width();
            var winHeight = $(window).height();
            var layerWidth = $this.outerWidth();
            var layerHeight = $this.outerHeight();

            $this.css({
                'left': (winWidth - layerWidth) / 2 + 'px',
                'top': (winHeight - layerHeight) / 2 + 'px'
            });
        });
    });
</script>