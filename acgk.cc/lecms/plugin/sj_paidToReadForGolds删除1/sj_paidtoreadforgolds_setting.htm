
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>付费阅读(消耗金币设置)</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<div class="layui-card">
	<div class="layui-card-header">付费阅读批量设置(消耗金币)</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?sj_paidtoreadforgolds-setting" method="post">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label required">金币</label>
					<div class="layui-input-inline">
						<input name="golds" type="number" value="1" class="layui-input" required="required" lay-verify="required"  />
					</div>
					<div class="layui-form-mid layui-word-aux">每篇文章消耗的金币数量！</div>

				</div>
			</div>
			<div class="layui-form-item" id="i_type">
				<label class="layui-form-label required">修改范围</label>
				<div class="layui-input-block type">
					<input  name="type" type="radio" value="1" title="全部内容" checked lay-filter="type">
					<input name="type" type="radio" value="3" title="金币等于" lay-filter="type">
					<input  name="type" type="radio" value="4" title="ID大于" lay-filter="type">
				</div>
			</div>
			<div class="layui-form-item layui-hide" id="num">
				<div class="layui-inline">
					<label class="layui-form-label">数值</label>
					<div class="layui-input-inline">
						<input name="num" type="number" value="1" class="layui-input"  />
					</div>
					<div class="layui-form-mid layui-word-aux">修改范围选择“金币等于”或“ID大于”时，请填写此内容！举个例子：例如选择金币等于，此内容填写为10，效果为修改金币等于10的所有内容！</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit   lay-filter="form">保存</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
layui.use(['form','layer'], function(){
	var layer = layui.layer, form = layui.form;
	//监听提交
	  form.on('radio(type)', function(data){
		if(data.value==3 || data.value==4){
			$('#num').removeClass('layui-hide')
		}else{
			$('#num').addClass('layui-hide')
		}
	  });
		//监听提交
		form.on('submit(form)', function () {
			if (window.hasOwnProperty('editor')) {
				window.editor.async();
			}
			adminAjax.postform('#form',function (data) {
				if( data.err == 0 ){
					var icon = 1;
				}else{
					var icon = 5;
				}
				layer.msg(data.msg, {icon: icon});
			});
			return false;
		});
	function isInteger(value) {
		if (Number.isInteger(parseInt(value, 10))) {
			return true;
		} else {
			return false;
		}
	}
});
</script>
</body>
</html>
