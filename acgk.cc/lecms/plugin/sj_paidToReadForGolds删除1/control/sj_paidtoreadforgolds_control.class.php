<?php
defined('ROOT_PATH') or exit;
class sj_paidtoreadforgolds_control  extends admin_control
{
    public function setting()
    {
        if($_POST){
            $value=R('golds','P');
            $type=R('type','P');
            $num=R('num','P');
            if(empty($value) || !is_numeric($value)){
                E(1,'金币设置错误');
            }
            if($type==2 || $type==3){
                if(empty($num) || !is_numeric($num)){
                    E(1,'数值设置错误');
                }
            }
            try{
                //修改全部
                if($type==1){
                    $where=['id'=>['>'=>0]];
                }
                //修改金币等于x的
                if($type==3){
                    $where=['golds'=>$num];
                }
                if($type==4){
                    $where=['id'=>['>'=>$num]];
                }
                $rs=$this->cms_article->find_update($where,['golds'=>$value]);
            }catch (Exception $e){
                 E(1,'系统错误：'.$e->getMessage());
            }
            E(0,'设置成功');
        }
        $this->display();
    }
}