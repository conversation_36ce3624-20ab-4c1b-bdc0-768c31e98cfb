<?php
global $run;
;//当前文章信息
$arc=$run->cms_content->get($_GET['id']);
//检查阅读权限、检查用户等级、是否已购买
$hasRank=0;
if(!empty($run->_user)){
    $allowUserGroupArr=[1,2,3,10,12];//
    //有阅读权限的用户组
    if(in_array($run->_user['groupid'],$allowUserGroupArr)){
        $hasRank=1;
    }else{
        //是否已购买此内容
        $myBuy=$run->user_buy->find_fetch(array('uid'=>$run->_user['uid'],'aid'=>$arc['id']), array(), 0, 1);
        $myBuyData = $myBuy ? current($myBuy) : array();
        if(!empty($myBuyData)){
            $hasRank=1;
        }
    }
}else{
    $hasRank=-1;
}
$style="<style>
.locked {
    overflow: hidden;
    margin: 12px 0; /* 优化上下间距 */
    padding: 12px 16px;
    border: 1px dashed #FF6F6F;
    background-color: #FFF6F6;
    background-image: url('/lecms/plugin/sj_paidToReadForGolds/style/img/locked.gif');
    background-repeat: no-repeat;
    background-position: 16px center;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    text-align: center; 
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.locked a {
    color: #007BFF;
    padding: 0 8px;
    font-weight: bold;
    text-decoration: none;
    transition: color 0.3s ease;
}

.locked a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.showhide {
    overflow: hidden;
    border: 1px dashed #FF6F6F;
    margin: 16px 0; /* 优化上下间距 */
    padding: 16px;
    background-color: #FFF;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: left;
}

.showhide h4 {
    text-align: center; 
    font-size: 16px;
    color: #FF6F6F;
    font-weight: bold;
    margin-bottom: 12px; 
    border-bottom: 1px solid #FF6F6F;
    padding-bottom: 8px;
    letter-spacing: 1px;
}

.locked:hover, .showhide:hover {
    border-color: #FF4C4C;
    background-color: #FFF0F0;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.locked a:focus, .showhide a:focus {
    outline: none;
    text-decoration: underline;
}

@media (max-width: 768px) {
    .locked, .showhide {
        margin: 10px 0;
        padding: 12px;
        font-size: 13px;
    }

    .locked {
        background-position: 10px center;
        padding-left: 40px;
    }
}

</style>";
//格式化内容
if(-1==$hasRank){
    $replacement= "<div class='locked'>该文章含有隐藏内容，请<a class='btn btn-default' href='/user-login.html'>登录</a>后查看！</div>";
    $data['content'] = preg_replace('/\[hide\].*?\[\/hide\]/', $replacement, $data['content']);
    $data['content'].=$style;
}elseif(0==$hasRank){
    $replacement= "<div class='locked'>当前隐藏内容，VIP免金币查看(<a target='_blank' href='/my-vip.html'>去开通</a>)";
    if($arc['golds']>0){
        $replacement.=" 或 支付<m style='font-size: 18px;color:red;font-weight: 900;'> {$arc['golds']} </m>金币";
        $replacement.="<span><a id='sj_kami_buy' data-id='{$arc['id']}' data-golds='{$arc['golds']}' style='cursor: pointer'>购买</a></span><br /><br />";
    }
  	$replacement.="<br /><p style='font-size: 16px;color: #1cb916;'>此隐藏内容有失效可能,请根据发布时间判断<br />【漫画-动漫】失效周期快,不介意金币购买<br />失效不在补档,请您理解,感谢支持</p>";
    $replacement.="</div>";
    $data['content'] = preg_replace('/\[hide\](.|\s)*?\[\/hide\]/', $replacement, $data['content']);
    $data['content'].=$style;
}else{
    $arr=["[hide]","[/hide]"];
    $arr2=[
        '<div class="showhide"><h4>收费区域</h4>'.PHP_EOL,
        '</div>'.PHP_EOL
    ];
    $data['content'] = str_replace($arr, $arr2, $data['content']);
    $data['content'].=$style;
}
$data['intro'] = preg_replace('/\[hide\](.|\s)*?\[\/hide\]/', '', $data['intro']);


