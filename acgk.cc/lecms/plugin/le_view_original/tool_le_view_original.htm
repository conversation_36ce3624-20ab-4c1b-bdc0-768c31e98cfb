{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">模板伪原创设置（更改配置后请清除文件缓存）</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?tool-le_view_original-ajax-1" method="post">
			<div class="layui-form-item">
				<label class="layui-form-label required">前缀</label>
				<div class="layui-input-inline">
					{$input[css_class_prefix]}
				</div>
				<div class="layui-form-mid layui-word-aux">给所有的class加上指定前缀</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label required">长度</label>
				<div class="layui-input-inline">
					{$input[css_class_length]}
				</div>
				<div class="layui-form-mid layui-word-aux">前缀后面的固定长度</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">分隔符</label>
				<div class="layui-input-inline">
					{$input[css_class_separator]}
				</div>
				<div class="layui-form-mid layui-word-aux">建议用 - _</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">加密方式</label>
				<div class="layui-input-block">
					{$input[css_class_type]}
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">保存</button>
				</div>
			</div>
		</form>
	</div>
</div>
<script type="text/javascript">
	layui.use('form', function(){
		var form = layui.form;

		//监听提交
		form.on('submit(form)', function(){
			adminAjax.submit("#form");
		});
	});
</script>
</body>
</html>
