<?php
//模板伪原创插件设置
function le_view_original(){
    if(empty($_POST)){
        $setting = $this->kv->get('view_original_setting');
        $input = array();

        $input['css_class_prefix'] = form::get_text('css_class_prefix', $setting['css_class_prefix']);
        $input['css_class_length'] = form::get_number('css_class_length', $setting['css_class_length']);
        $input['css_class_separator'] = form::get_text('css_class_separator', $setting['css_class_separator']);

        $arr = array(0=>'简单', 1=>'复杂');
        $input['css_class_type'] = form::get_radio_layui('css_class_type', $arr, $setting['css_class_type']);

        $this->assign('input', $input);
        $this->display();
    }else{
        _trim($_POST);

        $arr = array(
            'css_class_prefix'=>R('css_class_prefix', 'P'),
            'css_class_length'=>(int)R('css_class_length', 'P'),
            'css_class_separator'=>R('css_class_separator', 'P'),
            'css_class_type'=>(int)R('css_class_type', 'P')
        );
        empty($arr['css_class_prefix']) && E(1, '前缀不能为空！');
        empty($arr['css_class_length']) && E(1, '长度不能为空！');

        $this->kv->set('view_original_setting', $arr);

        E(0, '修改成功！');
    }
}