{inc:user/header.htm}
<style>
  .panel-post {position: relative;}
  .btn-post {position: absolute;right: 0;bottom: 10px;}
  .img-border {border-radius: 3px;box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);}
  .embed-responsive img {position: absolute;object-fit: cover;width: 100%;height: 100%;border: 0;}
  .comment-content {padding: 15px;background: #efefef;color: #444;}
  .panel-user h4 {font-weight: normal;font-size: 14px;}
  .float-right{float: right;}
  .pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span{border-bottom-right-radius:3px;border-top-right-radius:3px;}
  .pager li:first-child > a, .pager li:last-child > a, .pager li:first-child > span, .pager li:last-child > span{border-bottom-left-radius:3px;border-top-left-radius:3px;}
</style>
  <main class="content">
    <div id="content-container" class="container">
      <div class="row">
        {inc:user/menu.htm}
        <!--右侧主体部分 start-->
        <div class="col-md-9">
          <div class="panel panel-default">
            <div class="panel-body">
              <h2 class="page-header">{lang:my_favorites}</h2>
              {if:empty($favorites_arr)}
              <div class="alert alert-warning"><b>{lang:no_data}</b></div>
              {else}
              {loop:$favorites_arr $v}
              <div class="row">
                {php}$cms = isset($cms_arr[$v['id']]) ? $cms_arr[$v['id']] : array();{/php}
                <div class="col-md-12">
                  <h4>
                    <a href="{$cms[url]}" title="{$cms[title]}" target="_blank">{$cms[title]}</a>
                  </h4>
                  <p class="text-muted">{lang:date}：{$v[date]}<button type="button" fid="{$v[fid]}" class="del btn btn-danger btn-xs float-right">{lang:delete}</button></p>
                </div>
              </div>
              <hr/>
              {/loop}
              {/if}
              {if:$pages}<div class="pager">{$pages}</div>{/if}
            </div>
          </div>
        </div>
        <!--右侧主体部分 end-->
      </div>
    </div>
  </main>
{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;

    $("#my-favorites").addClass("active");

    $(".del").click(function () {
      var fid = $(this).attr("fid");
      layer.confirm('{lang:delete_confirm}', {
        btn: ['{lang:confirm}','{lang:cancel}'],
        title:'{lang:tips}'
      }, function () {
        $.post("index.php?my-favorites-act-del-ajax-1",{fid: fid},function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location.reload(); }, 1000);
          return false;
        },'json');
      });
    });
  });
</script>
</body>
</html>