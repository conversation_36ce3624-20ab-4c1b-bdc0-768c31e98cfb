<?php
//收藏插件 个人中心 我的收藏
public function favorites(){
    if( empty($_POST) ) {
        $models = $this->_cfg['table_arr'];

        $where['uid'] = $this->_uid;
        // 初始分页
        $pagenum = 10;
        $total = $this->favorites->find_count($where);
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, intval(R('page'))));
        $pages = paginator::pages_bootstrap($page, $maxpage, 'index.php?my-favorites-page-{page}'); //这里使用bootstrap风格
        $this->assign('pages', $pages);
        $this->assign('total', $total);

        $cms_arr = array();
        $favorites_arr = $this->favorites->list_arr($where, 'fid', -1, ($page-1)*$pagenum, $pagenum, $total);
        foreach($favorites_arr as $k=>&$v) {
            $v['date'] = isset($v['dateline']) ? date('Y-m-d H:i:s', $v['dateline']) : date('Y-m-d H:i:s');

            $id = $v['id'];
            $mid = $v['mid'];
            $table = isset($models[$mid]) ? $models[$mid] : '';
            if( !empty($table)  ){
                if( !isset($cms_arr[$id]) ){
                    $this->cms_content->table = 'cms_'.$table;
                    $data = $this->cms_content->get($id);
                    if($data){
                        $this->cms_content->format($data, $mid);
                        $cms_arr[$id] = $data;
                    }else{
                        $this->favorites->delete($v['fid']);    //内容不存在的，则收藏也删除
                    }
                }
            }else{
                $this->favorites->delete($v['fid']);    //模型不存在的，则收藏也删除
            }
        }

        $this->assign('favorites_arr', $favorites_arr);
        $this->assign('cms_arr', $cms_arr);

        $this->_cfg['titles'] = lang('my_favorites').'_'.$this->_cfg['webname'];
        $this->_var['topcid'] = -1;

        $this->assign('cfg', $this->_cfg);
        $this->assign('cfg_var', $this->_var);

        $GLOBALS['run'] = &$this;
        $_ENV['_theme'] = &$this->_cfg['theme'];

        $this->display('my_favorites.htm');
    }else{
        $act = R('act','R');
        if($act == 'del'){
            $fid = (int)R('fid','P');
            if( empty($fid) ){
                $this->message(0, lang('data_error'));
            }else{
                $data = $this->favorites->get($fid);
                if( empty($data) || $data['uid'] != $this->_uid){
                    $this->message(0, lang('data_no_exists'));
                }else{
                    $err = $this->favorites->xdelete($fid, $this->_uid);
                    if($err){
                        $this->message(0, $err);
                    }else{
                        $this->message(1, lang('delete_successfully'));
                    }
                }
            }
        }
    }
}