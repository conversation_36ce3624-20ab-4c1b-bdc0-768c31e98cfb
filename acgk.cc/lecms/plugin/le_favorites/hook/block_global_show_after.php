<?php
//收藏插件 判断内容是否已经收藏
if( isset($_show['favorites']) ){
    $uid = isset($run->_user['uid']) ? (int)$run->_user['uid'] : 0;
    if( empty($uid) ){
        $_show['has_favorites'] = 0;
    }else{
        if( $run->favorites->find_fetch_key(array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id)) ){
            $_show['has_favorites'] = 1;
        }else{
            $_show['has_favorites'] = 0;
        }
    }
    $_show['favorites_url'] = $run->_cfg['webdir'].'index.php?show-favorites-mid-'.$mid.'-id-'.$id.'-uid-'.$uid.'.html';
}