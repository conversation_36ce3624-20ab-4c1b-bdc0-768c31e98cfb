<?php
defined('ROOT_PATH') or exit;

class favorites extends model {

    function __construct() {
        $this->table = 'favorites';	// 表名
        $this->pri = array('fid');	// 主键
        $this->maxid = 'fid';		// 自增字段
    }

    // 获取内容列表
    public function list_arr($where, $orderby, $orderway, $start, $limit, $total) {
        // 优化大数据量翻页
        if($start > 1000 && $total > 2000 && $start > $total/2) {
            $orderway = -$orderway;
            $newstart = $total-$start-$limit;
            if($newstart < 0) {
                $limit += $newstart;
                $newstart = 0;
            }
            $list_arr = $this->find_fetch($where, array($orderby => $orderway), $newstart, $limit);
            return array_reverse($list_arr, TRUE);
        }else{
            return $this->find_fetch($where, array($orderby => $orderway), $start, $limit);
        }
    }

    //收藏和取消收藏
    public function do_favorites($uid = 0, $mid = 0, $id = 0){
        if( empty($mid) || empty($id) ){
            return array('err'=>1, 'msg'=>lang('data_error'));
        }elseif ( empty($uid) ){
            return array('err'=>1, 'msg'=>lang('please_login'));
        }

        $models = $this->models->get($mid);
        if( empty($models) ){
            return array('err'=>1, 'msg'=>lang('data_error'));
        }
        $table = $models['tablename'];
        // 初始模型表名
        $this->cms_content->table = 'cms_'.$table;
        $_show = $this->cms_content->read($id);
        if( empty($_show) ){
            return array('err'=>1, 'msg'=>lang('data_error'));
        }

        $where = array('uid'=>$uid, 'mid'=>$mid, 'id'=>$id);
        $data = $this->find_fetch($where, array('fid' => 1), 0, 1);
        if($data) {
            $data = current($data);
            if( isset($_show['favorites']) && $_show['favorites'] ){
                $favorites = $_show['favorites'] - 1;

                $this->cms_content->update(array('id'=>$id, 'favorites'=>$favorites));
            }else{
                $favorites = 0;
            }
            $this->delete($data['fid']);
            return array('err'=>0, 'msg'=>lang('cancel_favorites'), 'favorites_count'=>$favorites, 'has_favorites'=>0);
        }else{
            if( isset($_show['favorites']) ){
                $favorites = $_show['favorites'] + 1;
                $this->cms_content->update(array('id'=>$id, 'favorites'=>$favorites));

                $post = array('mid'=>$mid, 'id'=>$id, 'uid'=>$uid, 'dateline'=>$_ENV['_time']);
                $fid = $this->create($post);
                if( $fid ){
                    return array('err'=>0, 'msg'=>lang('favorites_successfully'), 'favorites_count'=>$favorites, 'has_favorites'=>1);
                }else{
                    return array('err'=>1, 'msg'=>lang('favorites_failed'));
                }
            }else{
                return array('err'=>1, 'msg'=>lang('favorites_failed'));
            }
        }
    }

    //删除收藏
    public function xdelete($fid = 0, $uid = 0, $isadmin = 0){
        $data = $this->get($fid);
        if(empty($data)) return lang('data_error');

        if(!$isadmin && $data['uid'] != $uid){
            return lang('delete_failed');
        }
        if( $this->delete($fid) ){
            $models = $this->models->get($data['mid']);
            if( $models ){
                $table = $models['tablename'];
                // 初始模型表名
                $this->cms_content->table = 'cms_'.$table;
                $_show = $this->cms_content->read($data['id']);
                if( isset($_show['favorites']) && $_show['favorites'] ){
                    $favorites = $_show['favorites'] - 1;
                    $this->cms_content->update(array('id'=>$data['id'], 'favorites'=>$favorites));
                }
            }
            return '';
        }else{
            return lang('delete_failed');
        }
    }
}
