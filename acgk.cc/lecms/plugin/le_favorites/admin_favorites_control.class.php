<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-12-05
 * Time: 10:32
 * Description: 收藏夹插件 控制器
 */

defined('ROOT_PATH') or exit;

class admin_favorites_control extends admin_control {

    // 内容管理
    public function index() {
        // 模型名称
        $mod_name = array();

        $models = $this->models->get_models();
        foreach ($models as $v){
            if($v['mid'] > 1){
                if( $_ENV['_config']['FORM_HASH'] == 'zh-cn' ){
                    $modelname = $v['name'];
                }else{
                    $modelname = ucfirst($v['tablename']);
                }
                $mod_name[$v['mid']] = $modelname;
            }
        }

        $this->assign('mod_name', $mod_name);
        $this->display();
    }

    //获取列表
    public function get_list(){
        //分页
        $page = isset( $_REQUEST['page'] ) ? intval($_REQUEST['page']) : 1;
        $pagenum = isset( $_REQUEST['limit'] ) ? intval($_REQUEST['limit']) : 15;

        //获取查询条件
        $mid = isset( $_REQUEST['mid'] ) ? max(2, intval($_REQUEST['mid']) ) : 2;
        $uid = isset( $_REQUEST['uid'] ) ? intval($_REQUEST['uid']) : 0;

        //组合查询条件
        $where = array();
        if( $uid ){
            $where['uid'] = $uid;
        }
        $where['mid'] = $mid;

        //数据量
        $total = $this->favorites->find_count($where);

        //页数
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, $page));

        // 获取列表
        $data_arr = array();
        $cms_arr = $this->favorites->list_arr($where, 'fid', -1, ($page-1)*$pagenum, $pagenum, $total);
        $keys = array();
        foreach($cms_arr as $v) {
            $keys[] = $v['id'];
        }

        $models = $this->models->get($mid);
        $table = $models['tablename'];

        $this->cms_content->table = 'cms_'.$table;
        $list_arr = $this->cms_content->mget($keys);
        foreach ($list_arr as &$c){
            $this->cms_content->format($c, $mid);
        }

        $content_key = 'cms_'.$table.'-id-';

        foreach($cms_arr as &$v) {
            if(isset($list_arr[$content_key.$v['id']])){
                $v['title'] = '<a target="_blank" href="'.$list_arr[$content_key.$v['id']]['url'].'">'.$list_arr[$content_key.$v['id']]['title'].'</a>';
            }else{
                $v['title'] = lang('unknown');
            }
            $v['date'] = isset($v['dateline']) ? date('Y-m-d H:i:s', $v['dateline']) : date('Y-m-d H:i:s');
            $data_arr[] = $v;   //排序需要索引从0开始
        }
        unset($cms_arr);
        //组合数据 输出到页面
        $arr = array(
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data_arr,
        );
        exit( json_encode($arr) );
    }

    // 删除
    public function del() {
        $id = (int) R('id', 'P');

        empty($id) && E(1, lang('data_error'));

        $err = $this->favorites->xdelete($id, 0, 1);
        if($err) {
            E(1, $err);
        }else{
            E(0, lang('delete_successfully'));
        }
    }

    // 批量删除
    public function batch_del() {
        $id_arr = R('id_arr', 'P');

        if(!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach($id_arr as $id) {
                $err = $this->favorites->xdelete($id, 0, 1);
                if($err) $err_num++;
            }

            if($err_num) {
                E(1, $err_num.lang('num_del_failed'));
            }else{
                E(0, lang('delete_successfully'));
            }
        }else{
            E(1, lang('data_error'));
        }
    }
}
