<?php
defined('ROOT_PATH') || exit;
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];	//表前缀

$sql_table = "CREATE TABLE IF NOT EXISTS ".$tableprefix."favorites (
  fid int(10) unsigned NOT NULL AUTO_INCREMENT,
  mid tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '模型ID',
  id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '内容ID',
  uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  dateline int(10) unsigned NOT NULL DEFAULT '0' COMMENT '收藏时间',
  PRIMARY KEY (fid),
  KEY uid_mid (uid,mid),
  UNIQUE KEY uid_mid_id (uid,mid,id)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";
$this->db->query($sql_table);

//文章主表增加字段
$cms_article_field = $this->db->get_field('cms_article');
if( !in_array('favorites', $cms_article_field) ){
    $sql = "ALTER TABLE {$tableprefix}cms_article ADD COLUMN favorites int(10) unsigned NOT NULL DEFAULT '0' COMMENT '收藏数'";
    $this->db->query($sql);
}