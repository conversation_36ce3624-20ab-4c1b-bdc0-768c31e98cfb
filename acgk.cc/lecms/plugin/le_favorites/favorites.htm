{if:isset($gdata[favorites])}
<style>
        .content_favorites {
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background-color: #f9f9f9; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); 
        }

        #favorites_do {
            margin-right: 5px; 
            color: #ffcc00;
        }

        #favorites_html {
            font-weight: bold; 
        }

        #favorites_count {
            color: #888;
        }
</style>
<div class="content_favorites">
    {if:$gdata[has_favorites]}
    <i id="favorites_do" class="fa fa-star" aria-hidden="true"><font id="favorites_html">取消收藏</font></i> 
    {else}
    <i id="favorites_do" class="fa fa-star-o" aria-hidden="true"><font id="favorites_html">加入收藏</font></i>
    {/if}
</div>
<script type="text/javascript">
    $("#favorites_do").click(function () {
        $.getJSON("{$gdata[favorites_url]}", function(data){
            if(data.err){
                alert(data.msg);
            }else{
                if( data.has_favorites == 1 ){
                    $("#favorites_do").attr("class", "fa fa-star");
                    $("#favorites_html").html("取消收藏");
                }else{
                    $("#favorites_do").attr("class", "fa fa-star-o");
                    $("#favorites_html").html("加入收藏");
                }
                $("#favorites_count").html(data.favorites_count);
                alert(data.msg);
            }
        });
    });
</script>
{/if}