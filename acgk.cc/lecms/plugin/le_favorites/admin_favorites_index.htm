{inc:header.htm}
<div class="layuimini-container">
	<div class="layuimini-main">
		<fieldset class="table-search-fieldset">
			<legend>搜索</legend>
			<div>
				<form class="layui-form layui-form-pane">
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">内容模型</label>
							<div class="layui-input-inline">
								<select name="mid" id="mid">
									{loop:$mod_name $v $k}<option value="{$k}">{$v}</option>{/loop}
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">用户ID</label>
							<div class="layui-input-inline">
								<input placeholder="请输入用户ID" autocomplete="off" type="number" class="layui-input" name="uid" />
							</div>
						</div>
						<div class="layui-inline">
							<button type="submit" class="layui-btn layui-btn-primary"  lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
						</div>
					</div>
				</form>
			</div>
		</fieldset>
		<script type="text/html" id="toolbar">
			<div class="layui-btn-container">
				<button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete">批量删除</button>
			</div>
		</script>
		<table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table>

		<script type="text/html" id="currentTableBar">
			<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
		</script>
	</div>
</div>

<script type="text/javascript">
	layui.use(['form','layer', 'table'], function () {
		var layer = layui.layer, form = layui.form, table = layui.table;
		var mid = $("#mid").val();

		table.render({
			elem: '#data-table',
			url: 'index.php?admin_favorites-get_list-mid-'+mid+'-',
			height: 'full-145',
			toolbar: '#toolbar',
			defaultToolbar: ['filter', 'exports', 'print'],
			cellMinWidth: 50,
			cols: [[
				{type: "checkbox", width: 50, fixed: 'left'},
				{field: 'uid', width: 100, title: '用户ID', align: 'center'},
				{field: 'title', title: '收藏内容'},
				{field: 'date', width: 165, title: '收藏时间', align: 'center'},
				{title: '操作', width: 65, toolbar: '#currentTableBar', align: "center"}
			]],
			limits: [10, 15, 20, 25, 50, 100],
			limit: 15,
			page: true
		});
		// 监听搜索操作
		form.on('submit(data-search-btn)', function (data) {
			//执行搜索重载
			table.reload('data-table', {
				page: {
					curr: 1
				}
				, where: {
					mid: data.field.mid,
					uid: data.field.uid
				}
			}, 'data');

			return false;
		});
		/**
		 * toolbar监听事件 table列表 头部的操作
		 */
		table.on('toolbar(data-table-filter)', function (obj) {
			if (obj.event === 'delete') {  // 监听删除操作
				var checkStatus = table.checkStatus('data-table')
						, data = checkStatus.data;
				var len = data.length;
				if(len == 0){
					layer.msg('请选择数据！',{icon:5});
					return false;
				}else{
					layer.confirm('删除不可恢复，确定删除？', function () {
						var id_arr = [];
						for (var i in data) {
							id_arr[i] = data[i]['fid'];
						}
						adminAjax.postd("index.php?admin_favorites-batch_del-ajax-1", {"id_arr": id_arr});
					});
				}
			}
		});

		//监听每一行的操作
		table.on('tool(data-table-filter)', function (obj) {
			var data = obj.data;

			if (obj.event === 'delete') {
				layer.confirm('删除不可恢复，确定删除？', function () {
					adminAjax.postd("index.php?admin_favorites-del-ajax-1", {"id":data.fid});
				});
			}
		});
	});
</script>
</body>
</html>
