{inc:user/header.htm}
<style>
  .result-container {
    text-align: center;
    padding: 60px 20px;
  }
  .result-icon {
    font-size: 80px;
    margin-bottom: 30px;
  }
  .success-icon {
    color: #28a745;
  }
  .pending-icon {
    color: #ffc107;
  }
  .error-icon {
    color: #dc3545;
  }
  .result-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .result-desc {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
  }
  .order-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 30px 0;
    text-align: left;
  }
  .order-info .row {
    margin-bottom: 10px;
  }
  .order-info .row:last-child {
    margin-bottom: 0;
  }
  .btn-group {
    margin-top: 30px;
  }
  .btn-group .btn {
    margin: 0 10px;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: bold;
  }
  .auto-check {
    background: #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
  }
</style>

<main class="content">
  <div id="content-container" class="container">
    <div class="row">
      {inc:user/menu.htm}
      <!--右侧主体部分 start-->
      <div class="col-md-9">
        <div class="panel panel-default">
          <div class="panel-body">
            <div class="result-container">
              {if:$status_text == '支付成功'}
                <i class="fa fa-check-circle result-icon success-icon"></i>
                <h2 class="result-title text-success">支付成功！</h2>
                <p class="result-desc">
                  恭喜您，支付已完成！奖励已自动发放到您的账户。
                </p>
              {elseif:$status_text == '支付中，请稍候...'}
                <i class="fa fa-clock-o result-icon pending-icon"></i>
                <h2 class="result-title text-warning">支付处理中</h2>
                <p class="result-desc">
                  您的支付正在处理中，请稍候...一般在1-3分钟内完成到账。
                </p>
                <div class="auto-check">
                  <i class="fa fa-refresh fa-spin"></i>
                  <span id="check-status">正在自动检查支付状态...</span>
                </div>
              {else}
                <i class="fa fa-times-circle result-icon error-icon"></i>
                <h2 class="result-title text-danger">支付异常</h2>
                <p class="result-desc">
                  支付状态异常，请检查订单状态或联系客服处理。
                </p>
              {/if}
              
              {if:!empty($order)}
              <div class="order-info">
                <h4><i class="fa fa-info-circle"></i> 订单信息</h4>
                <div class="row">
                  <div class="col-md-6">
                    <strong>订单号：</strong>{$order['order_no']}
                  </div>
                  <div class="col-md-6">
                    <strong>订单金额：</strong>￥{$order['amount']}
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <strong>订单类型：</strong>
                    {if:$order['type'] == 1}金币充值{else}VIP开通{/if}
                  </div>
                  <div class="col-md-6">
                    <strong>奖励内容：</strong>
                    {if:$order['type'] == 1}
                      {$order['value']} 金币
                    {else}
                      {$order['value']} 天VIP
                    {/if}
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <strong>支付方式：</strong>
                    {if:$order['pay_type'] == 'alipay'}支付宝{else}微信支付{/if}
                  </div>
                  <div class="col-md-6">
                    <strong>创建时间：</strong>{if:!empty($order['create_time'])}<span id="order-time">{$order['create_time']}</span><script>(function(){var el=document.getElementById('order-time');if(el){var ts=parseInt(el.textContent);if(ts>0){var d=new Date(ts*1000);el.textContent=d.getFullYear()+'-'+String(d.getMonth()+1).padStart(2,'0')+'-'+String(d.getDate()).padStart(2,'0')+' '+String(d.getHours()).padStart(2,'0')+':'+String(d.getMinutes()).padStart(2,'0')+':'+String(d.getSeconds()).padStart(2,'0');}}})();</script>{else}未知{/if}
                  </div>
                </div>
              </div>
              {/if}
              
              <div class="btn-group">
                {if:$status_text == '支付中，请稍候...'}
                  <button type="button" class="btn btn-primary" onclick="checkOrderStatus()">
                    <i class="fa fa-refresh"></i> 手动检查
                  </button>
                {/if}
                <a href="/my-orders.html" class="btn btn-info">
                  <i class="fa fa-list-alt"></i> 查看记录
                </a>
                <a href="/my-recharge.html" class="btn btn-success">
                  <i class="fa fa-credit-card"></i> 继续充值
                </a>
                <a href="/my-index.html" class="btn btn-secondary">
                  <i class="fa fa-user-circle"></i> 用户中心
                </a>
              </div>
              
              {if:$status_text == '支付成功'}
              <div class="alert alert-success" style="margin-top: 30px;">
                <h5><i class="fa fa-gift"></i> 温馨提示</h5>
                <p>奖励已发放到您的账户，您可以：</p>
                <ul style="text-align: left; display: inline-block;">
                  {if:$order['type'] == 1}
                  <li>使用金币购买付费内容</li>
                  <li>参与站内活动和抽奖</li>
                  <li>兑换虚拟礼品和道具</li>
                  {else}
                  <li>访问VIP专区内容</li>
                  <li>享受VIP专属特权</li>
                  <li>获得优先客服支持</li>
                  {/if}
                </ul>
              </div>
              {elseif:$status_text == '支付中，请稍候...'}
              <div class="alert alert-warning" style="margin-top: 30px;">
                <h5><i class="fa fa-clock-o"></i> 支付说明</h5>
                <p>1. 支付完成后，系统会自动到账，一般在1-3分钟内完成</p>
                <p>2. 如果超过10分钟未到账，请点击"手动检查"或联系客服</p>
                <p>3. 请不要重复支付，避免造成资金损失</p>
              </div>
              {else}
              <div class="alert alert-danger" style="margin-top: 30px;">
                <h5><i class="fa fa-exclamation-triangle"></i> 遇到问题？</h5>
                <p>如果您的支付遇到问题，请：</p>
                <ul style="text-align: left; display: inline-block;">
                  <li>检查网络连接是否正常</li>
                  <li>确认支付信息是否正确</li>
                  <li>联系在线客服获取帮助</li>
                  <li>保留支付凭证以备查询</li>
                </ul>
              </div>
              {/if}
            </div>
          </div>
        </div>
      </div>
      <!--右侧主体部分 end-->
    </div>
  </div>
</main>

{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;



    // 检查是否在新标签页中打开（用于支付窗口）
    var isPaymentWindow = window.opener && window.opener !== window;

    console.log('支付返回页面加载完成', {
      isPaymentWindow: isPaymentWindow,
      status: '{$status_text}',
      autoClose: '{$auto_close}'
    });

    // 已删除自动检测功能

    {if:$status_text == '支付成功' || $auto_close}
    if (isPaymentWindow) {
      // 在支付窗口中，支付成功后显示提示并自动关闭
      console.log('检测到支付成功，显示提示并准备关闭窗口');

      // 显示成功提示
      showSuccessMessage();

      // 3秒后自动关闭
      setTimeout(function() {
        console.log('支付成功，自动关闭支付窗口');
        window.close();
      }, 3000);
    } else {
      // 如果不是在支付窗口中，可能是直接访问的返回页面
      console.log('非支付窗口环境，支付成功');
    }
    {/if}

    // 已删除父窗口通知功能

    // 显示成功消息
    function showSuccessMessage() {
      var successDiv = document.createElement('div');
      successDiv.style.cssText = 'position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 999999; background: #4CAF50; color: white; padding: 15px 25px; border-radius: 8px; font-size: 16px; font-weight: bold; box-shadow: 0 4px 12px rgba(0,0,0,0.3); animation: slideDown 0.5s ease-out;';
      successDiv.innerHTML = '✅ 支付成功！窗口即将自动关闭...';

      // 添加动画样式
      if (!document.getElementById('success-animation-style')) {
        var style = document.createElement('style');
        style.id = 'success-animation-style';
        style.textContent = '@keyframes slideDown { from { opacity: 0; transform: translateX(-50%) translateY(-20px); } to { opacity: 1; transform: translateX(-50%) translateY(0); } }';
        document.head.appendChild(style);
      }

      document.body.appendChild(successDiv);
    }
    {elseif:$status_text == '支付中，请稍候...' && !empty($order)}
    // 自动检查支付状态
    var checkCount = 0;
    var maxCheck = isPaymentWindow ? 60 : 10; // 支付窗口检查更久
    var checkInterval = setInterval(function(){
      checkCount++;
      if(checkCount > maxCheck) {
        clearInterval(checkInterval);
        $('#check-status').text('自动检查已停止，请手动检查或联系客服');
        return;
      }

      checkOrderStatus(false);
    }, isPaymentWindow ? 2000 : 5000); // 支付窗口检查更频繁
    {/if}

    // 如果是支付窗口，添加特殊样式和提示
    if (isPaymentWindow) {
      {if:$status_text == '支付成功' || $auto_close}
      $('body').prepend('<div style="background: #d4edda; border-bottom: 2px solid #28a745; padding: 15px; text-align: center; font-weight: bold; color: #155724;"><i class="fa fa-check-circle"></i> 支付成功！窗口将自动关闭，请返回原页面手动刷新查看账户变化</div>');
      {else}
      $('body').prepend('<div style="background: #e3f2fd; border-bottom: 2px solid #2196f3; padding: 10px; text-align: center; font-weight: bold; color: #1976d2;"><i class="fa fa-info-circle"></i> 这是支付窗口，支付完成后请手动刷新原页面</div>');
      {/if}

      // 添加手动关闭按钮
      $('.btn-group').prepend('<button type="button" class="btn btn-secondary" onclick="window.close()"><i class="fa fa-times"></i> 关闭窗口</button>');
    }
  });

  // 检测并通知支付状态
  function detectAndNotifyPaymentStatus() {
    var isPaymentWindow = window.opener && window.opener !== window;

    if (!isPaymentWindow) {
      console.log('不是支付窗口，跳过通知');
      return;
    }

    // 检查页面内容是否包含支付成功的标识
    var pageText = document.body.innerText || document.body.textContent || '';
    var pageTitle = document.title || '';
    var currentStatus = '{$status_text}';

    console.log('检测支付状态:', {
      pageTitle: pageTitle,
      currentStatus: currentStatus,
      pageTextLength: pageText.length
    });

    // 检查是否支付成功
    var isSuccess = false;

    // 1. 检查后端返回的状态
    if (currentStatus === '支付成功' || '{$auto_close}' === '1') {
      isSuccess = true;
      console.log('后端确认支付成功');
    }

    // 2. 检查页面内容
    var successKeywords = ['支付成功', '付款成功', '交易成功', '恭喜您，支付已完成', '奖励已自动发放'];
    for (var i = 0; i < successKeywords.length; i++) {
      if (pageText.indexOf(successKeywords[i]) > -1 || pageTitle.indexOf(successKeywords[i]) > -1) {
        isSuccess = true;
        console.log('页面内容确认支付成功，关键词:', successKeywords[i]);
        break;
      }
    }

    // 3. 检查URL参数
    var urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('trade_status') === 'TRADE_SUCCESS') {
      isSuccess = true;
      console.log('URL参数确认支付成功');
    }

    if (isSuccess) {
      console.log('确认支付成功，通知父窗口');
      notifyParentWindow('success');
      showSuccessMessage();

      // 自动关闭窗口
      setTimeout(function() {
        console.log('支付成功，自动关闭窗口');
        window.close();
      }, 2000);
    } else {
      console.log('支付状态未确认为成功');
    }
  }

  // 检查订单状态
  function checkOrderStatus(showLoading = true) {
    {if:!empty($order)}
    if(showLoading) {
      var loadIndex = layer.load(2, {shade: [0.3, '#000']});
    }

    $.ajax({
      type: "POST",
      url: "my-check.html",
      data: {order_no: '{$order['order_no']}'},
      dataType: 'json',
      success: function(result) {
        if(showLoading) {
          layer.close(loadIndex);
        }

        if(!result.err){
          $('#check-status').text('状态：' + result.data.status_text);
          if(result.data.status == 1) {
            // 支付成功
            var isPaymentWindow = window.opener && window.opener !== window;
            if (isPaymentWindow) {
              // 在支付窗口中，显示成功信息后自动关闭
              $('#check-status').html('<span style="color: #4caf50;"><i class="fa fa-check-circle"></i> 支付成功！窗口即将关闭...</span>');

              // 显示成功提示
              showSuccessMessage();

              // 自动关闭窗口
              setTimeout(function() {
                window.close();
              }, 3000);
            } else {
              // 在普通页面中，刷新页面
              layer.alert('支付成功！页面即将刷新...', function(){
                location.reload();
              });
            }
          }
        } else {
          if(showLoading) {
            layer.alert('检查失败：' + result.msg);
          }
          $('#check-status').text('检查失败，请稍后重试');
        }
      },
      error: function(){
        if(showLoading) {
          layer.close(loadIndex);
          layer.alert('网络错误，请重试！');
        }
        $('#check-status').text('网络错误，请稍后重试');
      }
    });
    {else}
    layer.alert('订单信息不完整！');
    {/if}
  }
</script>
