# Rainbow Pay Plugin Protection
<Files "callback.php">
    # 允许所有访问，绕过WAF
    Satisfy Any
    Allow from all
    
    # 禁用mod_security（如果存在）
    <IfModule mod_security.c>
        SecRuleEngine Off
    </IfModule>
    
    # 禁用mod_evasive（如果存在）
    <IfModule mod_evasive24.c>
        DOSPageCount 0
        DOSSiteCount 0
    </IfModule>
</Files>

<Files "lecms_notify.php">
    Satisfy Any
    Allow from all
    <IfModule mod_security.c>
        SecRuleEngine Off
    </IfModule>
</Files>

<Files "enhanced_notify.php">
    Satisfy Any
    Allow from all
    <IfModule mod_security.c>
        SecRuleEngine Off
    </IfModule>
</Files>

<Files "simple_notify.php">
    Satisfy Any
    Allow from all
    <IfModule mod_security.c>
        SecRuleEngine Off
    </IfModule>
</Files>

# 允许特定User-Agent（如果知道彩虹易支付的UA）
<IfModule mod_rewrite.c>
    RewriteEngine On
    # 如果是回调文件且来源是支付平台，跳过WAF检查
    RewriteCond %{REQUEST_URI} ^/lecms/plugin/sj_rainbow_pay/(callback|.*notify)\.php$
    RewriteCond %{HTTP_USER_AGENT} ^(curl|wget|HTTPClient|Java|Python|PHP|Rainbow|Pay) [NC]
    RewriteRule .* - [E=MODSEC_ENABLE:Off]
</IfModule>
