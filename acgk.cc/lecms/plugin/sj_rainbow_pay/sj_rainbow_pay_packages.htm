<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>套餐管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
            <legend>搜索</legend>
            <div>
                <form class="layui-form layui-form-pane">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">套餐类型</label>
                            <div class="layui-input-inline">
                                <select name="type">
                                    <option value="" selected="selected">全部</option>
                                    <option value="1">金币充值</option>
                                    <option value="2">VIP开通</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value="" selected="selected">全部</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn">
                                <i class="layui-icon"></i> 搜索
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>

        <script type="text/html" id="toolbar">
            <div class="layui-btn-container">
                <a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="add">添加套餐</a>
                <a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete">批量删除</a>
            </div>
        </script>

        <table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table>

        <script type="text/html" id="currentTableBar">
            <div class="layui-btn-group">
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="toggle">{{# if(d.status == 1) { }}禁用{{# } else { }}启用{{# } }}</a>
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
            </div>
        </script>

        <script type="text/html" id="statusTpl">
            {{# if(d.status == 1) { }}
            <span class="layui-badge layui-bg-green">{{d.status_text}}</span>
            {{# } else { }}
            <span class="layui-badge">{{d.status_text}}</span>
            {{# } }}
        </script>
    </div>
</div>

<script type="text/javascript">
layui.use(['form', 'layer', 'table', 'miniTab'], function () {
    var layer = layui.layer, form = layui.form, table = layui.table, miniTab = layui.miniTab;
    
    table.render({
        elem: '#data-table',
        url: 'index.php?sj_rainbow_pay-getpackageslist-',
        height: 'full-145',
        toolbar: '#toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cellMinWidth: 50,
        escape: false,
        cols: [[
            {type: "checkbox", width: 50, fixed: 'left'},
            {field: 'name', width: 200, title: '套餐名称', align: 'left'},
            {field: 'type_text', width: 100, title: '类型', align: 'center'},
            {field: 'amount_text', width: 100, title: '价格', align: 'center'},
            {field: 'value_text', width: 150, title: '奖励', align: 'center'},
            {field: 'sort', width: 80, title: '排序', align: 'center'},
            {field: 'status', width: 100, title: '状态', align: 'center', templet: '#statusTpl'},
            {field: 'create_time_text', width: 170, title: '创建时间', align: 'center'},
            {title: '操作', width: 180, toolbar: '#currentTableBar', align: "center"}
        ]],
        limits: [20, 50, 100, 500, 1000],
        limit: 20,
        page: true
    });

    // 监听搜索操作
    form.on('submit(data-search-btn)', function (data) {
        table.reload('data-table', {
            page: {curr: 1},
            where: {
                type: data.field.type,
                status: data.field.status,
            }
        }, 'data');
        return false;
    });

    // 监听工具栏事件
    table.on('toolbar(data-table-filter)', function (obj) {
        if (obj.event === 'add') {
            miniTab.openNewTabByIframe({
                href: "index.php?sj_rainbow_pay-package_form",
                title: "添加套餐",
            });
        } else if (obj.event === 'delete') {
            var checkStatus = table.checkStatus('data-table'), data = checkStatus.data;
            var len = data.length;
            if (len == 0) {
                layer.msg('请选择数据', {icon: 5});
                return false;
            } else {
                layer.confirm('确定删除？', function (index) {
                    var id_arr = [];
                    for (var i in data) {
                        id_arr[i] = data[i]['id'];
                    }
                    adminAjax.postd("index.php?sj_rainbow_pay-batch_delete_packages", {"id_arr": id_arr});
                    layer.close(index);
                });
            }
        }
    });

    // 监听行工具事件
    table.on('tool(data-table-filter)', function (obj) {
        var data = obj.data;
        if (obj.event === 'edit') {
            // 使用LECMS正确的URL格式：控制器-方法-参数-值
            var editUrl = 'index.php?sj_rainbow_pay-package_form-id-' + data.id;
            console.log('编辑套餐URL:', editUrl);

            layer.open({
                type: 2,
                title: '编辑套餐',
                area: ['800px', '600px'],
                content: editUrl,
                success: function(layero, index) {
                    console.log('套餐编辑弹窗打开成功，URL:', editUrl);
                },
                end: function() {
                    // 关闭弹窗后刷新表格
                    console.log('套餐编辑弹窗关闭，刷新表格');
                    table.reload('data-table');
                }
            });
        } else if (obj.event === 'toggle') {
            layer.confirm('确定要' + (data.status == 1 ? '禁用' : '启用') + '该套餐吗？', function (index) {
                var loadIndex = layer.load(2, {shade: [0.3, '#000']});
                $.ajax({
                    url: "index.php?sj_rainbow_pay-toggle_package_status",
                    type: "POST",
                    data: {"id": data.id},
                    dataType: 'json',
                    success: function(result) {
                        layer.close(loadIndex);
                        if (!result.err) {
                            layer.msg('操作成功！', {icon: 1});
                            table.reload('data-table');
                        } else {
                            layer.alert('操作失败：' + result.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.alert('网络错误，请重试！', {icon: 2});
                    }
                });
                layer.close(index);
            });
        } else if (obj.event === 'delete') {
            layer.confirm('确定删除？', function (index) {
                var loadIndex = layer.load(2, {shade: [0.3, '#000']});
                $.ajax({
                    url: "index.php?sj_rainbow_pay-delete_package",
                    type: "POST",
                    data: {"id": data.id},
                    dataType: 'json',
                    success: function(result) {
                        layer.close(loadIndex);
                        if (!result.err) {
                            layer.msg('删除成功！', {icon: 1});
                            table.reload('data-table');
                        } else {
                            layer.alert('删除失败：' + result.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.alert('网络错误，请重试！', {icon: 2});
                    }
                });
                layer.close(index);
            });
        }
    });
});
</script>
</body>
</html>
