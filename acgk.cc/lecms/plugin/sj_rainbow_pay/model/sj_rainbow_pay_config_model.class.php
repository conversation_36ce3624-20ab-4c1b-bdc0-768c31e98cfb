<?php
defined('ROOT_PATH') or exit;

class sj_rainbow_pay_config extends model {

    function __construct() {
        $this->table = 'sj_rainbow_pay_config';
        $this->pri = array('id');
        $this->maxid = 'id';
    }

    // 获取配置值
    public function get_config($key) {
        $data = $this->find_fetch(array('`key`' => $key), array(), 0, 1);
        return $data ? current($data)['value'] : '';
    }

    // 设置配置值
    public function set_config($key, $value) {
        $existing = $this->find_fetch(array('`key`' => $key), array(), 0, 1);
        if ($existing) {
            return $this->update(array('id' => current($existing)['id'], 'value' => $value));
        } else {
            return $this->create(array('`key`' => $key, 'value' => $value));
        }
    }

    // 获取所有配置
    public function get_all_config() {
        $data = $this->find_fetch(array(), array(), 0, 0);
        $config = array();
        if (!empty($data)) {
            foreach ($data as $item) {
                $config[$item['key']] = $item['value'];
            }
        }
        return $config;
    }

    // 批量更新配置
    public function batch_update_config($configs) {
        $success = true;
        foreach ($configs as $key => $value) {
            if (!$this->set_config($key, $value)) {
                $success = false;
            }
        }
        return $success;
    }
}
