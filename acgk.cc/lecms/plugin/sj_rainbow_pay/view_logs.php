<?php
// 在线日志查看工具
header('Content-Type: text/html; charset=utf-8');

// 简单的安全检查
$allowed_ips = array('127.0.0.1', '::1'); // 可以添加您的IP
$client_ip = $_SERVER['REMOTE_ADDR'] ?? '';

?>
<!DOCTYPE html>
<html>
<head>
    <title>彩虹易支付回调日志查看器</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .log-box { background: #000; color: #0f0; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 600px; overflow-y: auto; white-space: pre-wrap; }
        .btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .info-box { background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 15px; margin: 10px 0; }
        .error-box { background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 10px 0; }
        .success-box { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 彩虹易支付回调日志查看器</h1>
        
        <?php
        $log_file = dirname(__FILE__) . '/vps_callback.log';
        
        // 处理操作
        if (isset($_POST['action'])) {
            if ($_POST['action'] == 'clear_log' && file_exists($log_file)) {
                file_put_contents($log_file, '');
                echo '<div class="success-box">✅ 日志已清空</div>';
            }
        }
        
        // 显示日志文件信息
        if (file_exists($log_file)) {
            $file_size = filesize($log_file);
            $file_time = date('Y-m-d H:i:s', filemtime($log_file));
            echo "<div class='info-box'>";
            echo "<strong>📄 日志文件信息：</strong><br>";
            echo "文件路径：$log_file<br>";
            echo "文件大小：" . number_format($file_size) . " 字节<br>";
            echo "最后修改：$file_time<br>";
            echo "</div>";
        } else {
            echo "<div class='error-box'>❌ 日志文件不存在：$log_file</div>";
        }
        ?>
        
        <div style="margin: 20px 0;">
            <button class="btn" onclick="location.reload()">🔄 刷新日志</button>
            <button class="btn" onclick="autoRefresh()">⏰ 自动刷新 (10秒)</button>
            <button class="btn" onclick="stopAutoRefresh()">⏹️ 停止自动刷新</button>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="clear_log">
                <button type="submit" class="btn btn-danger" onclick="return confirm('确定要清空日志吗？')">🗑️ 清空日志</button>
            </form>
        </div>
        
        <?php if (file_exists($log_file)): ?>
        <h3>📋 回调日志内容：</h3>
        <div class="log-box" id="log-content">
            <?php
            $content = file_get_contents($log_file);
            if (empty($content)) {
                echo "日志文件为空";
            } else {
                // 只显示最后1000行
                $lines = explode("\n", $content);
                $recent_lines = array_slice($lines, -1000);
                echo htmlspecialchars(implode("\n", $recent_lines));
            }
            ?>
        </div>
        <?php endif; ?>
        
        <h3>🔧 快速测试工具：</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <p><strong>测试链接：</strong></p>
            <p>
                <a href="debug_user_status.php" target="_blank" class="btn">👤 查看用户状态</a>
                <a href="check_callback.php" target="_blank" class="btn">🔍 回调检查工具</a>
            </p>
            
            <p><strong>手动触发回调测试：</strong></p>
            <form method="get" action="vps_notify.php" target="_blank">
                <input type="text" name="pid" placeholder="商户ID" value="1028" style="margin: 2px; padding: 5px;">
                <input type="text" name="trade_no" placeholder="支付订单号" style="margin: 2px; padding: 5px;">
                <input type="text" name="out_trade_no" placeholder="商户订单号" style="margin: 2px; padding: 5px;">
                <input type="text" name="type" placeholder="支付方式" value="alipay" style="margin: 2px; padding: 5px;">
                <input type="text" name="name" placeholder="商品名称" value="测试商品" style="margin: 2px; padding: 5px;">
                <input type="text" name="money" placeholder="金额" value="0.01" style="margin: 2px; padding: 5px;">
                <input type="text" name="trade_status" placeholder="状态" value="TRADE_SUCCESS" style="margin: 2px; padding: 5px;">
                <input type="text" name="sign" placeholder="签名" style="margin: 2px; padding: 5px;">
                <br>
                <button type="submit" class="btn" style="margin-top: 10px;">🧪 测试回调</button>
            </form>
            <small style="color: #666;">注意：请使用真实的订单号和正确的签名进行测试</small>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-radius: 5px;">
            <h4>💡 使用说明：</h4>
            <ul>
                <li>此工具用于实时查看彩虹易支付的回调日志</li>
                <li>可以帮助调试VIP充值不到账的问题</li>
                <li>建议在测试支付时打开此页面，实时查看回调情况</li>
                <li>如果日志文件过大，只显示最后1000行</li>
            </ul>
        </div>
    </div>
    
    <script>
        let autoRefreshInterval;
        
        function autoRefresh() {
            autoRefreshInterval = setInterval(function() {
                location.reload();
            }, 10000);
            alert('已开启自动刷新，每10秒刷新一次');
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                alert('已停止自动刷新');
            }
        }
        
        // 自动滚动到日志底部
        window.onload = function() {
            const logContent = document.getElementById('log-content');
            if (logContent) {
                logContent.scrollTop = logContent.scrollHeight;
            }
        };
    </script>
</body>
</html>
