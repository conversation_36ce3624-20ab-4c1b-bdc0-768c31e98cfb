<?php
// VPS专用回调处理 - 完美修复版本
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 如果是直接访问（没有回调参数），显示状态信息
if (empty($_GET) && empty($_POST)) {
    header('Content-Type: text/plain; charset=utf-8');
    echo "=== 彩虹易支付回调处理器 ===\n";
    echo "状态: 运行正常\n";
    echo "时间: " . date('Y-m-d H:i:s') . "\n";
    echo "此文件用于处理支付回调，不应直接访问\n";
    exit;
}

// 简单日志函数
function log_message($message) {
    $log_file = dirname(__FILE__) . '/vps_callback.log';
    $log_entry = date('Y-m-d H:i:s') . " - " . $message . "\n";
    @file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// 记录回调开始
log_message("=== 回调开始 ===");
log_message("请求方法: " . $_SERVER['REQUEST_METHOD']);
log_message("GET参数: " . json_encode($_GET));
log_message("POST参数: " . json_encode($_POST));

// 获取回调参数
$params = $_GET;

// 基本参数验证
if (empty($params['out_trade_no'])) {
    log_message("错误: 缺少订单号");
    exit('fail');
}

if (empty($params['trade_status'])) {
    log_message("错误: 缺少交易状态");
    exit('fail');
}

if (empty($params['sign'])) {
    log_message("错误: 缺少签名");
    exit('fail');
}

$order_no = $params['out_trade_no'];
$trade_status = $params['trade_status'];
$trade_no = isset($params['trade_no']) ? $params['trade_no'] : '';
$pay_type = isset($params['type']) ? $params['type'] : '';

log_message("订单号: $order_no");
log_message("交易状态: $trade_status");
log_message("支付类型: $pay_type");

// 检查交易状态
if ($trade_status != 'TRADE_SUCCESS') {
    log_message("交易状态不是成功: $trade_status");
    exit('fail');
}

// 数据库配置
$db_config = array(
    'host' => '127.0.0.1',
    'port' => 3306,
    'name' => 'root',
    'user' => 'root',
    'password' => 'ServBay.dev',
    'tablepre' => 'le_'
);

// 尝试读取LECMS配置
$config_paths = array(
    '/www/wwwroot/acgk.cc/lecms/config/config.inc.php',
    dirname(__FILE__) . '/../../../config/config.inc.php',
    $_SERVER['DOCUMENT_ROOT'] . '/lecms/config/config.inc.php'
);

foreach ($config_paths as $config_path) {
    if (file_exists($config_path)) {
        log_message("找到配置文件: $config_path");
        include $config_path;
        if (isset($_ENV['_config']['db']['master'])) {
            $lecms_db = $_ENV['_config']['db']['master'];
            $db_config = array(
                'host' => $lecms_db['host'],
                'port' => $lecms_db['port'],
                'name' => $lecms_db['name'],
                'user' => $lecms_db['user'],
                'password' => $lecms_db['password'],
                'tablepre' => $lecms_db['tablepre']
            );
            log_message("使用LECMS数据库配置");
            break;
        }
    }
}

log_message("数据库: {$db_config['host']}:{$db_config['port']}/{$db_config['name']}");

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['name']};charset=utf8";
    $pdo = new PDO($dsn, $db_config['user'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    log_message("数据库连接成功");
} catch (PDOException $e) {
    log_message("数据库连接失败: " . $e->getMessage());
    exit('fail');
}

$tablepre = $db_config['tablepre'];

// 获取配置
try {
    $config_sql = "SELECT * FROM {$tablepre}sj_rainbow_pay_config";
    $config_result = $pdo->query($config_sql)->fetchAll(PDO::FETCH_ASSOC);
    
    $config = array();
    foreach ($config_result as $item) {
        $config[$item['key']] = $item['value'];
    }
    log_message("配置加载成功，配置项: " . count($config));
} catch (Exception $e) {
    log_message("配置加载失败: " . $e->getMessage());
    exit('fail');
}

// 获取密钥
$key = '';
if ($pay_type == 'alipay') {
    $key = isset($config['alipay_key']) ? $config['alipay_key'] : '';
} elseif ($pay_type == 'wxpay') {
    $key = isset($config['wechat_key']) ? $config['wechat_key'] : '';
}

if (empty($key)) {
    log_message("密钥为空，支付类型: $pay_type");
    exit('fail');
}

// 验证签名
$sign = $params['sign'];
$verify_params = $params;
unset($verify_params['sign'], $verify_params['sign_type']);

ksort($verify_params);
$string = '';
foreach ($verify_params as $k => $v) {
    if ($v !== '' && $v !== null && $k != 'sign' && $k != 'sign_type') {
        $string .= $k . '=' . $v . '&';
    }
}
$string = rtrim($string, '&');
$calculated_sign = md5($string . $key);

log_message("签名字符串: $string");
log_message("接收签名: $sign");
log_message("计算签名: $calculated_sign");

if ($sign != $calculated_sign) {
    log_message("签名验证失败");
    exit('fail');
}

log_message("签名验证成功");

// 查询订单
try {
    $order_sql = "SELECT * FROM {$tablepre}sj_rainbow_pay_orders WHERE order_no = ? LIMIT 1";
    $order_stmt = $pdo->prepare($order_sql);
    $order_stmt->execute(array($order_no));
    $order = $order_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        log_message("订单不存在: $order_no");
        exit('fail');
    }
    
    log_message("找到订单: ID={$order['id']}, 用户={$order['uid']}, 状态={$order['status']}");
    
    if ($order['status'] == 1) {
        log_message("订单已处理");
        exit('success');
    }
} catch (Exception $e) {
    log_message("订单查询失败: " . $e->getMessage());
    exit('fail');
}

// 开始处理订单
$pdo->beginTransaction();

try {
    // 更新订单状态
    $update_sql = "UPDATE {$tablepre}sj_rainbow_pay_orders SET status = 1, trade_no = ?, pay_time = ? WHERE order_no = ?";
    $update_stmt = $pdo->prepare($update_sql);
    $update_result = $update_stmt->execute(array($trade_no, time(), $order_no));
    
    if (!$update_result) {
        throw new Exception("订单更新失败");
    }
    
    log_message("订单状态更新成功");
    
    // 查询用户
    $user_sql = "SELECT * FROM {$tablepre}user WHERE uid = ? LIMIT 1";
    $user_stmt = $pdo->prepare($user_sql);
    $user_stmt->execute(array($order['uid']));
    $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("用户不存在: " . $order['uid']);
    }
    
    log_message("=== 用户信息 ===");
    log_message("UID: {$user['uid']}, 用户名: {$user['username']}");
    log_message("当前用户组: {$user['groupid']}, 当前金币: {$user['golds']}");
    log_message("当前VIP时间: {$user['vip_times']} (" . ($user['vip_times'] ? date('Y-m-d H:i:s', $user['vip_times']) : '无') . ")");
    
    // 处理奖励
    if ($order['type'] == 1) {
        // 金币充值
        $new_golds = intval($user['golds']) + intval($order['value']);
        $user_sql = "UPDATE {$tablepre}user SET golds = ? WHERE uid = ?";
        $user_stmt = $pdo->prepare($user_sql);
        $user_result = $user_stmt->execute(array($new_golds, $order['uid']));
        log_message("金币充值: {$user['golds']} + {$order['value']} = $new_golds");
        
    } elseif ($order['type'] == 2) {
        // VIP开通 - 完美修复版本
        log_message("=== VIP开通处理开始 ===");
        log_message("订单VIP天数: {$order['value']} 天");
        log_message("用户当前组: {$user['groupid']}, 当前VIP时间: {$user['vip_times']}");
        
        if ($user['groupid'] == 12) {
            // 已经是VIP用户，进行续费
            $current_vip = intval($user['vip_times']);
            $new_vip_times = ($current_vip > time() ? $current_vip : time()) + intval($order['value']) * 86400;
            $user_sql = "UPDATE {$tablepre}user SET vip_times = ? WHERE uid = ?";
            $user_stmt = $pdo->prepare($user_sql);
            $user_result = $user_stmt->execute(array($new_vip_times, $order['uid']));
            log_message("VIP续费执行: SQL = $user_sql");
            log_message("VIP续费参数: new_vip_times = $new_vip_times, uid = {$order['uid']}");
            log_message("VIP续费结果: " . ($user_result ? '成功' : '失败'));
            
        } else {
            // 非VIP用户开通VIP
            $new_vip_times = time() + intval($order['value']) * 86400;
            $user_sql = "UPDATE {$tablepre}user SET groupid = 12, vip_times = ? WHERE uid = ?";
            $user_stmt = $pdo->prepare($user_sql);
            $user_result = $user_stmt->execute(array($new_vip_times, $order['uid']));
            log_message("VIP开通执行: SQL = $user_sql");
            log_message("VIP开通参数: groupid = 12, vip_times = $new_vip_times, uid = {$order['uid']}");
            log_message("VIP开通结果: " . ($user_result ? '成功' : '失败'));
        }
    } else {
        throw new Exception("未知订单类型: " . $order['type']);
    }
    
    if (!$user_result) {
        throw new Exception("用户奖励更新失败");
    }
    
    // 提交事务
    $pdo->commit();
    log_message("订单处理成功: $order_no");

    // 发送Telegram通知
    send_telegram_notification($order, $user);

    exit('success');
    
} catch (Exception $e) {
    $pdo->rollback();
    log_message("订单处理失败: " . $e->getMessage());
    exit('fail');
}

// 发送Telegram通知函数
function send_telegram_notification($order, $user) {
    global $config, $pdo, $tablepre;

    try {
        // 检查是否启用Telegram通知
        if (empty($config['telegram_enabled']) || $config['telegram_enabled'] != '1') {
            log_message("Telegram通知未启用");
            return;
        }

        if (empty($config['telegram_bot_token']) || empty($config['telegram_chat_id'])) {
            log_message("Telegram配置不完整");
            return;
        }

        // 获取套餐信息
        $package_name = '未知套餐';
        try {
            $package_sql = "SELECT name FROM {$tablepre}sj_rainbow_pay_packages WHERE id = ? LIMIT 1";
            $package_stmt = $pdo->prepare($package_sql);
            $package_stmt->execute(array($order['package_id']));
            $package = $package_stmt->fetch(PDO::FETCH_ASSOC);
            if ($package) {
                $package_name = $package['name'];
            }
        } catch (Exception $e) {
            log_message("获取套餐信息失败: " . $e->getMessage());
        }

        // 准备消息内容
        $type_text = ($order['type'] == 1) ? '金币充值' : 'VIP开通';
        $value_text = ($order['type'] == 1) ? $order['value'] . ' 金币' : $order['value'] . ' 天VIP';

        $message = "💰 支付成功通知\n";
        $message .= "用户：{$user['username']}\n";
        $message .= "套餐：{$package_name}\n";
        $message .= "类型：{$type_text}\n";
        $message .= "奖励：{$value_text}\n";
        $message .= "金额：￥{$order['amount']}\n";
        $message .= "订单：{$order['order_no']}\n";
        $message .= "时间：" . date('Y-m-d H:i:s');

        // 发送通知
        $result = send_telegram_message($config['telegram_bot_token'], $config['telegram_chat_id'], $message);

        if ($result['success']) {
            log_message("Telegram通知发送成功");
        } else {
            log_message("Telegram通知发送失败: " . $result['error']);
        }

    } catch (Exception $e) {
        log_message("Telegram通知处理异常: " . $e->getMessage());
    }
}

// 发送Telegram消息函数
function send_telegram_message($bot_token, $chat_id, $message) {
    try {
        $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";

        $data = array(
            'chat_id' => $chat_id,
            'text' => $message,
            'parse_mode' => 'HTML'
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'LECMS-TelegramBot/1.0');

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return array('success' => false, 'error' => "CURL错误：$error");
        }

        if ($http_code != 200) {
            return array('success' => false, 'error' => "HTTP错误：$http_code");
        }

        $result = json_decode($response, true);
        if ($result && $result['ok']) {
            return array('success' => true);
        } else {
            $error_msg = isset($result['description']) ? $result['description'] : '未知错误';
            return array('success' => false, 'error' => $error_msg);
        }

    } catch (Exception $e) {
        return array('success' => false, 'error' => $e->getMessage());
    }
}
?>
