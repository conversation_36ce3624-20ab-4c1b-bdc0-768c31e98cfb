<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>彩虹易支付管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="fa fa-credit-card"></i> 彩虹易支付管理
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md4">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        <i class="fa fa-cog"></i> 支付配置
                                    </div>
                                    <div class="layui-card-body">
                                        <p>配置支付宝和微信支付参数</p>
                                        <a href="index.php?sj_rainbow_pay-setting" class="layui-btn layui-btn-normal">
                                            <i class="fa fa-cog"></i> 支付配置
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        <i class="fa fa-list"></i> 订单管理
                                    </div>
                                    <div class="layui-card-body">
                                        <p>查看和管理所有支付订单</p>
                                        <a href="index.php?sj_rainbow_pay-orders" class="layui-btn layui-btn-normal">
                                            <i class="fa fa-list"></i> 订单管理
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        <i class="fa fa-gift"></i> 套餐管理
                                    </div>
                                    <div class="layui-card-body">
                                        <p>管理金币和VIP充值套餐</p>
                                        <a href="index.php?sj_rainbow_pay-packages" class="layui-btn layui-btn-normal">
                                            <i class="fa fa-gift"></i> 套餐管理
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-card" style="margin-top: 15px;">
                            <div class="layui-card-header">
                                <i class="fa fa-info-circle"></i> 使用说明
                            </div>
                            <div class="layui-card-body">
                                <div class="layui-collapse">
                                    <div class="layui-colla-item">
                                        <h2 class="layui-colla-title">配置说明</h2>
                                        <div class="layui-colla-content layui-show">
                                            <p>1. 首先在"支付配置"中设置支付宝和微信的API参数</p>
                                            <p>2. 在"套餐管理"中添加金币充值和VIP开通套餐</p>
                                            <p>3. 用户可在前台进行在线充值，订单会在"订单管理"中显示</p>
                                            <p>4. 支持手动补单功能，未支付的订单可以手动标记为已支付</p>
                                        </div>
                                    </div>
                                    <div class="layui-colla-item">
                                        <h2 class="layui-colla-title">兼容性说明</h2>
                                        <div class="layui-colla-content">
                                            <p>本插件完全兼容sj_kami插件的数据结构：</p>
                                            <p>• 金币充值使用 golds 字段</p>
                                            <p>• VIP开通使用 groupid 和 vip_times 字段</p>
                                            <p>• 用户组：11=普通用户，12=VIP用户</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['element'], function(){
    var element = layui.element;
});
</script>
</body>
</html>
