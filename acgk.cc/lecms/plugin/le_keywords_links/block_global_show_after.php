<?php
//关键词内链插件
$le_keywords_links_setting = $run->kv->xget('le_keywords_links_setting');
if($le_keywords_links_setting){
    $class = $le_keywords_links_setting['class'] == '' ? '' : ' class="'.$le_keywords_links_setting['class'].'"';
    $target = $le_keywords_links_setting['target'] == 0 ? '' : ' target="_blank"';

    $style = $class.$target;

    $keywords_links_arr = $run->keywords_links->find_fetch(array(), array('orderby'=>1, 'id' => 1));
    if( $keywords_links_arr ){
        $contentstr = $_show['content'];
        foreach ($keywords_links_arr as $keywords){
            $patterns = '#(?=[^>]*(?=<(?!/a>)|$))'.$keywords['keyword'].'#';
            $replacements = '<a href="'.$keywords['url'].'" '.$style.' title="'.$keywords['keyword'].'">'.$keywords['keyword'].'</a>';

            $contentstr = preg_replace($patterns, $replacements, $contentstr, $keywords['count']);
        }

        $_show['content'] = $contentstr;

        unset($keywords_links_arr);
    }
}