{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?le_keywords_links-{$_GET['action']}-ajax-1" method="post">
			<input name="id" type="hidden" value="{$data[id]}" />
			<div class="layui-form-item">
				<label class="layui-form-label required">关键词</label>
				<div class="layui-input-inline">
					<input type="text" name="keyword" value="{$data[keyword]}" autocomplete="off" placeholder="请输入关键词" class="layui-input" lay-verify="required" lay-reqtext="关键词不能为空" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label required">链接网址</label>
				<div class="layui-input-block">
					<input type="text" name="url" value="{$data[url]}" autocomplete="off" placeholder="请输入链接网址" class="layui-input" lay-verify="required" lay-reqtext="链接网址不能为空" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label required">替换次数</label>
				<div class="layui-input-inline">
					<input type="number" name="count" value="{$data[count]}" autocomplete="off" placeholder="请输入替换次数" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">不建议超过5次</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">排序值</label>
				<div class="layui-input-inline">
					<input type="number" name="orderby" value="{$data[orderby]}" autocomplete="off" placeholder="请输入排序值" class="layui-input" />
				</div>
				<div class="layui-form-mid layui-word-aux">数值越小，优先级越高</div>
			</div>

			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">保存</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
	layui.use(['form','layer', 'miniTab'], function () {
		var layer = layui.layer, form = layui.form, miniTab = layui.miniTab;

		//监听提交
		form.on('submit(form)', function () {
			adminAjax.postform('#form',function (data) {
				var json = toJson(data);
				if( json.err == 0 ){
					var icon = 1;
				}else{
					var icon = 5;
				}
				layer.msg(json.msg, {icon: icon});
				if(json.err==0) {
					setTimeout(function(){
						miniTab.reloadIframe('index.php?le_keywords_links-index');
						miniTab.deleteCurrentByIframe();
					}, 1500);
				}
			});
			return false;
		});
	});
</script>
</body>
</html>
