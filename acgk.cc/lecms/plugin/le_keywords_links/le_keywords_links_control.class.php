<?php
defined('ROOT_PATH') or exit;

class le_keywords_links_control extends admin_control {

    //设置
    public function setting(){
        if(empty($_POST)) {

            $le_keywords_links_setting = $this->kv->xget('le_keywords_links_setting');

            $input = array();
            $input['class'] = form::get_text('class', $le_keywords_links_setting['class']);
            $arr = array(1=>'是', 0=>'否');
            $input['target'] = form::get_radio_layui('target', $arr, $le_keywords_links_setting['target']);
            $this->assign('input', $input);

            $this->display('setting.htm');
        }else{
            _trim($_POST);

            $arr = array(
                'class'=>R('class', 'P'),
                'target'=>(int)R('target', 'P')
            );

            $this->kv->set('le_keywords_links_setting', $arr);

            E(0, '修改成功！');
        }
    }

    public function index(){
        $this->display();
    }

    //获取列表
    public function get_list(){
        //分页
        $page = isset( $_REQUEST['page'] ) ? intval($_REQUEST['page']) : 1;
        $pagenum = isset( $_REQUEST['limit'] ) ? intval($_REQUEST['limit']) : 15;

        //获取查询条件
        $keyword = isset( $_REQUEST['keyword'] ) ? trim($_REQUEST['keyword']) : '';
        if($keyword) {
            $keyword = urldecode($keyword);
            $keyword = safe_str($keyword);
        }

        //组合查询条件
        $where= array();
        if( $keyword ){
            $where['keyword'] = array('LIKE'=>$keyword);
        }

        //数据量
        if( $where ){
            $total = $this->keywords_links->find_count($where);
        }else{
            $total = $this->keywords_links->count();
        }

        //页数
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, $page));

        // 获取列表
        $data_arr = array();
        $cms_arr = $this->keywords_links->list_arr($where, 'id', -1, ($page-1)*$pagenum, $pagenum, $total);
        foreach($cms_arr as &$v) {
            $data_arr[] = $v;   //排序需要索引从0开始
        }
        unset($cms_arr);
        //组合数据 输出到页面
        $arr = array(
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data_arr,
        );
        exit( json_encode($arr) );
    }

    //添加
    public function add() {
        if(empty($_POST)) {
            $data['count'] = 1;
            $data['orderby'] = 0;

            $this->assign('data', $data);

            $this->display('le_keywords_links_set.htm');
        }else{
            $keyword = trim(strip_tags(R('keyword', 'P')));
            $url = trim(strip_tags(R('url', 'P')));
            $count = intval(R('count', 'P'));
            $orderby = intval(R('orderby', 'P'));
            $count > 200 && $count = 200;

            empty($keyword) && E(1, '没有填写关键词哦！');
            empty($url) && E(1, '没有填写链接网址哦！');
            empty($count) && E(1, '没有填写替换次数哦');

            if( $this->keywords_links->find_fetch_key(array('keyword'=> $keyword)) ){
                E(1, '关键词已经存在了哦！');
            }

            // 写入内容表
            $data = array(
                'keyword' => $keyword,
                'url' => $url,
                'dateline' => $_ENV['_time'],
                'count'=>$count,
                'orderby'=>$orderby
            );
            $id = $this->keywords_links->create($data);
            if(!$id) {
                E(1, '写入关键词表出错');
            }

            E(0, '发布完成');
        }
    }

    //编辑表格字段
    public function set(){
        if( !empty($_POST) ){
            $field = trim( R('field','P') );
            $id = intval( R('id','P') );
            $value = trim( R('value','P') );

            if($field == 'count'){
                $value = (int)$value;
                empty($value) && E(1, '没有填写替换次数哦');

                $value > 200 && $value = 200;
            }elseif($field == 'keyword'){
                $value = trim(strip_tags($value));
                $data = $this->keywords_links->get($id);
                if(empty($data)) E(1, '内容不存在！');

                if( $data['keyword'] != $value && $this->keywords_links->find_fetch_key(array('keyword'=> $value)) ){
                    E(1, '关键词已经存在了哦！');
                }
            }

            $data = array(
                'id' => $id,
                $field => $value,
            );
            if(!$this->keywords_links->update($data)) {
                E(1, '更新失败');
            }
            E(0, '更新'.$field.'成功');
        }
    }

    // 删除
    public function del() {
        $id = (int) R('id', 'P');
        empty($id) && E(1, '内容ID不能为空！');

        $res = $this->keywords_links->delete($id);
        if(!$res) {
            E(1, '删除失败！');
        }else{
            E(0, '删除成功！');
        }
    }

    public function batch_del() {
        $id_arr = R('id_arr', 'P');
        if(!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach($id_arr as $v) {
                $res = $this->keywords_links->delete($v);
                if(!$res) $err_num++;
            }

            if($err_num) {
                E(1, $err_num.' 条内容删除失败！');
            }else{
                E(0, '删除成功！');
            }
        }else{
            E(1, '参数不能为空！');
        }
    }

}
