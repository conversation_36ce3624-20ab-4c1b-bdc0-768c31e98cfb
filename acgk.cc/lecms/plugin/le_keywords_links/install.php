<?php
defined('ROOT_PATH') || exit;

$arr = array(
    'class' => 'le_keywords',
    'target' => 1,
);
$this->kv->set('le_keywords_links_setting', $arr);

$tableprefix = $_ENV['_config']['db']['master']['tablepre'];	//表前缀
$sql = "DROP TABLE IF EXISTS ".$tableprefix."keywords_links;";
$this->db->query($sql);
$sql_table = "CREATE TABLE ".$tableprefix."keywords_links (
  id int(10) unsigned NOT NULL AUTO_INCREMENT,
  keyword varchar(50) NOT NULL DEFAULT '',
  url varchar(255) NOT NULL DEFAULT '',
  count tinyint(1) unsigned NOT NULL DEFAULT '0',
  orderby int(10) unsigned NOT NULL DEFAULT '0',
  dateline int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY  (id),
  UNIQUE KEY  (keyword)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";
$this->db->query($sql_table);