<?php
// 为帖子内容页面注入图片CSS样式
// 这个钩子在帖子内容页面执行

// 检查是否为前端页面（排除后台页面）
$script_name = $_SERVER['SCRIPT_NAME'];
if(strpos($script_name, '/admin/') !== false) {
    return;
}

// 获取当前控制器和动作
$control = isset($_GET['control']) ? strtolower($_GET['control']) : 'index';
$action = isset($_GET['action']) ? strtolower($_GET['action']) : 'index';

// 只在帖子内容页面（show页面）注入CSS
if($action == 'show' || $control == 'show') {
    // 使用静态变量确保CSS只注入一次
    static $content_css_injected = false;
    if (!$content_css_injected) {
        $content_css_injected = true;
        
        // 定义帖子内容页面的图片CSS样式 - 只影响文章内容区域
        $content_css = '
<style type="text/css">
img {
    width: 100%;
    height: auto;
    display: block;
    clip-path: inset(0 0 25% 0);
}
</style>
';
        
        // 输出CSS到页面
        echo $content_css;
    }
}
?>
