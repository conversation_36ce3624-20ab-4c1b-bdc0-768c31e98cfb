<?php
if($list_arr){
    $control = isset($_GET['control']) ? strtolower($_GET['control']) : 'index';
    $action = isset($_GET['action']) ? strtolower($_GET['action']) : 'index';
    if($action == 'index' && ($control == 'index' || $control == 'cate')){
        $keys = array();
        foreach ($list_arr as &$v){
            $v['piclist'] = array();
            $keys[] = $v['id'];
        }
        $this->cms_content_data->table = $this->table.'_data';
        $cms_content_data_list_arr = $this->cms_content_data->mget($keys);

        foreach ($cms_content_data_list_arr as $c){
            $contentstr = $c['content'];
            preg_match_all("/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/", $contentstr, $match);

            $key = $this->table.'-id-'.$c['id'];
            if(isset($match[1]) && isset($list_arr[$key])){
                $list_arr[$key]['piclist'] = array_slice($match[1], 0, 6);
            }
        }
        unset($cms_content_data_list_arr);

        // 注入图片样式CSS - 只在首页和分类页执行一次
        static $css_injected = false;
        if (!$css_injected) {
            $css_injected = true;

            // 定义您需要的CSS样式 - 只影响图片列表容器
            $custom_css = '
<style type="text/css">
/* 图片列表样式 - 只影响.tupiansa容器内的图片 */
.tupiansa img {
    clip-path: inset(0 0 10% 0);
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .tupiansa img {
        clip-path: inset(0 0 15% 0);
    }
}

</style>
';

            // 输出CSS到页面
            echo $custom_css;
        }
    }
}

