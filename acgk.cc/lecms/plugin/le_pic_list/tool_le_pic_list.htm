{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">如何使用？</div>
	<div class="layui-card-body">
<pre class="layui-code">
//假如你的列表是循环 $v
#if:$v[piclist]#
#loop:$v[piclist] $src#
图片标签的src="#$src#"
#/loop#
#/if#

记得把 # 改成大括号
</pre>
	</div>
</div>

<div class="layui-card" style="margin-top: 15px;">
	<div class="layui-card-header">图片样式优化功能</div>
	<div class="layui-card-body">
		<p>本插件已集成图片样式优化功能，会自动为不同页面的图片添加CSS样式：</p>
		<ul>
			<li><strong>首页和分类页</strong>：.tupiansa类下的图片固定尺寸，圆角边框，裁剪效果</li>
			<li><strong>帖子内容页</strong>：内容区域图片宽度100%自适应，裁剪底部25%区域</li>
			<li><strong>响应式设计</strong>：移动端自动调整图片尺寸和裁剪比例</li>
		</ul>

		<h4>应用的CSS样式：</h4>

		<h5>首页和分类页样式：</h5>
		<pre class="layui-code">
/* 图片列表样式 */
.tupiansa img {
    width: 110px;
    height: 80px;
    border-radius: 5px;
    clip-path: inset(0 0 10% 0);
    margin: 2px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .tupiansa img {
        width: calc(30%);
        clip-path: inset(0 0 15% 0);
    }
}
		</pre>

		<h5>帖子内容页样式：</h5>
		<pre class="layui-code">
/* 帖子内容页面图片样式 */
.article-content img,
.content img,
#content img,
.post-content img,
.show-content img {
    width: 100%;
    height: auto;
    display: block;
    clip-path: inset(0 0 25% 0);
}
		</pre>

		<div class="layui-form-mid layui-word-aux" style="color: #ff5722;">
			<i class="layui-icon layui-icon-tips"></i>
			样式会自动在首页和分类页生效，无需额外配置。如需修改样式，请直接编辑插件文件中的CSS代码。
		</div>
	</div>
</div>
</body>
</html>
