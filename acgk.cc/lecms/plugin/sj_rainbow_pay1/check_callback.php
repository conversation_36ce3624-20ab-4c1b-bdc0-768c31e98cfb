<?php
// 回调检查工具
header('Content-Type: text/html; charset=utf-8');

echo "<h2>彩虹易支付回调检查工具</h2>";

// 检查回调日志
$log_file = dirname(__FILE__) . '/vps_callback.log';
if (file_exists($log_file)) {
    echo "<h3>最近的回调日志：</h3>";
    echo "<pre style='background:#f5f5f5;padding:10px;border:1px solid #ddd;max-height:400px;overflow-y:auto;'>";
    
    // 读取最后100行
    $lines = file($log_file);
    $recent_lines = array_slice($lines, -100);
    echo htmlspecialchars(implode('', $recent_lines));
    
    echo "</pre>";
} else {
    echo "<p style='color:red;'>回调日志文件不存在：$log_file</p>";
}

// 检查订单状态
echo "<h3>检查订单状态：</h3>";
echo "<form method='get'>";
echo "<input type='text' name='order_no' placeholder='输入订单号' value='" . (isset($_GET['order_no']) ? htmlspecialchars($_GET['order_no']) : '') . "'>";
echo "<input type='submit' value='查询'>";
echo "</form>";

if (isset($_GET['order_no']) && !empty($_GET['order_no'])) {
    $order_no = $_GET['order_no'];
    
    // 连接数据库
    try {
        // 尝试读取LECMS配置
        $config_paths = array(
            dirname(__FILE__) . '/../../../config/config.inc.php',
            $_SERVER['DOCUMENT_ROOT'] . '/lecms/config/config.inc.php'
        );
        
        $db_config = array(
            'host' => '127.0.0.1',
            'port' => 3306,
            'name' => 'root',
            'user' => 'root',
            'password' => 'ServBay.dev',
            'tablepre' => 'le_'
        );
        
        foreach ($config_paths as $config_path) {
            if (file_exists($config_path)) {
                include $config_path;
                if (isset($_ENV['_config']['db']['master'])) {
                    $lecms_db = $_ENV['_config']['db']['master'];
                    $db_config = array(
                        'host' => $lecms_db['host'],
                        'port' => $lecms_db['port'],
                        'name' => $lecms_db['name'],
                        'user' => $lecms_db['user'],
                        'password' => $lecms_db['password'],
                        'tablepre' => $lecms_db['tablepre']
                    );
                    break;
                }
            }
        }
        
        $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['name']};charset=utf8";
        $pdo = new PDO($dsn, $db_config['user'], $db_config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 查询订单
        $sql = "SELECT * FROM {$db_config['tablepre']}sj_rainbow_pay_orders WHERE order_no = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute(array($order_no));
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($order) {
            echo "<div style='background:#e8f5e8;padding:10px;border:1px solid #4caf50;border-radius:5px;'>";
            echo "<h4>订单信息：</h4>";
            echo "<p><strong>订单号：</strong>" . htmlspecialchars($order['order_no']) . "</p>";
            echo "<p><strong>用户ID：</strong>" . htmlspecialchars($order['uid']) . "</p>";
            echo "<p><strong>金额：</strong>￥" . htmlspecialchars($order['amount']) . "</p>";
            echo "<p><strong>类型：</strong>" . ($order['type'] == 1 ? '金币充值' : 'VIP开通') . "</p>";
            echo "<p><strong>奖励：</strong>" . htmlspecialchars($order['value']) . ($order['type'] == 1 ? ' 金币' : ' 天VIP') . "</p>";
            echo "<p><strong>状态：</strong>" . ($order['status'] == 1 ? '<span style="color:green;">已支付</span>' : '<span style="color:red;">未支付</span>') . "</p>";
            echo "<p><strong>创建时间：</strong>" . date('Y-m-d H:i:s', $order['create_time']) . "</p>";
            if ($order['pay_time']) {
                echo "<p><strong>支付时间：</strong>" . date('Y-m-d H:i:s', $order['pay_time']) . "</p>";
            }
            echo "</div>";
            
            // 查询用户信息
            $user_sql = "SELECT uid, username, golds, groupid, vip_times FROM {$db_config['tablepre']}user WHERE uid = ?";
            $user_stmt = $pdo->prepare($user_sql);
            $user_stmt->execute(array($order['uid']));
            $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<div style='background:#e3f2fd;padding:10px;border:1px solid #2196f3;border-radius:5px;margin-top:10px;'>";
                echo "<h4>用户信息：</h4>";
                echo "<p><strong>用户名：</strong>" . htmlspecialchars($user['username']) . "</p>";
                echo "<p><strong>金币：</strong>" . htmlspecialchars($user['golds']) . "</p>";
                echo "<p><strong>用户组：</strong>" . htmlspecialchars($user['groupid']) . "</p>";
                if ($user['vip_times']) {
                    echo "<p><strong>VIP到期：</strong>" . date('Y-m-d H:i:s', $user['vip_times']) . "</p>";
                }
                echo "</div>";
            }
            
        } else {
            echo "<p style='color:red;'>订单不存在：$order_no</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color:red;'>数据库错误：" . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// 检查回调文件
echo "<h3>回调文件检查：</h3>";
$callback_files = array(
    'vps_notify.php' => '主回调处理器',
    'route/rainbow_pay_notify.php' => '路由回调文件'
);

foreach ($callback_files as $file => $desc) {
    $file_path = dirname(__FILE__) . '/' . $file;
    if (file_exists($file_path)) {
        echo "<p style='color:green;'>✓ $desc ($file) - 存在</p>";
    } else {
        echo "<p style='color:red;'>✗ $desc ($file) - 不存在</p>";
    }
}

echo "<h3>测试回调URL：</h3>";
$base_url = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'];
$callback_url = $base_url . str_replace($_SERVER['DOCUMENT_ROOT'], '', dirname(__FILE__)) . '/route/rainbow_pay_notify.php';
echo "<p>回调地址：<a href='$callback_url' target='_blank'>$callback_url</a></p>";

?>
