<?php
defined('ROOT_PATH') or exit;

class sj_rainbow_pay_control extends admin_control
{
    public function __construct()
    {
        parent::__construct();
        // 加载必要的模型
        $this->sj_rainbow_pay_config = core::model('sj_rainbow_pay_config');
        $this->sj_rainbow_pay_orders = core::model('sj_rainbow_pay_orders');
        $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');
        $this->user = core::model('user');
    }

    public function index()
    {
        $this->display();
    }

    // 设置页面
    public function setting()
    {
        if (!empty($_POST)) {
            $configs = array(
                'alipay_api_url' => R('alipay_api_url', 'P'),
                'alipay_pid' => R('alipay_pid', 'P'),
                'alipay_key' => R('alipay_key', 'P'),
                'wechat_api_url' => R('wechat_api_url', 'P'),
                'wechat_pid' => R('wechat_pid', 'P'),
                'wechat_key' => R('wechat_key', 'P'),
                'site_name' => R('site_name', 'P'),
                'telegram_enabled' => R('telegram_enabled', 'P') ? '1' : '0',
                'telegram_bot_token' => R('telegram_bot_token', 'P'),
                'telegram_chat_id' => R('telegram_chat_id', 'P'),
                'telegram_proxy' => R('telegram_proxy', 'P'),
                'telegram_template' => R('telegram_template', 'P') ?: "💰 支付成功通知\n用户：{username}\n套餐：{package_name}\n金额：￥{amount}\n订单：{order_no}\n时间：{pay_time}"
            );
            
            if ($this->sj_rainbow_pay_config->batch_update_config($configs)) {
                E(0, '设置保存成功！');
            } else {
                E(1, '设置保存失败！');
            }
        }
        
        $config = $this->sj_rainbow_pay_config->get_all_config();

        // 确保Telegram配置有默认值
        $default_config = array(
            'alipay_api_url' => '',
            'alipay_pid' => '',
            'alipay_key' => '',
            'wechat_api_url' => '',
            'wechat_pid' => '',
            'wechat_key' => '',
            'site_name' => '',
            'telegram_enabled' => '0',
            'telegram_bot_token' => '',
            'telegram_chat_id' => '',
            'telegram_proxy' => '',
            'telegram_template' => "💰 支付成功通知\n用户：{username}\n套餐：{package_name}\n金额：￥{amount}\n订单：{order_no}\n时间：{pay_time}"
        );

        // 合并默认配置和实际配置
        $config = array_merge($default_config, $config);

        $this->assign('config', $config);
        $this->display('sj_rainbow_pay_setting.htm');
    }

    // 测试Telegram通知
    public function test_telegram()
    {
        if (!empty($_POST)) {
            $bot_token = R('bot_token', 'P');
            $chat_id = R('chat_id', 'P');

            if (empty($bot_token) || empty($chat_id)) {
                E(1, 'Bot Token和Chat ID不能为空！');
            }

            // 首先进行网络连接测试
            $connectivity_test = $this->test_network_connectivity();
            if (!$connectivity_test['success']) {
                E(1, '网络连接测试失败：' . $connectivity_test['error'] . '\n\n建议：\n1. 检查服务器网络设置\n2. 联系服务器提供商\n3. 考虑使用代理服务器\n4. 临时禁用Telegram通知');
            }

            $message = "🔔 测试通知\n这是来自彩虹易支付插件的测试消息\n时间：" . date('Y-m-d H:i:s');

            $result = $this->send_telegram_message($bot_token, $chat_id, $message);

            if ($result['success']) {
                E(0, '测试通知发送成功！');
            } else {
                // 提供详细的错误信息和解决建议
                $error_msg = '发送失败：' . $result['error'];

                if (strpos($result['error'], 'Connection timed out') !== false) {
                    $error_msg .= '\n\n这是网络连接超时问题，可能的原因：\n';
                    $error_msg .= '1. 服务器无法访问Telegram API\n';
                    $error_msg .= '2. 防火墙阻止了HTTPS出站连接\n';
                    $error_msg .= '3. Telegram在当前网络环境被屏蔽\n\n';
                    $error_msg .= '建议解决方案：\n';
                    $error_msg .= '1. 联系服务器提供商确认网络策略\n';
                    $error_msg .= '2. 配置代理服务器\n';
                    $error_msg .= '3. 临时关闭Telegram通知功能\n';
                    $error_msg .= '4. 考虑使用其他通知方式';
                }

                E(1, $error_msg);
            }
        }
    }

    // 测试网络连接
    private function test_network_connectivity()
    {
        // 测试基本网络连接
        $test_urls = array(
            'https://www.baidu.com',
            'https://api.github.com'
        );

        foreach ($test_urls as $url) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            $response = curl_exec($ch);
            $error = curl_error($ch);
            curl_close($ch);

            if (!$error) {
                return array('success' => true);
            }
        }

        return array('success' => false, 'error' => '基本网络连接测试失败');
    }

    // 订单管理
    public function orders()
    {
        $this->display('sj_rainbow_pay_orders.htm');
    }

    // 获取订单列表
    public function getorderslist()
    {
        try {
            // 设置JSON响应头
            header('Content-Type: application/json; charset=utf-8');

            // 在方法内部加载模型
            $this->sj_rainbow_pay_orders = core::model('sj_rainbow_pay_orders');

            $page = isset($_REQUEST['page']) ? intval($_REQUEST['page']) : 1;
            $pagenum = isset($_REQUEST['limit']) ? intval($_REQUEST['limit']) : 15;

            $where = array();
            $status = isset($_REQUEST['status']) ? trim($_REQUEST['status']) : '';
            $type = isset($_REQUEST['type']) ? trim($_REQUEST['type']) : '';
            $order_no = isset($_REQUEST['order_no']) ? trim($_REQUEST['order_no']) : '';
            $username = isset($_REQUEST['username']) ? trim($_REQUEST['username']) : '';

            if ($status !== '') {
                $where['status'] = intval($status);
            }
            if ($type !== '') {
                $where['type'] = intval($type);
            }
            if ($order_no !== '') {
                $where['order_no'] = array('LIKE' => $order_no);
            }

            // 如果有用户名搜索，需要先查询用户ID
            if ($username !== '') {
                $users = $this->user->find_fetch(array('username' => array('LIKE' => $username)), array(), 0, 100);
                if (!empty($users)) {
                    $uids = array_column($users, 'uid');
                    $where['uid'] = array('IN' => $uids);
                } else {
                    $where['uid'] = 0; // 没有找到用户，设置一个不存在的ID
                }
            }

            $total = $this->sj_rainbow_pay_orders->find_count($where);
            $maxpage = max(1, ceil($total / $pagenum));
            $page = min($maxpage, max(1, $page));

            $data = $this->sj_rainbow_pay_orders->list_arr($where, 'id', -1, ($page - 1) * $pagenum, $pagenum, $total);

            // 确保data是数组
            if (!is_array($data)) {
                $data = array();
            }

            $arr = array(
                'code' => 0,
                'msg' => '',
                'count' => intval($total),
                'data' => $data,
            );

            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json; charset=utf-8');
            // 记录详细错误信息
            error_log('sj_rainbow_pay订单列表获取失败: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行: ' . $e->getLine());
            $error_arr = array(
                'code' => 1,
                'msg' => '数据获取失败：' . $e->getMessage(),
                'count' => 0,
                'data' => array(),
                'debug' => array(
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                )
            );
            echo json_encode($error_arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    // 补单操作
    public function repairorder()
    {
        $order_no = trim(R('order_no', 'P'));
        if (empty($order_no)) {
            E(1, '订单号不能为空！');
        }

        $order = $this->sj_rainbow_pay_orders->get_by_order_no($order_no);
        if (empty($order)) {
            E(1, '订单不存在！订单号：' . $order_no);
        }

        if ($order['status'] == 1) {
            E(1, '订单已支付，无需补单！');
        }

        // 1. 使用模型的update_status方法更新订单状态
        $trade_no = 'manual_' . time();
        $pay_time = time();

        $update_result = $this->sj_rainbow_pay_orders->update_status($order_no, 1, $trade_no, $pay_time);
        if (!$update_result) {
            // 获取更详细的错误信息
            $debug_info = '订单ID: ' . $order['id'] . ', 订单号: ' . $order_no . ', 当前状态: ' . $order['status'];
            E(1, '订单状态更新失败！调试信息：' . $debug_info);
        }

        // 2. 处理用户奖励
        $user_data = $this->user->get($order['uid']);
        if (empty($user_data)) {
            E(1, '用户不存在！用户ID：' . $order['uid']);
        }

        // 用户表的主键是uid
        $user_update_data = array('uid' => $order['uid']);

        if ($order['type'] == 1) {
            // 金币充值
            $user_update_data['golds'] = intval($user_data['golds']) + intval($order['value']);
        } elseif ($order['type'] == 2) {
            // VIP开通
            if ($user_data['groupid'] == 11) {
                // 普通用户开通VIP
                $user_update_data['groupid'] = 12;
                $user_update_data['vip_times'] = time() + intval($order['value']) * 86400;
            } elseif ($user_data['groupid'] == 12) {
                // VIP用户续费
                $current_vip_times = intval($user_data['vip_times']);
                if ($current_vip_times > time()) {
                    $user_update_data['vip_times'] = $current_vip_times + intval($order['value']) * 86400;
                } else {
                    $user_update_data['vip_times'] = time() + intval($order['value']) * 86400;
                }
            }
        }

        $user_result = $this->user->update($user_update_data);
        if (!$user_result) {
            E(1, '用户奖励发放失败！用户ID：' . $order['uid'] . ', 奖励类型：' . $order['type']);
        }

        E(0, '补单成功！用户奖励已发放。');
    }

    // 处理用户奖励（与sj_kami兼容）
    private function process_user_reward($order)
    {
        try {
            $user_data = $this->user->get($order['uid']);
            if (empty($user_data)) {
                throw new Exception('用户不存在');
            }

            $update_data = array('uid' => $order['uid']);

            if ($order['type'] == 1) {
                // 金币充值 - 使用golds字段与sj_kami兼容
                $current_golds = intval($user_data['golds']);
                $add_golds = intval($order['value']);
                $update_data['golds'] = $current_golds + $add_golds;

            } elseif ($order['type'] == 2) {
                // VIP开通 - 使用groupid和vip_times字段与sj_kami兼容
                $add_days = intval($order['value']);
                if ($add_days > 0) {
                    if ($user_data['groupid'] == 11) {
                        // 普通用户开通VIP
                        $update_data['groupid'] = 12;
                        $update_data['vip_times'] = time() + $add_days * 86400;
                    } elseif ($user_data['groupid'] == 12) {
                        // VIP用户续费
                        $current_vip_times = intval($user_data['vip_times']);
                        if ($current_vip_times > time()) {
                            // VIP未过期，在现有时间基础上增加
                            $update_data['vip_times'] = $current_vip_times + $add_days * 86400;
                        } else {
                            // VIP已过期，从当前时间开始计算
                            $update_data['vip_times'] = time() + $add_days * 86400;
                        }
                    } else {
                        throw new Exception('用户组状态异常');
                    }
                } else {
                    throw new Exception('VIP天数无效');
                }
            } else {
                throw new Exception('订单类型无效');
            }

            $result = $this->user->update($update_data);
            if (!$result) {
                throw new Exception('用户数据更新失败');
            }

            return true;

        } catch (Exception $e) {
            // 记录错误日志
            error_log('Rainbow Pay - 用户奖励处理失败: ' . $e->getMessage() . ' - 订单: ' . $order['order_no']);
            return false;
        }
    }

    // 套餐管理
    public function packages()
    {
        $this->display('sj_rainbow_pay_packages.htm');
    }

    // 获取套餐列表
    public function getpackageslist()
    {
        try {
            // 设置JSON响应头
            header('Content-Type: application/json; charset=utf-8');

            // 在方法内部加载模型
            $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');

            $page = isset($_REQUEST['page']) ? intval($_REQUEST['page']) : 1;
            $pagenum = isset($_REQUEST['limit']) ? intval($_REQUEST['limit']) : 15;

            $where = array();
            $type = isset($_REQUEST['type']) ? trim($_REQUEST['type']) : '';
            $status = isset($_REQUEST['status']) ? trim($_REQUEST['status']) : '';

            if ($type !== '') {
                $where['type'] = intval($type);
            }
            if ($status !== '') {
                $where['status'] = intval($status);
            }

            $total = $this->sj_rainbow_pay_packages->find_count($where);
            $maxpage = max(1, ceil($total / $pagenum));
            $page = min($maxpage, max(1, $page));

            $data = $this->sj_rainbow_pay_packages->list_arr($where, 'sort', 1, ($page - 1) * $pagenum, $pagenum, $total);

            // 确保data是数组
            if (!is_array($data)) {
                $data = array();
            }

            $arr = array(
                'code' => 0,
                'msg' => '',
                'count' => intval($total),
                'data' => $data,
            );

            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json; charset=utf-8');
            // 记录详细错误信息
            error_log('sj_rainbow_pay套餐列表获取失败: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行: ' . $e->getLine());
            $error_arr = array(
                'code' => 1,
                'msg' => '数据获取失败：' . $e->getMessage(),
                'count' => 0,
                'data' => array(),
                'debug' => array(
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                )
            );
            echo json_encode($error_arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    // 套餐表单页面
    public function package_form()
    {
        // 确保加载套餐模型
        $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');

        $id = intval(R('id'));
        if ($id > 0) {
            $package = $this->sj_rainbow_pay_packages->get($id);
            if (empty($package)) {
                $this->message(0, '套餐不存在！', 'index.php?sj_rainbow_pay-packages');
                return;
            }
            $this->assign('package', $package);
        }
        $this->display('sj_rainbow_pay_package_form.htm');
    }

    // 保存套餐
    public function save_package()
    {
        if (!empty($_POST)) {
            // 确保加载套餐模型
            $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');
            $id = intval(R('id', 'P'));
            $name = trim(R('name', 'P'));
            $type = intval(R('type', 'P'));
            $amount = floatval(R('amount', 'P'));
            $value = intval(R('value', 'P'));
            $desc = trim(R('desc', 'P'));
            $status = intval(R('status', 'P'));
            $sort = intval(R('sort', 'P'));

            if (empty($name)) {
                E(1, '套餐名称不能为空！');
            }
            if (!in_array($type, array(1, 2))) {
                E(1, '套餐类型错误！');
            }
            if ($amount <= 0) {
                E(1, '价格必须大于0！');
            }
            if ($value <= 0) {
                E(1, '奖励数值必须大于0！');
            }

            $data = array(
                'name' => $name,
                'type' => intval($type),
                'amount' => floatval($amount),
                'value' => intval($value),
                'desc' => $desc,
                'status' => intval($status),
                'sort' => intval($sort)
            );

            if ($id > 0) {
                // 直接使用SQL更新，绕过LECMS模型层的问题
                $sql = "UPDATE " . $this->db->tablepre . "sj_rainbow_pay_packages SET
                        name = '" . addslashes($name) . "',
                        type = " . intval($type) . ",
                        amount = " . floatval($amount) . ",
                        value = " . intval($value) . ",
                        `desc` = '" . addslashes($desc) . "',
                        status = " . intval($status) . ",
                        sort = " . intval($sort) . "
                        WHERE id = " . intval($id);

                $result = $this->db->exec($sql);

                if ($result !== false) {
                    E(0, '套餐更新成功！');
                } else {
                    E(1, '套餐更新失败！SQL错误');
                }
            } else {
                // 直接使用SQL新增
                $sql = "INSERT INTO " . $this->db->tablepre . "sj_rainbow_pay_packages
                        (name, type, amount, value, `desc`, status, sort, create_time) VALUES (
                        '" . addslashes($name) . "',
                        " . intval($type) . ",
                        " . floatval($amount) . ",
                        " . intval($value) . ",
                        '" . addslashes($desc) . "',
                        " . intval($status) . ",
                        " . intval($sort) . ",
                        " . time() . ")";

                $result = $this->db->exec($sql);

                if ($result) {
                    E(0, '套餐添加成功！');
                } else {
                    E(1, '套餐添加失败！SQL错误');
                }
            }
        }
    }

    // 删除套餐
    public function delete_package()
    {
        // 确保加载套餐模型
        $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');

        $id = intval(R('id', 'P'));
        if ($id <= 0) {
            E(1, '参数错误！');
        }

        if ($this->sj_rainbow_pay_packages->delete($id)) {
            E(0, '删除成功！');
        } else {
            E(1, '删除失败！');
        }
    }

    // 批量删除套餐
    public function batch_delete_packages()
    {
        // 确保加载套餐模型
        $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');

        $id_arr = R('id_arr', 'P');
        if (!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach ($id_arr as $id) {
                if (!$this->sj_rainbow_pay_packages->delete($id)) {
                    $err_num++;
                }
            }
            if ($err_num) {
                E(1, $err_num . '个套餐删除失败！');
            } else {
                E(0, '批量删除成功！');
            }
        } else {
            E(1, '参数错误！');
        }
    }

    // 切换套餐状态
    public function toggle_package_status()
    {
        // 确保加载套餐模型
        $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');

        $id = intval(R('id', 'P'));
        if ($id <= 0) {
            E(1, '参数错误！');
        }

        $package = $this->sj_rainbow_pay_packages->get($id);
        if (empty($package)) {
            E(1, '套餐不存在！');
        }

        $new_status = $package['status'] == 1 ? 0 : 1;
        if ($this->sj_rainbow_pay_packages->update(array('id' => $id, 'status' => $new_status))) {
            E(0, '状态更新成功！');
        } else {
            E(1, '状态更新失败！');
        }
    }

    // 发送Telegram消息
    private function send_telegram_message($bot_token, $chat_id, $message)
    {
        // 获取代理配置
        $config = $this->sj_rainbow_pay_config->get_all_config();
        $proxy = isset($config['telegram_proxy']) ? trim($config['telegram_proxy']) : '';

        // 首先检查网络连接
        $connectivity_check = $this->check_telegram_connectivity($proxy);
        if (!$connectivity_check['success']) {
            return array('success' => false, 'error' => '网络连接检查失败：' . $connectivity_check['error']);
        }

        $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";

        $data = array(
            'chat_id' => $chat_id,
            'text' => $message,
            'parse_mode' => 'HTML'
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间到30秒
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 连接超时10秒
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'LECMS-TelegramBot/1.0');

        // 如果配置了代理，使用代理
        if (!empty($proxy)) {
            curl_setopt($ch, CURLOPT_PROXY, $proxy);
            curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);

        // 详细的错误信息
        if ($error) {
            $detailed_error = "CURL错误：$error";
            $detailed_error .= " | 连接时间：{$info['connect_time']}s";
            $detailed_error .= " | 总时间：{$info['total_time']}s";
            return array('success' => false, 'error' => $detailed_error);
        }

        if ($http_code != 200) {
            return array('success' => false, 'error' => "HTTP错误：$http_code | 响应：" . substr($response, 0, 200));
        }

        $result = json_decode($response, true);
        if ($result && $result['ok']) {
            return array('success' => true);
        } else {
            $error_msg = isset($result['description']) ? $result['description'] : '未知错误';
            return array('success' => false, 'error' => $error_msg);
        }
    }

    // 检查Telegram连接性
    private function check_telegram_connectivity($proxy = '')
    {
        // 尝试ping Telegram API
        $test_url = "https://api.telegram.org";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        // 如果配置了代理，使用代理
        if (!empty($proxy)) {
            curl_setopt($ch, CURLOPT_PROXY, $proxy);
            curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return array('success' => false, 'error' => "无法连接到Telegram API：$error");
        }

        if ($http_code >= 200 && $http_code < 400) {
            return array('success' => true);
        } else {
            return array('success' => false, 'error' => "Telegram API返回状态码：$http_code");
        }
    }

    // 发送支付成功通知
    public function send_payment_notification($order)
    {
        $config = $this->sj_rainbow_pay_config->get_all_config();

        // 检查是否启用Telegram通知
        if (empty($config['telegram_enabled']) || $config['telegram_enabled'] != '1') {
            return;
        }

        if (empty($config['telegram_bot_token']) || empty($config['telegram_chat_id'])) {
            return;
        }

        // 获取用户信息
        $user = $this->user->get_by_uid($order['uid']);
        if (empty($user)) {
            return;
        }

        // 获取套餐信息
        $package = $this->sj_rainbow_pay_packages->get_by_id($order['package_id']);
        $package_name = $package ? $package['name'] : '未知套餐';

        // 准备模板变量
        $variables = array(
            '{username}' => $user['username'],
            '{amount}' => $order['amount'],
            '{package_name}' => $package_name,
            '{order_no}' => $order['order_no'],
            '{pay_time}' => date('Y-m-d H:i:s', $order['pay_time'])
        );

        // 使用模板或默认消息
        $template = !empty($config['telegram_template']) ? $config['telegram_template'] :
                   "💰 支付成功通知\n用户：{username}\n套餐：{package_name}\n金额：￥{amount}\n订单：{order_no}\n时间：{pay_time}";

        $message = str_replace(array_keys($variables), array_values($variables), $template);

        // 发送通知
        $this->send_telegram_message($config['telegram_bot_token'], $config['telegram_chat_id'], $message);
    }
}
