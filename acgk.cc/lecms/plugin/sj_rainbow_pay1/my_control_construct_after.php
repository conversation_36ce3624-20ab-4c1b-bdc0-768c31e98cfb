<?php
// 在my控制器构造函数后加载sj_rainbow_pay相关模型
defined('ROOT_PATH') or exit;

// 确保sj_rainbow_pay模型正确加载
try {
    $this->sj_rainbow_pay_config = core::model('sj_rainbow_pay_config');
    $this->sj_rainbow_pay_orders = core::model('sj_rainbow_pay_orders');
    $this->sj_rainbow_pay_packages = core::model('sj_rainbow_pay_packages');
} catch (Exception $e) {
    // 如果模型加载失败，记录错误但不中断执行
    error_log('sj_rainbow_pay前端模型加载失败: ' . $e->getMessage());
}
