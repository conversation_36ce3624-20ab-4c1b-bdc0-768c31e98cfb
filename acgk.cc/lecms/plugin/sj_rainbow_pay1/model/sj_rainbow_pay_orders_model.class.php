<?php
defined('ROOT_PATH') or exit;

class sj_rainbow_pay_orders extends model {

    function __construct() {
        $this->table = 'sj_rainbow_pay_orders';
        $this->pri = array('id');
        $this->maxid = 'id';
    }

    // 获取订单列表
    public function list_arr($where, $orderby, $orderway, $start, $limit, $total) {
        try {
            // 优化大数据量翻页
            if($start > 1000 && $total > 2000 && $start > $total/2) {
                $orderway = -$orderway;
                $newstart = $total-$start-$limit;
                if($newstart < 0) {
                    $limit += $newstart;
                    $newstart = 0;
                }
                $list_arr = $this->find_fetch($where, array($orderby => $orderway), $newstart, $limit);
                $data = array_reverse($list_arr, TRUE);
            } else {
                $data = $this->find_fetch($where, array($orderby => $orderway), $start, $limit);
            }

            $statusArr = array(0=>'未支付', 1=>'已支付', 2=>'已取消');
            $typeArr = array(1=>'金币充值', 2=>'VIP开通');
            $payTypeArr = array('alipay'=>'支付宝', 'wechat'=>'微信支付');

            $rsData = array();
            if(!empty($data)){
                // 批量获取用户信息，提高性能
                $uids = array_column($data, 'uid');
                $uids = array_filter($uids); // 过滤掉空值
                $users = array();
                if (!empty($uids)) {
                    try {
                        $user_model = core::model('user');
                        $user_list = $user_model->find_fetch(array('uid' => array('IN' => $uids)), array(), 0, 0);
                        foreach ($user_list as $user) {
                            $users[$user['uid']] = $user;
                        }
                    } catch (Exception $e) {
                        // 如果用户模型加载失败，继续处理其他数据
                    }
                }

                foreach($data as $k=>$v){
                    $v['status_text'] = isset($statusArr[$v['status']]) ? $statusArr[$v['status']] : '-';
                    $v['type_text'] = isset($typeArr[$v['type']]) ? $typeArr[$v['type']] : '-';
                    $v['pay_type_text'] = isset($payTypeArr[$v['pay_type']]) ? $payTypeArr[$v['pay_type']] : '-';
                    $v['create_time_text'] = empty($v['create_time']) ? '-' : date('Y-m-d H:i:s', $v['create_time']);
                    $v['pay_time_text'] = empty($v['pay_time']) ? '-' : date('Y-m-d H:i:s', $v['pay_time']);
                    $v['amount_text'] = '￥'.$v['amount'];

                    // 获取用户信息
                    if($v['uid'] && isset($users[$v['uid']])) {
                        $v['username'] = $users[$v['uid']]['username'];
                    } elseif($v['uid']) {
                        $v['username'] = '用户ID：'.$v['uid'];
                    } else {
                        $v['username'] = '-';
                    }

                    $rsData[] = $v;
                }
            }
            return $rsData;
        } catch (Exception $e) {
            // 如果出现异常，返回空数组
            return array();
        }
    }

    // 生成订单号 - 防重复版本
    public function generate_order_no() {
        $max_attempts = 10;
        $attempts = 0;

        do {
            // 使用微秒时间戳确保唯一性
            $microtime = explode(' ', microtime());
            $timestamp = date('YmdHis') . substr($microtime[0], 2, 3); // 添加毫秒
            $order_no = 'RB' . $timestamp . mt_rand(100, 999);

            // 检查订单号是否已存在
            $existing = $this->get_by_order_no($order_no);
            if (empty($existing)) {
                return $order_no;
            }

            $attempts++;
            usleep(1000); // 等待1毫秒
        } while ($attempts < $max_attempts);

        // 如果还是重复，使用更复杂的生成方式
        return 'RB' . date('YmdHis') . uniqid() . mt_rand(10, 99);
    }

    // 根据订单号获取订单
    public function get_by_order_no($order_no) {
        $data = $this->find_fetch(array('order_no' => $order_no), array(), 0, 1);
        return $data ? current($data) : array();
    }

    // 更新订单状态
    public function update_status($order_no, $status, $trade_no = '', $pay_time = 0) {
        try {
            // 先获取订单信息
            $order = $this->get_by_order_no($order_no);
            if (empty($order)) {
                return false;
            }

            // LECMS的update方法需要包含主键
            $update_data = array(
                'id' => $order['id'],  // 必须包含主键
                'status' => intval($status)
            );

            if (!empty($trade_no)) {
                $update_data['trade_no'] = trim($trade_no);
            }

            if ($pay_time > 0) {
                $update_data['pay_time'] = intval($pay_time);
            }

            $result = $this->update($update_data);

            // 验证更新是否成功
            if ($result) {
                $updated_order = $this->get_by_order_no($order_no);
                return $updated_order && $updated_order['status'] == $status;
            }

            return false;

        } catch (Exception $e) {
            error_log('Rainbow Pay - 订单状态更新失败: ' . $e->getMessage() . ' - 订单号: ' . $order_no);
            return false;
        }
    }

    // 创建订单 - 防重复版本
    public function create_order($uid, $package_id, $pay_type) {
        // 手动加载套餐模型文件
        $model_file = dirname(__FILE__) . '/sj_rainbow_pay_packages_model.class.php';
        if (file_exists($model_file)) {
            require_once $model_file;
        }

        // 实例化套餐模型
        $packages_model = new sj_rainbow_pay_packages();

        // 获取套餐信息
        $package = $packages_model->get($package_id);
        if (empty($package) || $package['status'] != 1) {
            return array('err' => 1, 'msg' => '套餐不存在或已禁用');
        }

        // 暂时去掉重复订单检测，直接创建新订单
        // 后续可以通过前端防重复点击来避免重复订单

        $order_no = $this->generate_order_no();
        $order_data = array(
            'order_no' => $order_no,
            'uid' => $uid,
            'type' => $package['type'],
            'package_id' => $package_id,
            'amount' => $package['amount'],
            'value' => $package['value'],
            'pay_type' => $pay_type,
            'status' => 0,
            'create_time' => time()
        );

        $id = $this->create($order_data);
        if ($id) {
            $order_data['id'] = $id;
            return array('err' => 0, 'data' => $order_data);
        } else {
            return array('err' => 1, 'msg' => '订单创建失败');
        }
    }
}
