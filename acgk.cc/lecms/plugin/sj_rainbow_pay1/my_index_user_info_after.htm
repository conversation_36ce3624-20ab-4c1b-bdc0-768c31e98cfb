<!-- sj_rainbow_pay 账户状态显示 -->
</div>
</div>

<!-- 账户状态卡片 - 放在用户介绍下方 -->
<div class="col-md-9 col-sm-9 col-xs-12" style="margin-top: 15px;">
  <div class="ui-content">
    <style>
      .rainbow-pay-status {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin: 0;
      }
      .rainbow-pay-status h4 {
        margin-bottom: 15px;
        font-weight: 300;
        color: white;
        font-size: 18px;
      }
      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }
      .status-item-card {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        transition: all 0.3s;
      }
      .status-item-card:hover {
        background: rgba(255,255,255,0.2);
        transform: translateY(-2px);
      }
      .status-icon-small {
        font-size: 24px;
        margin-bottom: 8px;
        display: block;
      }
      .status-value-small {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        line-height: 1.2;
      }
      .status-label-small {
        font-size: 12px;
        opacity: 0.9;
      }
      .vip-expire-info {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 12px;
        margin-top: 15px;
        font-size: 14px;
        text-align: center;
      }
      .quick-actions-small {
        margin-top: 15px;
        text-align: center;
      }
      .quick-actions-small .btn {
        margin: 0 5px 5px 0;
        padding: 8px 15px;
        border-radius: 15px;
        font-size: 12px;
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        background: rgba(255,255,255,0.1);
        transition: all 0.3s;
        text-decoration: none;
        display: inline-block;
      }
      .quick-actions-small .btn:hover {
        background: rgba(255,255,255,0.2);
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
      }
      @media (max-width: 768px) {
        .status-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }
        .status-item-card {
          padding: 12px;
        }
        .status-value-small {
          font-size: 16px;
        }
        .rainbow-pay-status h4 {
          font-size: 16px;
        }
        .quick-actions-small .btn {
          font-size: 11px;
          padding: 6px 12px;
        }
      }
    </style>

    <!-- 彩虹易支付账户状态 -->
    <div class="rainbow-pay-status">
      <h4><i class="fa fa-credit-card"></i> 账户状态</h4>

      <div class="status-grid">
        <!-- 金币状态 -->
        <div class="status-item-card">
          <i class="fa fa-coins status-icon-small" style="color: #f39c12;"></i>
          <div class="status-value-small">{$_user['golds']}</div>
          <div class="status-label-small">当前金币</div>
        </div>

        <!-- VIP状态 -->
        <div class="status-item-card">
          <i class="fa fa-crown status-icon-small" style="color: #e74c3c;"></i>
          <div class="status-value-small">
            {if:stripos($_group['groupname'], 'VIP') !== false || stripos($_group['groupname'], 'vip') !== false || ($_user['groupid'] == 12 && $_user['vip_times'] > time()) || ($_user['vip_times'] > time())}
              VIP会员
            {else}
              普通用户
            {/if}
          </div>
          <div class="status-label-small">當前用戶組</div>
        </div>
      </div>
	
      <!-- VIP到期时间 -->
      {if:$_user['groupid'] == 12 && $_user['vip_times'] > time()}
      <div class="vip-expire-info">
        <strong>到期时间：</strong>{$vip_expire_time}
      </div>
      {/if}

      <!-- 快捷操作 -->
      <div class="quick-actions-small">
        <a href="/my-recharge.html" class="btn btn-sm">
          <i class="fa fa-coins"></i> 金币充值
        </a>
        <a href="/my-recharge.html" class="btn btn-sm">
          <i class="fa fa-crown"></i> VIP充值
        </a>
        <a href="/my-orders.html" class="btn btn-sm">
          <i class="fa fa-list-alt"></i> 充值记录
        </a>
      </div>
    </div>
  </div>
</div>

<!-- 恢复原有的布局结构 -->
<div class="col-md-9 col-sm-9 col-xs-12">
  <div class="ui-content">
    <div class="basicinfo">
