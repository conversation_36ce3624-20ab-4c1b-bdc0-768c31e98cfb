<?php
return array(
	'name' => '彩虹易支付',	// 插件名
	'brief' => '彩虹易支付插件，支持微信和支付宝支付，可开通VIP和充值金币，实时到账，支持补单功能！兼容sj_kami插件数据结构。',
	'version' => '1.0.0',			// 插件版本
	'cms_version' => '3.0.3',		// 插件支持的程序版本
	'update' => '2024-12-28',		// 插件最近更新
	'author' => 'LECMS开发者',				// 插件作者
	'authorurl' => 'https://www.lecms.cc',	// 插件作者主页
	'setting' => 'index.php?sj_rainbow_pay-setting',		// 插件设置URL
	'rank' => 100,				// 优先级
);
