{inc:user/header.htm}
<style>
  .order-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s;
  }
  .order-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }
  .order-no {
    font-weight: bold;
    color: #333;
  }
  .order-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
  }
  .status-unpaid {
    background-color: #fff3cd;
    color: #856404;
  }
  .status-paid {
    background-color: #d4edda;
    color: #155724;
  }
  .status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }
  .order-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .order-details {
    flex: 1;
  }
  .order-amount {
    font-size: 18px;
    font-weight: bold;
    color: #e74c3c;
  }
  .order-actions {
    margin-left: 20px;
  }
  .btn-check {
    background-color: #17a2b8;
    border-color: #17a2b8;
  }
  .btn-check:hover {
    background-color: #138496;
    border-color: #117a8b;
  }
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
  }
  .empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    display: block;
  }
</style>

<main class="content">
  <div id="content-container" class="container">
    <div class="row">
      {inc:user/menu.htm}
      <!--右侧主体部分 start-->
      <div class="col-md-9">
        <div class="panel panel-default">
          <div class="panel-body">
            <h2 class="page-header">
              <i class="fa fa-list-alt"></i> 充值记录
            </h2>
            
            {if:empty($data)}
            <div class="empty-state">
              <i class="fa fa-inbox"></i>
              <h4>暂无充值记录</h4>
              <p>您还没有进行过充值，<a href="/my-recharge.html">立即充值</a></p>
            </div>
            {else}
            <div class="orders-list">
              {loop:$data $order}
              <div class="order-item">
                <div class="order-header">
                  <div class="order-no">订单号：{$order['order_no']}</div>
                  <div class="order-status {if:$order['status'] == 0}status-unpaid{elseif:$order['status'] == 1}status-paid{else}status-cancelled{/if}">
                    {$order['status_text']}
                  </div>
                </div>
                
                <div class="order-info">
                  <div class="order-details">
                    <div class="row">
                      <div class="col-md-3">
                        <strong>类型：</strong>{$order['type_text']}
                      </div>
                      <div class="col-md-3">
                        <strong>奖励：</strong>
                        {if:$order['type'] == 1}
                          {$order['value']} 金币
                        {else}
                          {$order['value']} 天VIP
                        {/if}
                      </div>
                      <div class="col-md-3">
                        <strong>支付方式：</strong>{$order['pay_type_text']}
                      </div>
                      <div class="col-md-3">
                        <strong>创建时间：</strong>{$order['create_time_text']}
                      </div>
                    </div>
                    {if:$order['pay_time_text'] != '-'}
                    <div class="row" style="margin-top: 10px;">
                      <div class="col-md-12">
                        <strong>支付时间：</strong>{$order['pay_time_text']}
                      </div>
                    </div>
                    {/if}
                  </div>

                  <div class="order-amount">
                    ￥{$order['amount']}
                  </div>

                  {if:$order['status'] == 0}
                  <div class="order-actions">
                    <button type="button" class="btn btn-sm btn-check" onclick="checkOrder('{$order['order_no']}')">
                      <i class="fa fa-refresh"></i> 检查状态
                    </button>
                  </div>
                  {/if}
                </div>
              </div>
              {/loop}
            </div>
            
            <!-- 分页 -->
            <div class="text-center">
              {$pages}
            </div>
            {/if}
            
            <!-- 帮助信息 -->
            <div class="alert alert-info" style="margin-top: 30px;">
              <h5><i class="fa fa-question-circle"></i> 常见问题</h5>
              <p><strong>Q：支付后多久到账？</strong></p>
              <p>A：一般在1-3分钟内自动到账，最长不超过10分钟。</p>
              <p><strong>Q：支付成功但未到账怎么办？</strong></p>
              <p>A：请点击"检查状态"按钮，或联系客服处理。</p>
              <p><strong>Q：可以申请退款吗？</strong></p>
              <p>A：虚拟商品一经发放不支持退款，请谨慎购买。</p>
            </div>
          </div>
        </div>
      </div>
      <!--右侧主体部分 end-->
    </div>
  </div>
</main>

{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    $("#my-rainbow-pay-orders").addClass("active");
  });
  
  // 检查订单状态
  function checkOrder(orderNo) {
    var loadIndex = layer.load(2, {shade: [0.3, '#000']});
    
    $.ajax({
      type: "POST",
      url: "my-check.html",
      data: {order_no: orderNo},
      dataType: 'json',
      success: function(result) {
        layer.close(loadIndex);
        console.log('检查订单状态返回:', result); // 调试信息

        // 检查返回数据的结构
        if (typeof result !== 'object' || result === null) {
          layer.alert('服务器返回数据格式错误！');
          return;
        }

        if(!result.err){
          // 确保data存在且包含必要字段
          if(result.data && typeof result.data === 'object') {
            if(result.data.status == 1) {
              layer.alert('订单已支付成功！页面即将刷新...', function(){
                location.reload();
              });
            } else {
              var statusText = result.data.status_text || '未知状态';
              layer.alert('订单状态：' + statusText);
            }
          } else {
            layer.alert('服务器返回数据不完整！');
          }
        } else {
          var errorMsg = result.msg || '未知错误';
          layer.alert('检查失败：' + errorMsg);
        }
      },
      error: function(){
        layer.close(loadIndex);
        layer.alert('网络错误，请重试！');
      }
    });
  }
</script>
