<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{if:isset($package)}编辑套餐{else}添加套餐{/if}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="fa fa-gift"></i> {if:isset($package)}编辑套餐{else}添加套餐{/if}
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="index.php?sj_rainbow_pay-save_package" method="post">
                    {if:isset($package)}
                    <input type="hidden" name="id" value="{$package['id']}">
                    {/if}
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">套餐名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="{if:isset($package)}{$package['name']}{/if}"
                                   placeholder="请输入套餐名称" class="layui-input" required lay-verify="required">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">套餐类型</label>
                        <div class="layui-input-block">
                            <input type="radio" name="type" value="1" title="金币充值"
                                   {if:!isset($package) || $package['type'] == 1}checked{/if}>
                            <input type="radio" name="type" value="2" title="VIP开通"
                                   {if:isset($package) && $package['type'] == 2}checked{/if}>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">价格</label>
                        <div class="layui-input-block">
                            <input type="number" name="amount" value="{if:isset($package)}{$package['amount']}{/if}"
                                   placeholder="请输入价格" class="layui-input" step="0.01" min="0.01" required lay-verify="required">
                            <div class="layui-form-mid layui-word-aux">单位：元</div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">套餐描述</label>
                        <div class="layui-input-block">
                            <input type="text" name="desc" value="{if:isset($package)}{$package['desc']}{/if}"
                                   placeholder="请输入套餐描述" class="layui-input" maxlength="255">
                            <div class="layui-form-mid layui-word-aux">套餐的简短描述，显示在前台</div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label required">奖励数值</label>
                        <div class="layui-input-block">
                            <input type="number" name="value" value="{if:isset($package)}{$package['value']}{/if}"
                                   placeholder="请输入奖励数值" class="layui-input" min="1" required lay-verify="required">
                            <div class="layui-form-mid layui-word-aux">金币充值：金币数量；VIP开通：天数</div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">排序</label>
                        <div class="layui-input-block">
                            <input type="number" name="sort" value="{if:isset($package)}{$package['sort']}{else}0{/if}"
                                   placeholder="排序值，数字越小越靠前" class="layui-input" min="0">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="启用"
                                   {if:!isset($package) || $package['status'] == 1}checked{/if}>
                            <input type="radio" name="status" value="0" title="禁用"
                                   {if:isset($package) && $package['status'] == 0}checked{/if}>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="form">保存</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form, layer = layui.layer;

    // 监听提交
    form.on('submit(form)', function(data){
        var loadIndex = layer.load(2, {shade: [0.3, '#000']});

        $.ajax({
            url: data.form.action,
            type: 'POST',
            data: data.field,
            dataType: 'json',
            success: function(result) {
                layer.close(loadIndex);
                if (!result.err) {
                    layer.msg('保存成功！', {icon: 1}, function() {
                        // 关闭当前tab并刷新父页面
                        if (parent && parent.layui && parent.layui.miniTab) {
                            parent.layui.miniTab.deleteCurrentByIframe();
                            // 刷新父页面的表格
                            if (parent.layui.table) {
                                parent.layui.table.reload('data-table');
                            }
                        } else {
                            // 如果不是在iframe中，直接跳转
                            location.href = 'index.php?sj_rainbow_pay-packages';
                        }
                    });
                } else {
                    layer.alert('保存失败：' + result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.close(loadIndex);
                layer.alert('网络错误，请重试！', {icon: 2});
            }
        });

        return false; // 阻止表单默认提交
    });
});
</script>
</body>
</html>
