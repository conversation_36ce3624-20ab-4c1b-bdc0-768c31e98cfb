<?php
// VIP时间调试工具
header('Content-Type: text/html; charset=utf-8');

echo "<h2>VIP时间调试工具</h2>";

// 数据库配置
$db_config = array(
    'host' => '127.0.0.1',
    'port' => 3306,
    'name' => 'root',
    'user' => 'root',
    'password' => 'ServBay.dev',
    'tablepre' => 'le_'
);

try {
    // 尝试读取LECMS配置
    $config_paths = array(
        '/www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php',
        dirname(__FILE__) . '/../../../config/config.inc.php',
        $_SERVER['DOCUMENT_ROOT'] . '/lecms/config/config.inc.php'
    );
    
    foreach ($config_paths as $config_path) {
        if (file_exists($config_path)) {
            include $config_path;
            if (isset($_ENV['_config']['db']['master'])) {
                $lecms_db = $_ENV['_config']['db']['master'];
                $db_config = array(
                    'host' => $lecms_db['host'],
                    'port' => $lecms_db['port'],
                    'name' => $lecms_db['name'],
                    'user' => $lecms_db['user'],
                    'password' => $lecms_db['password'],
                    'tablepre' => $lecms_db['tablepre']
                );
                break;
            }
        }
    }
    
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['name']};charset=utf8";
    $pdo = new PDO($dsn, $db_config['user'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询用户信息
    echo "<h3>查询用户VIP信息：</h3>";
    echo "<form method='get'>";
    echo "<input type='text' name='uid' placeholder='输入用户ID' value='" . (isset($_GET['uid']) ? htmlspecialchars($_GET['uid']) : '1') . "'>";
    echo "<input type='submit' value='查询'>";
    echo "</form>";
    
    if (isset($_GET['uid']) && !empty($_GET['uid'])) {
        $uid = intval($_GET['uid']);
        
        // 查询用户信息
        $user_sql = "SELECT uid, username, groupid, golds, vip_times FROM {$db_config['tablepre']}user WHERE uid = ?";
        $user_stmt = $pdo->prepare($user_sql);
        $user_stmt->execute(array($uid));
        $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<div style='background:#e8f5e8;padding:15px;border:1px solid #4caf50;border-radius:5px;margin:10px 0;'>";
            echo "<h4>用户信息：</h4>";
            echo "<p><strong>用户ID：</strong>" . htmlspecialchars($user['uid']) . "</p>";
            echo "<p><strong>用户名：</strong>" . htmlspecialchars($user['username']) . "</p>";
            echo "<p><strong>用户组ID：</strong>" . htmlspecialchars($user['groupid']) . "</p>";
            echo "<p><strong>金币：</strong>" . htmlspecialchars($user['golds']) . "</p>";
            echo "<p><strong>VIP时间戳：</strong>" . htmlspecialchars($user['vip_times']) . "</p>";
            
            if ($user['vip_times']) {
                $vip_time = intval($user['vip_times']);
                $current_time = time();
                
                echo "<p><strong>VIP到期时间：</strong>" . date('Y-m-d H:i:s', $vip_time) . "</p>";
                echo "<p><strong>当前时间：</strong>" . date('Y-m-d H:i:s', $current_time) . "</p>";
                
                $remaining_seconds = $vip_time - $current_time;
                $remaining_days = ceil($remaining_seconds / 86400);
                
                if ($remaining_days > 0) {
                    echo "<p><strong>VIP状态：</strong><span style='color:green;'>有效</span></p>";
                    echo "<p><strong>剩余天数：</strong>" . $remaining_days . " 天</p>";
                } else {
                    echo "<p><strong>VIP状态：</strong><span style='color:red;'>已过期</span></p>";
                    echo "<p><strong>过期天数：</strong>" . abs($remaining_days) . " 天</p>";
                }
                
                // 调试信息
                echo "<hr>";
                echo "<h5>调试信息：</h5>";
                echo "<p>VIP时间戳: $vip_time</p>";
                echo "<p>当前时间戳: $current_time</p>";
                echo "<p>时间差(秒): $remaining_seconds</p>";
                echo "<p>时间差(天): " . ($remaining_seconds / 86400) . "</p>";
                echo "<p>向上取整天数: $remaining_days</p>";
            } else {
                echo "<p><strong>VIP状态：</strong>未开通</p>";
            }
            echo "</div>";
            
            // 查询最近的VIP订单
            $order_sql = "SELECT * FROM {$db_config['tablepre']}sj_rainbow_pay_orders WHERE uid = ? AND type = 2 ORDER BY create_time DESC LIMIT 3";
            $order_stmt = $pdo->prepare($order_sql);
            $order_stmt->execute(array($uid));
            $orders = $order_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($orders) {
                echo "<div style='background:#fff3cd;padding:15px;border:1px solid #ffeaa7;border-radius:5px;margin:10px 0;'>";
                echo "<h4>最近的VIP订单：</h4>";
                foreach ($orders as $order) {
                    echo "<div style='border-bottom:1px solid #ddd;padding:10px 0;'>";
                    echo "<p><strong>订单号：</strong>" . htmlspecialchars($order['order_no']) . "</p>";
                    echo "<p><strong>状态：</strong>" . ($order['status'] == 1 ? '<span style="color:green;">已支付</span>' : '<span style="color:red;">未支付</span>') . "</p>";
                    echo "<p><strong>VIP天数：</strong>" . htmlspecialchars($order['value']) . " 天</p>";
                    echo "<p><strong>创建时间：</strong>" . date('Y-m-d H:i:s', $order['create_time']) . "</p>";
                    if ($order['pay_time']) {
                        echo "<p><strong>支付时间：</strong>" . date('Y-m-d H:i:s', $order['pay_time']) . "</p>";
                    }
                    echo "</div>";
                }
                echo "</div>";
            }
            
        } else {
            echo "<p style='color:red;'>用户不存在：$uid</p>";
        }
    }
    
    // 修复VIP时间的工具
    if (isset($_POST['fix_vip']) && isset($_POST['fix_uid'])) {
        $fix_uid = intval($_POST['fix_uid']);
        $fix_days = intval($_POST['fix_days']);
        
        if ($fix_uid > 0 && $fix_days > 0) {
            $new_vip_time = time() + ($fix_days * 86400);
            
            $fix_sql = "UPDATE {$db_config['tablepre']}user SET groupid = 12, vip_times = ? WHERE uid = ?";
            $fix_stmt = $pdo->prepare($fix_sql);
            $fix_result = $fix_stmt->execute(array($new_vip_time, $fix_uid));
            
            if ($fix_result) {
                echo "<div style='background:#d4edda;padding:15px;border:1px solid #c3e6cb;border-radius:5px;margin:10px 0;'>";
                echo "<h4>✅ VIP时间修复成功</h4>";
                echo "<p>用户ID: $fix_uid</p>";
                echo "<p>VIP天数: $fix_days 天</p>";
                echo "<p>到期时间: " . date('Y-m-d H:i:s', $new_vip_time) . "</p>";
                echo "</div>";
            } else {
                echo "<div style='background:#f8d7da;padding:15px;border:1px solid #f5c6cb;border-radius:5px;margin:10px 0;'>";
                echo "<h4>❌ VIP时间修复失败</h4>";
                echo "</div>";
            }
        }
    }
    
    echo "<div style='background:#f8f9fa;padding:15px;border-radius:5px;margin:20px 0;'>";
    echo "<h3>🔧 VIP时间修复工具</h3>";
    echo "<form method='post'>";
    echo "<p>";
    echo "<label>用户ID:</label><br>";
    echo "<input type='number' name='fix_uid' value='1' style='width:200px;padding:5px;'>";
    echo "</p>";
    echo "<p>";
    echo "<label>VIP天数:</label><br>";
    echo "<input type='number' name='fix_days' value='30' style='width:200px;padding:5px;'>";
    echo "</p>";
    echo "<p>";
    echo "<button type='submit' name='fix_vip' style='background:#007bff;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;'>";
    echo "🔧 修复VIP时间";
    echo "</button>";
    echo "</p>";
    echo "</form>";
    echo "<small style='color:#666;'>注意：此操作会直接修改数据库，请谨慎使用</small>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color:red;'>数据库错误：" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<div style='margin-top:30px;padding:15px;background:#e3f2fd;border-radius:5px;'>";
echo "<h4>💡 使用说明：</h4>";
echo "<ul>";
echo "<li>此工具用于调试和修复VIP时间显示问题</li>";
echo "<li>可以查看用户的详细VIP信息</li>";
echo "<li>可以手动修复VIP时间（如果回调处理有问题）</li>";
echo "<li>显示详细的调试信息，便于排查问题</li>";
echo "</ul>";
echo "</div>";

?>
