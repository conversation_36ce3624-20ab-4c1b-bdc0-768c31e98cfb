<?php
// 移动端微信支付代理页面
// 解决彩虹易支付移动端微信支付空白页面问题

// 获取支付参数
$api_url = $_GET['api_url'] ?? '';
$params = $_GET;
unset($params['api_url']); // 移除api_url参数

if (empty($api_url) || empty($params)) {
    die('参数错误');
}

// 构建真实的支付URL
$submit_url = rtrim($api_url, '/') . '/submit.php';

// 使用CURL伪装成PC端请求支付页面
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $submit_url . '?' . http_build_query($params));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

// 关键：伪装成PC端Chrome浏览器
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

// 设置PC端HTTP头
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding: gzip, deflate, br',
    'Connection: keep-alive',
    'Upgrade-Insecure-Requests: 1'
));

$response = curl_exec($ch);
$error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($error || $http_code != 200) {
    die('获取支付页面失败：' . $error);
}

// 优化HTML内容，确保移动端友好显示
$optimized_html = optimizeForMobile($response);

// 输出优化后的HTML
echo $optimized_html;

// 优化HTML函数
function optimizeForMobile($html) {
    // 添加移动端viewport
    $viewport = '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';
    
    // 移动端优化CSS
    $mobile_css = '
    <style>
    /* 移动端微信支付优化样式 */
    body { 
        margin: 0; 
        padding: 10px; 
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        background: #f5f5f5; 
        line-height: 1.6;
    }
    
    .payment-container, .main-content, .content { 
        max-width: 100% !important; 
        margin: 0 auto; 
        background: white; 
        padding: 15px; 
        border-radius: 10px; 
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 15px;
    }
    
    /* 二维码容器优化 */
    .qrcode-container, .qr-code, .qrcode, [class*="qr"] { 
        text-align: center !important; 
        margin: 20px auto !important; 
        padding: 15px;
        background: white;
        border-radius: 8px;
    }
    
    /* 二维码图片优化 */
    .qrcode-container img, .qr-code img, .qrcode img, img[src*="qr"], img[src*="QR"] { 
        max-width: 280px !important; 
        width: 100% !important; 
        height: auto !important; 
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 10px;
        background: white;
    }
    
    /* 支付信息优化 */
    .payment-info, .pay-info, .order-info { 
        text-align: center; 
        margin: 15px 0; 
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    /* 金额显示优化 */
    .amount, .money, .price, [class*="amount"], [class*="money"] { 
        font-size: 24px !important; 
        font-weight: bold !important; 
        color: #e74c3c !important; 
        margin: 10px 0;
    }
    
    /* 提示信息优化 */
    .tips, .notice, .help, [class*="tip"] { 
        font-size: 14px; 
        color: #666; 
        line-height: 1.6; 
        margin: 15px 0;
        padding: 10px;
        background: #e8f5e8;
        border-radius: 6px;
        border-left: 4px solid #4caf50;
    }
    
    /* 按钮优化 */
    .btn, button, input[type="button"], input[type="submit"] {
        width: 100% !important;
        max-width: 300px;
        margin: 10px auto;
        padding: 12px 20px;
        font-size: 16px;
        border-radius: 6px;
        border: none;
        background: #667eea;
        color: white;
        cursor: pointer;
        display: block;
    }
    
    /* 表格优化 */
    table { 
        width: 100% !important; 
        font-size: 14px;
        border-collapse: collapse;
    }
    
    table td, table th {
        padding: 8px;
        border: 1px solid #ddd;
        text-align: left;
    }
    
    /* 隐藏不必要的元素 */
    .sidebar, .footer, .header-nav, [class*="ad"], [class*="banner"] {
        display: none !important;
    }
    
    /* 响应式优化 */
    @media (max-width: 480px) {
        body { padding: 5px; }
        .payment-container, .main-content, .content { padding: 10px; }
        .amount, .money, .price { font-size: 20px !important; }
        .qrcode-container img, .qr-code img { max-width: 250px !important; }
    }
    
    /* 强制显示所有二维码相关元素 */
    *[style*="display: none"] img[src*="qr"],
    *[style*="display: none"] img[src*="QR"],
    *[style*="display:none"] img[src*="qr"],
    *[style*="display:none"] img[src*="QR"] {
        display: block !important;
        visibility: visible !important;
    }
    
    /* 确保二维码容器可见 */
    .qrcode-container, .qr-code, .qrcode {
        display: block !important;
        visibility: visible !important;
    }
    </style>';
    
    // JavaScript优化
    $mobile_js = '
    <script>
    // 移动端优化JavaScript
    document.addEventListener("DOMContentLoaded", function() {
        // 强制显示所有二维码
        var qrImages = document.querySelectorAll("img");
        qrImages.forEach(function(img) {
            if (img.src && (img.src.indexOf("qr") > -1 || img.src.indexOf("QR") > -1)) {
                img.style.display = "block";
                img.style.visibility = "visible";
                img.style.maxWidth = "280px";
                img.style.width = "100%";
                img.style.height = "auto";
                
                // 确保父容器也可见
                var parent = img.parentElement;
                while (parent && parent !== document.body) {
                    parent.style.display = "block";
                    parent.style.visibility = "visible";
                    parent = parent.parentElement;
                }
            }
        });
        
        // 添加支付提示
        var body = document.body;
        var notice = document.createElement("div");
        notice.innerHTML = "<div style=\"background: #667eea; color: white; padding: 15px; text-align: center; border-radius: 8px; margin: 10px 0;\"><strong>📱 移动端微信支付</strong><br>请使用微信扫描下方二维码完成支付</div>";
        body.insertBefore(notice, body.firstChild);
    });
    </script>';
    
    // 在head标签中插入优化内容
    if (strpos($html, '<head>') !== false) {
        $html = str_replace('<head>', '<head>' . $viewport . $mobile_css . $mobile_js, $html);
    } else {
        // 如果没有head标签，在开头添加
        $html = $viewport . $mobile_css . $mobile_js . $html;
    }
    
    return $html;
}
?>
