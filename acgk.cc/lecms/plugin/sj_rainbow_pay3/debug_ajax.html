<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajax请求调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        .test-btn:hover {
            background: #5a6fd8;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .error {
            background: #ffebee;
            border-left-color: #f44336;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h2>Ajax请求调试工具</h2>
        
        <div class="info">
            <h4>当前环境信息：</h4>
            <p><strong>当前URL：</strong><span id="current-url"></span></p>
            <p><strong>User Agent：</strong><span id="user-agent"></span></p>
            <p><strong>设备类型：</strong><span id="device-type"></span></p>
            <p><strong>jQuery版本：</strong><span id="jquery-version"></span></p>
        </div>

        <button class="test-btn" onclick="testBasicAjax()">测试基础Ajax请求</button>
        <button class="test-btn" onclick="testCreateOrder()">测试创建订单请求</button>
        <button class="test-btn" onclick="testWithDifferentMethod()">测试GET请求</button>
        <button class="test-btn" onclick="clearResults()">清空结果</button>

        <div id="test-result" class="result" style="display: none;"></div>
    </div>

    <script>
        // 页面加载时显示环境信息
        $(document).ready(function() {
            $('#current-url').text(window.location.href);
            $('#user-agent').text(navigator.userAgent);
            $('#jquery-version').text($.fn.jquery || '未加载');
            
            var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            $('#device-type').text(isMobile ? '移动设备' : 'PC设备');
        });

        function showResult(content, type = 'info') {
            var resultDiv = $('#test-result');
            resultDiv.removeClass('error success').addClass(type);
            resultDiv.text(content);
            resultDiv.show();
        }

        function clearResults() {
            $('#test-result').hide();
        }

        // 测试基础Ajax请求
        function testBasicAjax() {
            showResult('正在测试基础Ajax请求...');
            
            $.ajax({
                type: "GET",
                url: window.location.href,
                timeout: 10000,
                beforeSend: function(xhr) {
                    console.log('发送基础Ajax请求');
                },
                success: function(response, status, xhr) {
                    showResult('基础Ajax请求成功！\n状态码: ' + xhr.status + '\n响应长度: ' + response.length + ' 字符', 'success');
                },
                error: function(xhr, status, error) {
                    var errorInfo = '基础Ajax请求失败！\n';
                    errorInfo += '状态码: ' + xhr.status + '\n';
                    errorInfo += '状态: ' + status + '\n';
                    errorInfo += '错误: ' + error + '\n';
                    errorInfo += 'readyState: ' + xhr.readyState + '\n';
                    if (xhr.responseText) {
                        errorInfo += '响应内容: ' + xhr.responseText.substring(0, 200);
                    }
                    showResult(errorInfo, 'error');
                }
            });
        }

        // 测试创建订单请求
        function testCreateOrder() {
            showResult('正在测试创建订单请求...');

            // 尝试从页面获取FORM_HASH
            var formHash = '';
            try {
                // 如果页面中有FORM_HASH，尝试获取
                var hashInput = $('input[name="FORM_HASH"]');
                if (hashInput.length > 0) {
                    formHash = hashInput.val();
                }
            } catch(e) {
                console.log('无法获取FORM_HASH:', e);
            }

            $.ajax({
                type: "POST",
                url: "my-create.html",
                data: {
                    package_id: 1,
                    pay_type: 'alipay',
                    FORM_HASH: formHash
                },
                timeout: 30000,
                cache: false,
                dataType: 'json',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                    console.log('发送创建订单请求');
                },
                success: function(response, status, xhr) {
                    var successInfo = '创建订单请求成功！\n';
                    successInfo += '状态码: ' + xhr.status + '\n';
                    successInfo += '响应类型: ' + typeof response + '\n';
                    successInfo += '响应内容: ' + JSON.stringify(response, null, 2);
                    showResult(successInfo, 'success');
                },
                error: function(xhr, status, error) {
                    var errorInfo = '创建订单请求失败！\n';
                    errorInfo += '状态码: ' + xhr.status + '\n';
                    errorInfo += '状态: ' + status + '\n';
                    errorInfo += '错误: ' + error + '\n';
                    errorInfo += 'readyState: ' + xhr.readyState + '\n';
                    errorInfo += '当前URL: ' + window.location.href + '\n';
                    errorInfo += '请求URL: my-create.html\n';
                    
                    if (xhr.responseText) {
                        errorInfo += '响应内容: ' + xhr.responseText.substring(0, 500);
                    }
                    
                    // 特殊错误分析
                    if (xhr.status === 0) {
                        errorInfo += '\n\n状态码0可能的原因：\n';
                        errorInfo += '1. 网络连接问题\n';
                        errorInfo += '2. 跨域请求被阻止\n';
                        errorInfo += '3. 请求URL不正确\n';
                        errorInfo += '4. 服务器未响应\n';
                        errorInfo += '5. 请求被浏览器安全策略阻止';
                    }
                    
                    showResult(errorInfo, 'error');
                }
            });
        }

        // 测试GET请求
        function testWithDifferentMethod() {
            showResult('正在测试GET方式的创建订单请求...');
            
            $.ajax({
                type: "GET",
                url: "my-create.html?package_id=1&pay_type=alipay",
                timeout: 30000,
                cache: false,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                    console.log('发送GET创建订单请求');
                },
                success: function(response, status, xhr) {
                    var successInfo = 'GET请求成功！\n';
                    successInfo += '状态码: ' + xhr.status + '\n';
                    successInfo += '响应类型: ' + typeof response + '\n';
                    successInfo += '响应内容: ' + response.substring(0, 500);
                    showResult(successInfo, 'success');
                },
                error: function(xhr, status, error) {
                    var errorInfo = 'GET请求失败！\n';
                    errorInfo += '状态码: ' + xhr.status + '\n';
                    errorInfo += '状态: ' + status + '\n';
                    errorInfo += '错误: ' + error + '\n';
                    errorInfo += 'readyState: ' + xhr.readyState + '\n';
                    
                    if (xhr.responseText) {
                        errorInfo += '响应内容: ' + xhr.responseText.substring(0, 500);
                    }
                    
                    showResult(errorInfo, 'error');
                }
            });
        }
    </script>
</body>
</html>
