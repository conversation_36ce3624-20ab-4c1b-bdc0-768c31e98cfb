2025-07-17 08:11:44 - === 回调开始 ===
2025-07-17 08:11:44 - 请求方法: GET
2025-07-17 08:11:44 - GET参数: {"pid":"1028","trade_no":"2025071708113877633","out_trade_no":"RB20250717081137944697","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 1100\u5929VIP","money":"399","trade_status":"TRADE_SUCCESS","sign":"1c1618dc43ac3447edb17016731ad54a","sign_type":"MD5"}
2025-07-17 08:11:44 - POST参数: []
2025-07-17 08:11:44 - 订单号: RB20250717081137944697
2025-07-17 08:11:44 - 交易状态: TRADE_SUCCESS
2025-07-17 08:11:44 - 支付类型: alipay
2025-07-17 08:11:44 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:11:44 - 使用LECMS数据库配置
2025-07-17 08:11:44 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:11:44 - 数据库连接成功
2025-07-17 08:11:44 - 配置加载成功，配置项: 12
2025-07-17 08:11:44 - 签名字符串: money=399&name=彩虹易支付 - 1100天VIP&out_trade_no=RB20250717081137944697&pid=1028&trade_no=2025071708113877633&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:11:44 - 接收签名: 1c1618dc43ac3447edb17016731ad54a
2025-07-17 08:11:44 - 计算签名: 1c1618dc43ac3447edb17016731ad54a
2025-07-17 08:11:44 - 签名验证成功
2025-07-17 08:11:44 - 找到订单: ID=86, 用户=1, 状态=0
2025-07-17 08:11:44 - 订单状态更新成功
2025-07-17 08:11:44 - === 用户信息 ===
2025-07-17 08:11:44 - UID: 1, 用户名: admin
2025-07-17 08:11:44 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:11:44 - 当前VIP时间: 0 (无)
2025-07-17 08:11:44 - === VIP开通处理开始 ===
2025-07-17 08:11:44 - 订单VIP天数: 1100 天
2025-07-17 08:11:44 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:11:44 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:11:44 - VIP开通参数: groupid = 12, vip_times = 1847751104, uid = 1
2025-07-17 08:11:44 - VIP开通结果: 成功
2025-07-17 08:11:44 - 订单处理成功: RB20250717081137944697
2025-07-17 08:11:45 - Telegram通知发送成功
2025-07-17 08:17:05 - === 回调开始 ===
2025-07-17 08:17:05 - 请求方法: GET
2025-07-17 08:17:05 - GET参数: {"pid":"1028","trade_no":"2025071708170193156","out_trade_no":"RB20250717081700000953","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"f38c37e739050b06eb92b6f6b0417938","sign_type":"MD5"}
2025-07-17 08:17:05 - POST参数: []
2025-07-17 08:17:05 - 订单号: RB20250717081700000953
2025-07-17 08:17:05 - 交易状态: TRADE_SUCCESS
2025-07-17 08:17:05 - 支付类型: alipay
2025-07-17 08:17:05 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:17:05 - 使用LECMS数据库配置
2025-07-17 08:17:05 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:17:05 - 数据库连接成功
2025-07-17 08:17:05 - 配置加载成功，配置项: 12
2025-07-17 08:17:05 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717081700000953&pid=1028&trade_no=2025071708170193156&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:17:05 - 接收签名: f38c37e739050b06eb92b6f6b0417938
2025-07-17 08:17:05 - 计算签名: f38c37e739050b06eb92b6f6b0417938
2025-07-17 08:17:05 - 签名验证成功
2025-07-17 08:17:05 - 找到订单: ID=87, 用户=1, 状态=0
2025-07-17 08:17:05 - 订单状态更新成功
2025-07-17 08:17:05 - === 用户信息 ===
2025-07-17 08:17:05 - UID: 1, 用户名: admin
2025-07-17 08:17:05 - 当前用户组: 12, 当前金币: 0
2025-07-17 08:17:05 - 当前VIP时间: 1847751104 (2028-07-21 08:11:44)
2025-07-17 08:17:05 - === VIP开通处理开始 ===
2025-07-17 08:17:05 - 订单VIP天数: 30 天
2025-07-17 08:17:05 - 用户当前组: 12, 当前VIP时间: 1847751104
2025-07-17 08:17:05 - VIP续费执行: SQL = UPDATE le_user SET vip_times = ? WHERE uid = ?
2025-07-17 08:17:05 - VIP续费参数: new_vip_times = 1850343104, uid = 1
2025-07-17 08:17:05 - VIP续费结果: 成功
2025-07-17 08:17:05 - 订单处理成功: RB20250717081700000953
2025-07-17 08:17:06 - Telegram通知发送成功
2025-07-17 08:23:26 - === 回调开始 ===
2025-07-17 08:23:26 - 请求方法: GET
2025-07-17 08:23:26 - GET参数: {"pid":"1028","trade_no":"2025071708231726628","out_trade_no":"RB20250717082316581141","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 10\u91d1\u5e01","money":"0.02","trade_status":"TRADE_SUCCESS","sign":"deb36333194495a098a9dadd44120b31","sign_type":"MD5"}
2025-07-17 08:23:26 - POST参数: []
2025-07-17 08:23:26 - 订单号: RB20250717082316581141
2025-07-17 08:23:26 - 交易状态: TRADE_SUCCESS
2025-07-17 08:23:26 - 支付类型: alipay
2025-07-17 08:23:26 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:23:26 - 使用LECMS数据库配置
2025-07-17 08:23:26 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:23:26 - 数据库连接成功
2025-07-17 08:23:26 - 配置加载成功，配置项: 12
2025-07-17 08:23:26 - 签名字符串: money=0.02&name=彩虹易支付 - 10金币&out_trade_no=RB20250717082316581141&pid=1028&trade_no=2025071708231726628&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:23:26 - 接收签名: deb36333194495a098a9dadd44120b31
2025-07-17 08:23:26 - 计算签名: deb36333194495a098a9dadd44120b31
2025-07-17 08:23:26 - 签名验证成功
2025-07-17 08:23:26 - 找到订单: ID=88, 用户=1, 状态=0
2025-07-17 08:23:26 - 订单状态更新成功
2025-07-17 08:23:26 - === 用户信息 ===
2025-07-17 08:23:26 - UID: 1, 用户名: admin
2025-07-17 08:23:26 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:23:26 - 当前VIP时间: 0 (无)
2025-07-17 08:23:26 - 金币充值: 0 + 10 = 10
2025-07-17 08:23:26 - 订单处理成功: RB20250717082316581141
2025-07-17 08:23:27 - Telegram通知发送成功
2025-07-17 08:23:52 - === 回调开始 ===
2025-07-17 08:23:52 - 请求方法: GET
2025-07-17 08:23:52 - GET参数: {"pid":"1028","trade_no":"2025071708234413727","out_trade_no":"RB20250717082343702763","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"e2971f0d1a08dc7b4a6148eb8fd52984","sign_type":"MD5"}
2025-07-17 08:23:52 - POST参数: []
2025-07-17 08:23:52 - 订单号: RB20250717082343702763
2025-07-17 08:23:52 - 交易状态: TRADE_SUCCESS
2025-07-17 08:23:52 - 支付类型: alipay
2025-07-17 08:23:52 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:23:52 - 使用LECMS数据库配置
2025-07-17 08:23:52 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:23:52 - 数据库连接成功
2025-07-17 08:23:52 - 配置加载成功，配置项: 12
2025-07-17 08:23:52 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717082343702763&pid=1028&trade_no=2025071708234413727&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:23:52 - 接收签名: e2971f0d1a08dc7b4a6148eb8fd52984
2025-07-17 08:23:52 - 计算签名: e2971f0d1a08dc7b4a6148eb8fd52984
2025-07-17 08:23:52 - 签名验证成功
2025-07-17 08:23:52 - 找到订单: ID=89, 用户=1, 状态=0
2025-07-17 08:23:52 - 订单状态更新成功
2025-07-17 08:23:52 - === 用户信息 ===
2025-07-17 08:23:52 - UID: 1, 用户名: admin
2025-07-17 08:23:52 - 当前用户组: 1, 当前金币: 10
2025-07-17 08:23:52 - 当前VIP时间: 0 (无)
2025-07-17 08:23:52 - === VIP开通处理开始 ===
2025-07-17 08:23:52 - 订单VIP天数: 30 天
2025-07-17 08:23:52 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:23:52 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:23:52 - VIP开通参数: groupid = 12, vip_times = 1755303832, uid = 1
2025-07-17 08:23:52 - VIP开通结果: 成功
2025-07-17 08:23:52 - 订单处理成功: RB20250717082343702763
2025-07-17 08:23:52 - Telegram通知发送成功
2025-07-17 08:24:31 - === 回调开始 ===
2025-07-17 08:24:31 - 请求方法: GET
2025-07-17 08:24:31 - GET参数: {"pid":"1028","trade_no":"2025071708242354228","out_trade_no":"RB20250717082422936998","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 1100\u5929VIP","money":"399","trade_status":"TRADE_SUCCESS","sign":"9a9fcbc8738368e8c1e549bbdc3a3284","sign_type":"MD5"}
2025-07-17 08:24:31 - POST参数: []
2025-07-17 08:24:31 - 订单号: RB20250717082422936998
2025-07-17 08:24:31 - 交易状态: TRADE_SUCCESS
2025-07-17 08:24:31 - 支付类型: alipay
2025-07-17 08:24:31 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:24:31 - 使用LECMS数据库配置
2025-07-17 08:24:31 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:24:31 - 数据库连接成功
2025-07-17 08:24:31 - 配置加载成功，配置项: 12
2025-07-17 08:24:31 - 签名字符串: money=399&name=彩虹易支付 - 1100天VIP&out_trade_no=RB20250717082422936998&pid=1028&trade_no=2025071708242354228&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:24:31 - 接收签名: 9a9fcbc8738368e8c1e549bbdc3a3284
2025-07-17 08:24:31 - 计算签名: 9a9fcbc8738368e8c1e549bbdc3a3284
2025-07-17 08:24:31 - 签名验证成功
2025-07-17 08:24:31 - 找到订单: ID=90, 用户=1, 状态=0
2025-07-17 08:24:31 - 订单状态更新成功
2025-07-17 08:24:31 - === 用户信息 ===
2025-07-17 08:24:31 - UID: 1, 用户名: admin
2025-07-17 08:24:31 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:24:31 - 当前VIP时间: 0 (无)
2025-07-17 08:24:31 - === VIP开通处理开始 ===
2025-07-17 08:24:31 - 订单VIP天数: 1100 天
2025-07-17 08:24:31 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:24:31 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:24:31 - VIP开通参数: groupid = 12, vip_times = 1847751871, uid = 1
2025-07-17 08:24:31 - VIP开通结果: 成功
2025-07-17 08:24:31 - 订单处理成功: RB20250717082422936998
2025-07-17 08:24:31 - Telegram通知发送成功
2025-07-17 08:26:12 - === 回调开始 ===
2025-07-17 08:26:12 - 请求方法: GET
2025-07-17 08:26:12 - GET参数: {"pid":"1028","trade_no":"2025071708260649335","out_trade_no":"RB20250717082605981787","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"7415676c4c9056a74d6503e3cc60e9f0","sign_type":"MD5"}
2025-07-17 08:26:12 - POST参数: []
2025-07-17 08:26:12 - 订单号: RB20250717082605981787
2025-07-17 08:26:12 - 交易状态: TRADE_SUCCESS
2025-07-17 08:26:12 - 支付类型: alipay
2025-07-17 08:26:12 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:26:12 - 使用LECMS数据库配置
2025-07-17 08:26:12 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:26:12 - 数据库连接成功
2025-07-17 08:26:12 - 配置加载成功，配置项: 12
2025-07-17 08:26:12 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717082605981787&pid=1028&trade_no=2025071708260649335&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:26:12 - 接收签名: 7415676c4c9056a74d6503e3cc60e9f0
2025-07-17 08:26:12 - 计算签名: 7415676c4c9056a74d6503e3cc60e9f0
2025-07-17 08:26:12 - 签名验证成功
2025-07-17 08:26:12 - 找到订单: ID=91, 用户=1, 状态=0
2025-07-17 08:26:12 - 订单状态更新成功
2025-07-17 08:26:12 - === 用户信息 ===
2025-07-17 08:26:12 - UID: 1, 用户名: admin
2025-07-17 08:26:12 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:26:12 - 当前VIP时间: 0 (无)
2025-07-17 08:26:12 - === VIP开通处理开始 ===
2025-07-17 08:26:12 - 订单VIP天数: 30 天
2025-07-17 08:26:12 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:26:12 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:26:12 - VIP开通参数: groupid = 12, vip_times = 1755303972, uid = 1
2025-07-17 08:26:12 - VIP开通结果: 成功
2025-07-17 08:26:12 - 订单处理成功: RB20250717082605981787
2025-07-17 08:26:13 - Telegram通知发送成功
2025-07-17 08:31:51 - === 回调开始 ===
2025-07-17 08:31:51 - 请求方法: GET
2025-07-17 08:31:51 - GET参数: {"pid":"1028","trade_no":"2025071708314673884","out_trade_no":"RB20250717083145239760","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"566c7b2279d04f7061b35ee6c5cece21","sign_type":"MD5"}
2025-07-17 08:31:51 - POST参数: []
2025-07-17 08:31:51 - 订单号: RB20250717083145239760
2025-07-17 08:31:51 - 交易状态: TRADE_SUCCESS
2025-07-17 08:31:51 - 支付类型: alipay
2025-07-17 08:31:51 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:31:51 - 使用LECMS数据库配置
2025-07-17 08:31:51 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:31:51 - 数据库连接成功
2025-07-17 08:31:51 - 配置加载成功，配置项: 12
2025-07-17 08:31:51 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717083145239760&pid=1028&trade_no=2025071708314673884&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:31:51 - 接收签名: 566c7b2279d04f7061b35ee6c5cece21
2025-07-17 08:31:51 - 计算签名: 566c7b2279d04f7061b35ee6c5cece21
2025-07-17 08:31:51 - 签名验证成功
2025-07-17 08:31:51 - 找到订单: ID=92, 用户=1, 状态=0
2025-07-17 08:31:51 - 订单状态更新成功
2025-07-17 08:31:51 - === 用户信息 ===
2025-07-17 08:31:51 - UID: 1, 用户名: admin
2025-07-17 08:31:51 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:31:51 - 当前VIP时间: 0 (无)
2025-07-17 08:31:51 - === VIP开通处理开始 ===
2025-07-17 08:31:51 - 订单VIP天数: 30 天
2025-07-17 08:31:51 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:31:51 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:31:51 - VIP开通参数: groupid = 12, vip_times = 1755304311, uid = 1
2025-07-17 08:31:51 - VIP开通结果: 成功
2025-07-17 08:31:51 - 订单处理成功: RB20250717083145239760
2025-07-17 08:31:52 - Telegram通知发送成功
2025-07-17 08:36:07 - === 回调开始 ===
2025-07-17 08:36:07 - 请求方法: GET
2025-07-17 08:36:07 - GET参数: {"pid":"1028","trade_no":"2025071708360377778","out_trade_no":"RB20250717083602266488","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"532ed6972d4cfa8a67659906ac6a154f","sign_type":"MD5"}
2025-07-17 08:36:07 - POST参数: []
2025-07-17 08:36:07 - 订单号: RB20250717083602266488
2025-07-17 08:36:07 - 交易状态: TRADE_SUCCESS
2025-07-17 08:36:07 - 支付类型: alipay
2025-07-17 08:36:07 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:36:07 - 使用LECMS数据库配置
2025-07-17 08:36:07 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:36:07 - 数据库连接成功
2025-07-17 08:36:07 - 配置加载成功，配置项: 12
2025-07-17 08:36:07 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717083602266488&pid=1028&trade_no=2025071708360377778&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:36:07 - 接收签名: 532ed6972d4cfa8a67659906ac6a154f
2025-07-17 08:36:07 - 计算签名: 532ed6972d4cfa8a67659906ac6a154f
2025-07-17 08:36:07 - 签名验证成功
2025-07-17 08:36:07 - 找到订单: ID=93, 用户=1, 状态=0
2025-07-17 08:36:07 - 订单状态更新成功
2025-07-17 08:36:07 - === 用户信息 ===
2025-07-17 08:36:07 - UID: 1, 用户名: admin
2025-07-17 08:36:07 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:36:07 - 当前VIP时间: 0 (无)
2025-07-17 08:36:07 - === VIP开通处理开始 ===
2025-07-17 08:36:07 - 订单VIP天数: 30 天
2025-07-17 08:36:07 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:36:07 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:36:07 - VIP开通参数: groupid = 12, vip_times = 1755304567, uid = 1
2025-07-17 08:36:07 - VIP开通结果: 成功
2025-07-17 08:36:07 - 订单处理成功: RB20250717083602266488
2025-07-17 08:36:08 - Telegram通知发送成功
2025-07-17 08:38:08 - === 回调开始 ===
2025-07-17 08:38:08 - 请求方法: GET
2025-07-17 08:38:08 - GET参数: {"pid":"1028","trade_no":"2025071708380224353","out_trade_no":"RB20250717083801816313","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"14ee29defaaaf6e06b8e0890b46f0c0a","sign_type":"MD5"}
2025-07-17 08:38:08 - POST参数: []
2025-07-17 08:38:08 - 订单号: RB20250717083801816313
2025-07-17 08:38:08 - 交易状态: TRADE_SUCCESS
2025-07-17 08:38:08 - 支付类型: alipay
2025-07-17 08:38:08 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:38:08 - 使用LECMS数据库配置
2025-07-17 08:38:08 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:38:08 - 数据库连接成功
2025-07-17 08:38:08 - 配置加载成功，配置项: 12
2025-07-17 08:38:08 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717083801816313&pid=1028&trade_no=2025071708380224353&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:38:08 - 接收签名: 14ee29defaaaf6e06b8e0890b46f0c0a
2025-07-17 08:38:08 - 计算签名: 14ee29defaaaf6e06b8e0890b46f0c0a
2025-07-17 08:38:08 - 签名验证成功
2025-07-17 08:38:08 - 找到订单: ID=94, 用户=1, 状态=0
2025-07-17 08:38:08 - 订单状态更新成功
2025-07-17 08:38:08 - === 用户信息 ===
2025-07-17 08:38:08 - UID: 1, 用户名: admin
2025-07-17 08:38:08 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:38:08 - 当前VIP时间: 0 (无)
2025-07-17 08:38:08 - === VIP开通处理开始 ===
2025-07-17 08:38:08 - 订单VIP天数: 30 天
2025-07-17 08:38:08 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:38:08 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:38:08 - VIP开通参数: groupid = 12, vip_times = 1755304688, uid = 1
2025-07-17 08:38:08 - VIP开通结果: 成功
2025-07-17 08:38:08 - 订单处理成功: RB20250717083801816313
2025-07-17 08:38:09 - Telegram通知发送成功
2025-07-17 08:41:38 - === 回调开始 ===
2025-07-17 08:41:38 - 请求方法: GET
2025-07-17 08:41:38 - GET参数: {"pid":"1028","trade_no":"2025071708413289396","out_trade_no":"RB20250717084131795980","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"3447e4fb1c1234f339e5b6cd8fdf5a2e","sign_type":"MD5"}
2025-07-17 08:41:38 - POST参数: []
2025-07-17 08:41:38 - 订单号: RB20250717084131795980
2025-07-17 08:41:38 - 交易状态: TRADE_SUCCESS
2025-07-17 08:41:38 - 支付类型: alipay
2025-07-17 08:41:38 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:41:38 - 使用LECMS数据库配置
2025-07-17 08:41:38 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:41:38 - 数据库连接成功
2025-07-17 08:41:38 - 配置加载成功，配置项: 12
2025-07-17 08:41:38 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717084131795980&pid=1028&trade_no=2025071708413289396&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:41:38 - 接收签名: 3447e4fb1c1234f339e5b6cd8fdf5a2e
2025-07-17 08:41:38 - 计算签名: 3447e4fb1c1234f339e5b6cd8fdf5a2e
2025-07-17 08:41:38 - 签名验证成功
2025-07-17 08:41:38 - 找到订单: ID=95, 用户=1, 状态=0
2025-07-17 08:41:38 - 订单状态更新成功
2025-07-17 08:41:38 - === 用户信息 ===
2025-07-17 08:41:38 - UID: 1, 用户名: admin
2025-07-17 08:41:38 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:41:38 - 当前VIP时间: 0 (无)
2025-07-17 08:41:38 - === VIP开通处理开始 ===
2025-07-17 08:41:38 - 订单VIP天数: 30 天
2025-07-17 08:41:38 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:41:38 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:41:38 - VIP开通参数: groupid = 12, vip_times = 1755304898, uid = 1
2025-07-17 08:41:38 - VIP开通结果: 成功
2025-07-17 08:41:38 - 订单处理成功: RB20250717084131795980
2025-07-17 08:41:39 - Telegram通知发送成功
2025-07-17 08:48:10 - === 回调开始 ===
2025-07-17 08:48:10 - 请求方法: GET
2025-07-17 08:48:10 - GET参数: {"pid":"1028","trade_no":"2025071708480385517","out_trade_no":"RB20250717084802229674","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"06c269ed5cd25a71f06f238adffa4673","sign_type":"MD5"}
2025-07-17 08:48:10 - POST参数: []
2025-07-17 08:48:10 - 订单号: RB20250717084802229674
2025-07-17 08:48:10 - 交易状态: TRADE_SUCCESS
2025-07-17 08:48:10 - 支付类型: alipay
2025-07-17 08:48:10 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:48:10 - 使用LECMS数据库配置
2025-07-17 08:48:10 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:48:10 - 数据库连接成功
2025-07-17 08:48:10 - 配置加载成功，配置项: 12
2025-07-17 08:48:10 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717084802229674&pid=1028&trade_no=2025071708480385517&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:48:10 - 接收签名: 06c269ed5cd25a71f06f238adffa4673
2025-07-17 08:48:10 - 计算签名: 06c269ed5cd25a71f06f238adffa4673
2025-07-17 08:48:10 - 签名验证成功
2025-07-17 08:48:10 - 找到订单: ID=96, 用户=1, 状态=0
2025-07-17 08:48:10 - 订单状态更新成功
2025-07-17 08:48:10 - === 用户信息 ===
2025-07-17 08:48:10 - UID: 1, 用户名: admin
2025-07-17 08:48:10 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:48:10 - 当前VIP时间: 0 (无)
2025-07-17 08:48:10 - === VIP开通处理开始 ===
2025-07-17 08:48:10 - 订单VIP天数: 30 天
2025-07-17 08:48:10 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:48:10 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:48:10 - VIP开通参数: groupid = 12, vip_times = 1755305290, uid = 1
2025-07-17 08:48:10 - VIP开通结果: 成功
2025-07-17 08:48:10 - 订单处理成功: RB20250717084802229674
2025-07-17 08:48:10 - Telegram通知发送成功
2025-07-17 08:49:10 - === 回调开始 ===
2025-07-17 08:49:10 - 请求方法: GET
2025-07-17 08:49:10 - GET参数: {"pid":"1028","trade_no":"2025071708490588672","out_trade_no":"RB20250717084905019707","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 30\u5929VIP","money":"39","trade_status":"TRADE_SUCCESS","sign":"3f5bf0765cdf98bfd5387520f22f48a4","sign_type":"MD5"}
2025-07-17 08:49:10 - POST参数: []
2025-07-17 08:49:10 - 订单号: RB20250717084905019707
2025-07-17 08:49:10 - 交易状态: TRADE_SUCCESS
2025-07-17 08:49:10 - 支付类型: alipay
2025-07-17 08:49:10 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:49:10 - 使用LECMS数据库配置
2025-07-17 08:49:10 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:49:10 - 数据库连接成功
2025-07-17 08:49:10 - 配置加载成功，配置项: 12
2025-07-17 08:49:10 - 签名字符串: money=39&name=彩虹易支付 - 30天VIP&out_trade_no=RB20250717084905019707&pid=1028&trade_no=2025071708490588672&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:49:10 - 接收签名: 3f5bf0765cdf98bfd5387520f22f48a4
2025-07-17 08:49:10 - 计算签名: 3f5bf0765cdf98bfd5387520f22f48a4
2025-07-17 08:49:10 - 签名验证成功
2025-07-17 08:49:10 - 找到订单: ID=97, 用户=1, 状态=0
2025-07-17 08:49:10 - 订单状态更新成功
2025-07-17 08:49:10 - === 用户信息 ===
2025-07-17 08:49:10 - UID: 1, 用户名: admin
2025-07-17 08:49:10 - 当前用户组: 1, 当前金币: 0
2025-07-17 08:49:10 - 当前VIP时间: 0 (无)
2025-07-17 08:49:10 - === VIP开通处理开始 ===
2025-07-17 08:49:10 - 订单VIP天数: 30 天
2025-07-17 08:49:10 - 用户当前组: 1, 当前VIP时间: 0
2025-07-17 08:49:10 - VIP开通执行: SQL = UPDATE le_user SET groupid = 12, vip_times = ? WHERE uid = ?
2025-07-17 08:49:10 - VIP开通参数: groupid = 12, vip_times = 1755305350, uid = 1
2025-07-17 08:49:10 - VIP开通结果: 成功
2025-07-17 08:49:10 - 订单处理成功: RB20250717084905019707
2025-07-17 08:49:11 - Telegram通知发送成功
2025-07-17 08:49:33 - === 回调开始 ===
2025-07-17 08:49:33 - 请求方法: GET
2025-07-17 08:49:33 - GET参数: {"pid":"1028","trade_no":"2025071708492813077","out_trade_no":"RB20250717084928241493","type":"alipay","name":"\u5f69\u8679\u6613\u652f\u4ed8 - 70\u91d1\u5e01","money":"60","trade_status":"TRADE_SUCCESS","sign":"e5810c12fba13b762d1d6f0e963429ee","sign_type":"MD5"}
2025-07-17 08:49:33 - POST参数: []
2025-07-17 08:49:33 - 订单号: RB20250717084928241493
2025-07-17 08:49:33 - 交易状态: TRADE_SUCCESS
2025-07-17 08:49:33 - 支付类型: alipay
2025-07-17 08:49:33 - 找到配置文件: /www/wwwroot/lecms3.0.3-master/lecms/config/config.inc.php
2025-07-17 08:49:33 - 使用LECMS数据库配置
2025-07-17 08:49:33 - 数据库: 127.0.0.1:3306/ksjaiwa
2025-07-17 08:49:33 - 数据库连接成功
2025-07-17 08:49:33 - 配置加载成功，配置项: 12
2025-07-17 08:49:33 - 签名字符串: money=60&name=彩虹易支付 - 70金币&out_trade_no=RB20250717084928241493&pid=1028&trade_no=2025071708492813077&trade_status=TRADE_SUCCESS&type=alipay
2025-07-17 08:49:33 - 接收签名: e5810c12fba13b762d1d6f0e963429ee
2025-07-17 08:49:33 - 计算签名: e5810c12fba13b762d1d6f0e963429ee
2025-07-17 08:49:33 - 签名验证成功
2025-07-17 08:49:33 - 找到订单: ID=98, 用户=1, 状态=0
2025-07-17 08:49:34 - 订单状态更新成功
2025-07-17 08:49:34 - === 用户信息 ===
2025-07-17 08:49:34 - UID: 1, 用户名: admin
2025-07-17 08:49:34 - 当前用户组: 12, 当前金币: 0
2025-07-17 08:49:34 - 当前VIP时间: 1755305350 (2025-08-16 08:49:10)
2025-07-17 08:49:34 - 金币充值: 0 + 70 = 70
2025-07-17 08:49:34 - 订单处理成功: RB20250717084928241493
2025-07-17 08:49:34 - Telegram通知发送成功
