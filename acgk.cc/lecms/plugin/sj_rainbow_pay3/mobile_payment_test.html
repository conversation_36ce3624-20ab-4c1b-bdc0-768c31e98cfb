<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端支付测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .device-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        .test-btn:hover {
            background: #5a6fd8;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        .mobile-payment-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .mobile-payment-loading.show {
            display: flex;
        }
        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 300px;
            margin: 20px;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading-text {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .loading-tips {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>移动端支付功能测试</h2>
        
        <div class="device-info">
            <h4>设备检测结果：</h4>
            <p id="device-type">检测中...</p>
            <p id="user-agent">User Agent: <span id="ua-text"></span></p>
        </div>

        <button class="test-btn" onclick="testMobilePayment()">测试移动端支付流程</button>
        <button class="test-btn" onclick="testPCPayment()">测试PC端支付流程</button>
        <button class="test-btn" onclick="showLoadingDemo()">演示加载界面</button>

        <div id="test-result" class="result" style="display: none;">
            <h4>测试结果：</h4>
            <div id="result-content"></div>
        </div>
    </div>

    <!-- 移动端支付加载遮罩 -->
    <div id="mobile-payment-loading" class="mobile-payment-loading">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在跳转支付页面</div>
            <div class="loading-tips">
                即将为您打开支付页面<br>
                请在支付页面完成付款<br>
                支付完成后会自动返回
            </div>
        </div>
    </div>

    <script>
        // 设备检测函数
        function detectDevice() {
            const userAgent = navigator.userAgent;
            document.getElementById('ua-text').textContent = userAgent;

            let deviceType = 'PC';
            let deviceDetails = [];

            if (/Android/i.test(userAgent)) {
                deviceType = 'Android';
                deviceDetails.push('Android设备');
            }
            if (/iPhone|iPad|iPod/i.test(userAgent)) {
                deviceType = 'iOS';
                deviceDetails.push('iOS设备');
            }
            if (/MicroMessenger/i.test(userAgent)) {
                deviceDetails.push('微信浏览器');
            }
            if (/AlipayClient/i.test(userAgent)) {
                deviceDetails.push('支付宝客户端');
            }
            if (/Mobile/i.test(userAgent)) {
                deviceDetails.push('移动浏览器');
            }

            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            
            document.getElementById('device-type').innerHTML = 
                `设备类型: ${deviceType}<br>` +
                `是否移动设备: ${isMobile ? '是' : '否'}<br>` +
                `特征: ${deviceDetails.length > 0 ? deviceDetails.join(', ') : '标准浏览器'}`;
        }

        // 显示加载界面演示
        function showLoadingDemo() {
            document.getElementById('mobile-payment-loading').classList.add('show');
            
            setTimeout(function() {
                document.getElementById('mobile-payment-loading').classList.remove('show');
                showResult('加载界面演示完成！');
            }, 3000);
        }

        // 测试移动端支付
        function testMobilePayment() {
            showResult('移动端支付测试：将在3秒后跳转到百度（模拟支付页面）');
            
            document.getElementById('mobile-payment-loading').classList.add('show');
            
            setTimeout(function() {
                // 模拟跳转到支付页面
                window.location.href = 'https://www.baidu.com';
            }, 3000);
        }

        // 测试PC端支付
        function testPCPayment() {
            const payWindow = window.open('https://www.baidu.com', '_blank');
            
            if (payWindow) {
                showResult('PC端支付测试：新窗口已打开（模拟支付页面）');
            } else {
                showResult('PC端支付测试：浏览器阻止了弹窗，这在移动端很常见');
            }
        }

        // 显示测试结果
        function showResult(message) {
            document.getElementById('result-content').innerHTML = message;
            document.getElementById('test-result').style.display = 'block';
        }

        // 页面加载时检测设备
        window.onload = function() {
            detectDevice();
        };
    </script>
</body>
</html>
