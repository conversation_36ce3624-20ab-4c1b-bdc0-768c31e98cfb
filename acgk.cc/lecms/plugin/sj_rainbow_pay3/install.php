<?php
defined('ROOT_PATH') || exit;
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];

// 创建配置表
$sql1 = "CREATE TABLE IF NOT EXISTS ".$tableprefix."sj_rainbow_pay_config (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL,
  `value` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";

// 创建订单表
$sql2 = "CREATE TABLE IF NOT EXISTS ".$tableprefix."sj_rainbow_pay_orders (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) NOT NULL COMMENT '1金币充值2VIP开通',
  `package_id` int(11) DEFAULT NULL COMMENT '套餐ID',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `value` int(11) NOT NULL COMMENT '奖励数值',
  `pay_type` varchar(20) NOT NULL COMMENT '支付方式',
  `status` tinyint(1) DEFAULT 0 COMMENT '0未支付1已支付2已取消',
  `trade_no` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `create_time` int(11) NOT NULL,
  `pay_time` int(11) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `uid` (`uid`),
  KEY `status` (`status`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";

// 创建套餐表
$sql3 = "CREATE TABLE IF NOT EXISTS ".$tableprefix."sj_rainbow_pay_packages (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '套餐名称',
  `type` tinyint(1) NOT NULL COMMENT '1金币充值2VIP开通',
  `amount` decimal(10,2) NOT NULL COMMENT '价格',
  `value` int(11) NOT NULL COMMENT '奖励数值',
  `desc` varchar(255) DEFAULT '' COMMENT '套餐描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态1启用0禁用',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";

$this->db->query($sql1);
$this->db->query($sql2);
$this->db->query($sql3);

// 插入默认配置
$default_configs = array(
    array('key' => 'alipay_api_url', 'value' => ''),
    array('key' => 'alipay_pid', 'value' => ''),
    array('key' => 'alipay_key', 'value' => ''),
    array('key' => 'wechat_api_url', 'value' => ''),
    array('key' => 'wechat_pid', 'value' => ''),
    array('key' => 'wechat_key', 'value' => ''),
    array('key' => 'site_name', 'value' => '彩虹易支付'),
);

foreach($default_configs as $config) {
    $sql = "INSERT IGNORE INTO ".$tableprefix."sj_rainbow_pay_config (`key`, `value`) VALUES ('".$config['key']."', '".$config['value']."')";
    $this->db->query($sql);
}

// 插入默认套餐
$default_packages = array(
    // 金币套餐 - 6个
    array('name' => '10金币', 'type' => 1, 'amount' => '10.00', 'value' => 10, 'desc' => '适合轻度用户', 'sort' => 1),
    array('name' => '20金币', 'type' => 1, 'amount' => '20.00', 'value' => 20, 'desc' => '热门推荐', 'sort' => 2),
    array('name' => '32金币', 'type' => 1, 'amount' => '40.00', 'value' => 32, 'desc' => '性价比之选', 'sort' => 3),
    array('name' => '44金币', 'type' => 1, 'amount' => '40.00', 'value' => 44, 'desc' => '重度黄油用户', 'sort' => 4),
    array('name' => '56金币', 'type' => 1, 'amount' => '50.00', 'value' => 56, 'desc' => '超值大礼包', 'sort' => 5),
    array('name' => '70金币', 'type' => 1, 'amount' => '60.00', 'value' => 70, 'desc' => '限时特惠活动', 'sort' => 6),

    // VIP套餐 - 6个
    array('name' => '月度VIP', 'type' => 2, 'amount' => '39.00', 'value' => 30, 'desc' => '体验特权', 'sort' => 7),
    array('name' => '季度VIP', 'type' => 2, 'amount' => '89.00', 'value' => 90, 'desc' => '享受特权', 'sort' => 8),
    array('name' => '半年VIP', 'type' => 2, 'amount' => '129.00', 'value' => 180, 'desc' => '半年畅玩', 'sort' => 9),
    array('name' => '年付VIP', 'type' => 2, 'amount' => '199.00', 'value' => 365, 'desc' => '年付畅享', 'sort' => 10),
    array('name' => '3年VIP', 'type' => 2, 'amount' => '399.00', 'value' => 1100, 'desc' => '超值3年会员', 'sort' => 11),
    array('name' => '终身VIP', 'type' => 2, 'amount' => '599.00', 'value' => 36500, 'desc' => '一次购买终身享受', 'sort' => 12),
);

foreach($default_packages as $package) {
    $sql = "INSERT INTO ".$tableprefix."sj_rainbow_pay_packages (`name`, `type`, `amount`, `value`, `desc`, `sort`, `create_time`) VALUES ('".$package['name']."', ".$package['type'].", ".$package['amount'].", ".$package['value'].", '".$package['desc']."', ".$package['sort'].", ".time().")";
    $this->db->query($sql);
}

return true;
