{inc:user/header.htm}
<!-- 使用插件自带的QRCode库 -->
<script src="{$_cfg['weburl']}lecms/plugin/sj_rainbow_pay/static/qrcode.min.js"></script>
<script>
// 检查QRCode库是否加载成功
document.addEventListener('DOMContentLoaded', function() {
  if (typeof QRCode === 'undefined') {
    console.warn('QRCode库未加载，将使用备用方案');
  } else {
    console.log('QRCode库加载成功');
  }
});
</script>
<style>
  .recharge-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 0;
    margin-bottom: 30px;
    overflow: hidden;
  }
  .recharge-header {
    background: rgba(255,255,255,0.1);
    padding: 30px;
    text-align: center;
    color: white;
  }
  .recharge-header h2 {
    margin: 0;
    font-weight: 300;
    font-size: 28px;
  }
  .recharge-content {
    background: white;
    padding: 40px;
  }
  .section-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
  }
  .section-title::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2);
  }
  .gold-section .section-title {
    color: #f39c12;
  }
  .gold-section .section-title::before {
    background: linear-gradient(45deg, #f39c12, #e67e22);
  }
  .vip-section .section-title {
    color: #e74c3c;
  }
  .vip-section .section-title::before {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
  }
  .pay-card {
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    padding: 10px 8px;
    margin-bottom: 10px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    height: 130px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .pay-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
  }
  .pay-card:hover::before {
    left: 100%;
  }
  .pay-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
  }
  .pay-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25);
  }
  .gold-card {
    border-color: #f39c12;
  }
  .gold-card.selected {
    border-color: #f39c12;
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    box-shadow: 0 10px 25px rgba(243, 156, 18, 0.3);
  }
  .vip-card {
    border-color: #e74c3c;
  }
  .vip-card.selected {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
  }
  .pay-card h4 {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
    line-height: 1.2;
  }
  .gold-card h4 {
    color: #f39c12;
  }
  .vip-card h4 {
    color: #e74c3c;
  }
  .pay-card .price {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
  }
  .gold-card .price {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .vip-card .price {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .pay-card .desc {
    color: #666;
    margin-bottom: 10px;
    font-size: 12px;
    line-height: 1.3;
    flex-grow: 1;
  }
  .pay-card .reward {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 6px 12px;
    font-size: 11px;
    color: #666;
    display: inline-block;
    margin-top: auto;
  }
  .gold-card .reward {
    background: #fff8e1;
    color: #f57c00;
  }
  .vip-card .reward {
    background: #ffebee;
    color: #d32f2f;
  }

  /* Grid布局下的卡片样式 */
  .packages-grid .pay-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  /* 套餐网格布局优化 */
  .packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }

  /* 确保6个套餐的完美布局 */
  @media (min-width: 1200px) {
    .packages-grid {
      grid-template-columns: repeat(3, 1fr);
      max-width: 1000px;
      margin: 20px auto 0;
    }
  }

  @media (min-width: 768px) and (max-width: 1199px) {
    .packages-grid {
      grid-template-columns: repeat(2, 1fr);
      max-width: 700px;
      margin: 20px auto 0;
    }
  }

  @media (max-width: 767px) {
    .packages-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-top: 15px;
    }
  }

  @media (max-width: 480px) {
    .packages-grid {
      gap: 10px;
      grid-template-columns: repeat(2, 1fr);
    }
  }
  .pay-method-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 15px;
    margin: 20px 0;
  }
  .pay-method-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
  }
  .pay-options {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
  }
  .pay-option {
    flex: 1;
    max-width: 150px;
    min-width: 100px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
  }
  .pay-option:hover {
    border-color: #667eea;
    transform: translateY(-2px);
  }
  .pay-option.selected {
    border-color: #667eea;
    background: #f8f9ff;
  }
  .pay-option img {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }
  .pay-option .name {
    font-weight: bold;
    color: #333;
  }
  .pay-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 15px 50px;
    border-radius: 30px;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.3s;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
  }
  .pay-btn:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a5acd);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
  }
  .pay-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  /* 支付提示区域样式 */
  .payment-notice {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: none; /* 默认隐藏 */
  }

  .payment-notice.show {
    display: block;
    animation: slideDown 0.5s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .payment-notice .notice-icon {
    font-size: 24px;
    margin-right: 8px;
  }

  .payment-notice .refresh-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    margin: 10px 5px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
  }

  .payment-notice .refresh-btn:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
  }

  .payment-notice .refresh-btn.primary {
    background: rgba(255,255,255,0.9);
    color: #667eea;
  }

  .payment-notice .refresh-btn.primary:hover {
    background: white;
    color: #667eea;
  }

  /* 支付提示条样式 */
  .payment-notice {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    text-align: center;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }

  .payment-notice .notice-icon {
    font-size: 20px;
    margin-right: 8px;
  }

  .payment-notice .refresh-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    margin-left: 15px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
  }

  .payment-notice .refresh-btn:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
  }

  /* 移动端支付加载样式 */
  .mobile-payment-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .mobile-payment-loading.show {
    display: flex;
  }

  .loading-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    max-width: 300px;
    margin: 20px;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-text {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }

  .loading-tips {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
  }
  .account-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
  }
  .account-info .row {
    align-items: center;
  }
  .account-info strong {
    font-size: 16px;
  }
  .account-info .value {
    font-size: 20px;
    font-weight: bold;
    margin-left: 10px;
  }
  .tips-section {
    background: #e8f5e8;
    border-left: 4px solid #4caf50;
    border-radius: 8px;
    padding: 20px;
    margin-top: 30px;
  }
  .tips-section h5 {
    color: #2e7d32;
    margin-bottom: 15px;
  }
  .tips-section p {
    margin-bottom: 8px;
    color: #388e3c;
  }
  .recharge-type-section {
    margin-bottom: 40px;
  }
  .type-selection-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: stretch;
    flex-wrap: wrap;
  }
  .type-card-wrapper {
    flex: 1;
    max-width: 400px;
    min-width: 250px;
  }
  .recharge-type-card {
    border: 3px solid #f0f0f0;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    background: white;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .recharge-type-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
  }
  .recharge-type-card:hover::before {
    left: 100%;
  }
  .recharge-type-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  }
  .recharge-type-card[data-type="gold"]:hover {
    border-color: #f39c12;
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  }
  .recharge-type-card[data-type="vip"]:hover {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  }
  .recharge-type-card .type-icon {
    font-size: 60px;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .recharge-type-card[data-type="gold"] .type-icon {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .recharge-type-card[data-type="vip"] .type-icon {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .recharge-type-card h3 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
  }
  .recharge-type-card p {
    color: #666;
    margin-bottom: 25px;
    font-size: 16px;
  }
  .type-features {
    text-align: left;
  }
  .type-features .feature {
    padding: 8px 0;
    color: #555;
    font-size: 14px;
  }
  .back-btn {
    float: right;
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }
  .back-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .recharge-header {
      padding: 20px;
    }
    .recharge-header h2 {
      font-size: 22px;
    }
    .recharge-content {
      padding: 20px;
    }
    .section-title {
      font-size: 18px;
      margin-bottom: 20px;
    }
    .pay-card {
      padding: 8px 6px;
      height: 70px;
      margin-bottom: 8px;
    }
    .pay-card h4 {
      font-size: 14px;
      margin-bottom: 0px;
    }
    .pay-card .price {
      font-size: 20px;
      margin: 8px 0;
    }
    .pay-card .desc {
      font-size: 11px;
      margin-bottom: 8px;
    }
    .pay-card .reward {
      padding: 4px 8px;
      font-size: 10px;
      border-radius: 12px;
    }
    .type-selection-container {
      flex-direction: row;
      gap: 15px;
    }
    .type-card-wrapper {
      max-width: none;
      min-width: 0;
      flex: 1;
    }
    .recharge-type-card {
      padding: 20px 15px;
    }
    .recharge-type-card .type-icon {
      font-size: 36px;
      margin-bottom: 12px;
    }
    .recharge-type-card h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }
    .recharge-type-card p {
      font-size: 13px;
      margin-bottom: 15px;
    }
    .pay-method-section {
      padding: 15px;
    }
    .pay-options {
      flex-direction: row;
      gap: 10px;
    }
    .pay-option {
      max-width: none;
      padding: 8px 12px;
    }
    .pay-btn {
      padding: 12px 30px;
      font-size: 16px;
      width: 100%;
      max-width: 300px;
    }
    .account-info {
      padding: 20px;
    }
    .account-info .row {
      flex-direction: column;
    }
    .account-info .col-md-6 {
      margin-bottom: 10px;
    }
    .tips-section {
      padding: 15px;
    }

    /* 移动端支付加载优化 */
    .mobile-payment-loading .loading-content {
      margin: 10px;
      padding: 30px 20px;
    }

    .loading-text {
      font-size: 16px;
    }

    .loading-tips {
      font-size: 13px;
    }
  }

  @media (max-width: 480px) {
    .pay-card {
      height: 110px;
      padding: 6px 4px;
    }
    .pay-card h4 {
      font-size: 13px;
    }
    .pay-card .price {
      font-size: 18px;
    }
    .pay-card .desc {
      font-size: 10px;
    }
    .type-selection-container {
      gap: 10px;
    }
    .recharge-type-card {
      padding: 15px 10px;
    }
    .recharge-type-card .type-icon {
      font-size: 32px;
    }
    .recharge-type-card h3 {
      font-size: 16px;
    }
    .recharge-type-card p {
      font-size: 12px;
    }
    .type-features .feature {
      font-size: 11px;
    }
  }
</style>

<main class="content">
  <div id="content-container" class="container">
    <div class="row">
      {inc:user/menu.htm}
      <!--右侧主体部分 start-->
      <div class="col-md-9">
        <!-- 充值容器 -->
        <div class="recharge-container">
          <div class="recharge-content">
            <!-- 账户信息 -->
            <div class="account-info">
              <div class="row">
                <div class="col-md-6">
                  <i class="fa fa-coins"></i>
                  <strong>当前金币：</strong>
                  <span class="value">{$_user['golds']}</span>
                </div>
                <div class="col-md-6">
                  <i class="fa fa-crown"></i>
                  <strong>用戶組：</strong>
                  {if:$_user['groupid'] == 12 && $_user['vip_times'] > time()}
                    <span class="value">VIP会员</span>
                    <div style="font-size: 14px; color: rgba(255,255,255,0.8); margin-top: 5px;">
                      <i class="fa fa-clock-o"></i> 到期时间：{$vip_expire_time}
                    </div>
                  {else}
                    <span class="value">普通用户</span>
                  {/if}
                </div>
              </div>
              <a href="/1.html" target="_blank" style="font-size: 16px;font-weight: bold;margin-left: 10px;color: coral;">有问题？未到账？点我联系客服</a>
            </div>

            <!-- 充值类型选择 -->
            <div class="recharge-type-section">
              <div class="section-title">
                <i class="fa fa-list"></i> 选择充值类型
              </div>
              <div class="type-selection-container">
                <div class="type-card-wrapper">
                  <div class="recharge-type-card" data-type="gold">
                    
                    <h3>金币充值</h3>
                    <p>购买金币用于付费内容阅读</p>
                    <div class="type-features">
                      <div class="feature">✓ 永久有效</div>
                      <div class="feature">✓ 按需消费</div>
                      <div class="feature">✓ 灵活使用</div>
                    </div>
                  </div>
                </div>
                <div class="type-card-wrapper">
                  <div class="recharge-type-card" data-type="vip">
                    <h3>VIP会员</h3>
                    <p>开通VIP享受更多特权服务</p>
                    <div class="type-features">
                      <div class="feature">✓ 免费阅读</div>
                      <div class="feature">✓ 专属特权</div>
                      <div class="feature">✓ 优先支持</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 金币充值区域 -->
            <div class="gold-section" id="gold-section" style="display: none;">
              <div class="section-title">
                <i class="fa fa-coins"></i> 金币充值套餐
                <button type="button" class="back-btn" onclick="showTypeSelection()">
                  <i class="fa fa-arrow-left"></i> 返回选择
                </button>
              </div>
              <div class="packages-grid" id="gold-packages">
                {loop:$gold_packages $package}
                <div class="pay-card gold-card" data-package="{$package['id']}" data-type="1">
                  <h4>{$package['name']}</h4>
                  <div class="price">￥{$package['amount']}</div>

                  <div class="reward">获得 {$package['value']} 金币</div>
                </div>
                {/loop}
              </div>
            </div>

            <!-- VIP开通区域 -->
            <div class="vip-section" id="vip-section" style="display: none;">
              <div class="section-title">
                <i class="fa fa-crown"></i> VIP会员套餐
                <button type="button" class="back-btn" onclick="showTypeSelection()">
                  <i class="fa fa-arrow-left"></i> 返回选择
                </button>
              </div>
              <div class="packages-grid" id="vip-packages">
                {loop:$vip_packages $package}
                <div class="pay-card vip-card" data-package="{$package['id']}" data-type="2">
                  <h4>{$package['name']}</h4>
                  <div class="price">￥{$package['amount']}</div>
                  <div class="desc">{$package['desc']}</div>
                  <div class="reward">开通 {$package['value']} 天VIP</div>
                </div>
                {/loop}
              </div>
            </div>

            <!-- 支付方式选择 -->
            <div class="pay-method-section">
              <div class="pay-method-title">
                <i class="fa fa-credit-card"></i> 选择支付方式
              </div>
              <div class="pay-options">
                <div class="pay-option" data-type="alipay">
                  <div class="name">支付宝</div>
                </div>
                <div class="pay-option" data-type="wxpay">
                  <div class="name">微信支付</div>
                </div>
              </div>
            </div>

            <!-- 支付按钮 -->
            <div class="text-center">
              <button type="button" id="pay-btn" class="pay-btn" disabled>
                <i class="fa fa-credit-card"></i> 立即支付
              </button>
            </div>

            <!-- PC端支付提示区域 -->
            <div id="payment-notice" class="payment-notice">
              <div style="margin-bottom: 15px;">
                <i class="fa fa-info-circle notice-icon"></i>
                <strong style="font-size: 18px;">支付页面已在新标签页打开</strong>
              </div>
              <div style="margin-bottom: 20px; font-size: 16px; line-height: 1.6;">
                请在新打开的标签页完成支付<br>
                支付成功后点击下方按钮立即查看到账情况
              </div>
              <div>
                <a href="javascript:void(0)" class="refresh-btn primary" onclick="refreshPageForPayment()">
                  <i class="fa fa-refresh"></i> 支付完成，立即刷新查看到账
                </a>
                <a href="javascript:void(0)" class="refresh-btn" onclick="hidePaymentNotice()">
                  <i class="fa fa-times"></i> 稍后手动刷新
                </a>
              </div>
              <div style="margin-top: 15px; font-size: 14px; opacity: 0.9;">
                <i class="fa fa-lightbulb-o"></i> 支付完成后，点击"立即刷新"按钮可以马上看到账户变化
              </div>
            </div>

            <!-- 移动端支付加载遮罩 -->
            <div id="mobile-payment-loading" class="mobile-payment-loading">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在跳转支付页面</div>
                <div class="loading-tips">
                  即将为您打开支付页面<br>
                  请在支付页面完成付款<br>
                  支付完成后会自动返回
                </div>
              </div>
            </div>

            <!-- 温馨提示 -->
            <div class="tips-section">
              <h5><i class="fa fa-lightbulb-o"></i> 温馨提示</h5>
              <p><i class="fa fa-check-circle"></i> 支付完成后，系统会自动到账，一般在1-3分钟内完成</p>
              <p><i class="fa fa-check-circle"></i> 如果超过10分钟未到账，请联系客服处理</p>
              <p><i class="fa fa-check-circle"></i> 可在 <a href="/my-orders.html" style="color: #2e7d32; font-weight: bold;">充值记录</a> 中查看订单状态</p>
              <p><i class="fa fa-check-circle"></i> VIP会员享受更多特权，金币可用于付费内容阅读</p>
            </div>
          </div>
        </div>
      </div>
      <!--右侧主体部分 end-->
    </div>
  </div>
</main>

{inc:user/footer.htm}
<script type="text/javascript">
  // 全局变量
  var selectedPackage = 0;
  var selectedType = 1;
  var selectedPayType = 'alipay';

  // 更新支付按钮状态
  function updatePayButton() {
    if(selectedPackage > 0 && selectedPayType) {
      $('#pay-btn').prop('disabled', false);
      var packageName = $('.pay-card.selected h4').text();
      var payName = $('.pay-option.selected .name').text();
      $('#pay-btn').html('<i class="fa fa-credit-card"></i> 使用' + payName + '支付 ' + packageName);
    } else {
      $('#pay-btn').prop('disabled', true);
      $('#pay-btn').html('<i class="fa fa-credit-card"></i> 请选择套餐和支付方式');
    }
  }

  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    $("#my-recharge").addClass("active");

    // 完全移除消息监听和回调处理，只依靠用户手动刷新

    // 充值类型选择
    $('.recharge-type-card').on('click', function(){
      var type = $(this).data('type');
      if(type === 'gold') {
        showGoldSection();
      } else if(type === 'vip') {
        showVipSection();
      }
    });

    // 套餐选择
    $('.pay-card').on('click', function(){
      $('.pay-card').removeClass('selected');
      $(this).addClass('selected');
      selectedPackage = $(this).data('package');
      selectedType = $(this).data('type');
      updatePayButton();
    });

    // 支付方式选择
    $('.pay-option').on('click', function(){
      $('.pay-option').removeClass('selected');
      $(this).addClass('selected');
      selectedPayType = $(this).data('type');
      updatePayButton();
    });

    // 默认选择支付宝
    $('.pay-option[data-type="alipay"]').addClass('selected');

    // 支付按钮点击 - 防重复提交的支付方案
    $('#pay-btn').on('click', function(){
      // 防止重复点击
      if ($(this).hasClass('disabled') || $(this).prop('disabled')) {
        console.log('支付按钮已禁用，防止重复提交');
        return;
      }

      if(selectedPackage == 0) {
        layer.alert('请选择充值套餐！', {icon: 2});
        return;
      }

      if(!selectedPayType) {
        layer.alert('请选择支付方式！', {icon: 2});
        return;
      }

      console.log('发起支付请求:', {
        package_id: selectedPackage,
        pay_type: selectedPayType
      });

      // 禁用支付按钮，防止重复提交
      $(this).addClass('disabled').prop('disabled', true).text('处理中...');

      var loadIndex = layer.load(2, {shade: [0.3, '#000']});

      $.ajax({
        type: "POST",
        url: "my-create.html",
        data: {
          package_id: selectedPackage,
          pay_type: selectedPayType,
          FORM_HASH: '{$form_hash}'
        },
        timeout: 30000, // 30秒超时
        cache: false,
        dataType: 'json',
        beforeSend: function(xhr) {
          // 添加请求头，确保移动端兼容性
          xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
          console.log('发送Ajax请求到:', "my-create.html");
        },
        success: function(response, status, xhr) {
          layer.close(loadIndex);
          console.log('服务器响应:', response);

          // 检查响应是否是JSON
          var result;
          try {
            if (typeof response === 'string') {
              result = JSON.parse(response);
            } else {
              result = response;
            }
          } catch (e) {
            console.error('JSON解析错误:', e);
            layer.alert('服务器响应格式错误，请检查后台配置！<br>响应内容：' + response.substring(0, 200), {icon: 2});
            // 恢复按钮状态
            $('#pay-btn').removeClass('disabled').prop('disabled', false).text('立即支付');
            return;
          }

          if(!result.err){
            // 支付数据直接在result中，不是在result.data中
            var payData = result;
            console.log('支付数据:', payData);

            // 根据服务器返回的设备信息智能选择支付方式
            if (payData.qrcode) {
              if (payData.is_wxpay_mobile) {
                // 移动端微信支付：特殊处理，确保显示二维码
                console.log('移动端微信支付：使用PC版页面确保二维码显示');
                openMobileWeChatPayment(payData.qrcode, payData.order_no);
              } else if (payData.is_mobile || payData.payment_method === 'redirect') {
                // 普通移动端支付：直接跳转
                console.log('移动端支付：直接跳转到支付页面');
                openMobilePayment(payData.qrcode, payData.order_no);
              } else {
                // PC端：新窗口打开
                console.log('PC端支付：新窗口打开支付页面');
                openPCPayment(payData.qrcode, payData.order_no);
              }
            } else {
              layer.alert('支付接口未返回支付链接，请联系客服！', {icon: 2});
              // 恢复按钮状态
              $('#pay-btn').removeClass('disabled').prop('disabled', false).text('立即支付');
            }
          } else {
            layer.alert(result.msg, {icon: 2});
            // 恢复按钮状态
            $('#pay-btn').removeClass('disabled').prop('disabled', false).text('立即支付');
          }
        },
        error: function(xhr, status, error){
          layer.close(loadIndex);
          console.log('Ajax完整错误信息:');
          console.log('xhr:', xhr);
          console.log('status:', status);
          console.log('error:', error);
          console.log('responseText:', xhr.responseText);
          console.log('readyState:', xhr.readyState);
          console.log('status code:', xhr.status);
          console.log('当前URL:', window.location.href);

          var errorMsg = '';

          // 针对不同错误类型提供具体的解决方案
          if (xhr.status === 0) {
            if (status === 'timeout') {
              errorMsg = '请求超时，请检查网络连接后重试！';
            } else if (status === 'abort') {
              errorMsg = '请求被取消，请重试！';
            } else {
              errorMsg = '网络连接失败，可能的原因：<br>';
              errorMsg += '1. 网络连接不稳定<br>';
              errorMsg += '2. 服务器暂时无法访问<br>';
              errorMsg += '3. 请检查网络设置后重试';
            }
          } else if (xhr.status === 404) {
            errorMsg = '请求地址不存在，请联系技术支持！<br>错误码: 404';
          } else if (xhr.status === 500) {
            errorMsg = '服务器内部错误，请稍后重试！<br>错误码: 500';
          } else if (xhr.status >= 400) {
            errorMsg = '请求失败，错误码: ' + xhr.status + '<br>';
            errorMsg += '请联系技术支持或稍后重试！';
          } else {
            errorMsg = '网络错误，请重试！<br>';
            errorMsg += '状态码: ' + xhr.status + '<br>';
            errorMsg += '错误信息: ' + error;
          }

          // 如果有响应内容，显示部分内容用于调试
          if (xhr.responseText && xhr.responseText.length > 0) {
            console.log('服务器响应内容:', xhr.responseText);
            // 只在开发环境显示响应内容
            if (window.location.hostname === 'localhost' || window.location.hostname.indexOf('127.0.0.1') !== -1) {
              errorMsg += '<br><br>调试信息: ' + xhr.responseText.substring(0, 200);
            }
          }

          layer.alert(errorMsg, {icon: 2});
          // 恢复按钮状态
          $('#pay-btn').removeClass('disabled').prop('disabled', false).html('<i class="fa fa-credit-card"></i> 立即支付');
        }
      });
    });
  });

  // 显示金币充值区域
  function showGoldSection() {
    $('.recharge-type-section').hide();
    $('#vip-section').hide();
    $('#gold-section').show();
    $('.pay-method-section').show();
    $('#pay-btn').parent().show();
    // 重置选择
    $('.pay-card').removeClass('selected');
    selectedPackage = 0;
    updatePayButton();
  }

  // 显示VIP开通区域
  function showVipSection() {
    $('.recharge-type-section').hide();
    $('#gold-section').hide();
    $('#vip-section').show();
    $('.pay-method-section').show();
    $('#pay-btn').parent().show();
    // 重置选择
    $('.pay-card').removeClass('selected');
    selectedPackage = 0;
    updatePayButton();
  }

  // 显示类型选择
  function showTypeSelection() {
    $('.recharge-type-section').show();
    $('#gold-section').hide();
    $('#vip-section').hide();
    $('.pay-method-section').hide();
    $('#pay-btn').parent().hide();
    // 重置选择
    $('.pay-card').removeClass('selected');
    selectedPackage = 0;
    selectedType = 1;
  }

  // 原二维码支付函数已被新标签页支付方案替代
  // 保留以备需要时恢复使用
  /*
  function showPaymentMonitor(orderNo, message) {
    // 原支付监控弹窗代码...
  }

  function showQRCodePayment(qrcode, orderNo) {
    // 原二维码支付代码...
  }
  */

  // 原二维码生成和心跳检测函数已被新标签页支付方案替代
  // 保留以备需要时恢复使用
  /*
  function generateQRCode(qrcode) { ... }
  function tryOnlineQRCode(container, qrcode) { ... }
  function tryQRCodeJS(container, qrcode) { ... }
  function showQRCodeFallback(container, qrcode) { ... }
  function startPaymentHeartbeat(orderNo) { ... }
  function copyPayLink(payLink) { ... }
  function fallbackCopyTextToClipboard(text) { ... }
  */



  // 检查订单状态 - 适配新标签页支付方案
  function checkOrderStatus(orderNo) {
    var loadIndex = layer.load(2, {shade: [0.1, '#000']});

    $.ajax({
      type: "POST",
      url: "my-check.html",
      data: {order_no: orderNo},
      dataType: 'json',
      timeout: 10000,
      success: function(result) {
        layer.close(loadIndex);

        if(!result.err && result.data){
          var status = result.data.status;
          var statusText = result.data.status_text;

          if(status == 1) {
            // 支付成功
            $('#monitor-status-text').html('<span style="color: #4caf50; font-size: 18px;"><i class="fa fa-check-circle"></i> 支付成功！</span>');
            $('#monitor-countdown').html('<span style="color: #4caf50;">支付完成，正在处理...</span>');

            // 清理定时器
            if (window.orderMonitorInterval) {
              clearInterval(window.orderMonitorInterval);
              window.orderMonitorInterval = null;
            }

            // 关闭支付窗口
            if (window.paymentWindow && !window.paymentWindow.closed) {
              window.paymentWindow.close();
            }

            layer.msg('支付成功！', {icon: 1});
            setTimeout(function(){
              layer.closeAll();
              showPaymentSuccessDialog(result.data);
            }, 1000);
          } else if(status == 0) {
            // 未支付
            $('#monitor-status-text').html('<span style="color: #ff9800;"><i class="fa fa-clock-o"></i> ' + statusText + '</span>');
            layer.msg('订单尚未支付，请在支付窗口完成支付', {icon: 0});
          } else {
            // 其他状态
            $('#monitor-status-text').html('<span style="color: #f44336;"><i class="fa fa-times-circle"></i> ' + statusText + '</span>');
            layer.msg('订单状态异常：' + statusText, {icon: 2});
          }
        } else {
          $('#monitor-status-text').html('<span style="color: #f44336;"><i class="fa fa-exclamation-triangle"></i> 检查失败</span>');
          layer.msg('检查失败：' + (result.msg || '未知错误'), {icon: 2});
        }
      },
      error: function(xhr, status, error){
        layer.close(loadIndex);
        $('#monitor-status-text').html('<span style="color: #f44336;"><i class="fa fa-wifi"></i> 网络错误</span>');
        layer.msg('网络错误，请检查网络连接后重试', {icon: 2});
      }
    });
  }

  // 页面加载时隐藏支付相关区域
  $(document).ready(function(){
    $('.pay-method-section').hide();
    $('#pay-btn').parent().hide();
  });

  // 移动端支付处理
  function openMobilePayment(paymentUrl, orderNo) {
    console.log('移动端支付处理:', paymentUrl);

    // 显示加载界面
    showMobilePaymentLoading();

    // 延迟跳转，让用户看到加载提示
    setTimeout(function() {
      // 直接在当前窗口跳转到支付页面
      window.location.href = paymentUrl;
    }, 1500);
  }

  // 移动端微信支付特殊处理
  function openMobileWeChatPayment(paymentUrl, orderNo) {
    console.log('移动端微信支付特殊处理:', paymentUrl);

    // 显示特殊的加载提示
    showMobileWeChatLoading();

    // 延迟跳转，确保用户看到提示
    setTimeout(function() {
      // 直接跳转到经过服务器代理的PC版支付页面
      window.location.href = paymentUrl;
    }, 2000);
  }

  // PC端支付处理
  function openPCPayment(paymentUrl, orderNo) {
    console.log('PC端支付处理:', paymentUrl);

    // 新标签页打开支付页面
    var payWindow = window.open(paymentUrl, '_blank');

    if (payWindow) {
      // 显示PC端支付提示
      showPaymentNotice();
    } else {
      // 浏览器阻止了弹窗，提示用户
      layer.alert('浏览器阻止了支付窗口，请允许弹窗或直接点击下方链接：<br><a href="' + paymentUrl + '" target="_blank" style="color: #667eea;">点击这里打开支付页面</a>', {
        icon: 0,
        title: '支付提示'
      });
    }

    // 立即恢复支付按钮状态
    $('#pay-btn').removeClass('disabled').prop('disabled', false).html('<i class="fa fa-credit-card"></i> 立即支付');
  }

  // 兼容旧版本的函数名
  function openPaymentPageOnly(paymentUrl, orderNo) {
    // 自动检测设备类型
    var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
      openMobilePayment(paymentUrl, orderNo);
    } else {
      openPCPayment(paymentUrl, orderNo);
    }
  }

  // 显示支付提示区域
  function showPaymentNotice() {
    $('#payment-notice').addClass('show');

    // 滚动到提示区域
    $('html, body').animate({
      scrollTop: $('#payment-notice').offset().top - 100
    }, 500);
  }

  // 隐藏支付提示区域
  function hidePaymentNotice() {
    $('#payment-notice').removeClass('show');
  }

  // 显示移动端支付加载界面
  function showMobilePaymentLoading() {
    $('#mobile-payment-loading').addClass('show');
  }

  // 显示移动端微信支付特殊加载界面
  function showMobileWeChatLoading() {
    // 更新加载文本为微信支付专用提示
    $('#mobile-payment-loading .loading-text').text('正在为您优化微信支付页面');
    $('#mobile-payment-loading .loading-tips').html(
      '正在获取PC版支付页面，确保二维码正常显示<br>' +
      '请稍候，即将为您打开支付页面<br>' +
      '支付完成后会自动返回'
    );
    $('#mobile-payment-loading').addClass('show');
  }

  // 隐藏移动端支付加载界面
  function hideMobilePaymentLoading() {
    $('#mobile-payment-loading').removeClass('show');

    // 恢复默认文本
    $('#mobile-payment-loading .loading-text').text('正在跳转支付页面');
    $('#mobile-payment-loading .loading-tips').html(
      '即将为您打开支付页面<br>' +
      '请在支付页面完成付款<br>' +
      '支付完成后会自动返回'
    );
  }

  // 刷新页面查看支付结果
  function refreshPageForPayment() {
    layer.msg('正在刷新页面，请稍候...', {
      icon: 16,
      time: 1000
    });

    setTimeout(function() {
      location.reload();
    }, 1000);
  }

  // 检测设备类型
  function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  // 检测微信环境
  function isWeChatBrowser() {
    return /MicroMessenger/i.test(navigator.userAgent);
  }

  // 检测支付宝环境
  function isAlipayBrowser() {
    return /AlipayClient/i.test(navigator.userAgent);
  }
</script>
