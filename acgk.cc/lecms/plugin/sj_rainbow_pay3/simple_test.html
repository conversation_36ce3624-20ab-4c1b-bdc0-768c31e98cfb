<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Ajax测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .test-btn { 
            background: #667eea; color: white; border: none; 
            padding: 15px 30px; border-radius: 8px; 
            font-size: 16px; cursor: pointer; 
            width: 100%; margin: 10px 0; 
        }
        .result { 
            background: #f8f9fa; padding: 15px; 
            border-radius: 8px; margin-top: 20px; 
            white-space: pre-wrap; font-family: monospace; 
            font-size: 12px; max-height: 400px; overflow-y: auto; 
        }
        .error { background: #ffebee; }
        .success { background: #e8f5e8; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h2>简单Ajax测试</h2>
        
        <p><strong>当前URL：</strong><span id="current-url"></span></p>
        <p><strong>设备类型：</strong><span id="device-type"></span></p>
        
        <button class="test-btn" onclick="testSimplePost()">测试简单POST请求</button>
        <button class="test-btn" onclick="testWithoutFormHash()">测试不带FORM_HASH的请求</button>
        <button class="test-btn" onclick="testGetRequest()">测试GET请求</button>
        
        <div id="test-result" class="result" style="display: none;"></div>
    </div>

    <script>
        $(document).ready(function() {
            $('#current-url').text(window.location.href);
            var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            $('#device-type').text(isMobile ? '移动设备' : 'PC设备');
        });

        function showResult(content, type = 'info') {
            var resultDiv = $('#test-result');
            resultDiv.removeClass('error success').addClass(type);
            resultDiv.text(content);
            resultDiv.show();
        }

        function testSimplePost() {
            showResult('正在测试简单POST请求...');
            
            $.ajax({
                type: "POST",
                url: "my-create.html",
                data: {
                    package_id: 1,
                    pay_type: 'alipay'
                },
                timeout: 10000,
                success: function(response, status, xhr) {
                    var result = '简单POST请求成功！\n';
                    result += '状态码: ' + xhr.status + '\n';
                    result += '响应类型: ' + typeof response + '\n';
                    result += '响应内容: ' + JSON.stringify(response, null, 2);
                    showResult(result, 'success');
                },
                error: function(xhr, status, error) {
                    var result = '简单POST请求失败！\n';
                    result += '状态码: ' + xhr.status + '\n';
                    result += '状态: ' + status + '\n';
                    result += '错误: ' + error + '\n';
                    result += 'readyState: ' + xhr.readyState + '\n';
                    if (xhr.responseText) {
                        result += '响应内容: ' + xhr.responseText.substring(0, 500);
                    }
                    showResult(result, 'error');
                }
            });
        }

        function testWithoutFormHash() {
            showResult('正在测试不带FORM_HASH的请求...');
            
            $.ajax({
                type: "POST",
                url: "my-create.html",
                data: {
                    package_id: 1,
                    pay_type: 'alipay'
                },
                timeout: 10000,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                },
                success: function(response, status, xhr) {
                    var result = '不带FORM_HASH的请求成功！\n';
                    result += '状态码: ' + xhr.status + '\n';
                    result += '响应类型: ' + typeof response + '\n';
                    result += '响应内容: ' + JSON.stringify(response, null, 2);
                    showResult(result, 'success');
                },
                error: function(xhr, status, error) {
                    var result = '不带FORM_HASH的请求失败！\n';
                    result += '状态码: ' + xhr.status + '\n';
                    result += '状态: ' + status + '\n';
                    result += '错误: ' + error + '\n';
                    result += 'readyState: ' + xhr.readyState + '\n';
                    if (xhr.responseText) {
                        result += '响应内容: ' + xhr.responseText.substring(0, 500);
                    }
                    showResult(result, 'error');
                }
            });
        }

        function testGetRequest() {
            showResult('正在测试GET请求...');
            
            $.ajax({
                type: "GET",
                url: "my-create.html?package_id=1&pay_type=alipay",
                timeout: 10000,
                success: function(response, status, xhr) {
                    var result = 'GET请求成功！\n';
                    result += '状态码: ' + xhr.status + '\n';
                    result += '响应类型: ' + typeof response + '\n';
                    result += '响应内容: ' + response.substring(0, 500);
                    showResult(result, 'success');
                },
                error: function(xhr, status, error) {
                    var result = 'GET请求失败！\n';
                    result += '状态码: ' + xhr.status + '\n';
                    result += '状态: ' + status + '\n';
                    result += '错误: ' + error + '\n';
                    result += 'readyState: ' + xhr.readyState + '\n';
                    if (xhr.responseText) {
                        result += '响应内容: ' + xhr.responseText.substring(0, 500);
                    }
                    showResult(result, 'error');
                }
            });
        }
    </script>
</body>
</html>
