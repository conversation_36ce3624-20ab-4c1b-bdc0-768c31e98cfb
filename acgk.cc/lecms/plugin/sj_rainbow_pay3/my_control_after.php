<?php
// 在线充值页面 - 对应URL: /my-recharge.html
function recharge()
{
    $this->_cfg['titles'] = '在线充值';
    $this->_var['topcid'] = -1;

    // 清理过期的临时支付页面文件
    $this->cleanup_temp_files();

    // 获取套餐列表
    $gold_packages = $this->sj_rainbow_pay_packages->get_frontend_packages(1);
    $vip_packages = $this->sj_rainbow_pay_packages->get_frontend_packages(2);

    // 计算VIP信息
    $vip_days = 0;
    $vip_expire_time = '';
    if ($this->_user['groupid'] == 12 && $this->_user['vip_times'] > time()) {
        $vip_days = ceil(($this->_user['vip_times'] - time()) / 86400);
        $vip_expire_time = date('Y-m-d H:i:s', $this->_user['vip_times']);
    }

    // 设置模板变量
    $this->assign('gold_packages', $gold_packages);
    $this->assign('vip_packages', $vip_packages);
    $this->assign('vip_days', $vip_days);
    $this->assign('vip_expire_time', $vip_expire_time);
    $this->assign('cfg', $this->_cfg);
    $this->assign('_cfg', $this->_cfg);
    $this->assign('_user', $this->_user);
    $this->assign('my_url', $this->_my_url);
    $this->display('templet/my_rainbow_pay.htm');
}

// 创建订单 - 对应URL: /my-create.html
function create()
{
    // 添加调试日志
    error_log('Rainbow Pay - 创建订单请求开始');
    error_log('Rainbow Pay - 请求方法: ' . $_SERVER['REQUEST_METHOD']);
    error_log('Rainbow Pay - POST数据: ' . json_encode($_POST));
    error_log('Rainbow Pay - GET数据: ' . json_encode($_GET));

    // 检查是否为POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        error_log('Rainbow Pay - 错误: 不是POST请求');
        E(1, '请求方式错误！必须使用POST方法');
    }

    // 检查POST数据
    if (empty($_POST)) {
        error_log('Rainbow Pay - 错误: POST数据为空');
        E(1, 'POST数据为空！');
    }

    // 验证FORM_HASH（CSRF保护）- 暂时禁用用于调试
    /*
    if (!form_submit()) {
        error_log('Rainbow Pay - 错误: FORM_HASH验证失败');
        E(1, '表单验证失败，请刷新页面重试！');
    }
    */
    error_log('Rainbow Pay - 跳过FORM_HASH验证（调试模式）');

    $package_id = intval(R('package_id', 'P'));
    $pay_type = trim(R('pay_type', 'P'));

    error_log('Rainbow Pay - 接收参数: package_id=' . $package_id . ', pay_type=' . $pay_type);

    if ($package_id <= 0) {
        error_log('Rainbow Pay - 错误: 套餐ID无效');
        E(1, '请选择充值套餐！');
    }

    if (!in_array($pay_type, array('alipay', 'wxpay'))) {
        error_log('Rainbow Pay - 错误: 支付方式无效');
        E(1, '请选择支付方式！');
    }

        // 创建订单
        $result = $this->sj_rainbow_pay_orders->create_order($this->_uid, $package_id, $pay_type);
        if ($result['err']) {
            E(1, $result['msg']);
        }
        
        $order = $result['data'];

        // 获取支付配置
        $config = $this->sj_rainbow_pay_config->get_all_config();
        
        if ($pay_type == 'alipay') {
            $api_url = $config['alipay_api_url'];
            $pid = $config['alipay_pid'];
            $key = $config['alipay_key'];
        } else { // wxpay
            $api_url = $config['wechat_api_url'];
            $pid = $config['wechat_pid'];
            $key = $config['wechat_key'];
        }
        
        if (empty($api_url) || empty($pid) || empty($key)) {
            E(1, '支付配置不完整，请联系管理员！');
        }
        
        // 获取用户IP
        $client_ip = $this->get_client_ip();

        // 构建支付参数 - 使用VPS专用回调
        // 对于移动端微信支付，强制设置为PC设备类型，避免彩虹易支付的移动端限制
        $device_type = $this->get_device_type();
        if ($this->is_mobile_device() && $pay_type == 'wxpay') {
            $device_type = 'pc'; // 伪装成PC端，确保微信支付正常显示二维码
        }

        $params = array(
            'pid' => $pid,
            'type' => $pay_type,
            'out_trade_no' => $order['order_no'],
            'notify_url' => $this->_cfg['weburl'] . 'lecms/plugin/sj_rainbow_pay/route/rainbow_pay_notify.php',
            'return_url' => $this->_cfg['weburl'] . 'my-payreturn.html',
            'name' => $config['site_name'] . ' - ' . ($order['type'] == 1 ? $order['value'] . '金币' : $order['value'] . '天VIP'),
            'money' => $order['amount'],
            'clientip' => $client_ip,
            'device' => $device_type, // 使用处理后的设备类型
            'param' => ''
        );

        // 生成签名
        $sign = $this->generate_sign($params, $key);
        $params['sign'] = $sign;
        $params['sign_type'] = 'MD5';

        // 检测设备类型，为移动端提供更好的支付体验
        $is_mobile = $this->is_mobile_device();
        $original_device_type = $this->get_device_type();

        // 构建支付URL
        $submit_url = rtrim($api_url, '/') . '/submit.php';

        // 对于移动端微信支付，使用代理页面确保显示二维码
        if ($is_mobile && $pay_type == 'wxpay') {
            // 使用专门的移动端微信支付代理页面
            $proxy_params = $params;
            $proxy_params['api_url'] = $api_url;

            // 构建代理页面URL
            $payment_url = $this->_cfg['weburl'] . 'lecms/plugin/sj_rainbow_pay/mobile_wechat_proxy.php?' . http_build_query($proxy_params);
            $payment_method = 'redirect'; // 移动端直接跳转
        } else {
            // 普通流程
            $payment_url = $submit_url . '?' . http_build_query($params);
            $payment_method = $is_mobile ? 'redirect' : 'newwindow';
        }

        // 返回支付页面URL和设备信息
        $pay_data = array(
            'order_no' => $order['order_no'],
            'trade_no' => $order['order_no'], // 使用订单号作为trade_no
            'pay_type' => 'redirect',  // 标识为重定向支付
            'qrcode' => $payment_url,   // 支付页面URL
            'is_mobile' => $is_mobile,  // 是否为移动设备
            'device_type' => $original_device_type, // 原始设备类型
            'payment_method' => $payment_method, // 支付方式
            'is_wxpay_mobile' => ($is_mobile && $pay_type == 'wxpay') // 是否为移动端微信支付
        );

        E(0, '订单创建成功！', '', $pay_data);
}

// 支付返回页面 - 对应URL: /my-payreturn.html
function payreturn()
{
    $this->_cfg['titles'] = '支付结果';
    $this->_var['topcid'] = -1;

    $order_no = R('out_trade_no');
    $trade_status = R('trade_status');

    $order = array();
    $status_text = '支付状态未知';
    $auto_close = false;

    if ($order_no) {
        $order = $this->sj_rainbow_pay_orders->get_by_order_no($order_no);
        if (!empty($order) && $order['uid'] == $this->_uid) {
            if ($order['status'] == 1) {
                $status_text = '支付成功';
                $auto_close = true;
            } elseif ($order['status'] == 0) {
                $status_text = '支付中，请稍候...';
            } else {
                $status_text = '支付失败';
            }
        }
    }

    // 如果是支付成功且trade_status为TRADE_SUCCESS，也标记为自动关闭
    if ($trade_status == 'TRADE_SUCCESS') {
        $status_text = '支付成功';
        $auto_close = true;
    }

    // 设置模板变量
    $this->assign('order', $order);
    $this->assign('status_text', $status_text);
    $this->assign('auto_close', $auto_close);
    $this->assign('cfg', $this->_cfg);
    $this->assign('_cfg', $this->_cfg);
    $this->assign('_user', $this->_user);
    $this->assign('my_url', $this->_my_url);
    $this->display('templet/my_rainbow_pay_return.htm');
}

// 充值记录 - 对应URL: /my-orders.html
function orders()
{
    $this->_cfg['titles'] = '充值记录';
    $this->_var['topcid'] = -1;
    
    $where = array('uid' => $this->_uid);

    // 初始分页
    $pagenum = 10;
    $total = $this->sj_rainbow_pay_orders->find_count($where);
    $maxpage = max(1, ceil($total / $pagenum));
    $page = min($maxpage, max(1, intval(R('page'))));
    $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('orders', 'my', true));

    $this->assign('pages', $pages);
    $this->assign('total', $total);

    $data = $this->sj_rainbow_pay_orders->list_arr($where, 'create_time', -1, ($page - 1) * $pagenum, $pagenum, $total);
    
    // 设置模板变量
    $this->assign('data', $data);
    $this->assign('cfg', $this->_cfg);
    $this->assign('_cfg', $this->_cfg);
    $this->assign('_user', $this->_user);
    $this->assign('my_url', $this->_my_url);
    $this->display('templet/my_rainbow_pay_orders.htm');
}

// 账户状态功能已集成到用户中心首页，此函数已移除

// 检查订单状态（Ajax） - 对应URL: /my-check.html
function check()
{
    try {
        $order_no = trim(R('order_no', 'P'));
        if (empty($order_no)) {
            E(1, '订单号不能为空！');
        }

        $order = $this->sj_rainbow_pay_orders->get_by_order_no($order_no);
        if (empty($order)) {
            E(1, '订单不存在！');
        }

        if ($order['uid'] != $this->_uid) {
            E(1, '无权限查看此订单！');
        }

        $status_text = array(
            0 => '未支付',
            1 => '已支付',
            2 => '已取消'
        );

        $status = intval($order['status']);
        $response_data = array(
            'status' => $status,
            'status_text' => isset($status_text[$status]) ? $status_text[$status] : '未知状态',
            'pay_time' => $order['pay_time'] ? date('Y-m-d H:i:s', $order['pay_time']) : '',
            'order_no' => $order['order_no'],
            'amount' => $order['amount'],
            'type' => $order['type']
        );

        // 返回数据格式与前端JavaScript期望一致
        E(0, '查询成功', '', array('data' => $response_data));

    } catch (Exception $e) {
        E(1, '查询失败：' . $e->getMessage());
    }
}

// 生成签名 - 按照彩虹易支付官方文档
function generate_sign($params, $key)
{
    // 1. 按参数名ASCII码从小到大排序，sign、sign_type、和空值不参与签名
    ksort($params);
    $string = '';
    foreach ($params as $k => $v) {
        if ($v !== '' && $v !== null && $k != 'sign' && $k != 'sign_type') {
            $string .= $k . '=' . $v . '&';
        }
    }
    $string = rtrim($string, '&');

    // 2. 拼接商户密钥并MD5加密（小写）
    return md5($string . $key);
}

// 获取客户端IP
function get_client_ip()
{
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ips[0]);
    } elseif (isset($_SERVER['HTTP_X_REAL_IP']) && !empty($_SERVER['HTTP_X_REAL_IP'])) {
        return $_SERVER['HTTP_X_REAL_IP'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }
    return '127.0.0.1';
}

// 获取设备类型 - 优化移动端支付体验
function get_device_type()
{
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

    if (strpos($user_agent, 'MicroMessenger') !== false) {
        return 'wechat'; // 微信内浏览器
    } elseif (strpos($user_agent, 'AlipayClient') !== false) {
        return 'alipay'; // 支付宝客户端
    } elseif (strpos($user_agent, 'QQ/') !== false) {
        return 'qq'; // 手机QQ内浏览器
    } elseif (strpos($user_agent, 'Mobile') !== false || strpos($user_agent, 'Android') !== false || strpos($user_agent, 'iPhone') !== false || strpos($user_agent, 'iPad') !== false) {
        return 'mobile'; // 手机/平板浏览器
    } else {
        return 'pc'; // 电脑浏览器
    }
}

// 检测是否为移动设备
function is_mobile_device()
{
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    return preg_match('/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $user_agent);
}

// 清理过期的临时支付页面文件
function cleanup_temp_files()
{
    $temp_dir = dirname(__FILE__) . '/temp/';

    // 如果temp目录不存在，创建它
    if (!is_dir($temp_dir)) {
        mkdir($temp_dir, 0755, true);
        return;
    }

    // 清理超过1小时的临时文件
    $files = glob($temp_dir . 'temp_payment_*.html');
    $current_time = time();

    foreach ($files as $file) {
        if (is_file($file)) {
            $file_time = filemtime($file);
            // 删除超过1小时的文件
            if ($current_time - $file_time > 3600) {
                unlink($file);
            }
        }
    }
}

// 获取PC版支付页面URL - 伪装User-Agent解决移动端微信支付问题
function get_pc_payment_url($api_url, $params)
{
    $submit_url = rtrim($api_url, '/') . '/submit.php';

    // 使用服务器端CURL请求，伪装成PC端浏览器
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $submit_url . '?' . http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // 关键：伪装成PC端Chrome浏览器
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    // 设置PC端常见的HTTP头
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'Connection: keep-alive',
        'Upgrade-Insecure-Requests: 1',
        'Sec-Fetch-Dest: document',
        'Sec-Fetch-Mode: navigate',
        'Sec-Fetch-Site: none'
    ));

    $response = curl_exec($ch);
    $error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($error || $http_code != 200) {
        // 如果代理请求失败，回退到普通URL
        return $submit_url . '?' . http_build_query($params);
    }

    // 检查响应内容，确保获取到了正确的支付页面
    if (strpos($response, 'qrcode') !== false || strpos($response, '支付') !== false) {
        // 创建一个临时的支付页面文件
        return $this->create_proxy_payment_page($response, $params);
    }

    // 如果没有获取到正确内容，回退到普通URL
    return $submit_url . '?' . http_build_query($params);
}

// 创建代理支付页面
function create_proxy_payment_page($html_content, $params)
{
    // 生成唯一的临时文件名
    $temp_file = 'temp_payment_' . md5($params['out_trade_no'] . time()) . '.html';
    $temp_path = dirname(__FILE__) . '/temp/' . $temp_file;

    // 确保temp目录存在
    $temp_dir = dirname($temp_path);
    if (!is_dir($temp_dir)) {
        mkdir($temp_dir, 0755, true);
    }

    // 优化HTML内容，确保在移动端正常显示
    $optimized_html = $this->optimize_payment_html($html_content);

    // 写入临时文件
    file_put_contents($temp_path, $optimized_html);

    // 返回临时文件的URL
    return $this->_cfg['weburl'] . 'lecms/plugin/sj_rainbow_pay/temp/' . $temp_file;
}

// 优化支付页面HTML，确保移动端友好
function optimize_payment_html($html)
{
    // 添加移动端viewport
    $viewport = '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';

    // 添加移动端优化CSS
    $mobile_css = '
    <style>
    body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
    .payment-container { max-width: 400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .qrcode-container { text-align: center; margin: 20px 0; }
    .qrcode-container img { max-width: 100%; height: auto; }
    .payment-info { text-align: center; margin: 15px 0; }
    .amount { font-size: 24px; font-weight: bold; color: #e74c3c; }
    .tips { font-size: 14px; color: #666; line-height: 1.5; margin-top: 20px; }
    @media (max-width: 480px) {
        body { padding: 10px; }
        .payment-container { padding: 15px; }
        .amount { font-size: 20px; }
    }
    </style>';

    // 在head标签中插入优化内容
    if (strpos($html, '<head>') !== false) {
        $html = str_replace('<head>', '<head>' . $viewport . $mobile_css, $html);
    } else {
        // 如果没有head标签，在开头添加
        $html = $viewport . $mobile_css . $html;
    }

    return $html;
}

// 发送POST请求
function post_request($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'LECMS Rainbow Pay Plugin');

    $response = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        return false;
    }

    return $response;
}
