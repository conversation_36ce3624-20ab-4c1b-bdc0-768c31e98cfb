<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>支付配置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="fa fa-cog"></i> 支付配置
            </div>
            <div class="layui-card-body">
                <form class="layui-form" action="index.php?sj_rainbow_pay-setting" method="post">
                    <div class="layui-tab">
                        <ul class="layui-tab-title">
                            <li class="layui-this">支付宝配置</li>
                            <li>微信支付配置</li>
                            <li>基础配置</li>
                            <li>Telegram通知</li>
                        </ul>
                        <div class="layui-tab-content">
                            <!-- 支付宝配置 -->
                            <div class="layui-tab-item layui-show">
                                <div class="layui-form-item">
                                    <label class="layui-form-label required">API地址</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="alipay_api_url" value="{$config['alipay_api_url']}"
                                               placeholder="例如：https://pay.v8jisu.cn" class="layui-input">
                                        <div class="layui-form-mid layui-word-aux">彩虹易支付API基础地址（不包含/submit.php或/mapi.php）</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label required">商户ID</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="alipay_pid" value="{$config['alipay_pid']}"
                                               placeholder="支付宝商户ID" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label required">商户密钥</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="alipay_key" value="{$config['alipay_key']}"
                                               placeholder="支付宝商户密钥" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 微信支付配置 -->
                            <div class="layui-tab-item">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">API地址</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="wechat_api_url" value="{$config['wechat_api_url']}"
                                               placeholder="例如：https://pay.v8jisu.cn" class="layui-input">
                                        <div class="layui-form-mid layui-word-aux">彩虹易支付API基础地址（不包含/submit.php或/mapi.php）</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">商户ID</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="wechat_pid" value="{$config['wechat_pid']}"
                                               placeholder="微信支付商户ID" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">商户密钥</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="wechat_key" value="{$config['wechat_key']}"
                                               placeholder="微信支付商户密钥" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 基础配置 -->
                            <div class="layui-tab-item">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">站点名称</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="site_name" value="{$config['site_name']}"
                                               placeholder="显示在支付页面的站点名称" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">回调地址</label>
                                    <div class="layui-input-block">
                                        <input type="text" value="{$cfg['weburl']}lecms/plugin/sj_rainbow_pay/vps_notify.php"
                                               class="layui-input" readonly>
                                        <div class="layui-form-mid layui-word-aux">请将此地址配置到易支付后台（VPS专用回调）</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Telegram通知配置 -->
                            <div class="layui-tab-item">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">启用通知</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="telegram_enabled" value="1"
                                               {if:isset($config['telegram_enabled']) && $config['telegram_enabled'] == '1'}checked{/if}
                                               lay-skin="switch" lay-text="开启|关闭">
                                        <div class="layui-form-mid layui-word-aux">开启后支付成功将发送通知到Telegram</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">Bot Token</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="telegram_bot_token" value="{if:isset($config['telegram_bot_token'])}{$config['telegram_bot_token']}{/if}"
                                               placeholder="例如：123456789:ABCdefGHIjklMNOpqrsTUVwxyz" class="layui-input">
                                        <div class="layui-form-mid layui-word-aux">从 @BotFather 获取的Bot Token</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">Chat ID</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="telegram_chat_id" value="{if:isset($config['telegram_chat_id'])}{$config['telegram_chat_id']}{/if}"
                                               placeholder="例如：-1001234567890 或 123456789" class="layui-input">
                                        <div class="layui-form-mid layui-word-aux">接收通知的群组或用户ID，可以是负数</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">通知模板</label>
                                    <div class="layui-input-block">
                                        <textarea name="telegram_template" placeholder="支付通知模板" class="layui-textarea" rows="6">{if:isset($config['telegram_template'])}{$config['telegram_template']}{else}💰 支付成功通知
用户：{username}
套餐：{package_name}
金额：￥{amount}
订单：{order_no}
时间：{pay_time}{/if}</textarea>
                                        <div class="layui-form-mid layui-word-aux">
                                            可用变量：{username} 用户名、{amount} 金额、{package_name} 套餐名称、{order_no} 订单号、{pay_time} 支付时间<br>
                                            默认模板：💰 支付成功通知\n用户：{username}\n套餐：{package_name}\n金额：￥{amount}\n订单：{order_no}\n时间：{pay_time}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">代理设置</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="telegram_proxy" value="{if:isset($config['telegram_proxy'])}{$config['telegram_proxy']}{/if}"
                                               placeholder="例如：proxy.example.com:8080" class="layui-input">
                                        <div class="layui-form-mid layui-word-aux">如果服务器无法直接访问Telegram，可配置代理服务器</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button type="button" class="layui-btn layui-btn-normal" id="test-telegram">
                                            <i class="layui-icon layui-icon-dialogue"></i> 测试通知
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-primary" id="test-network">
                                            <i class="layui-icon layui-icon-wifi"></i> 网络诊断
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="form">保存配置</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['form', 'element'], function(){
    var form = layui.form;
    var element = layui.element;
    
    // 监听提交
    form.on('submit(form)', function(data){
        return true;
    });

    // 测试Telegram通知
    $('#test-telegram').click(function() {
        var bot_token = $('input[name="telegram_bot_token"]').val();
        var chat_id = $('input[name="telegram_chat_id"]').val();

        if (!bot_token || !chat_id) {
            layer.msg('请先填写Bot Token和Chat ID', {icon: 2});
            return;
        }

        var loadIndex = layer.load(2, {shade: [0.3, '#000']});
        $.ajax({
            url: 'index.php?sj_rainbow_pay-test_telegram',
            type: 'POST',
            data: {
                bot_token: bot_token,
                chat_id: chat_id
            },
            dataType: 'json',
            success: function(result) {
                layer.close(loadIndex);
                if (!result.err) {
                    layer.msg('测试通知发送成功！', {icon: 1});
                } else {
                    // 显示详细错误信息
                    layer.open({
                        type: 1,
                        title: '测试失败',
                        area: ['600px', '400px'],
                        content: '<div style="padding: 20px;"><pre style="white-space: pre-wrap; word-wrap: break-word;">' + result.msg + '</pre></div>'
                    });
                }
            },
            error: function() {
                layer.close(loadIndex);
                layer.alert('网络错误，请重试！', {icon: 2});
            }
        });
    });

    // 网络诊断
    $('#test-network').click(function() {
        layer.open({
            type: 1,
            title: '网络连接诊断',
            area: ['700px', '500px'],
            content: '<div style="padding: 20px;">' +
                '<h3>🔍 Telegram连接问题诊断</h3>' +
                '<div style="margin: 20px 0;">' +
                '<h4>常见问题和解决方案：</h4>' +
                '<p><strong>1. 连接超时 (Connection timed out)</strong></p>' +
                '<ul>' +
                '<li>服务器无法访问Telegram API</li>' +
                '<li>防火墙阻止HTTPS出站连接</li>' +
                '<li>网络环境限制访问Telegram</li>' +
                '</ul>' +
                '<p><strong>解决方案：</strong></p>' +
                '<ul>' +
                '<li>联系服务器提供商确认网络策略</li>' +
                '<li>配置代理服务器（在上方代理设置中填写）</li>' +
                '<li>临时关闭Telegram通知功能</li>' +
                '</ul>' +
                '</div>' +
                '<div style="margin: 20px 0;">' +
                '<h4>2. 临时解决方案：</h4>' +
                '<p>如果无法解决网络问题，可以：</p>' +
                '<ul>' +
                '<li>将"启用通知"开关设置为关闭</li>' +
                '<li>支付功能仍然正常工作，只是不发送通知</li>' +
                '<li>后续网络问题解决后再开启通知</li>' +
                '</ul>' +
                '</div>' +
                '<div style="text-align: center; margin-top: 30px;">' +
                '<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">关闭</button>' +
                '</div>' +
                '</div>'
        });
    });
});
</script>
</body>
</html>
