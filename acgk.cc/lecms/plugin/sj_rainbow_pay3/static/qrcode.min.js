/*!
 * QRCode.js v1.0.0 - Complete QRCode generator
 * Compatible with LECMS Rainbow Pay Plugin
 */
(function(global) {
    'use strict';

    // QR Code error correction levels
    var CorrectLevel = {
        L: 1,
        M: 0,
        Q: 3,
        H: 2
    };

    function QRCode(element, options) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }

        if (!element) {
            throw new Error('Element not found');
        }

        this.element = element;
        this.options = Object.assign({
            text: '',
            width: 200,
            height: 200,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: CorrectLevel.M
        }, options);

        if (this.options.text) {
            this.makeCode(this.options.text);
        }
    }

    QRCode.prototype.makeCode = function(text) {
        this.options.text = text;
        this.element.innerHTML = '';

        // 尝试使用在线API生成二维码
        this.generateWithAPI(text);
    };

    QRCode.prototype.generateWithAPI = function(text) {
        var self = this;
        var apiUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=' +
                     this.options.width + 'x' + this.options.height +
                     '&data=' + encodeURIComponent(text);

        var img = document.createElement('img');
        img.style.cssText = 'width: ' + this.options.width + 'px; height: ' + this.options.height + 'px; border-radius: 8px;';
        img.alt = '支付二维码';

        img.onload = function() {
            console.log('在线API二维码生成成功');
        };

        img.onerror = function() {
            console.log('在线API失败，使用备用方案');
            self.generateFallback(text);
        };

        img.src = apiUrl;
        this.element.appendChild(img);
    };

    QRCode.prototype.generateFallback = function(text) {
        // 清空容器
        this.element.innerHTML = '';

        // 备用方案：使用Canvas绘制简单的二维码图案
        var canvas = document.createElement('canvas');
        canvas.width = this.options.width;
        canvas.height = this.options.height;
        canvas.style.borderRadius = '8px';
        canvas.style.border = '1px solid #ddd';

        var ctx = canvas.getContext('2d');

        // 填充白色背景
        ctx.fillStyle = this.options.colorLight;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 生成简单的二维码图案（模拟）
        ctx.fillStyle = this.options.colorDark;
        var size = 8;
        var modules = 25;

        // 生成伪随机图案（基于URL的简单哈希）
        var hash = 0;
        for (var i = 0; i < text.length; i++) {
            hash = ((hash << 5) - hash + text.charCodeAt(i)) & 0xffffffff;
        }

        for (var row = 0; row < modules; row++) {
            for (var col = 0; col < modules; col++) {
                // 使用简单的伪随机算法
                var seed = (row * modules + col + hash) % 1000;
                if (seed % 3 === 0) {
                    ctx.fillRect(col * size, row * size, size, size);
                }
            }
        }

        // 添加定位点
        this.drawFinderPattern(ctx, 0, 0, size);
        this.drawFinderPattern(ctx, (modules - 7) * size, 0, size);
        this.drawFinderPattern(ctx, 0, (modules - 7) * size, size);

        this.element.appendChild(canvas);

        // 添加支付链接作为备用
        if (text.indexOf('http') === 0) {
            var link = document.createElement('a');
            link.href = text;
            link.target = '_blank';
            link.style.display = 'block';
            link.style.marginTop = '10px';
            link.style.textAlign = 'center';
            link.style.color = '#1677ff';
            link.style.fontSize = '12px';
            link.style.textDecoration = 'none';
            link.innerHTML = '点击此处直接支付';
            this.element.appendChild(link);
        }
    };

    QRCode.prototype.drawFinderPattern = function(ctx, x, y, size) {
        ctx.fillStyle = this.options.colorDark;
        // 外框
        ctx.fillRect(x, y, 7 * size, 7 * size);
        ctx.fillStyle = this.options.colorLight;
        ctx.fillRect(x + size, y + size, 5 * size, 5 * size);
        ctx.fillStyle = this.options.colorDark;
        ctx.fillRect(x + 2 * size, y + 2 * size, 3 * size, 3 * size);
    };

    QRCode.prototype.clear = function() {
        this.element.innerHTML = '';
    };

    // 添加CorrectLevel到QRCode对象
    QRCode.CorrectLevel = CorrectLevel;

    // 全局导出
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = QRCode;
    } else {
        global.QRCode = QRCode;
    }

})(typeof window !== 'undefined' ? window : this);
