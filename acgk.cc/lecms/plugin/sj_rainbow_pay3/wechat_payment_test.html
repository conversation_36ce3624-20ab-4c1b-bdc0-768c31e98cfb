<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端微信支付测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .header h2 {
            color: #333;
            margin: 0;
        }
        .device-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .device-info h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        .device-info p {
            margin: 5px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .test-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: all 0.3s;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .test-btn.wechat {
            background: linear-gradient(45deg, #1aad19, #00d100);
        }
        .test-btn.alipay {
            background: linear-gradient(45deg, #1677ff, #00a6fb);
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #4caf50;
            display: none;
        }
        .result.show {
            display: block;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🔧 移动端微信支付测试</h2>
            <p>测试彩虹易支付移动端微信支付优化方案</p>
        </div>
        
        <div class="device-info">
            <h4>📱 设备检测结果</h4>
            <p id="device-type">检测中...</p>
            <p id="browser-info">浏览器信息加载中...</p>
            <p id="payment-support">支付支持检测中...</p>
        </div>

        <div class="test-section">
            <h4>🧪 支付方式测试</h4>
            <p>测试不同支付方式在当前设备上的表现：</p>
            
            <button class="test-btn wechat" onclick="testWeChatPayment()">
                🟢 测试微信支付 (移动端优化)
            </button>
            
            <button class="test-btn alipay" onclick="testAlipayPayment()">
                🔵 测试支付宝支付 (标准流程)
            </button>
            
            <button class="test-btn" onclick="testPCMode()">
                💻 测试PC模式 (新窗口)
            </button>
        </div>

        <div class="test-section">
            <h4>🔍 问题诊断</h4>
            <p>检查常见的移动端支付问题：</p>
            
            <button class="test-btn" onclick="checkPopupBlocking()">
                检查弹窗阻止
            </button>
            
            <button class="test-btn" onclick="checkUserAgent()">
                检查User-Agent
            </button>
            
            <button class="test-btn" onclick="testProxyPage()">
                测试代理页面
            </button>
        </div>

        <div id="test-result" class="result">
            <h4>📊 测试结果</h4>
            <div id="result-content"></div>
        </div>

        <div class="warning">
            <strong>⚠️ 注意事项：</strong><br>
            • 移动端微信支付使用特殊的代理页面确保二维码显示<br>
            • 支付宝支付在移动端使用标准跳转流程<br>
            • PC端继续使用新窗口打开方式
        </div>

        <div class="success">
            <strong>✅ 优化效果：</strong><br>
            • 解决移动端微信支付空白页面问题<br>
            • 确保所有设备都能正常显示支付二维码<br>
            • 提升移动端用户支付成功率
        </div>
    </div>

    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            
            let deviceType = 'PC';
            let deviceFeatures = [];
            
            if (/Android/i.test(userAgent)) {
                deviceType = 'Android';
                deviceFeatures.push('Android设备');
            }
            if (/iPhone|iPad|iPod/i.test(userAgent)) {
                deviceType = 'iOS';
                deviceFeatures.push('iOS设备');
            }
            if (/MicroMessenger/i.test(userAgent)) {
                deviceFeatures.push('微信浏览器');
            }
            if (/AlipayClient/i.test(userAgent)) {
                deviceFeatures.push('支付宝客户端');
            }
            if (/Mobile/i.test(userAgent)) {
                deviceFeatures.push('移动浏览器');
            }

            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            
            document.getElementById('device-type').textContent = 
                `设备类型: ${deviceType} ${isMobile ? '(移动设备)' : '(桌面设备)'}`;
            
            document.getElementById('browser-info').textContent = 
                `特征: ${deviceFeatures.length > 0 ? deviceFeatures.join(', ') : '标准浏览器'}`;
            
            // 支付支持检测
            let paymentSupport = [];
            if (isMobile) {
                paymentSupport.push('移动端直接跳转');
                if (/MicroMessenger/i.test(userAgent)) {
                    paymentSupport.push('微信内支付优化');
                }
            } else {
                paymentSupport.push('PC端新窗口');
            }
            
            document.getElementById('payment-support').textContent = 
                `支付方式: ${paymentSupport.join(', ')}`;
        }

        // 测试微信支付
        function testWeChatPayment() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (isMobile) {
                showResult('🟢 移动端微信支付测试', 
                    '检测到移动设备，将使用代理页面确保二维码显示<br>' +
                    '• 服务器端伪装PC访问<br>' +
                    '• 优化移动端显示效果<br>' +
                    '• 确保二维码正常显示<br>' +
                    '<div class="code">代理URL: mobile_wechat_proxy.php</div>');
            } else {
                showResult('💻 PC端微信支付测试', 
                    'PC端将使用标准新窗口方式打开支付页面');
            }
        }

        // 测试支付宝支付
        function testAlipayPayment() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (isMobile) {
                showResult('🔵 移动端支付宝支付测试', 
                    '支付宝支付使用标准移动端流程<br>' +
                    '• 直接跳转到支付页面<br>' +
                    '• 无需特殊处理<br>' +
                    '• 兼容性良好');
            } else {
                showResult('💻 PC端支付宝支付测试', 
                    'PC端将使用新窗口方式打开支付页面');
            }
        }

        // 测试PC模式
        function testPCMode() {
            const payWindow = window.open('about:blank', '_blank');
            
            if (payWindow) {
                payWindow.close();
                showResult('✅ PC模式测试成功', 
                    '浏览器支持新窗口打开，PC端支付功能正常');
            } else {
                showResult('❌ PC模式测试失败', 
                    '浏览器阻止了弹窗，这在移动端很常见<br>' +
                    '这就是为什么需要移动端优化方案的原因');
            }
        }

        // 检查弹窗阻止
        function checkPopupBlocking() {
            const testWindow = window.open('', '_blank', 'width=1,height=1');
            
            if (testWindow) {
                testWindow.close();
                showResult('✅ 弹窗检查', '浏览器允许弹窗');
            } else {
                showResult('⚠️ 弹窗被阻止', 
                    '浏览器阻止了弹窗，移动端支付需要使用直接跳转方式');
            }
        }

        // 检查User-Agent
        function checkUserAgent() {
            const ua = navigator.userAgent;
            showResult('🔍 User-Agent检查', 
                `当前User-Agent:<br><div class="code">${ua}</div>` +
                '代理页面将伪装成PC端Chrome浏览器访问彩虹易支付');
        }

        // 测试代理页面
        function testProxyPage() {
            showResult('🔧 代理页面测试', 
                '代理页面功能：<br>' +
                '• 服务器端CURL请求<br>' +
                '• 伪装PC端User-Agent<br>' +
                '• 移动端显示优化<br>' +
                '• 强制显示二维码<br>' +
                '<div class="code">文件: mobile_wechat_proxy.php</div>');
        }

        // 显示测试结果
        function showResult(title, content) {
            document.getElementById('result-content').innerHTML = 
                `<h5>${title}</h5><p>${content}</p>`;
            document.getElementById('test-result').classList.add('show');
            
            // 滚动到结果区域
            document.getElementById('test-result').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'nearest' 
            });
        }

        // 页面加载时执行检测
        window.onload = function() {
            detectDevice();
        };
    </script>
</body>
</html>
