<?php
//百度sitemap插件 生成
function sitemaps(){
    $fmt = R('fmt', 'G');
    $allow_fmt = array('html', 'xml', 'txt');
    if( !in_array($fmt, $allow_fmt) ){core::error404();}

    $sitemaps_setting = $this->kv->get('le_sitemaps_setting');

    $cfg = $this->runtime->xget();
    $domain = $cfg['weburl'];
    $this->assign('cfg', $cfg);

    if($fmt == 'html'){
        //优先从缓存读取
        $le_sitemaps = $this->runtime->get('le_sitemaps_html');
        if($le_sitemaps){
            $Htmlx = $le_sitemaps;
        }else{
            $Htmlx = '';

            //分类
            $category = $this->category->find_fetch(array('type'=>0), array('cid'=>1));
            foreach ($category as $cate){
                $url = $this->category->category_url($cate);
                $Htmlx .= '<li><a href="' . $url . '" title="' . $cate['name'] . '" target="_blank">' . $cate['name'] . '</a></li>';
            }

            //内容URL 和 标签URL
            $models_arr = $this->models->find_fetch(array(), array('mid' => 1));
            foreach ($models_arr as $m) {
                $mid = $m['mid'];
                if($mid > 1){
                    $content_count = isset($sitemaps_setting['content_count_'.$mid]) ? (int)$sitemaps_setting['content_count_'.$mid] : 0;
                    $tag_count = isset($sitemaps_setting['tag_count_'.$mid]) ? (int)$sitemaps_setting['tag_count_'.$mid] : 0;
                    $table = $m['tablename'];

                    if ($content_count) {
                        $this->cms_content->table = 'cms_'.$table;
                        $list_arr = $this->cms_content->find_fetch(array(), array('id' => -1), 0, $content_count);
                        foreach ($list_arr as $v){
                            $url = $this->cms_content->content_url($v);
                            $Htmlx .= '<li><a href="' . $url . '" title="' . $v['title'] . '" target="_blank">' . $v['title'] . '</a></li>';
                        }
                    }

                    if ($tag_count) {
                        $this->cms_content_tag->table = 'cms_'.$table.'_tag';
                        $list_arr = $this->cms_content_tag->find_fetch(array(), array('tagid' => -1), 0, $tag_count);
                        foreach ($list_arr as $v){
                            $url = $this->cms_content->tag_url($mid, $v);
                            $Htmlx .= '<li><a href="' . $url . '" title="' . $v['name'] . '" target="_blank">' . $v['name'] . '</a></li>';
                        }
                    }
                }
            }

            if( $sitemaps_setting['life'] ){
                $this->runtime->set('le_sitemaps_html', $Htmlx, $sitemaps_setting['life']);
            }
        }

        $this->assign('htmlx', $Htmlx);

        $lastTime = date('Y-m-d H:i:s');
        $this->assign('lastTime', $lastTime);

        $GLOBALS['run'] = &$this;

        $this->_cfg = $this->runtime->xget();
        $_ENV['_theme'] = &$this->_cfg['theme'];
        $this->display('sitemaps_html.htm');
    }elseif($fmt == 'txt'){
        //优先从缓存读取
        $le_sitemaps = $this->runtime->get('le_sitemaps_txt');
        $le_sitemaps = false;
        if($le_sitemaps){
            $Txtx = $le_sitemaps;
        }else{
            $Txtx = $domain . PHP_EOL;
$domain ='http://'.$_SERVER['HTTP_HOST'];
            //分类
            $category = $this->category->find_fetch(array('type'=>0), array('cid'=>1));
            foreach ($category as $cate){
                $url = $this->category->category_url($cate);
                $Txtx .= $domain.$url . PHP_EOL;
            }

            //内容URL 和 标签URL
            $models_arr = $this->models->find_fetch(array(), array('mid' => 1));
            foreach ($models_arr as $m) {
                $mid = $m['mid'];
                if($mid > 1){
                    $content_count = isset($sitemaps_setting['content_count_'.$mid]) ? (int)$sitemaps_setting['content_count_'.$mid] : 0;
                    $tag_count = isset($sitemaps_setting['tag_count_'.$mid]) ? (int)$sitemaps_setting['tag_count_'.$mid] : 0;
                    $table = $m['tablename'];

                    if ($content_count) {
                        $this->cms_content->table = 'cms_'.$table;
                        $list_arr = $this->cms_content->find_fetch(array(), array('id' => -1), 0, $content_count);
                        foreach ($list_arr as $v){
                            $url = $this->cms_content->content_url($v);
                            $Txtx .= $domain.$url . PHP_EOL;
                        }
                    }

                    if ($tag_count) {
                        $this->cms_content_tag->table = 'cms_'.$table.'_tag';
                        $list_arr = $this->cms_content_tag->find_fetch(array(), array('tagid' => -1), 0, $tag_count);
                        foreach ($list_arr as $v){
                            $url = $this->cms_content->tag_url($mid, $v);
                            $Txtx .= $domain.$url . PHP_EOL;
                        }
                    }
                }
            }

            if( $sitemaps_setting['life'] ){
                $this->runtime->set('le_sitemaps_txt', $Txtx, $sitemaps_setting['life']);
            }
        }

        $this->assign('txt', $Txtx);

        $lastTime = date('Y-m-d H:i:s');
        $this->assign('lastTime', $lastTime);

        $GLOBALS['run'] = &$this;

        $this->_cfg = $this->runtime->xget();
        $_ENV['_theme'] = &$this->_cfg['theme'];
        $this->display('sitemaps_txt.htm');
    }elseif ($fmt == 'xml'){
    $today = gmdate('Y-m-d\TH:i:s\Z'); // 修改时间格式为UTC
    $sitemaps_setting = $this->kv->get('le_sitemaps_setting');
    $cfg = $this->runtime->xget();
    $domain = rtrim($cfg['weburl'], '/'); // 新增域名处理
    
    $le_sitemaps = false; // 保持原缓存逻辑结构
    if($le_sitemaps){
        $baidu_items = $le_sitemaps;
    }else{
        include_once PLUGIN_PATH.'le_zhanqunsitemaps_pro_v303/baidusitemap.class.php';
        $sitemapObj = new baidusitemap();
        
        // 首页（添加域名）
        $sitemapObj->baiduxml_item(
            $domain.'/', // 修正这里
            $today,
            $sitemaps_setting['baidu_changefreq_index'],
            $sitemaps_setting['baidu_priority_index']
        );

        // 处理XML文件路径（保持原有逻辑）
        $xmldz=ROOT_PATH.'sitemaps/txt2/*.xml';
        $xmlFiles = glob($xmldz);
        if (!empty($xmlFiles)) {
            foreach ($xmlFiles as $xurl) {
                $sitemapObj->baiduxml_item(
                    str_replace(ROOT_PATH, $domain.'/', $xurl), // 修正这里
                    $today,
                    $sitemaps_setting['baidu_changefreq_index'],
                    $sitemaps_setting['baidu_priority_index']
                );
            }
        }

        // 分类目录（添加域名）
        $category = $this->category->find_fetch(array('type'=>0), array('cid'=>1));
        foreach ($category as $cate){
            $sitemapObj->baiduxml_item(
                $domain . $this->category->category_url($cate), // 修正这里
                $today,
                $sitemaps_setting['baidu_changefreq_category'],
                $sitemaps_setting['baidu_priority_category']
            );
        }

        // 内容模型（保持原遍历逻辑）
        $models_arr = $this->models->find_fetch(array(), array('mid' => 1));
        foreach ($models_arr as $m) {
            $mid = $m['mid'];
            if($mid > 1){
                // 内容处理（添加域名）
                $content_count = $sitemaps_setting['content_count_'.$mid] ?? 0;
                if ($content_count) {
                    $this->cms_content->table = 'cms_'.$m['tablename'];
                    $list_arr = $this->cms_content->find_fetch(array(), array('id' => -1), 0, $content_count);
                    foreach ($list_arr as $v){
                        $sitemapObj->baiduxml_item(
                            $domain . $this->cms_content->content_url($v), // 修正这里
                            gmdate('Y-m-d\TH:i:s\Z', $v['lasttime']), // 时间格式修正
                            $sitemaps_setting['baidu_changefreq_content_'.$mid],
                            $sitemaps_setting['baidu_priority_content_'.$mid]
                        );
                    }
                }

                // 标签处理（添加域名）
                $tag_count = $sitemaps_setting['tag_count_'.$mid] ?? 0;
                if ($tag_count) {
                    $this->cms_content_tag->table = 'cms_'.$m['tablename'].'_tag';
                    $list_arr = $this->cms_content_tag->find_fetch(array(), array('tagid' => -1), 0, $tag_count);
                    foreach ($list_arr as $v){
                        $sitemapObj->baiduxml_item(
                            $domain . $this->cms_content->tag_url($mid, $v), // 修正这里
                            $today, // 标签页使用当前时间
                            $sitemaps_setting['baidu_changefreq_tag_'.$mid],
                            $sitemaps_setting['baidu_priority_tag_'.$mid]
                        );
                    }
                }
            }
        }
        
        $baidu_items = $sitemapObj->baidu_items;
        if( $sitemaps_setting['life'] ){
            $this->runtime->set('le_sitemaps_xml', $baidu_items, $sitemaps_setting['life']);
        }
    }
    
    // 保持原模板渲染逻辑
    $this->assign('baidu_items', $baidu_items);
    $GLOBALS['run'] = &$this;
    $this->_cfg = $this->runtime->xget();
    $_ENV['_theme'] = &$this->_cfg['theme'];
    $this->display('sitemaps_xml.htm');
}else{
        core::error404();
    }
}
//生成txt文件
function sitemaps_txt(){
    $start_time = microtime(1);
    $mid = max(2, R('mid','G'));
    $method = (int)R('method','G');
    $ym = R('ym','G');
    $cfg = $this->runtime->xget();
    if( !isset($cfg['table_arr'][$mid]) ){
        core::error404();
    }

    //txt文件存放目录
    $txt_dir = ROOT_PATH.'/sitemaps/'.$ym.'/txt'.$mid;
    if(!is_dir($txt_dir) && !mkdir($txt_dir, 0755, true)) {
        exit("创建文件夹{$txt_dir}失败！");
    }

    $sitemaps_setting = $this->kv->get('le_sitemaps_setting');
    $pagenum = isset($sitemaps_setting['count']) ? (int)$sitemaps_setting['count'] : 1000;
    $page = max(1, intval(R('page')));

    $table = $cfg['table_arr'][$mid];
    $this->cms_content->table = 'cms_'.$table;

    $total = $this->cms_content->count();
    $maxpage = max(1, ceil($total/$pagenum));
    if($page > $maxpage){
        exit('全部生成完毕！');
    }

    if($method){    //部分
        $files = glob($txt_dir.'/*.txt');
        $txtfile_count = count($files);

        if($txtfile_count > $maxpage){
            exit('全部生成完毕，无需生成！');
        }else{
            $page < $txtfile_count && $page = $txtfile_count;
            $txt_filename = $page.'.txt';
            $xml_filename = $page.'.xml';
            $list_arr = $this->cms_content->list_arr(array(), 'id', 1, ($page-1)*$pagenum, $pagenum, $total);
            // var_dump($list_arr);
            // die;
        }
    }else{  //全部
        $txt_filename = $page.'.txt';
        $xml_filename = $page.'.xml';
        $list_arr = $this->cms_content->list_arr(array(), 'id', 1, ($page-1)*$pagenum, $pagenum, $total);
        // var_dump($list_arr);
        //     die;
    }
    $xml1= <<<EOD
<?xml version="1.0" encoding="utf-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    
EOD;
    $xml2= <<<EOD
</urlset>
    
EOD;
    
    if(empty($list_arr)){
        exit('全部生成完毕！');
    }
    $urls = '';
    $urlsxmls='';
    foreach ($list_arr as $v){
        $urls .= 'http://'.$ym.$this->cms_content->content_url($v) . PHP_EOL;//组装txt
        $dateline=$v["dateline"];//时间
        $datexin = date('Y-m-d', $dateline);
        $urlsxml = 'http://'.$ym.$this->cms_content->content_url($v);//url
        $xmlContent = <<<EOD
    <url>
        <loc>{urls}</loc>
        <lastmod>{shijian}</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>

EOD;
            $newString = str_replace('{urls}', $urlsxml, $xmlContent);
            $newString = str_replace('{shijian}', $datexin, $newString);
            $urlsxmls .= $newString;
    }
    
    $txt_filepath = $txt_dir.'/'.$txt_filename;
    $xml_filepath = $txt_dir.'/'.$xml_filename;
    FW($txt_filepath, $urls);
    FW($xml_filepath, $xml1.$urlsxmls.$xml2);

    echo $txt_filename.'创建成功！<br>耗时：';
    echo number_format(microtime(1) - $start_time, 2).'秒！<br>';

    if($page == $maxpage){
        exit('全部生成完毕！');
    }

    $jumpurl = "{$cfg['weburl']}index.php?views-sitemaps_txt-ym-{$ym}-mid-{$mid}-method-{$method}-page-".++$page;
    echo '<script>setTimeout(function(){ window.location.href = "'.$jumpurl.'"; }, 500);</script>';
    exit();
}