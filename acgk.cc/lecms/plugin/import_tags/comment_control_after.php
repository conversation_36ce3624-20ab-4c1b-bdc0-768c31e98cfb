<?php
//导入TXT文件到草稿箱
function import_tags()
{
    set_time_limit(0);
    $start_time = microtime(1);
    $limit = (int) R('limit', 'G');
    $setting = $this->kv->get('import_tags_setting');
    $import_model = $setting['import_model'];
    $txt_dir = APP_PATH . $setting['txt_dir'];
    if (!is_dir($txt_dir)) {
        exit('txt文件夹路径不是一个文件夹哦！');
    }
    $maxcount = 50;
    //每次执行50条
    if ($limit) {
        $maxcount = $limit;
    }
    $dh = opendir($txt_dir);
    $txtfile_arr = array();
    while ($txtfile = readdir($dh)) {
        if ($txtfile == '.' || $txtfile == '..') {
            continue;
        }
        $ext = preg_replace('/\\W/', '', strtolower(substr(strrchr($txtfile, '.'), 1, 10)));
        if ($ext == 'txt') {
            $txtfile_arr[] = $txtfile;
            if (count($txtfile_arr) == $maxcount) {
                break;
            }
        }
    }
    closedir($dh);
    if (empty($txtfile_arr)) {
        exit($txt_dir . ' 无txt文件或者已全部导入！');
    }
    $succ = 0;
    if ($import_model == 1) {
        foreach ($txtfile_arr as $txt) {
            $txtfile_path = $txt_dir . '/' . $txt;
            if (is_file($txtfile_path)) {
                $title = substr($txt, 0, -4);
                $name = _file_get_contents($txtfile_path);
                //转为UTF-8
                $encoding = mb_detect_encoding($name, array('GB2312', 'GBK', 'UTF-16', 'UCS-2', 'UTF-8', 'BIG5', 'ASCII'));
                if ($encoding != 'UTF-8') {
                    $name = mb_convert_encoding($name, 'UTF-8', 'GB2312');
                }
                //$names = explode(PHP_EOL, $name);
                $names = explode("\r\n", $name);
                empty($name) && E(1, '标签名不得为空');
                $i = 0;
                foreach ($names as $key => $name) {
                    $i++;
                    $name = $name;
                    $data = array('name' => $name);
                    // 初始模型表名
                    $mid = max(2, R('mid', 'R'));
                    $modles = $this->models->get($mid);
                    if (empty($modles)) {
                        exit('mid值错误！');
                    }
                    $table = $modles['tablename'];
                    $this->cms_content->table = 'cms_' . $table;
                    $this->cms_content_tag->table = 'cms_' . $table . '_tag';
                    $this->cms_content_tag_data->table = 'cms_' . $table . '_tag_data';
                    if ($this->cms_content_tag->find_fetch(array('name' => $name), array(), 0, 1)) {
                        E(1, '导入标签已经存在啦');
                    }
                    $tag_data = $this->cms_content_tag->create($data);
                }
                if ($tag_data['err']) {
                    exit($tag_data['msg']);
                } else {
                    unlink($txtfile_path);
                    //成功后删除txt文件
                    $succ++;
                }
            }
        }
    }
    if ($limit) {
        exit("成功导入{$succ}个文件！");
    } else {
        echo '成功导入' . $succ . '文件<br>';
        echo number_format(microtime(1) - $start_time, 4);
        echo '<script>setTimeout(function(){ location.reload() }, 2000);</script>';
        exit;
    }
}