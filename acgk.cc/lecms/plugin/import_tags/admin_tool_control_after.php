<?php
//导入txt文件到草稿箱插件设置
function import_tags_setting()
{
    if (empty($_POST)) {
        $setting = $this->kv->get('import_tags_setting');
        $input = array();
        $arr_import_model = array(0 => '手动输入模式', 1 => 'txt导入模式');
        $input['import_model'] = form::get_radio_layui('import_model', $arr_import_model, $setting['import_model']);
        $input['add_tagname'] = form::get_textarea('add_tagname', $setting['add_tagname'], '', 'placeholder="启用手动输入模式，标签名一行一个，适合少量手动添加"');
        $input['txt_dir'] = form::get_text('txt_dir', $setting['txt_dir'], '', 'placeholder="启用txt导入模式，输入txt文件夹路径，适合大量导入"');
        $this->assign('input', $input);
        $this->display('import_tags_setting.htm');
    } else {
        $arr = array('import_model' => (int) R('import_model', 'P'), 'add_tagname' => R('add_tagname', 'P'), 'txt_dir' => R('txt_dir', 'P'));
        $import_model = (int) R('import_model', 'P');
        if ($import_model == 0) {
            $name = R('add_tagname', 'P');
            $names = explode("\r\n", $name);
            empty($name) && E(1, '标签名不得为空');
            $i = 0;
            foreach ($names as $key => $name) {
                $i++;
                $name = $name;
                $data = array('name' => $name);
                // 初始模型表名
                $mid = max(2, R('mid', 'R'));
                $modles = $this->models->get($mid);
                if (empty($modles)) {
                    exit('mid值错误！');
                }
                $table = $modles['tablename'];
                $this->cms_content->table = 'cms_' . $table;
                $this->cms_content_tag->table = 'cms_' . $table . '_tag';
                $this->cms_content_tag_data->table = 'cms_' . $table . '_tag_data';
				
				if($this->cms_content_tag->find_fetch(array('name'=>$name), array(), 0, 1)){
                E(1, '手动添加标签已经存在啦');
                }
				
                $tag_data = $this->cms_content_tag->create($data);
            }
            if ($tag_data) {
                E(0, '手动添加标签成功！');
            } else {
                E(1, '手动添加标签失败！');
            }
        }
        $this->kv->set('import_tags_setting', $arr);
        E(0, '保存成功！');
    }
}