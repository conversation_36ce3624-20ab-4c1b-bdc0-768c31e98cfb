{inc:header.htm}

<div class="layui-card">
	<div class="layui-card-header">插件设置</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?tool-import_tags_setting-ajax-1" method="post">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label required">导入模式</label>
					<div class="layui-input-block">{$input[import_model]}</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-block">
					<label class="layui-form-label">手动导入</label>
					<div class="layui-input-block">
						{$input[add_tagname]}
					</div>
				</div>
			</div>
           <pre class="layui-code"><font color="red">TXT导入注意事项：</font>存储tag的txt文件如果太大，可以先用TXT工具将一个txt分割成多个txt后，再存放TXT文件夹导入。TXT在线分行工具：https://uutool.cn/txt-incise/</pre>			
			<div class="layui-form-item">
				<div class="layui-block">
					<label class="layui-form-label">TXT文件夹</label>
					<div class="layui-input-block">
						{$input[txt_dir]}
						<div class="layui-form-mid layui-word-aux">txt文件夹路径不需要/结尾，例如plugin/import_tags/txt，txt文件要求是UTF-8，ANSI编码将自动执行转码 </div>
					</div>
					
				</div>
			</div>			
			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<button class="layui-btn" lay-submit lay-filter="form">保存</button>
				</div>
			</div>
		</form>
	</div>
</div>
<div class="layui-card">
	<div class="layui-card-header">成功导入后将删除txt文件，请注意数据备份！如何导入？</div>
	<div class="layui-card-body"> 
		<p>访问：域名/index.php?comment-import_tags，会一直循环导入，直至全部完成！</p>
		<p>访问：域名/index.php?comment-import_tags-limit-10，一次导入10个txt文件，不建议这个值太大，<font color="blue">推荐使用带limit参数执行定时任务！</font></p>
	</div>
</div>
<script type="text/javascript">
	layui.use('form', function(){
		var form = layui.form;
		var $ = layui.jquery;

		//监听提交
		form.on('submit(form)', function(){
			adminAjax.submit("#form");
		});
		$("#form").find("textarea").val("");//递交成功清空textarea
		return false;
	});
</script>
</body>
</html>
