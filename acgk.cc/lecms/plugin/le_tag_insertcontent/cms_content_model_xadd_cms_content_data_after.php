<?php
//Tag插入内容插件
$this->cms_content_tag->table = 'cms_'.$table.'_tag';
$tag_list = $this->cms_content_tag->find_fetch(array(), array('count' => -1));
if( $tag_list ){
    $patterns = $replacements = array();
    $newcontentstr = '';
    foreach ($tag_list as $tag){
        $patterns[] = '#(?=[^>]*(?=<(?!/a>)|$))'.$tag['name'].'#';
        $replacements[] = '<a class="taglink tag-'.$tag['tagid'].$tag['count'].'" href="'.$this->cms_content->tag_url($mid, $tag['name'], $tag['tagid']).'" target="_blank" title="'.$tag['name'].'">'.$tag['name'].'</a>';
    }
    if($patterns && $replacements && $contentstr){
        $newcontentstr = preg_replace($patterns, $replacements, $contentstr, 1);
    }

    if($newcontentstr){
        $cms_content_data['content'] = $newcontentstr;
    }

    unset($tag_list);
    unset($patterns);
    unset($replacements);
}