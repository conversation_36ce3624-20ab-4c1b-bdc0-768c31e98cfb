{inc:user/header.htm}
<style>
  .panel-post {position: relative;}
  .btn-post {position: absolute;right: 0;bottom: 10px;}
  .img-border {border-radius: 3px;box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);}
  .embed-responsive img {position: absolute;object-fit: cover;width: 100%;height: 100%;border: 0;}
  .comment-content {padding: 15px;background: #efefef;color: #444;}
  .panel-user h4 {font-weight: normal;font-size: 14px;}
  .float-right{float: right;}
  .pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span{border-bottom-right-radius:3px;border-top-right-radius:3px;}
  .pager li:first-child > a, .pager li:last-child > a, .pager li:first-child > span, .pager li:last-child > span{border-bottom-left-radius:3px;border-top-left-radius:3px;}
</style>
  <main class="content">
    <div id="content-container" class="container">
      <div class="row">
        {inc:user/menu.htm}
        <!--右侧主体部分 start-->
        <div class="col-md-9">
          <div class="panel panel-default">
            <div class="panel-body">
              <div class="panel-post">
                <h2 class="page-header">我的投稿</h2>
                <div style="position:absolute;bottom:12px;right:0;">
                  <div class="form-inline nice-validator n-default">
                    <a href="index.php?my-drafts-act-add.html" class="btn btn-primary"><i class="fa fa-edit"></i> 投稿</a>
                  </div>
                </div>
              </div>
              {if:empty($cms_arr)}
              <div class="alert alert-warning"><b>暂无投稿</b></div>
              {else}
              {loop:$cms_arr $v}
              <div class="row">
                <div class="col-md-3 text-center">
                  <a href="{$v[url]}" title="{$v[title]}" target="_blank" class="img-thumb">
                    <div class="embed-responsive embed-responsive-4by3 img-zoom">
                      <img src="{$v[pic]}" class="embed-responsive-item" alt="{$v[title]}" />
                    </div>
                  </a>
                </div>
                <div class="col-md-9">
                  <h4>
                    {$v[title]}
                  </h4>
                  <p class="comment-content">{$v[intro]}</p>
                  <div class="text-muted">发布时间 {$v[date]}
                    <div class="btn-group float-right">
                      <a href="index.php?my-drafts-act-edit-id-{$v[id]}.html" class="btn btn-primary btn-xs">编辑</a>
                      <button type="button" cmsid="{$v[id]}" class="del btn btn-danger btn-xs">删除</button>
                    </div>
                  </div>
                </div>
              </div>
              <hr/>
              {/loop}
              {/if}
              <div class="pager">{$pages}</div>
            </div>
          </div>
        </div>
        <!--右侧主体部分 end-->
      </div>
    </div>
  </main>
{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;

    $("#my-drafts").addClass("active");

    $(".del").click(function () {
      var id = $(this).attr("cmsid");
      layer.confirm('不可恢复，确定删除？', function () {
        $.post("index.php?my-drafts-act-del-ajax-1",{id: id},function(res){
          if(res.status){
            var icon = 1;
          }else{
            var icon = 5;
          }
          layer.msg(res.message, {icon: icon});
          if(res.status) setTimeout(function(){ window.location.reload(); }, 1000);
          return false;
        },'json');
      });
    });
  });
</script>
</body>
</html>