<?php
//草稿箱 - 我的投稿（列表、添加、编辑、删除）
function drafts(){
    $mid = 2;   //文章
    $this->assign('mid', $mid);

    $le_drafts_setting = $this->kv->get('le_drafts_setting');

    $act = isset($_GET['act']) ? trim($_GET['act']) : 'list';
    if($act == 'del'){
        $id = R('id','P');
        if( empty($id) ){
            $this->message(0, '参数错误！');
        }else{
            $data = $this->drafts->get($id);
            if( empty($data) || $data['uid'] != $this->_uid){
                $this->message(0, '内容不存在！');
            }else{
                $err = $this->drafts->xdelete($id);
                if($err){
                    $this->message(0, '删除失败，请重试！');
                }else{
                    $this->message(1, '删除成功！');
                }
            }
        }
    }elseif ($act == 'add'){
        if(!isset($le_drafts_setting['open_tougao']) && empty($le_drafts_setting['open_tougao'])){
            $this->message(0, '已关闭投稿功能！');
        }
        $disable_uid_arr = isset($le_drafts_setting['disable_uid']) ? explode(',', $le_drafts_setting['disable_uid']) : array();
        if( in_array($this->_uid, $disable_uid_arr) ){
            $this->message(0, '您已被禁止使用投稿功能！');
        }
        if(isset($le_drafts_setting['tougao_limit']) && !empty($le_drafts_setting['tougao_limit'])){
            $starttime = mktime(0,0,0,date('m'),date('d'),date('Y'));
            $where = array('uid'=>$this->_uid,'dateline'=>array('>'=>$starttime));
            $total = $this->drafts->find_count($where);
            if( $total >= (int)$le_drafts_setting['tougao_limit'] ){
                $this->message(0, '今日投稿已达上限，不能再投稿啦！');
            }
        }

        if( !empty($_POST) ){
            $models = $this->_cfg['table_arr'];
            $table = isset($models[$mid]) ? $models[$mid] : 'article';

            //日期形式的转为时间戳
            if(isset($_POST['dateline']) && strpos($_POST['dateline'], '-') !== false){
                $_POST['dateline'] = strtotime($_POST['dateline']);
            }

            $_POST['mid'] = $mid;
            $res = $this->drafts->xadd($_POST, $this->_user, $table);
            if( $res['err'] ){
                E(1, $res['msg']);
            }
            E(0, '投稿成功');
        }else{
            $data = array();
            $this->assign('data', $data);

            // 获取分类下拉框
            $category_arr = $this->category->get_category();

            $cidhtml = '<select name="cid" id="cid" class="form-control">';
            if(empty($category_arr)) {
                $cidhtml .= '<option value="0">没有分类</option>';
            }else{
                $cidhtml .= '<option value="0">选择分类</option>';
                foreach($category_arr as $curr_mid => $arr) {
                    if($mid != $curr_mid) continue;

                    foreach($arr as $v) {
                        if($v['contribute'] == 0){
                            continue;
                        }
                        $disabled = $v['type'] == 1 ? ' disabled="disabled"' : '';
                        $cidhtml .= '<option value="'.$v['cid'].'"'.$disabled.'>';
                        $cidhtml .= str_repeat("　", $v['pre']-1);
                        $cidhtml .= '|─'.$v['name'].($v['type'] == 1 ? '[频道]' : '').'</option>';
                    }
                }
            }
            $cidhtml .= '</select>';
            $this->assign('cidhtml', $cidhtml);

            $edit_cid_id = '-mid-'.$mid.'-nodb-1';
            $this->assign('edit_cid_id', $edit_cid_id);

            $this->assign('act', $act);
            $actName = '发布';
            $this->assign('actName', $actName);

            $my_drafts_url = $this->cms_content->user_url('drafts','my');
            $this->assign('my_drafts_url', $my_drafts_url);

            $this->assign_value('min', date('Y-m-d H:i:s'));

            $this->_cfg['titles'] = '发布投稿_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);
            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];

            $this->display('my_drafts_set.htm');
        }
    }elseif ($act == 'edit'){
        if( !empty($_POST) ){
            $models = $this->_cfg['table_arr'];
            $table = isset($models[$mid]) ? $models[$mid] : 'article';

            $_POST['mid'] = $mid;
            $res = $this->drafts->xedit($_POST, $this->_user, $table);
            if( $res['err'] ){
                E(1, $res['msg']);
            }
            E(0, '编辑成功');
        }else{
            $id = (int)R('id','G');
            $data = $this->drafts->get($id);
            if(empty($data) || $data['mid'] != $mid || $data['uid'] != $this->_uid){
                $this->message(0, '内容不存在！');
            }
            $this->assign('data', $data);

            // 获取分类下拉框
            $category_arr = $this->category->get_category();

            $cidhtml = '<select name="cid" id="cid" class="form-control">';
            if(empty($category_arr)) {
                $cidhtml .= '<option value="0">没有分类</option>';
            }else{
                $cidhtml .= '<option value="0">选择分类</option>';
                foreach($category_arr as $curr_mid => $arr) {
                    if($mid != $curr_mid) continue;

                    foreach($arr as $v) {
                        if($v['contribute'] == 0){
                            continue;
                        }
                        $disabled = $v['type'] == 1 ? ' disabled="disabled"' : '';
                        $cidhtml .= '<option value="'.$v['cid'].'"'.($v['type'] == 0 && $v['cid'] == $data['cid'] ? ' selected="selected"' : '').$disabled.'>';
                        $cidhtml .= str_repeat("　", $v['pre']-1);
                        $cidhtml .= '|─'.$v['name'].($v['type'] == 1 ? '[频道]' : '').'</option>';
                    }
                }
            }
            $cidhtml .= '</select>';
            $this->assign('cidhtml', $cidhtml);

            $edit_cid_id = '-mid-'.$mid.'-nodb-1';
            $this->assign('edit_cid_id', $edit_cid_id);

            $this->assign('act', $act);
            $actName = '编辑';
            $this->assign('actName', $actName);

            $my_drafts_url = $this->cms_content->user_url('drafts','my');
            $this->assign('my_drafts_url', $my_drafts_url);

            $this->_cfg['titles'] = '编辑投稿_'.$this->_cfg['webname'];
            $this->_var['topcid'] = -1;

            $this->assign('cfg', $this->_cfg);
            $this->assign('cfg_var', $this->_var);
            $GLOBALS['run'] = &$this;
            $_ENV['_theme'] = &$this->_cfg['theme'];
            $this->display('my_drafts_set.htm');
        }
    }elseif ($act == 'upload_pic'){ //上传缩略图
        $mid = max(2, (int)R('mid','R'));

        $data = $this->drafts->upload_pic($mid);
        echo json_encode($data);
        exit();
    }elseif ($act == 'list'){
        $where['mid'] = $mid;
        $where['uid'] = $this->_uid;

        // 初始分页
        $pagenum = 10;
        $total = $this->drafts->find_count($where);
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, intval(R('page'))));
        $pages = paginator::pages_bootstrap($page, $maxpage, 'index.php?my-drafts-act-list-mid-'.$mid.'-page-{page}'); //这里使用bootstrap风格
        $this->assign('pages', $pages);
        $this->assign('total', $total);

        $cms_arr = $this->drafts->list_arr($where, 'id', -1, ($page-1)*$pagenum, $pagenum, $total);
        foreach($cms_arr as &$v) {
            $this->cms_content->format($v, $mid);
            unset($v['url']);
        }
        $this->assign('cms_arr', $cms_arr);

        $this->_cfg['titles'] = '我的投稿_'.$this->_cfg['webname'];
        $this->_var['topcid'] = -1;

        $this->assign('cfg', $this->_cfg);
        $this->assign('cfg_var', $this->_var);
        $GLOBALS['run'] = &$this;
        $_ENV['_theme'] = &$this->_cfg['theme'];
        $this->display('my_drafts.htm');
    }
}