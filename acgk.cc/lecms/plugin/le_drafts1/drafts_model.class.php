<?php
defined('ROOT_PATH') or exit;

class drafts extends model {
    private $data = array();		// 防止重复查询
    public $crawl_post_user_agent = array(
        "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; AcooBrowser; .NET CLR 1.1.4322; .NET CLR 2.0.50727)",
        "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Acoo Browser; SLCC1; .NET CLR 2.0.50727; Media Center PC 5.0; .NET CLR 3.0.04506)",
        "Mozilla/4.0 (compatible; MSIE 7.0; AOL 9.5; AOLBuild 4337.35; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)",
        "Mozilla/5.0 (Windows; U; MSIE 9.0; Windows NT 9.0; en-US)",
        "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)",
        "Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 1.0.3705; .NET CLR 1.1.4322)",
        "Mozilla/4.0 (compatible; MSIE 7.0b; Windows NT 5.2; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.2; .NET CLR 3.0.04506.30)",
        "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/523.15 (KHTML, like Gecko, Safari/419.3) Arora/0.3 (Change: 287 c9dfb30)",
        "Mozilla/5.0 (X11; U; Linux; en-US) AppleWebKit/527+ (KHTML, like Gecko, Safari/419.3) Arora/0.6",
        "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.2pre) Gecko/20070215 K-Ninja/2.1.1",
        "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9) Gecko/20080705 Firefox/3.0 Kapiko/3.0",
        "Mozilla/5.0 (X11; Linux i686; U;) Gecko/20070322 Kazehakase/0.4.5",
        "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.9.0.8) Gecko Fedora/1.9.0.8-1.fc10 Kazehakase/0.5.6",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.56 Safari/535.11",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_3) AppleWebKit/535.20 (KHTML, like Gecko) Chrome/19.0.1036.7 Safari/535.20",
        "Opera/9.80 (Macintosh; Intel Mac OS X 10.6.8; U; fr) Presto/2.9.168 Version/11.52",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0",
    );
	function __construct() {
		$this->table = 'drafts';			// 表名
		$this->pri = array('id');	// 主键
		$this->maxid = 'id';		// 自增字段
	}

	// 暂时用些方法解决获取 cfg 值
	function __get($var) {
		if($var == 'cfg') {
			return $this->cfg = $this->runtime->xget();
		}else{
			return parent::__get($var);
		}
	}

	// 格式化内容数组
	public function format(&$v, $mid, $dateformat = 'Y-m-d H:i', $titlenum = 0, $intronum = 0) {
		// hook drafts_model_format_before.php

		if(empty($v)) return FALSE;

		$v['date'] = date($dateformat, $v['dateline']);
		$v['subject'] = $titlenum ? utf8::cutstr_cn($v['title'], $titlenum) : $v['title'];
		$intronum && $v['intro'] = utf8::cutstr_cn($v['intro'], $intronum);

        if( empty($v['pic']) ){
            $v['pic'] = $this->cfg['webdir'].'static/img/nopic.gif';
        }else{
            if( substr($v['pic'], 0, 2) != '//' && substr($v['pic'], 0, 4) != 'http' ){ //不是外链图片
                $v['pic'] = $this->cfg['webdir'].$v['pic'];
            }
        }

		// hook drafts_model_format_after.php
	}

	// 获取内容列表
	public function list_arr($where, $orderby, $orderway, $start, $limit, $total) {
		// 优化大数据量翻页
		if($start > 1000 && $total > 2000 && $start > $total/2) {
			$orderway = -$orderway;
			$newstart = $total-$start-$limit;
			if($newstart < 0) {
				$limit += $newstart;
				$newstart = 0;
			}
			$list_arr = $this->find_fetch($where, array($orderby => $orderway), $newstart, $limit);
			return array_reverse($list_arr, TRUE);
		}else{
			return $this->find_fetch($where, array($orderby => $orderway), $start, $limit);
		}
	}

	//检查数据
    public function check_post($post){
        if( empty($post['cid']) ){
            return '您没有选择分类哦';
        }elseif ( empty($post['title']) ){
            return '您的标题忘了填哦';
        }elseif ( strlen($post['content']) < 5 ){
            return '您的内容字数太少了哦';
        }elseif ( isset($post['alias']) && $post['alias'] && $err_msg = $this->only_alias->check_alias($post['alias']) ){
            return $err_msg;
        }elseif(isset($post['alias']) && $post['alias'] && $this->find_fetch_key(array('alias'=> $post['alias']))) {
            return '已经被其它草稿内容别名使用';
        }elseif ( isset($post['id']) && empty($post['id']) ){
            return 'ID不存在哦';
        }
        return '';
    }

    //添加草稿
    public function xadd($post = array(), $user = array(), $table = 'article'){
        // hook drafts_model_xadd_before.php

        //火车头数据过滤
        if(isset($post['alias']) && $post['alias'] == '[db:别名]') $post['alias'] = '';
        if(isset($post['tags']) && $post['tags'] == '[db:标签]' ) $post['tags'] = '';
        if(isset($post['pic']) && $post['pic'] == '[db:缩略图]' ) $post['pic'] = '';
        if(isset($post['flag']) && $post['flag'] == '[db:属性]' ) $post['flag'] = array();
        if(isset($post['intro']) && $post['intro'] == '[db:摘要]' ) $post['intro'] = '';
        if(isset($post['author']) && $post['author'] == '[db:作者]' ) $post['author'] = '';
        if(isset($post['source']) && $post['source'] == '[db:来源]' ) $post['source'] = '';
        if(isset($post['views']) && $post['views'] == '[db:浏览量]' ) $post['views'] = 0;
        if(isset($post['seo_title']) && $post['seo_title'] == '[db:SEO标题]' ) $post['seo_title'] = '';
        if(isset($post['seo_keywords']) && $post['seo_keywords'] == '[db:SEO关键词]' ) $post['seo_keywords'] = '';
        if(isset($post['seo_description']) && $post['seo_description'] == '[db:SEO描述]' ) $post['seo_description'] = '';
        if(isset($post['jumpurl']) && $post['jumpurl'] == '[db:跳转URL]' ) $post['jumpurl'] = '';
        if(isset($post['isremote']) && $post['isremote'] == '[db:远程图片本地化]' ) $post['isremote'] = 0;
        if(isset($post['iscomment']) && $post['iscomment'] == '[db:禁止评论]' ) $post['iscomment'] = 0;
        if(isset($post['mid']) && $post['mid'] == '[db:模型ID]' ) $post['mid'] = 2;
        //end

	    unset($post['id']);
	    $err = $this->cms_content->check_post($post);
	    if($err){
            return array('err'=>1 ,'msg'=>$err);
        }elseif(!isset($post['mid']) || $post['mid'] < 2){
            return array('err'=>1 ,'msg'=>'没有模型ID参数');
        }elseif(isset($post['alias']) && $post['alias'] && $this->find_fetch_key(array('alias'=> $post['alias']))) {
            return array('err'=>1 ,'msg'=>'已经被其它草稿内容别名使用');
        }

        $isremote = isset($post['isremote']) ? (int)$post['isremote'] : 0;
        $uid = isset($user['uid']) ? (int)$user['uid'] : 1;
        $contentstr = isset($post['content']) ? trim($post['content']) : '';
        // 如果摘要为空，自动生成摘要
        $intro = isset($post['intro']) ? trim($post['intro']) : '';
        $intro = auto_intro($intro, $contentstr);

        $tagstr = isset($post['tags']) ? trim($post['tags'], ", \t\n\r\0\x0B") : '';
        $flags = isset($post['flag']) ? (array)$post['flag'] : array();
        $author =isset($post['author']) ? trim($post['author']) : '';
        if( empty($author) ){
            $author = empty($user['author'] ) ? $user['username'] : $user['author'];
        }
        $auto_pic = isset($this->cfg['auto_pic']) ? (int)$this->cfg['auto_pic'] : 0;    //自动提取缩略图

        // hook drafts_model_xadd_info_after.php

        //分类检查
        $cid = isset($post['cid']) ? (int)$post['cid'] : 0;
        $categorys = $this->category->read($cid);
        if(empty($categorys)){
            return array('err'=>1 ,'msg'=>'分类不存在');
        }
        $mid = (int)$categorys['mid'];
        $models = $this->models->get($mid);
        if(empty($models) || $models['tablename'] != $table){
            return array('err'=>1 ,'msg'=>'分类ID非法');
        }

        // hook drafts_model_xadd_category_after.php

        $cms_content = array(
            'mid' => $mid,
            'cid' => $cid,
            'title' => isset($post['title']) ? trim(strip_tags($post['title'])) : '',
            'alias' => isset($post['alias']) ? trim($post['alias']) : '',
            'tags' => $tagstr,
            'intro' => $intro,
            'pic' => isset($post['pic']) ? trim($post['pic']) : '',
            'uid' => $uid,
            'author' => $author,
            'source' => isset($post['source']) ? trim($post['source']) : '',
            'dateline' => (isset($post['dateline']) && $post['dateline'] ) ? $post['dateline'] : $_ENV['_time'],
            'lasttime' => (isset($post['lasttime']) && $post['lasttime'] )  ? $post['lasttime'] : $_ENV['_time'],
            'ip' => ip2long($_ENV['_ip']),
            'iscomment' => isset($post['iscomment']) ? (int)$post['iscomment'] : 0,
            'flags' => implode(',', $flags),
            'seo_title' => isset($post['seo_title']) ? trim(strip_tags($post['seo_title'])) : '',
            'seo_keywords' => isset($post['seo_keywords']) ? trim(strip_tags($post['seo_keywords'])) : '',
            'seo_description' => isset($post['seo_description']) ? trim(strip_tags($post['seo_description'])) : '',
            'jumpurl' => isset($post['jumpurl']) ? trim($post['jumpurl']) : '',
            'views' => isset($post['views']) ? (int)$post['views'] : 0,
            'orderby' => isset($post['orderby']) ? (int)$post['orderby'] : 0,
        );
        // hook drafts_model_xadd_cms_content_after.php

        $endstr = '';
        if($isremote) {
            $endstr .= $this->cms_content->get_remote_img($table, $contentstr, $cms_content['uid'], $cid, 0, 0);
        }

        //提取内容图片做为缩略图
        if( empty($cms_content['pic']) && $auto_pic){
            $pattern = "/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/";
            preg_match_all($pattern,$contentstr,$match);
            if( isset($match[1]) ){
                $cms_content['pic'] = isset($match[1][0]) ? $match[1][0] : '';
            }
        }

        $cms_content['content'] = $contentstr;

        $id = $this->create($cms_content);
        if($id) {
            // hook drafts_model_xadd_cms_content_success.php

            return array('err'=>0, 'msg'=>'OK'.$endstr);
        }else{
            return array('err'=>1 ,'msg'=>'写入草稿表出错！'.$endstr);
        }
    }

    //编辑草稿
    public function xedit($post = array(), $user = array(), $table = 'article'){
        // hook drafts_model_xedit_before.php

        $id = isset($post['id']) ? (int)$post['id'] : 0;
        if( empty($id) ){
            return array('err'=>1 ,'msg'=>'ID参数错误');
        }
        $err = $this->cms_content->check_post($post);
        if($err){
            return array('err'=>1 ,'msg'=>$err);
        }elseif(!isset($post['mid']) || $post['mid'] < 2){
            return array('err'=>1 ,'msg'=>'没有模型ID参数');
        }

        $olddata = $this->get($id);
        if(empty($olddata)){
            return array('err'=>1 ,'msg'=>'草稿不存在');
        }

        if(isset($post['alias']) && $post['alias'] && $post['alias'] != $olddata['alias'] && $this->find_fetch_key(array('alias'=> $post['alias']))) {
            return array('err'=>1 ,'msg'=>'已经被其它草稿内容别名使用');
        }

        $isremote = isset($post['isremote']) ? (int)$post['isremote'] : 0;
        $uid = isset($user['uid']) ? (int)$user['uid'] : $olddata['uid'];
        $contentstr = isset($post['content']) ? trim($post['content']) : '';
        // 如果摘要为空，自动生成摘要
        $intro = isset($post['intro']) ? trim($post['intro']) : '';
        $intro = auto_intro($intro, $contentstr);

        $tagstr = isset($post['tags']) ? trim($post['tags'], ", \t\n\r\0\x0B") : '';
        $flags = isset($post['flag']) ? (array)$post['flag'] : array();
        $author =isset($post['author']) ? trim($post['author']) : '';
        if( empty($author) ){
            $author = empty($user['author'] ) ? $user['username'] : $user['author'];
        }
        $auto_pic = isset($this->cfg['auto_pic']) ? (int)$this->cfg['auto_pic'] : 0;    //自动提取缩略图

        // hook drafts_model_xedit_info_after.php

        //分类检查
        $cid = isset($post['cid']) ? (int)$post['cid'] : 0;
        $categorys = $this->category->read($cid);
        if(empty($categorys)){
            return array('err'=>1 ,'msg'=>'分类不存在');
        }
        $mid = (int)$categorys['mid'];
        $models = $this->models->get($mid);
        if(empty($models) || $models['tablename'] != $table){
            return array('err'=>1 ,'msg'=>'分类ID非法');
        }

        // hook drafts_model_xedit_category_after.php

        $cms_content = array(
            'id' => $id,
            'mid' => $mid,
            'cid' => $cid,
            'title' => isset($post['title']) ? trim(strip_tags($post['title'])) : '',
            'alias' => isset($post['alias']) ? trim($post['alias']) : '',
            'tags' => $tagstr,
            'intro' => $intro,
            'pic' => isset($post['pic']) ? trim($post['pic']) : '',
            'uid' => $uid,
            'author' => $author,
            'source' => isset($post['source']) ? trim($post['source']) : '',
            'dateline' => isset($post['dateline']) ? $post['dateline'] : $_ENV['_time'],
            'lasttime' => isset($post['lasttime']) ? $post['lasttime'] : $_ENV['_time'],
            'ip' => ip2long($_ENV['_ip']),
            'iscomment' => isset($post['iscomment']) ? (int)$post['iscomment'] : 0,
            'flags' => implode(',', $flags),
            'seo_title' => isset($post['seo_title']) ? trim(strip_tags($post['seo_title'])) : '',
            'seo_keywords' => isset($post['seo_keywords']) ? trim(strip_tags($post['seo_keywords'])) : '',
            'seo_description' => isset($post['seo_description']) ? trim(strip_tags($post['seo_description'])) : '',
            'jumpurl' => isset($post['jumpurl']) ? trim($post['jumpurl']) : '',
            'views' => isset($post['views']) ? (int)$post['views'] : 0,
            'orderby' => isset($post['orderby']) ? (int)$post['orderby'] : 0,
        );
        // hook drafts_model_xadd_cms_content_after.php

        $endstr = '';
        if($isremote) {
            $endstr .= $this->cms_content->get_remote_img($table, $contentstr, $cms_content['uid'], $cid, $id, 0);
        }

        //提取内容图片做为缩略图
        if( empty($cms_content['pic']) && $auto_pic ){
            $pattern = "/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/";
            preg_match_all($pattern,$contentstr,$match);
            if( isset($match[1]) ){
                $cms_content['pic'] = isset($match[1][0]) ? $match[1][0] : '';
            }
        }

        $cms_content['content'] = $contentstr;

        if($this->update($cms_content)) {
            // hook drafts_model_xedit_cms_content_success.php

            return array('err'=>0, 'msg'=>'OK'.$endstr);
        }else{
            return array('err'=>1 ,'msg'=>'更新草稿表出错！'.$endstr);
        }
    }

	// 内容关联删除 - 草稿未发布时删除
	public function xdelete($id) {
		// hook drafts_model_xdelete_before.php

		// 内容读取
		$data = $this->read($id);
		if(empty($data)) return '内容不存在！';

		// 删除缩略图
        if($data['pic'] && is_file(ROOT_PATH.$data['pic'])){
            unlink(ROOT_PATH.$data['pic']);
        }

        //判断内容里面是否有图片
        $contentstr = $data['content'];
        $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/";
        preg_match_all($pattern, $contentstr,$match);
        if( isset($match[1]) ){
            foreach ($match[1] as $imgurl){
                $path = str_replace($this->cfg['weburl'],'',$imgurl);
                $file = ROOT_PATH.$path;
                try{
                    is_file($file) && unlink($file);
                }catch(Exception $e) {}
            }
        }

		//删除主表
		$ret = $this->delete($id);
        // hook drafts_model_xdelete_after.php
		return $ret ? '' : '删除失败！';
	}

	// 发布草稿到正式表
    public function release($table = 'article', $drafts_id = 0, $setting = array(), $dingshi = 0, $high_release = 0){
        // hook drafts_model_release_before.php

        // 内容读取
        $data = $this->read($drafts_id);
        if(empty($data)) return array('err'=>1, 'msg'=>'内容不存在！');

        $endstr = '';

        if(isset($setting['check_title']) && !empty($setting['check_title'])){
            $this->cms_content->table = 'cms_'.$table;
            if( $this->cms_content->find_count(array('title'=>$data['title'])) ){
                $this->delete($drafts_id);
                return array('err'=>1, 'msg'=>$data['title'].' 标题已经存在啦！');
            }
        }

        empty($data['uid']) AND $data['uid'] = 1;

        //高级发布可能不不同模型混合发布，需要重新检查mid
        if($high_release){
            $table = isset($this->_cfg['table_arr'][$data['mid']]) ? $this->_cfg['table_arr'][$data['mid']] : '';
        }

        //插件设置本地化图片
        if(isset($setting['isremote']) && !empty($setting['isremote'])){
            $endstr .= $this->cms_content->get_remote_img($table, $data['content'], $data['uid'], $data['cid']);
        }

        $user = $this->user->get($data['uid']);

        //要用flag
        if($data['flags']){
            $data['flag'] = explode(',', $data['flags']);
        }else{
            $data['flag'] = array();
        }

        $this->cms_content_attach->table = 'cms_'.$table.'_attach';
        //本地缩略图写入附件表
        if($data['pic'] && strlen($data['pic']) < 255 && is_file(ROOT_PATH.$data['pic'])){
            $info = pathinfo(ROOT_PATH.$data['pic']);

            $attach_data = array(
                'cid' => $data['cid'],
                'uid' => $data['uid'],
                'id' => 0,
                'filename' => $info['filename'],
                'filetype' => $info['extension'],
                'filesize' => filesize(ROOT_PATH.$data['pic']),
                'filepath' => str_replace_once('upload/'.$table.'/', '', $data['pic']),
                'dateline' => $_ENV['_time'],
                'isimage' => 1,
            );

            if(!$this->cms_content_attach->create($attach_data)) {
                return array('err'=>1, 'msg'=>'写入附件表失败');
            }
        }

        //本地内容图写入附件表
        $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/";
        preg_match_all($pattern, $data['content'],$match);
        if( isset($match[1]) ){
            foreach ($match[1] as $imgurl){
                $path = str_replace($this->cfg['weburl'],'',$imgurl);
                $file = ROOT_PATH.$path;
                if( strlen($file) < 255 && is_file($file) ){
                    $info = pathinfo($file);
                    $attach_data = array(
                        'cid' => $data['cid'],
                        'uid' => $data['uid'],
                        'id' => 0,
                        'filename' => $info['filename'],
                        'filetype' => $info['extension'],
                        'filesize' => filesize($file),
                        'filepath' => str_replace_once(ROOT_PATH.'upload/'.$table.'/', '', $file),
                        'dateline' => $_ENV['_time'],
                        'isimage' => 1,
                    );
                    $this->cms_content_attach->create($attach_data);
                }
            }
        }

        if($dingshi){
            $data['lasttime'] = $data['dateline'];
        }else{
            //把草稿数据的时间改成当前
            $data['dateline'] = $data['lasttime'] = $_ENV['_time'];
        }

        unset($data['id']);
        $res = $this->cms_content->xadd($data, $user, $table);
        if($res['err']){
            return array('err'=>1, 'msg'=>$res['msg']);
        }

        if(isset($res['msg'])){
            $endstr .= $res['msg'];
        }

        $res = $this->delete($drafts_id);

        // hook drafts_model_release_after.php

        return array('err'=>0, 'msg'=>$endstr);
    }

    //草稿箱 上传缩略图  不上传到附件表
    public function upload_pic($mid = 2, $fileName = 'upfile'){
        $models = $this->models->get($mid);
        if( empty($models) ){
            return array('err'=>1, 'msg'=>'mid 参数错误！');
        }
        $table = $models['tablename'];

        $updir = 'upload/'.$table.'/';
        $config = array(
            'maxSize'=>$this->cfg['up_img_max_size'],
            'allowExt'=>$this->cfg['up_img_ext'],
            'upDir'=>ROOT_PATH.$updir,
        );

        $up = new upload($config, $fileName);
        $info = $up->getFileInfo();
        if($info['state'] == 'SUCCESS') {
            $path = $updir . $info['path'];   //相对路径
            $src_file = ROOT_PATH . $path;    //绝对路径

            $thumb = image::thumb_name($path);
            image::thumb($src_file, ROOT_PATH.$thumb, $models['width'], $models['height'], $this->cfg['thumb_type'], $this->cfg['thumb_quality']);

            $data = array(
                'err'=>0,
                'msg'=>'上传成功',
                'data'=>array(
                    'src'=> $thumb ,
                    'path' => $path,
                    'title'=>substr($info['name'],0, -(strlen($info['ext'])+1)), //不含后缀名
                )
            );

            return $data;
        }else{
            return array('err'=>1, 'msg'=>$info['state']);
        }
    }

    // hook drafts_model_after.php
}
