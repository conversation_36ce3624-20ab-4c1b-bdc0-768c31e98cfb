<?php
defined('ROOT_PATH') or exit;

class drafts_pycoy_control extends control{
    // 配置参数
    protected $pwd = 'tudou23';      // 接口密码
    protected $mid = 2;              // 固定模型ID
    protected $uid_range = [2,10];   // 用户ID随机范围
    
    // 映射表（对应Python的forum_configs）
    private $forum_map = [
        52 => ['cid' => 1, 'name' => '游戏'],
        43 => ['cid' => 2, 'name' => '动漫'],
        40 => ['cid' => 3, 'name' => '漫画']
    ];

    public function __construct(){
        // 验证密码
        if(trim(R('pwd')) !== $this->pwd){
            core::error404();
        }
        
        // 验证模型
        $this->table = $this->models->get_table($this->mid);
        if(empty($this->table)) core::error404();
    }

    public function dopost(){
        // 获取基础参数
        $fid = (int)R('fid', 'P');  // 新增fid参数
        $title = trim(strip_tags(R('title', 'P')));
        $content = trim(R('content', 'P'));
        
        // 验证必填字段
        if(empty($title)) exit('标题不能为空');
        if(strlen($content) < 10) exit('内容过短');
        if(!isset($this->forum_map[$fid])) exit('无效板块ID');
        
        // 获取分类配置
        $cid = $this->forum_map[$fid]['cid'];
        $category = $this->category->read($cid);
        if(empty($category) || $category['mid'] != $this->mid){
            exit('分类配置错误');
        }
        
        // 随机用户处理
        $uid = $this->get_random_user();
        $user = $this->user->get($uid);
        if(empty($user)){
            exit('用户'.$uid.'不存在');
        }

        // 构建数据
        $post = [
            'mid' => $this->mid,
            'cid' => $cid,
            'title' => $title,
            'content' => $content,
            'uid' => $uid,
            'author' => $user['username'],
            'dateline' => $_ENV['_time'],
            'lasttime' => $_ENV['_time'],
            'ip' => ip2long($_ENV['_ip']),
            'views' => rand(50, 999),
            'iscomment' => 1,
            'orderby' => rand(1,9999999)
        ];

        // 写入草稿
        if(!$this->drafts->create($post)){
            exit('写入草稿失败');
        }
        
        exit('发布成功');
    }

    private function get_random_user(){
        // 获取有效用户池
        $valid_users = [];
        for($i=$this->uid_range[0]; $i<=$this->uid_range[1]; $i++){
            if($this->user->get($i)){
                $valid_users[] = $i;
            }
        }
        
        if(empty($valid_users)){
            exit('没有可用发布用户');
        }
        
        return $valid_users[array_rand($valid_users)];
    }
}