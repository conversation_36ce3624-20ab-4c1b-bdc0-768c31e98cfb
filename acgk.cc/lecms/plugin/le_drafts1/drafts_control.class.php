<?php
defined('ROOT_PATH') or exit;

class drafts_control extends admin_control{
    public $_mid = 2;
    public $_table = 'article';
    public $_name = '文章';

    function __construct()
    {
        parent::__construct();

        $this->_mid = max(2, (int)R('mid','R'));
        $models = $this->models->get($this->_mid);
        empty($models) && $this->message(1, '模型不存在！');

        $this->_table = $models['tablename'];
        $this->_name = $models['name'];

        $this->assign('mid',$this->_mid);
        $this->assign('table',$this->_table);
        $this->assign('name',$this->_name);
    }

    // 内容管理
    public function index() {
        // hook admin_drafts_control_index_before.php

        // 获取分类下拉框
        $cidhtml = $this->category->get_cidhtml_by_mid($this->_mid, 0, '所有'.$this->_name);
        $this->assign('cidhtml', $cidhtml);

        // hook admin_drafts_control_index_after.php

        $this->display();
    }

    //获取内容table数据
    public function get_list(){
        //分页
        $page = isset( $_REQUEST['page'] ) ? intval($_REQUEST['page']) : 1;
        $pagenum = isset( $_REQUEST['limit'] ) ? intval($_REQUEST['limit']) : 15;

        //获取查询条件
        $cid = isset( $_REQUEST['cid'] ) ? intval($_REQUEST['cid']) : 0;
        $keyword = isset( $_REQUEST['keyword'] ) ? trim($_REQUEST['keyword']) : '';
        if($keyword) {
            $keyword = urldecode($keyword);
            $keyword = safe_str($keyword);
        }

        //组合查询条件
        $where['mid'] = $this->_mid;
        if( $cid ){
            $where['cid'] = $cid;
        }
        if( $keyword ){
            $where['title'] = array('LIKE'=>$keyword);
        }
        // hook admin_drafts_control_get_list_before.php
        //数据量
        $total = $this->drafts->find_count($where);

        //页数
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, $page));

        //数据列
        $data_arr = array();
        $cms_arr = $this->drafts->list_arr($where, 'id', -1, ($page-1)*$pagenum, $pagenum, $total);

        $category = $this->category->find_fetch(array('mid'=>$this->_mid), array('orderby'=>1));
        $category_key = 'category-cid-';

        foreach($cms_arr as &$v) {
            $this->drafts->format($v, $this->_mid);

            if(isset($category[$category_key.$v['cid']])){
                $v['cate'] = $category[$category_key.$v['cid']]['name'];
            }else{
                $v['cate'] = '';
            }

            $data_arr[] = $v;   //排序需要索引从0开始
        }
        // hook admin_drafts_control_get_list_after.php
        unset($cms_arr);

        //组合数据 输出到页面
        $arr = array(
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data_arr,
        );
        exit( json_encode($arr) );
    }

    //编辑表格字段
    public function set(){
        if( !empty($_POST) ){
            $field = trim( R('field','P') );
            $id = intval( R('id','P') );
            $value = trim( R('value','P') );
            $data = array(
                'id' => $id,
                $field => $value,
            );
            // hook admin_drafts_control_set_before.php

            if(!$this->drafts->update($data)) {
                E(1, '更新失败');
            }
            // hook admin_drafts_control_set_after.php
            E(0, '更新'.$field.'成功');
        }
    }

    //添加
    public function add() {
        // hook admin_drafts_control_add_before.php

        $uid = $this->_user['uid'];
        if(empty($_POST)) {
            $habits = (array)$this->kv->get('user_habits_uid_'.$uid);
            //默认值
            $data = array(
                'cid'=>isset($habits['drafts_last_add_cid']) ? (int)$habits['drafts_last_add_cid'] : 0,
                'views'=>0,
                'iscomment'=>0, //允许评论
                'author'=>empty($this->_user['author'] ) ? $this->_user['username'] : $this->_user['author'],
                'orderby'=>0,
            );

            $this->assign('data', $data);

            //分类下拉框
            $cidhtml = $this->category->get_cidhtml_by_mid($this->_mid, $data['cid']);
            $this->assign('cidhtml', $cidhtml);

            //属性
            $flaghtml = $this->cms_content->flag_html();
            $this->assign('flaghtml', $flaghtml);

            $edit_cid_id = '-mid-'.$this->_mid.'-nodb-1';
            $this->assign('edit_cid_id', $edit_cid_id);

            $this->assign_value('min', date('Y-m-d H:i:s'));

            $this->display('drafts_set.htm');
        }else{
            // hook admin_drafts_control_add_post_before.php

            //日期形式的转为时间戳
            if(isset($_POST['dateline']) && strpos($_POST['dateline'], '-') !== false){
                $_POST['dateline'] = strtotime($_POST['dateline']);
            }

            $res = $this->drafts->xadd($_POST, $this->_user, $this->_table);
            if( $res['err'] ){
                E(1, $res['msg']);
            }

            // 记住最后一次发布的分类ID。
            $habits = (array) $this->kv->get('user_habits_uid_'.$uid);
            $habits['drafts_last_add_cid'] = (int)R('cid', 'P');
            $this->kv->set('user_habits_uid_'.$uid, $habits);

            // hook admin_drafts_control_add_post_after.php

            E(0, '发布草稿成功');
        }
    }

    // 编辑
    public function edit(){
        // hook admin_drafts_control_edit_before.php
        $uid = $this->_user['uid'];
        if(empty($_POST)) {
            $id = intval(R('id'));
            $cid = intval(R('cid'));

            // 读取内容
            $data = $this->drafts->get($id);
            if(empty($data)) $this->message(0, '内容不存在！', -1);

            //分类下拉框
            $cidhtml = $this->category->get_cidhtml_by_mid($this->_mid, $cid);
            $this->assign('cidhtml', $cidhtml);

            //属性
            $flaghtml = $this->cms_content->flag_html($data['flags']);
            $this->assign('flaghtml', $flaghtml);

            $edit_cid_id = '-mid-'.$this->_mid.'-nodb-1';
            $this->assign('edit_cid_id', $edit_cid_id);

            $data['content'] = htmlspecialchars($data['content']);
            $data['intro'] = str_replace('<br />', "\n", strip_tags($data['intro'], '<br>'));

            $data['dateline'] = date('Y-m-d H:i:s', $data['dateline']);

            $this->assign('data', $data);

            $this->assign_value('min', date('Y-m-d H:i:s'));

            // hook admin_drafts_control_edit_after.php

            $this->display('drafts_set.htm');
        }else{
            // hook admin_drafts_control_edit_post_before.php

            //日期形式的转为时间戳
            if(isset($_POST['dateline']) && strpos($_POST['dateline'], '-') !== false){
                $_POST['dateline'] = strtotime($_POST['dateline']);
            }

            $res = $this->drafts->xedit($_POST, $this->_user, $this->_table);
            if( $res['err'] ){
                E(1, $res['msg']);
            }
            // hook admin_drafts_control_edit_post_after.php
            E(0, '编辑草稿成功');
        }
    }

    // 删除
    public function del() {
        // hook admin_drafts_control_del_before.php

        $id = (int) R('id', 'P');
        empty($id) && E(1, '内容ID不能为空！');

        // hook admin_drafts_control_del_after.php

        $err = $this->drafts->xdelete($id);
        if($err) {
            E(1, $err);
        }else{
            // hook admin_drafts_control_del_success.php
            E(0, '删除成功！');
        }
    }

    // 批量删除
    public function batch_del() {
        // hook admin_drafts_control_batch_del_before.php

        $id_arr = R('id_arr', 'P');
        if(!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach($id_arr as $v) {
                $err = $this->drafts->xdelete($v);
                if($err) $err_num++;

                // hook admin_drafts_control_batch_del_foreach.php
            }

            if($err_num) {
                E(1, $err_num.' 篇内容删除失败！');
            }else{
                // hook admin_drafts_control_batch_del_success.php
                E(0, '删除成功！');
            }
        }else{
            E(1, '参数不能为空！');
        }
    }

    //发布
    public function release(){
        // hook admin_drafts_control_release_before.php

        $id = (int) R('id', 'P');
        empty($id) && E(1, '内容ID不能为空！');

        $setting = $this->kv->get('le_drafts_setting');

        // hook admin_drafts_control_release_after.php

        $res = $this->drafts->release($this->_table, $id, $setting);
        if($res['err']) {
            E(1, $res['msg']);
        }else{
            // hook admin_drafts_control_release_success.php
            E(0, $res['msg']);
        }
    }

    //批量发布
    public function batch_release(){
        // hook admin_drafts_control_batch_release_before.php
        $endstr = '';
        $id_arr = R('id_arr', 'P');
        if(!empty($id_arr) && is_array($id_arr)) {
            $setting = $this->kv->get('le_drafts_setting');
            $err_num = $succ_num = 0;
            foreach($id_arr as $v) {
                $res = $this->drafts->release($this->_table, $v, $setting);
                if($res['err']){
                    $err_num++;
                }else{
                    $succ_num++;
                }

                // hook admin_drafts_control_batch_release_foreach.php
            }

            if($err_num) {
                E(1, $err_num.' 篇内容发布失败！');
            }else{
                // hook admin_drafts_control_batch_release_success.php
                E(0, '发布成功'.$endstr);
            }
        }else{
            E(1, '参数不能为空！');
        }
    }

    //批量修改分类
    public function batch_edit_cid(){
        $id_arr = R('id_arr', 'P');
        $cid = R('cid', 'P');
        if(!empty($id_arr) && is_array($id_arr) && !empty($cid)) {
            $categorys = $this->category->read($cid);
            if(empty($categorys)) E(1, '分类ID不存在');
            // 防止提交到其他模型的分类
            if($categorys['mid'] != $this->_mid) E(1, '分类ID非法！');
            if($categorys['type'] == 1) E(1, '频道分类不能发布内容！');

            // 初始模型表名
            $this->drafts->find_update(array('id'=>array("IN" => $id_arr)), array('cid'=>$cid));

            E(0, '修改成功！');
        }else{
            E(1, '参数不能为空！');
        }
    }

    //草稿箱上传缩略图，不存入附件表
    public function upload_pic(){
        $mid = max(2, (int)R('mid','R'));

        $data = $this->drafts->upload_pic($mid);
        echo json_encode($data);
        exit();
    }

    //设置
    public function setting(){
        if(empty($_POST)){
            $setting = $this->kv->get('le_drafts_setting');
            $input = array();
            $input['locoy_pwd'] = form::get_text('locoy_pwd', $setting['locoy_pwd']);
            $input['locoy_uid'] = form::get_textarea('locoy_uid', $setting['locoy_uid']);
            $input['corn_limit'] = form::get_text('corn_limit', $setting['corn_limit']);
            $input['in_hour'] = form::get_text('in_hour', $setting['in_hour']);
            $arr = array(
                0=>'ID升序',
                1=>'ID降序',
                2=>'随机(orderby升序)'
            );
            $input['orderway'] = form::get_radio_layui('orderway',$arr,$setting['orderway']);

            $arr1 = array(1=>'是',0=>'否');
            $input['isremote'] = form::get_radio_layui('isremote',$arr1,$setting['isremote']);
            $input['check_title'] = form::get_radio_layui('check_title',$arr1,$setting['check_title']);
            $input['high_release'] = form::get_textarea('high_release', $setting['high_release']);
            $input['open_tougao'] = form::get_radio_layui('open_tougao',$arr1,$setting['open_tougao']);
            $input['tougao_limit'] = form::get_number('tougao_limit', $setting['tougao_limit']);
            $input['disable_uid'] = form::get_textarea('disable_uid', $setting['disable_uid']);

            $this->assign('input', $input);
            $this->assign('pwd', $setting['locoy_pwd']);
            $this->display();
        }else{
            $arr = array(
                'locoy_pwd' =>trim(R('locoy_pwd','P')),//火车头免登录发布密码
                'locoy_uid'=>trim(R('locoy_uid','P')),
                'corn_limit'=>trim(R('corn_limit','P')),   //定时发布 每次发布数量
                'orderway'=>intval(R('orderway','P')),
                'isremote'=>intval(R('isremote','P')),
                'in_hour'=>R('in_hour','P'),
                'check_title'=>intval(R('check_title','P')),
                'high_release'=>R('high_release','P'),
                'open_tougao'=>intval(R('open_tougao','P')),
                'tougao_limit'=>intval(R('tougao_limit','P')),
                'disable_uid'=>R('disable_uid','P'),
            );

            $this->kv->set('le_drafts_setting', $arr);
            E(0, '保存成功！');
        }
    }

    // hook admin_drafts_control_after.php
}