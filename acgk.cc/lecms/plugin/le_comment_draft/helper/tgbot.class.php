<?php
defined('ROOT_PATH') or exit;

class tgbot {
    private $bot_token;
    private $chat_id;
    
    public function __construct($bot_token, $chat_id) {
        $this->bot_token = $bot_token;
        $this->chat_id = $chat_id;
    }
    
    public function sendMessage($message) {
        $url = "https://api.telegram.org/bot{$this->bot_token}/sendMessage";
        $data = [
            'chat_id' => $this->chat_id,
            'text' => $message,
            'parse_mode' => 'HTML'
        ];
        
        $options = [
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/x-www-form-urlencoded',
                'content' => http_build_query($data)
            ]
        ];
        
        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        
        return json_decode($result, true);
    }
}
