<?php
$maxcommentid = $this->cms_content_comment_draft->create($comment_data);
if($maxcommentid) {
    // 发送 Telegram 通知
    $bot_token = '**********************************************';
    $chat_id = '7966090073';
    
    $message = "新评论待审核:\n";
    $message .= "用户: " . $comment_data['author'] . "\n";
    $message .= "内容: " . $comment_data['content'] . "\n";
    $message .= "IP: " . long2ip($comment_data['ip']) . "\n";
    $message .= "时间: " . date('Y-m-d H:i:s', $comment_data['dateline']);
    
    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    $params = [
        'chat_id' => $chat_id,
        'text' => $message,
        'parse_mode' => 'HTML'
    ];
    
    // 发送请求到 Telegram API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $result = curl_exec($ch);
    curl_close($ch);
    
    $this->message(1, '发表评论成功，等待管理员审核！');
} else {
    $this->message(0, '写入评论表出错！');
}
