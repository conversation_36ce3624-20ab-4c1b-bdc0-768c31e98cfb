<?php
defined('ROOT_PATH') || exit;

$tableprefix = $_ENV['_config']['db']['master']['tablepre'];	//表前缀
$sql_table = "CREATE TABLE IF NOT EXISTS ".$tableprefix."cms_comment_draft (
  commentid int(10) unsigned NOT NULL AUTO_INCREMENT,
  mid tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '模型ID',
  id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '内容ID',
  uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID', 
  author varchar(20) NOT NULL DEFAULT '' COMMENT '作者',
  content varchar(255) NOT NULL DEFAULT '' COMMENT '评论内容',
  ip int(10) NOT NULL DEFAULT '0' COMMENT 'IP',
  dateline int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发表时间',	
  reply_commentid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '回复某评论ID',
  PRIMARY KEY  (commentid),
  KEY mid_id (mid,id),
  KEY ip (ip,commentid)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";
$this->db->query($sql_table);