<?php
defined('ROOT_PATH') or exit;

class comment_draft_control extends admin_control {
    public $_mid = 2;
    public $_table = 'article';
    public $_name = '文章';

    function __construct(){
        parent::__construct();

        $this->_mid = max(2, (int)R('mid','R'));
        $models = $this->models->get($this->_mid);
        empty($models) && $this->message(1, '内容不存在！');

        $this->_table = $models['tablename'];
        $this->_name = $models['name'];

        $this->assign('mid',$this->_mid);
        $this->assign('table',$this->_table);
        $this->assign('name',$this->_name);
    }
	
  	// 添加设置页面方法
    public function setting() {
        if($this->method == 'POST') {
            $bot_token = R('bot_token', 'P');
            $chat_id = R('chat_id', 'P');

            kv_set('comment_draft_bot_token', $bot_token);
            kv_set('comment_draft_chat_id', $chat_id);

            $this->message(0, '设置保存成功！');
        }

        $bot_token = kv_get('comment_draft_bot_token');
        $chat_id = kv_get('comment_draft_chat_id');

        $this->assign('bot_token', $bot_token);
        $this->assign('chat_id', $chat_id);
        $this->display('setting.htm');
    }

    // 内容管理
    public function index() {
        $midhtml = $this->cms_content_comment->get_commenthtml_mid($this->_mid, 'lay-filter="mid"');
        $this->assign('midhtml',$midhtml);

        $this->display();
    }

    //ajax获取列表
    public function get_list(){
        //分页
        $page = isset( $_REQUEST['page'] ) ? intval($_REQUEST['page']) : 1;
        $pagenum = isset( $_REQUEST['limit'] ) ? intval($_REQUEST['limit']) : 15;

        //获取查询条件
        $keyword = isset( $_REQUEST['keyword'] ) ? trim($_REQUEST['keyword']) : '';
        if($keyword) {
            $keyword = urldecode($keyword);
            $keyword = safe_str($keyword);
        }
        $uid = isset( $_REQUEST['uid'] ) ? trim($_REQUEST['uid']) : 0;
        $id = isset( $_REQUEST['id'] ) ? trim($_REQUEST['id']) : 0;

        //组合查询条件
        $where['mid'] = $this->_mid;
        if( $id ){
            $where['id'] = $id;
        }
        if( $uid ){
            $where['uid'] = $uid;
        }
        if( $keyword ){
            $where['name'] = array('LIKE'=>$keyword);
        }

        $total = $this->cms_content_comment_draft->find_count($where);

        //页数
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, $page));

        // 获取列表
        $data_arr = array();
        $cms_arr = $this->cms_content_comment_draft->list_arr($where, -1, ($page-1)*$pagenum, $pagenum, $total);

        $keys = array();
        foreach($cms_arr as $v) {
            $keys[] = $v['id'];
        }
        $this->cms_content->table = 'cms_'.$this->_table;
        $list_arr = $this->cms_content->mget($keys);

        foreach($cms_arr as &$v) {
            $this->cms_content_comment_draft->format($v, 'Y-m-d H:i', false);

            $key = 'cms_'.$this->_table.'-id-'.$v['id'];
            if(isset($list_arr[$key])){
                $v['title'] = $list_arr[$key]['title'];
            }else{
                $v['title'] = '无主题';
            }

            $data_arr[] = $v;   //排序需要索引从0开始
        }

        unset($cms_arr);
        //组合数据 输出到页面
        $arr = array(
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data_arr,
        );
        exit( json_encode($arr) );
    }

    //编辑表格字段
    public function set(){
        if( !empty($_POST) ){
            $field = trim( R('field','P') );
            $commentid = intval( R('commentid','P') );
            $value = trim( R('value','P') );
            $data = array(
                'commentid' => $commentid,
                $field => $value,
            );

            if(!$this->cms_content_comment_draft->update($data)) {
                E(1, '更新失败');
            }
            E(0, '更新'.$field.'成功');
        }
    }

    // 删除待审核评论
    public function del() {
        $commentid = (int) R('commentid', 'P');
        empty($commentid) && E(1, '评论ID不能为空！');

        $res = $this->cms_content_comment_draft->delete($commentid);
        if($res) {
            E(0, '删除成功！');
        }else{
            E(1, '删除失败！');
        }
    }

    // 批量删除待审核评论
    public function batch_del() {
        $id_arr = R('id_arr', 'P');
        if(!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach($id_arr as $commentid) {
                $res = $this->cms_content_comment_draft->delete($commentid);
                if(!$res) $err_num++;
            }

            if($err_num) {
                E(1, $err_num.' 条评论删除失败！');
            }else{
                E(0, '删除成功！');
            }
        }else{
            E(1, '参数不能为空！');
        }
    }

    //通过评论
    public function batch_adopt(){
        $id_arr = R('id_arr', 'P');
        if(!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach($id_arr as $commentid) {
                $err = $this->cms_content_comment_draft->adopt($this->_table, $commentid);
                if($err) $err_num++;
            }

            if($err_num) {
                E(1, $err_num.' 条评论审核失败！');
            }else{
                E(0, '审核成功！');
            }
        }else{
            E(1, '参数不能为空！');
        }
    }
}
