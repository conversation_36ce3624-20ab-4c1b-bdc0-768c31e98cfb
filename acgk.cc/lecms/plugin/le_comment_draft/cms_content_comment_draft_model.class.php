<?php
defined('ROOT_PATH') or exit;

class cms_content_comment_draft extends model {
    function __construct() {
        $this->table = 'cms_comment_draft';            // 表名
        $this->pri = array('commentid');    // 主键
        $this->maxid = 'commentid';        // 自增字段
    }

    // 格式化评论数组
    public function format(&$v, $dateformat = 'Y-m-d H:i:s', $humandate = TRUE) {
        if (empty($v)) return FALSE;

        $v['date'] = $humandate ? human_date($v['dateline'], $dateformat) : date($dateformat, $v['dateline']);
        $v['fullip'] = long2ip($v['ip']);
        $v['ip'] = substr($v['fullip'], 0, strrpos($v['fullip'], '.')) . '.*';
    }

    // 获取评论列表
    public function list_arr($where, $orderway, $start, $limit, $total) {
        // 优化大数据量翻页
        if ($start > 1000 && $total > 2000 && $start > $total / 2) {
            $orderway = -$orderway;
            $newstart = $total - $start - $limit;
            if ($newstart < 0) {
                $limit += $newstart;
                $newstart = 0;
            }
            $list_arr = $this->find_fetch($where, array('commentid' => $orderway), $newstart, $limit);
            return array_reverse($list_arr, TRUE);
        } else {
            return $this->find_fetch($where, array('commentid' => $orderway), $start, $limit);
        }
    }

    //通过评论
    public function adopt($table = 'article', $commentid = 0) {
        $this->cms_content->table = 'cms_' . $table;

        $comment_draft = $this->get($commentid);
        if (empty($comment_draft)) return '未读取到待审核评论！';

        $id = $comment_draft['id'];
        $data = $this->cms_content->read($id);
        if (empty($data)) return '未读取到内容信息！';

        unset($comment_draft['commentid']);
        $maxid = $this->cms_content_comment->create($comment_draft);
        if (!$maxid) {
            return '写入评论表出错！';
        }

        $data['comments']++;
        if (!$this->cms_content->update($data)) {
            return '写入内容表出错！';
        }

        $ret = $this->cms_content_comment_sort->set(array($comment_draft['mid'], $id), array(
            'cid' => $data['cid'],
            'comments' => $data['comments'],
            'lastdate' => $comment_draft['dateline'],
        ));
        if (!$ret) {
            return '写入评论排序表出错！';
        }

        $this->delete($commentid);

        // 发送邮件通知
        $this->sendEmailNotification($comment_draft);

        return '';
    }

    // 发送邮件通知
    private function sendEmailNotification($comment_draft) {
        $to = '<EMAIL>';
        $subject = '新评论待审核';
        $message = "有新的评论待审核:\n\n" .
                   "评论内容: " . $comment_draft['content'] . "\n" .
                   "评论者: " . $comment_draft['name'] . "\n" .
                   "评论时间: " . date('Y-m-d H:i:s', $comment_draft['dateline']);

        // 使用 PHPMailer 发送邮件
        $mail = new PHPMailer\PHPMailer\PHPMailer();
        $mail->isSMTP();
        $mail->Host = 'mail.sinpor.top';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'sinpor7000';
        $mail->SMTPSecure = 'ssl';
        $mail->Port = 465;

        $mail->setFrom('<EMAIL>', '评论系统');
        $mail->addAddress($to);
        $mail->Subject = $subject;
        $mail->Body = $message;

        if (!$mail->send()) {
            error_log('邮件发送失败: ' . $mail->ErrorInfo);
        }
    }
}
