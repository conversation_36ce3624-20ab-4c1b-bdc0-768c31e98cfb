<?php
defined('ROOT_PATH') || exit;
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];	//表前缀
$sql = "CREATE TABLE IF NOT EXISTS ".$tableprefix."kami (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` int(1) DEFAULT '-1' COMMENT '-1未使用,1已使用',
  `pwd` varchar(255) DEFAULT NULL COMMENT '卡密信息',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '使用时间',
  `uid` varchar(30) DEFAULT NULL COMMENT '会员ID',
  `value` int(6) DEFAULT '0',
  `type` int(1) DEFAULT '1' COMMENT '1金币2Vip',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";
$this->db->query($sql);
