<?php
 function vip(){
     if(!empty($_POST)){
         $kami=trim(R('kami','P'));
         $data1=$this->kami->find_fetch(array('pwd'=>$kami), array(), 0, 1);
         $data = $data1 ? current($data1) : array();
         if(empty($data)){
             E(1,'卡密错误或已被使用！');
         }
         if($data['status']==1){
             E(1,'该卡密已被使用！');
         }
        $msg='';
         $updateUserData=array(
             'uid'=>$this->_user['uid'],
         );
         if($data['type']==1){
             $msg.="{$data['value']}金币 ";
             $updateUserData['golds']=$this->_user['golds']+$data['value'];
         }elseif($data['type']==2){
             $msg.="{$data['value']}天Vip ";
             if($this->_user['groupid']==11 && $data['value']>0){
                 $updateUserData['groupid']=12;
                 $updateUserData['vip_times']=time()+$data['value']*86400;
             }elseif($this->_user['groupid']==12){
                 $updateUserData['vip_times']=$this->_user['vip_times']+$data['value']*86400;
             }
         }
         $updateKamiData=array(
             'id'=>$data['id'],
             'uid'=>$this->_user['uid'],
             'update_time'=>time(),
             'status'=>1,
         );
        if(!$this->user->update($updateUserData) || !$this->kami->update($updateKamiData)) {
            E(1, '卡密使用失败！');
        }
        E(0, '卡密使用成功，已为您增加：'.$msg);
     }
    $this->_cfg['titles'] = '升级会员';
    $this->_var['topcid'] = -1;
    $this->assign('cfg', $this->_cfg);
    $this->assign('cfg_var', $this->_var);
    $GLOBALS['run'] = &$this;
    $_ENV['_theme'] = &$this->_cfg['theme'];
    $this->display('templet/my_vip.htm');
}
function  kami()
{
    $this->_cfg['titles'] = '我使用的卡密';
    $where['uid'] = $this->_uid;
    // 初始分页
    $pagenum = 10;
    $total = $this->kami->find_count($where);
    $maxpage = max(1, ceil($total/$pagenum));
    $page = min($maxpage, max(1, intval(R('page'))));
    $pages = paginator::pages_bootstrap($page, $maxpage, $this->urls->user_url('kami', 'my', true)); //这里使用bootstrap风格
    $this->assign('pages', $pages);
    $this->assign('total', $total);
    $data = $this->kami->list_arr2($where, 'update_time', -1, ($page-1)*$pagenum, $pagenum, $total);
    foreach ($data as $k=>$v){
        $msg='';
        if($v['type']==1){
            $msg.="金币+{$v['value']} ";
        }elseif ($v['type']==2){
            $msg.="Vip+{$v['value']}天 ";
        }
        $data[$k]['msg']=$msg;
    }
    $this->assign('data', $data);
    $this->assign('cfg', $this->_cfg);
    $this->assign('cfg_var', $this->_var);
    $GLOBALS['run'] = &$this;
    $_ENV['_theme'] = &$this->_cfg['theme'];
    $this->display('templet/my_kami.htm');
}

