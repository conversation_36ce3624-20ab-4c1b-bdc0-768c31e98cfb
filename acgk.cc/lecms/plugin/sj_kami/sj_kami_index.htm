<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
            <legend>搜索</legend>
            <div>
                <form class="layui-form layui-form-pane">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">卡密类型</label>
                            <div class="layui-input-inline">
                                <select name="type">
                                    <option value="" selected="selected">全部</option>
                                    <option value="1">金币</option>
                                    <option value="2">VIP</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value="" selected="selected">全部</option>
                                    <option value="1">已使用</option>
                                    <option value="-1">未使用</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">卡密</label>
                            <div class="layui-input-inline">
                                <input placeholder="模糊搜索(特殊字符不支持)" autocomplete="off" type="text" class="layui-input" name="pwd" />
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜索</button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>
        <script type="text/html" id="toolbar">
            <div class="layui-btn-container">
                <a class="layui-btn layui-btn-sm layui-btn-normal" data-status="" lay-event="getList">全部</a>
                <a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="make">生成卡密</a> |
                <a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete">批量删除</a>
            </div>
        </script>
        <table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table>

        <script type="text/html" id="currentTableBar">
            <div class="layui-btn-group">
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
            </div>
        </script>
    </div>
</div>

<script type="text/javascript">
    layui.use(['form', 'layer', 'table', 'miniTab', 'jquery'], function () {
        var layer = layui.layer, form = layui.form, table = layui.table, miniTab = layui.miniTab, $ = layui.jquery;
        table.render({
            elem: '#data-table',
            url: 'index.php?sj_kami-get_list-',
            height: 'full-145',
            toolbar: '#toolbar',
            defaultToolbar: ['filter', 'exports', 'print'],
            cellMinWidth: 50,
            escape: false,
            cols: [[
                {type: "checkbox", width: 50, fixed: 'left'},
                {field: 'type', width: 130, title: '卡密类型', align: 'center'},
                {field: 'value', width: 150, title: '卡密内容', align: 'center'},
                {field: 'pwd', title: '卡密', align: 'left'},
                {field: 'create_time', width: 170, title: '创建时间', align: 'center'},
                {field: 'update_time', width: 170, title: '使用时间', align: 'center'},
                {field: 'status', title: '状态', width: 100, align: 'center', event: 'setStatus', style: 'cursor: pointer;'},
                {field: 'user_nickname', title: '使用会员', width: 100},
                {title: '操作', width: 140, toolbar: '#currentTableBar', align: "center"}
            ]],
            limits: [20, 50, 100, 500, 1000],
            limit: 15,
            page: true,
            order: [[4, 'desc']] // 按创建时间降序排序
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            // 执行搜索重载
            table.reload('data-table', {
                page: {curr: 1},
                where: {
                    type: data.field.type,
                    status: data.field.status,
                    pwd: data.field.pwd,
                }
            }, 'data');
            return false;
        });

        // 监听下拉框操作
        table.on('toolbar(data-table-filter)', function (obj) {
            if (obj.event === 'make') {
                miniTab.openNewTabByIframe({
                    href: "index.php?sj_kami-make",
                    title: "生成卡密",
                });
            } else if (obj.event === 'getList') {
                let type = $(this).data('status');
                table.reload('data-table', {
                    url: 'index.php?sj_kami-get_list-&status=' + type,
                    page: {
                        curr: 1
                    }
                }, 'data');
            } else if (obj.event === 'delete') {
                var checkStatus = table.checkStatus('data-table'),
                    data = checkStatus.data;
                var len = data.length;
                if (len == 0) {
                    layer.msg('请选择数据', {icon: 5});
                    return false;
                } else {
                    adminAjax.confirm('确定删除？', function () {
                        var id_arr = [];
                        for (var i in data) {
                            id_arr[i] = data[i]['id'];
                        }
                        adminAjax.postd("index.php?sj_kami-batch_del-ajax-1", {"id_arr": id_arr});
                    });
                }
            }
        });

        // 监听单元格编辑
        table.on('edit(data-table-filter)', function (obj) {
            var value = obj.value, data = obj.data, field = obj.field;
            adminAjax.postd("index.php?sj_kami-set-ajax-1", {"id": field.id, "field": field});
        });

        // 监听每一行的操作
        table.on('tool(data-table-filter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'delete') {
                adminAjax.confirm('确定删除？', function () {
                    adminAjax.postd("index.php?sj_kami-del-ajax-1", {"id": data.id});
                });
            } else if (obj.event === 'setStatus') {
                let status1 = data.status;
                let status_num = status1 == '未使用' ? 1 : -1;
                let status_text = status1 == '未使用' ? '已使用' : '未使用';
                adminAjax.confirm('是否将该卡密状态更改为:' + status_text, function () {
                    adminAjax.postd("index.php?sj_kami-set-ajax-1", {"id": data.id});
                });
            }
        });
    });
</script>
</body>
</html>