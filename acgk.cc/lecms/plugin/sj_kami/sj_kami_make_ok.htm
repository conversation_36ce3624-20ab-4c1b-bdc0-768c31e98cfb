
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>生成卡密</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
	<style>
.result {
	text-align: center;

}
.result .success svg {
	color: #32C682;
	text-align: center;
	margin-top: 40px;

}
.result .error svg {
	color: #f56c6c;
	text-align: center;
	margin-top: 40px;

}
.result .title {
	margin-top: 25px;

}
.result .desc {
	margin-top: 25px;
	width: 60%;
	margin-left: 20%;
	color: rgba(0, 0, 0, .45);
}
.result .content {
	margin-top: 20px;
	width: 80%;
	border-radius: 10px;
	background-color: whitesmoke;
	height: 200px;
	margin-left: 10%;
}
.result .action {
	padding-top: 10px;
	border-top: 1px whitesmoke solid;
	margin-top: 25px;
}
	</style>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<body>
		<div class="layui-card">
			<div class="layui-card-body">
				<div class="result">
					<div class="success">
					<svg viewBox="64 64 896 896" data-icon="check-circle" width="80px" height="80px" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0 0 51.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"></path><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path></svg>
				    </div>
					<h2 class="title">卡密生成完毕</h2>
					<p class="desc">
						你可以关闭此页面，或者选择再次生成！
					</p>
					<textarea class="layui-textarea" style="max-height: 500px;overflow-y: scroll;padding: 10px;    width: 600px; margin: 20px auto;">
						{$data}
					</textarea>
					<div class="action">
						<a class="layui-btn" href="index.php?sj_kami-make">再次生成</a>
					</div>
				</div>
			</div>
		</div>

</body>
</html>