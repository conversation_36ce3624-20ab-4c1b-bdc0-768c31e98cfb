<?php

defined('ROOT_PATH') or exit;

class sj_kami_control extends admin_control
{
    public function index()
    {
        $this->display();
    }

    public function make()
    {
        $this->display('sj_kami_make.htm');
    }
    public function make_do()
    {
        if (!empty($_POST)) {
            $sum = R('sum', 'P');
            if ($sum > 300) {
                E(1, '每次最多生成300份卡密！');
            }
            $type = R('type', 'P');
            $num = R('num', 'P');
            $kamiStr='';
            for ($i = 0; $i < $sum; $i++) {
                $data = [
                    'pwd' => $this->getrandom(),
                    'type'=>$type,
                    'value' => $num,
                    'create_time' => time()
                ];
                $id = $this->kami->create($data);
                if (!$id) {
                    E(1, '生成失败');
                }
                $kamiStr.=$data['pwd'].PHP_EOL;
            }
            $kamiStr=trim($kamiStr);
            $this->assign('data',$kamiStr);
            $this->display('sj_kami_make_ok.htm');die;
        }
    }
    public function get_list()
    {
        //分页
        $page = isset($_REQUEST['page']) ? intval($_REQUEST['page']) : 1;
        $pagenum = isset($_REQUEST['limit']) ? intval($_REQUEST['limit']) : 15;
        //获取查询类型
        $status = isset($_REQUEST['status']) ? trim($_REQUEST['status']) : '';
        $type = isset($_REQUEST['type']) ? trim($_REQUEST['type']) : '';
        $pwd = isset($_REQUEST['pwd']) ? trim($_REQUEST['pwd']) : '';
        $where = array();
        if (!empty($status)) {
            $where['status'] = $status;
        }
        if (!empty($type)) {
            $where['type'] = $type;
        }
        if (!empty($pwd)) {
            $where['pwd'] = array('LIKE'=>$pwd);;
        }
        //数据量
        if ($where) {
            $total = $this->kami->find_count($where);
        } else {
            $total = $this->kami->count();
        }

        //页数
        $maxpage = max(1, ceil($total / $pagenum));
        $page = min($maxpage, max(1, $page));
        // 获取列表
        $cms_arr = $this->kami->list_arr($where, 'id', -1, ($page - 1) * $pagenum, $pagenum, $total);
        //组合数据 输出到页面
        $arr = array(
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $cms_arr,
        );
        exit(json_encode($arr));
        die;
    }

    public function set()
    {
        if (!empty($_POST)) {
            $id = intval(R('id', 'P'));
            $data = $this->kami->get($id);
            if (empty($data)) E(1, '该卡密不存在');
            $status = $data['status'] == -1 ? 1 : -1;
            $data1 = array(
                'id' => $id,
                'status' => $status,
            );
            if ($status == 1) {
                $data1['update_time'] = time();
                $data1['uid'] = '后台操作';
            } else {
                $data1['update_time'] = '';
                $data1['uid'] = '';
            }
            if (!$this->kami->update($data1)) {
                E(1, '更改失败！');
            }
            E(0, '更改成功');
        }
    }

    // 删除
    public function del()
    {
        $id = (int)R('id', 'P');
        empty($id) && E(1, '参数错误');
        $res = $this->kami->delete($id);
        if (!$res) {
            E(1, '删除失败！');
        } else {
            E(0, '删除成功!');
        }
    }

    //批量删除
    public function batch_del()
    {
        $id_arr = R('id_arr', 'P');
        if (!empty($id_arr) && is_array($id_arr)) {
            $err_num = 0;
            foreach ($id_arr as $id) {
                $res = $this->kami->delete($id);
                if (!$res) $err_num++;
            }
            if ($err_num) {
                E(1, $err_num . '删除失败！');
            } else {
                E(0, '删除成功!');
            }
        } else {
            E(1, '参数错误');
        }
    }

    protected function getrandom($length = 36)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890@*#";
        return substr(str_shuffle($chars), 0, $length);
    }
}