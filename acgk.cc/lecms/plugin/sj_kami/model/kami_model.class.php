<?php

defined('ROOT_PATH') or exit;

class <PERSON><PERSON> extends model {

    function __construct() {
        $this->table = 'kami';	// 表名
        $this->pri = array('id');	// 主键
        $this->maxid = 'id';		// 自增字段
    }

    // 获取内容列表
    public function list_arr($where, $orderby, $orderway, $start, $limit, $total) {
        global $run;
        // 优化大数据量翻页
        if($start > 1000 && $total > 2000 && $start > $total/2) {
            $orderway = -$orderway;
            $newstart = $total-$start-$limit;
            if($newstart < 0) {
                $limit += $newstart;
                $newstart = 0;
            }
            $list_arr = $this->find_fetch($where, array($orderby => $orderway), $newstart, $limit);
            $data= array_reverse($list_arr, TRUE);
        }else{
            $data= $this->find_fetch($where, array($orderby => $orderway), $start, $limit);
        }
        $statusArr=array(-1=>'未使用',1=>'已使用');
        $typeArr=array(1=>'金币',2=>'Vip天数');
        $rsData = array();
        if(!empty($data)){
            foreach($data as $k=>$v){
                if($v['type']==1){
                    $v['value'].=' 金币';
                }
                if($v['type']==2){
                    $v['value'].=' 天VIP';
                }
                $v['type']=isset($typeArr[$v['type']])?$typeArr[$v['type']]:'-';
                $v['status']=isset($statusArr[$v['status']])?$statusArr[$v['status']]:'-';
                //$v['create_time']=empty($v['create_time'])?'-':date('Y-m-d H:i:s', $v['create_time']);
                //$v['update_time']=empty($v['update_time'])?'-':date('Y-m-d H:i:s', $v['update_time']);
                if(!empty($v['uid'])){
                    if(is_numeric($v['uid'])){
                        $user = $run->user->get($v['uid']);
                        $v['user_nickname']=!empty($user)?$user['username']:'会员id：'.$v['uid'];
                    }else{
                        $v['user_nickname']=$v['uid'];
                    }
                }
                $rsData[]=$v;
            }
        }
        return $rsData;
    }
    // 获取内容列表
    public function list_arr2($where, $orderby, $orderway, $start, $limit, $total) {
        global $run;
        // 优化大数据量翻页
        if($start > 1000 && $total > 2000 && $start > $total/2) {
            $orderway = -$orderway;
            $newstart = $total-$start-$limit;
            if($newstart < 0) {
                $limit += $newstart;
                $newstart = 0;
            }
            $list_arr = $this->find_fetch($where, array($orderby => $orderway), $newstart, $limit);
            $data= array_reverse($list_arr, TRUE);
        }else{
            $data= $this->find_fetch($where, array($orderby => $orderway), $start, $limit);
        }
        $statusArr=array(-1=>'未使用',1=>'已使用');
        $rsData = array();
        if(!empty($data)){
            foreach($data as $k=>$v){
                $v['status']=isset($statusArr[$v['status']])?$statusArr[$v['status']]:'-';
                $v['create_time']=empty($v['create_time'])?'-':date('Y-m-d H:i:s', $v['create_time']);
                $v['update_time']=empty($v['update_time'])?'-':date('Y-m-d H:i:s', $v['update_time']);
                $rsData[]=$v;
            }
        }
        return $rsData;
    }
    public function check($str)
    {

    }
}
