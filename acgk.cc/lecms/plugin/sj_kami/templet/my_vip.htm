{inc:user/header.htm}
<style>
  .basicinfo {margin: 15px 0;}
  .basicinfo .row > .col-xs-4 {padding-right: 0;}
  .basicinfo .row > div {margin: 5px 0;}
    @media (max-width: 768px) {
    .scroll-text {
    font-size: 20px;
    }
  }
</style>

<main class="content">
  <div id="content-container" class="container">
    <div class="row">
      {inc:user/menu.htm}
      <!--右侧主体部分 start-->
      <div class="col-md-9">
        <div class="panel panel-default">
          <div class="panel-body">
            <h2 class="page-header">
              开通VIP会员或充值金币
            </h2>
            <div class="row">

              <div class="col-md-9 col-sm-9 col-xs-12">
                <div class="ui-content">

                    <div class="list-group">
                      <button   class="list-group-item active">
                        请您对应的商品进行购买,自动发送卡密,激活立即到账！<span class="scroll-text" style="color: aqua;font-weight: 900;"  id="scrollToKaimi">有卡密?点我激活</span>
                      </button>
                      <div style="text-align: center;font-size: 18px;">
                      <a href="https://kaa.777723.xyz/buy/1" target="_blank" class="list-group-item">月付VIP[31天]&nbsp;<m style="color: red;">去购买</m></a>
                      <a href="https://kaa.777723.xyz/buy/2" target="_blank" class="list-group-item">季付VIP[90天]&nbsp;<m style="color: red;">去购买</m></a>
                      <a href="https://kaa.777723.xyz/buy/3" target="_blank" class="list-group-item">半年VIP[180天]&nbsp;<m style="color: red;">去购买</m></a>
                      <a href="https://kaa.777723.xyz/buy/4" target="_blank" class="list-group-item">年付VIP[365天]&nbsp;<m style="color: red;">去购买</m></a>
                      <li class="list-group-item">金币【点击数字购买】<br /><a href="https://kaa.777723.xyz/buy/5" target="_blank">10金币</a>&nbsp;|&nbsp;<a href="https://kaa.777723.xyz/buy/6" target="_blank">20金币</a>&nbsp;|&nbsp;<a href="https://kaa.777723.xyz/buy/7" target="_blank">32金币</a><br /><a href="https://kaa.777723.xyz/buy/8" target="_blank" >43金币</a>&nbsp;|&nbsp;<a href="https://kaa.777723.xyz/buy/9" target="_blank">55金币</a>&nbsp;|&nbsp;<a href="https://kaa.777723.xyz/buy/10" target="_blank" >66金币</a></li>
                      </div>
                      <div class="list-group-item active" style="background-color: #ff8f00;color: #000;font-weight: 900;">本页面中显示VIP-金币购买,点击跳转后看到不同的商品名称<br />购买后实际给您的商品是对应的VIP-金币卡密,请对应购买哦~<p style="color: red;background: #000;">以上链接购买后收到卡密,往下滑动看到卡密框填入,确认即到账</p></div>
                  	</div>
                    {hook:my_index_user_info_after.htm}
                </div>
              </div>
            </div>
            <div class="" style="background-color: #17889b1f;">
            <h2 class="page-header">
              激活卡密,填入您购买的卡密,确认即到账
            </h2>
            <div class="row">
              <div class="col-md-9 col-sm-9 col-xs-12">
              <form class="form-horizontal" >
                <div class="form-group">
                  <label for="kami" class="col-sm-2 control-label">卡密框</label>
                  <div class="col-sm-10">
                    <input type="text" class="form-control"  required id="kami" placeholder="请输入卡密,VIP和金币均可直接激活" style="border: 2px solid #000;">
                  </div>
                </div>
               <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-10" style="text-align: center;">
                    <a href="https://www.acgk.cc" target="_blank">如果点击【激活卡密】没反应,点我前往主站激活卡密</a><br>
                    <a href="https://www.acgk.cc" target="_blank">如果点击【激活卡密】没反应,点我前往主站激活卡密</a><br>
                    <button type="button" id="submit" class="btn btn-default" style="font-size: 15px;font-weight: 900;color: red;">激活卡密</button>
                  </div>
                </div>
              </form>
              </div>
            </div>
  		  </div>
          </div>
        </div>
      </div>
      <!--右侧主体部分 end-->
    </div>
  </div>
</main>

{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    
    // 添加点击事件
    $('#scrollToKaimi').on('click', function() {
      $('html, body').animate({
        scrollTop: $('.form-group').offset().top
      }, 500);
    });
   
    $("#my-vip").addClass("active");
    $('#submit').on('click',function(){
      var kami = $('#kami').val();
      if(kami.length < 30){
        layer.alert('请输入正确的卡密！')
        return false;
      }
      $.ajax({
          type: "POST",
          url: "",
          data: {kami: kami}, //表单传值
          dataType: 'json',
          success: function(data) {
              if (!data.err) {
                layer.alert(data.msg)
                $('#kami').val('');
              } else {
                layer.alert(data.msg)
              }
          }
      });
    });

    // 新增弹窗，显示 "测试测试"
    layer.open({
      type: 1,
      title: '卡密购买',
      area: ['300px', 'auto'], // 弹窗大小
      content: '<div style="padding: 20px; text-align: center;">金币购买资源存在失效风险,不能承受失效的请勿充值或开通VIP<br />金币购买资源存在失效风险,不能承受失效的请勿充值或开通VIP<br /><a href="/52926.html" target="_blank">卡密购买及激活, 点我跳转教程<br />卡密购买及激活, 点我跳转教程<br />卡密购买及激活, 点我跳转教程</a><br><br /><span class="list-group-item active" style="background-color: #ff8f00;color: #000;font-weight: 900;">金币购买资源存在失效风险,不能承受失效的请勿充值或开通VIP<br>金币购买资源存在失效风险,不能承受失效的请勿充值或开通VIP<p style="color: red;background: #000;">以上链接购买后收到卡密,往下滑动看到卡密框填入,确认即到账</p></span></div>',
      shadeClose: true, // 点击遮罩关闭弹窗
      btn: ['关闭'],
      btnAlign: 'c' // 按钮居中
    });
  });
</script>
</body>
</html>