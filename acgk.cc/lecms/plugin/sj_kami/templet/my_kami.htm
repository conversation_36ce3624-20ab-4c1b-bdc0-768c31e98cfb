{inc:user/header.htm}
<style>
  .embed-responsive img {position: absolute;object-fit: cover;width: 100%;height: 100%;border: 0;}
  .comment-content {padding: 15px;background: #efefef;color: #444;}
  .panel-user h4 {font-weight: normal;font-size: 14px;}
  .float-right{float: right;}
  .pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span{border-bottom-right-radius:3px;border-top-right-radius:3px;}
  .pager li:first-child > a, .pager li:last-child > a, .pager li:first-child > span, .pager li:last-child > span{border-bottom-left-radius:3px;border-top-left-radius:3px;}
</style>
  <main class="content">
    <div id="content-container" class="container">
      <div class="row">
        {inc:user/menu.htm}
        <!--右侧主体部分 start-->
        <div class="col-md-9">
          <div class="panel panel-default">
            <div class="panel-body">
              <h2 class="page-header">我使用的卡密</h2>
              {if:empty($data)}
              <div class="alert alert-warning"><b>{lang:no_data}</b></div>
              {else}
              <div class="table-responsive">
                <table class="table table-bordered">
                <thead>
                  <tr>
                    <th>卡密</th>
                    <th>使用结果</th>
                    <th>使用时间</th>
                  </tr>
                </thead>
                <tbody>
                {loop:$data $v}
                  <tr>
                    <td>{$v[pwd]}</td>
                    <td><span style="color: red"> {$v[msg]} </span></td>
                    <td>{$v[update_time]}</td>
                  </tr>
                {/loop}
                </tbody>
              </table>
              </div>
              {/if}
              <div class="pager">{$pages}</div>
            </div>
          </div>
        </div>
        <!--右侧主体部分 end-->
      </div>
    </div>
  </main>
{inc:user/footer.htm}
<script type="text/javascript">
  layui.use(['form'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$;
    $("#my-kami").addClass("active");

  });
</script>
</body>
</html>