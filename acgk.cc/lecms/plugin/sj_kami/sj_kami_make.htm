
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>生成卡密</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/css/public.css" media="all">
    <link rel="stylesheet" href="../../../static/layui/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <script src="../../../static/js/jquery.js" type="text/javascript"></script>
    <script src="../../../static/layui/lib/layui-v2.8.15/layui.js" charset="utf-8"></script>
    <script src="../../../static/layui/js/lay-config.js" charset="utf-8"></script>
    <script type="text/javascript">
        window.admin_lang = "zh-cn";
    </script>
    <script src="../../../static/admin/js/admin.js" charset="utf-8"></script>
</head>
<div class="layui-card">
	<div class="layui-card-header">生成设置</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?sj_kami-make_do" method="post">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label required">生成份数</label>
					<div class="layui-input-block">
						<input name="sum" type="number" value="100" class="layui-input" required="required" lay-verify="required"  />
					</div>
				</div>
			</div>
			<div class="layui-form-item" id="i_type">
				<label class="layui-form-label required">卡密类型</label>
				<div class="layui-input-block">
					<input lay-filter="type" name="type" type="radio" value="1" title="金币" checked>
					<input lay-filter="type" name="type" type="radio" value="2" title="VIP天数" >
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label required">数量</label>
					<div class="layui-input-inline">
						<input name="num" type="number" value="1" class="layui-input" required="required" lay-verify="required"  />
					</div>
					<div class="layui-form-mid layui-word-aux">1、“卡密类型”选择“金币”，这里的数量就是每份卡密增加多少金币！2、“卡密类型”选择“VIP天数”，这里的数量就是每份卡密给用户增加VIP多少天！</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" type="submit" lay-filter="form">开始生成</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
layui.use(['form','layer'], function(){
	var layer = layui.layer, form = layui.form;
	//监听提交
});
</script>
</body>
</html>
<
