<?php
//获取域名
$httphost = strtolower(R('HTTP_HOST', 'S'));
if(strpos($httphost, $this->data['cfg']['webdomain']) === false){   //不是主站
    $httphost_arr = explode('.', $httphost);
    $www = '';
    if(count($httphost_arr) > 2){
        $httphost_arr = array_slice($httphost_arr, -2);
        $httphost = implode('.', $httphost_arr);
        if($httphost_arr[0] == 'www'){
            $www = 'www';
        }
    }

    //优先从缓存里面读取
    $domain_pre = substr($httphost, 0, strpos($httphost, '.'));
    $cache_key = 'website_group_'.$domain_pre;
    $cache_key = str_replace('-','_',$cache_key);   //没有这句，如果域名带 - ，会导致报错
    $website_group = $this->get($cache_key);
    if(empty($website_group)){
        $website_group = $this->website_group->find_fetch(array('webdomain'=>$httphost), array('id' => 1), 0, 1);
        if($website_group){
            $website_group = current($website_group);
            $this->set($cache_key, $website_group, 7*86400);
        }
    }

    if($website_group){
        $website_group_content = _json_decode($website_group['content']);

        //主题和主题路径
        if(isset($website_group_content['theme']) && !empty($website_group_content['theme'])){
            $this->data['cfg']['theme'] = $website_group_content['theme'];
            $this->data['cfg']['tpl'] = $this->data['cfg']['webdir'].'view/'.$this->data['cfg']['theme'].'/';
        }

        //移动端主题和主题路径
        if(isset($website_group_content['theme_mobile']) && !empty($website_group_content['theme_mobile']) && $key == 'cfg' && !empty($this->data['cfg']['open_mobile_view']) && is_mobile()==1){
            $this->data['cfg']['theme'] = $website_group_content['theme_mobile'];
            $this->data['cfg']['mobile_view'] = $website_group_content['theme_mobile'];  //不加这个，多次调用后移动端模版会复原20241118
            $this->data['cfg']['tpl'] = $this->data['cfg']['webdir'].'view/'.$this->data['cfg']['theme'].'/';
        }

        //域名 ， 完整域名
        $this->data['cfg']['webdomain'] = $www.$website_group['webdomain'];
        $this->data['cfg']['webroot'] = http().$this->data['cfg']['webdomain']; //完整域名，不带安装目录
        $this->data['cfg']['weburl'] = http().$this->data['cfg']['webdomain'].$this->data['cfg']['webdir']; //完整域名，带安装目录

        //站点SEO信息
        if(isset($website_group_content['webname']) && !empty($website_group_content['webname'])){
            $this->data['cfg']['webname'] = $website_group_content['webname'];
        }
        if(isset($website_group_content['seo_title']) && !empty($website_group_content['seo_title'])){
            $this->data['cfg']['seo_title'] = $website_group_content['seo_title'];
        }
        if(isset($website_group_content['seo_keywords']) && !empty($website_group_content['seo_keywords'])){
            $this->data['cfg']['seo_keywords'] = $website_group_content['seo_keywords'];
        }
        if(isset($website_group_content['seo_description']) && !empty($website_group_content['seo_description'])){
            $this->data['cfg']['seo_description'] = $website_group_content['seo_description'];
        }

        //内容URL格式
        if(isset($website_group_content['link_show_type']) && !empty($website_group_content['link_show_type'])){
            //主站是灵活型（link_show_end = ''），分站不是灵活型
            if($this->data['cfg']['link_show_type'] == 7 && $website_group_content['link_show_type'] != 7){
                $this->data['cfg']['link_show_end'] = $_ENV['_config']['url_suffix'];
            }
            $this->data['cfg']['link_show_type'] = (int)$website_group_content['link_show_type'];
        }

        //模板伪静态标识
        if(isset($website_group_content['theme_bj']) && !empty($website_group_content['theme_bj'])){
            $this->data['cfg']['theme_bj'] = $website_group_content['theme_bj'];
        }

        //备案信息
        if(isset($website_group_content['beian']) && !empty($website_group_content['beian'])){
            $this->data['cfg']['beian'] = $website_group_content['beian'];
        }

        //统计代码
        if(isset($website_group_content['tongji'])){
            $this->data['cfg']['tongji'] = $website_group_content['tongji'];
        }

        //模板logo名，需要模板支持
        if(isset($website_group_content['logoname']) && !empty($website_group_content['logoname'])){
            $this->data['cfg']['logoname'] = $website_group_content['logoname'];
        }

        if(isset($website_group_content['copyright']) && !empty($website_group_content['copyright'])){
            $this->data['cfg']['copyright'] = $website_group_content['copyright'];
        }

        //关闭网站
        if(isset($website_group_content['close_website'])){
            $this->data['cfg']['close_website'] = (int)$website_group_content['close_website'];
        }

    }
    unset($website_group);
    unset($www);
    unset($cache_key);
}