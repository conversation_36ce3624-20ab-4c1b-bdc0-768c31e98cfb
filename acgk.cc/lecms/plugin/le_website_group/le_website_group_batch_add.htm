{inc:header.htm}

<div class="layui-card">
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?le_website_group-{$_GET['action']}-ajax-1" method="post">

			<div class="layui-form-item">
				<label class="layui-form-label required">批量添加</label>
				<div class="layui-input-block">
					<textarea name="content" placeholder="一行一个，格式如下" class="layui-textarea" lay-verify="required"></textarea>
					<div class="layui-form-mid layui-word-aux">网站域名$网站名称$SEO标题$SEO关键词$SEO描述$网站模板$伪原创标识$内容URL$备案号</div>
				</div>

			</div>

			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
				</div>
			</div>
		</form>
		<blockquote class="layui-elem-quote">内容URL请填写数字0~8</blockquote>
	</div>
</div>

<script type="text/javascript">
	layui.use(['form','layer', 'miniTab'], function () {
		var layer = layui.layer, form = layui.form, miniTab = layui.miniTab;

		//监听提交
		form.on('submit(form)', function () {
			adminAjax.postform('#form',function (data) {
				var json = toJson(data);
				if( json.err == 0 ){
					var icon = 1;
				}else{
					var icon = 5;
				}
				layer.msg(json.msg, {icon: icon});
				if(json.err==0) {
					setTimeout(function(){
						miniTab.reloadIframe('index.php?le_website_group-index');
						miniTab.deleteCurrentByIframe();
					}, 1500);
				}
			});
			return false;
		});
	});
</script>
</body>
</html>
