<?php
defined('ROOT_PATH') || exit;

/**
 * 站群数据列表
 * @param string orderby 排序方式
 * @param int orderway 降序(-1),升序(1)
 * @param int start 开始位置
 * @param int limit 显示几条
 * @return array
 */
function block_website_group($conf) {
	global $run;

	$where = array();

	// hook block_website_group_before.php

    $orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline')) ? $conf['dateline'] : 'id';
    $orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1;
    $start = _int($conf, 'start');
    $limit = _int($conf, 'limit', 10);

	$list_arr = $run->website_group->find_fetch($where, array($orderby => $orderway), $start, $limit);
	foreach($list_arr as &$v){
	    $v['url'] = '//'.$v['webdomain'];
        $arr = _json_decode($v['content']);
        $v['title'] = isset($arr['webname']) ? $arr['webname'] : $v['webdomain'];
    }

	// hook block_website_group_after.php

    return array('list'=>$list_arr);
}
