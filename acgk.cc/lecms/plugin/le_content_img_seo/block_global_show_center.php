<?php
//内容图片自动加alt
if( isset($run->_cfg['le_content_img_seo']) && !empty($run->_cfg['le_content_img_seo']) ){
    $pattern="/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/";
    preg_match_all($pattern, $_show['content'], $match);

    $find_arr = array('{title}', '{cate_name}', '{webname}', '{count}');    //查找的参数
    if( isset($match[0]) ){
        $img_count = 1;

        foreach ($match[0] as $k=>$img){
            $replace_arr = array("{$_show['title']}", "{$run->_var['name']}", "{$run->_cfg['webname']}", $img_count);   //查找的参数 替换值
            $new_alt = str_replace($find_arr, $replace_arr, $run->_cfg['le_content_img_seo']);  //替换后的值

            if( stripos($img, "alt=") != false ){    //图片本身含有alt= 参数，则替换参数值
                $img_new = preg_replace('/alt=["\'](.*?)["\']/', 'alt="'.$new_alt.'"', $img);
            }else{  //图片不含alt参数，则加上alt
                $img_new = str_replace_once('<img', '<img alt="'.$new_alt.'" ', $img);
            }

            //去掉title属性
            if( stripos($img_new, "title=") != false ){
                $img_new = preg_replace('/title=["\'](.*?)["\']/', '', $img_new);
            }

            //外链图片？
            if( strpos($img_new, $run->_cfg['webdomain']) === false ){
                $img_new = str_replace_once('<img', '<img rel="external nofollow" referrerpolicy="no-referrer" ', $img_new);
            }

            $_show['content'] = str_replace_once($img, $img_new, $_show['content']);

            $img_count++;
        }
        unset($match[0]);
    }
    unset($find_arr);
}