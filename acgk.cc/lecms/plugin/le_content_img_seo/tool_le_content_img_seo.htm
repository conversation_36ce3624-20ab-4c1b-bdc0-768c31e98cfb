{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">内容图片自动加alt 设置</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?tool-le_content_img_seo-ajax-1" method="post">
			<div class="layui-form-item">
				<label class="layui-form-label required">参数</label>
				<div class="layui-input-block">
					{$input[le_content_img_seo]}
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">保存</button>
				</div>
			</div>
		</form>
	</div>
</div>
<div class="layui-card">
	<div class="layui-card-header">说明</div>
	<div class="layui-card-body">
		<blockquote class="layui-elem-quote">
			<p>{title} 表示内容标题</p>
			<p>{count} 表示第几张图片</p>
			<p>{cate_name} 表示内容所属分类名</p>
			<p>{webname} 表示网站名称</p>
		</blockquote>
	</div>
</div>
<script type="text/javascript">
	layui.use('form', function(){
		var form = layui.form;

		//监听提交
		form.on('submit(form)', function(){
			adminAjax.submit("#form");
		});
	});
</script>
</body>
</html>
