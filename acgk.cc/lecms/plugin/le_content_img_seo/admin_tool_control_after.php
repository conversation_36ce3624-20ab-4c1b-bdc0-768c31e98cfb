<?php
//内容图片自动加alt 插件设置
function le_content_img_seo(){
    if(empty($_POST)){
        $cfg = $this->kv->xget('cfg');
        $input = array();
        $input['le_content_img_seo'] = form::get_text('le_content_img_seo', $cfg['le_content_img_seo']);

        $this->assign('input', $input);
        $this->display();
    }else{
        _trim($_POST);
        $le_content_img_seo = R('le_content_img_seo', 'P');
        if(empty($le_content_img_seo)){
            E(1, '不能为空哦！');
        }
        $this->kv->xset('le_content_img_seo', $le_content_img_seo, 'cfg');

        $this->kv->save_changed();
        $this->runtime->delete('cfg');

        E(0, '修改成功！');
    }
}
