{inc:header.htm}
<style>
	.layui-elem-field legend{font-size: 16px;}
</style>
<div class="layui-card">
	<div class="layui-card-header">草稿箱插件</div>
	<div class="layui-card-body">
		<div class="layui-tab">
			<ul class="layui-tab-title">
				<li class="layui-this">设置</li>
				<li>使用</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<form id="form" class="layui-form" action="index.php?drafts-setting-ajax-1" method="post">
						<fieldset class="layui-elem-field">
							<legend>发布设置</legend>
							<div class="layui-field-box">
								<div class="layui-form-item">
									<label class="layui-form-label">密码</label>
									<div class="layui-input-inline">
										{$input[locoy_pwd]}
									</div>
									<div class="layui-form-mid layui-word-aux">安全校验：火车头接口和定时发布</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">UID</label>
									<div class="layui-input-block">
										{$input[locoy_uid]}
										<div class="layui-form-mid layui-word-aux">发布用户UID（格式一：1，格式二：1,2,3，格式三：1~10 三种格式，后两种格式表示随机一个UID，uid必须是要真实存在的）</div>
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">发布数量</label>
									<div class="layui-input-inline">
										{$input[corn_limit]}
									</div>
									<div class="layui-form-mid layui-word-aux">定时发布草稿，每次发布草稿数量（优先级低于高级发布和limit参数）</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">发布方式</label>
									<div class="layui-input-block">
										{$input[orderway]}
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">指定时间</label>
									<div class="layui-input-inline">
										{$input[in_hour]}
									</div>
									<div class="layui-form-mid layui-word-aux">英文逗号隔开，指定时间内发布（0~23）不在该时间段，不发布草稿，留空表示不限制</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">高级发布</label>
									<div class="layui-input-block">
										{$input[high_release]}
										<div class="layui-form-mid layui-word-aux">格式（分类ID|发布数量）：cid|count,cid|count</div>
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">本地化</label>
									<div class="layui-input-inline">
										{$input[isremote]}
									</div>
									<div class="layui-form-mid layui-word-aux">发布到正式内容时，内容图片本地化。（一般不建议开启，特别是批量发布）</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">标题去重</label>
									<div class="layui-input-inline">
										{$input[check_title]}
									</div>
									<div class="layui-form-mid layui-word-aux">发布到正式内容时，判断标题是否重复，重复就删除草稿不发布（影响效率，大批量发布或者大数据时，不建议开启）</div>
								</div>
							</div>
						</fieldset>
						<fieldset class="layui-elem-field">
							<legend>投稿设置</legend>
							<div class="layui-field-box">
								<div class="layui-form-item">
									<label class="layui-form-label">开启投稿</label>
									<div class="layui-input-inline">
										{$input[open_tougao]}
									</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">限制数量</label>
									<div class="layui-input-inline">
										{$input[tougao_limit]}
									</div>
									<div class="layui-form-mid layui-word-aux">每个用户当天投稿数量限制，0表示不限制</div>
								</div>
								<div class="layui-form-item">
									<label class="layui-form-label">禁止UID</label>
									<div class="layui-input-block">
										{$input[disable_uid]}
										<div class="layui-form-mid layui-word-aux">禁止某用户投稿，多个用,隔开</div>
									</div>
								</div>
							</div>
						</fieldset>

						<div class="layui-form-item">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="form">保存</button>
							</div>
						</div>
					</form>
				</div>
				<div class="layui-tab-item">
					<blockquote class="layui-elem-quote">mid-2 表示是文章模型，其他模型请自行修改2为对应的mid值（内容管理-模型管理）</blockquote>
					<fieldset class="layui-elem-field">
						<legend>获取分类接口（火车头发布获取分类）</legend>
						<div class="layui-field-box">
							<pre class="layui-code">域名/index.php?drafts_locoy-category-mid-2-pwd-{$pwd}</pre>
						</div>
					</fieldset>

					<fieldset class="layui-elem-field">
						<legend>发布接口（火车头发布内容到草稿箱）</legend>
						<div class="layui-field-box">
							<pre class="layui-code">域名/index.php?drafts_locoy-dopost-mid-2-pwd-{$pwd}</pre>
						</div>
					</fieldset>

					<fieldset class="layui-elem-field">
						<legend>定时任务发布草稿到正式内容（无limit参数则使用设置的数量）</legend>
						<div class="layui-field-box">
							<pre class="layui-code">域名/index.php?drafts_locoy-docorn-mid-2-pwd-{$pwd}-limit-5</pre>
						</div>
					</fieldset>

					<fieldset class="layui-elem-field">
						<legend>根据草稿发布时间，定时任务发布草稿到正式内容（需要在发布时间小于当前系统时间，跟设置参数无关）</legend>
						<div class="layui-field-box">
							<pre class="layui-code">域名/index.php?drafts_locoy-docorntime-mid-2-pwd-{$pwd}-limit-5</pre>
						</div>
					</fieldset>
				</div>
			</div>
		</div>

	</div>
</div>

<script type="text/javascript">
	layui.use('form', function(){
		var form = layui.form;

		//监听提交
		form.on('submit(form)', function(){
			adminAjax.submit("#form");
		});
	});
</script>
</body>
</html>
