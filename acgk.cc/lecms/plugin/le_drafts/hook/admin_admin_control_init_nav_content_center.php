<?php
defined('ROOT_PATH') or exit;

$models_arr = $this->models->get_models();
unset($models_arr['models-mid-1']); //去掉单页模型

$son_menu = array();
foreach ($models_arr as $model){
    $son_menu[] = array('title' => $model['name'].'草稿', 'href' => 'index.php?drafts-index-mid-'.$model['mid'], 'icon' => isset($model['icon']) ? $model['icon'] : '', 'target' => '_self');
}

$menu['menuInfo']['content']['child'][] = array(
    'title' => '草稿箱',
    'icon' => 'fa fa-trash',
    'href' => '',
    'target' => '_self',
    'child' =>$son_menu,
);