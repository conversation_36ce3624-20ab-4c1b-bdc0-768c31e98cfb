{inc:header.htm}
<div class="layuimini-container">
	<div class="layuimini-main">
		<fieldset class="table-search-fieldset">
			<legend>搜索</legend>
			<div>
				<form class="layui-form layui-form-pane">
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">分类</label>
							<div class="layui-input-inline cids">
								{$cidhtml}
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">标题</label>
							<div class="layui-input-inline">
								<input placeholder="请输入标题" autocomplete="off" type="text" class="layui-input" name="keyword" />
							</div>
						</div>
						<div class="layui-inline">
							<button type="submit" class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
						</div>
					</div>
				</form>
			</div>
		</fieldset><!--搜索 end-->
		<script type="text/html" id="toolbar">
			<div class="layui-btn-container">
				<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="add">添加{$name}草稿</button>
				<button class="layui-btn layui-btn-sm" lay-event="release">批量发布</button>
				<button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete">批量删除</button>
				<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="cids">修改分类</button>
			</div>
		</script><!--添加 删除 end-->
		<table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table><!--列表 end-->
		<script type="text/html" id="currentTableBar">
			<div class="layui-btn-group">
				<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="release">发布</a>
				<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
			</div>
		</script><!--列表每一行的数据操作 end-->
	</div>
</div>

<script type="text/javascript">
	var mid = "{$mid}";
	var table = "{$table}";
	var name = "{$name}";

	layui.use(['form','layer', 'table', 'miniTab'], function () {
		var layer = layui.layer, form = layui.form, table = layui.table, miniTab = layui.miniTab;

		// 数据列
		table.render({
			elem: '#data-table',
			url: 'index.php?drafts-get_list-mid-'+mid+'-',
			height: 'full-145',
			toolbar: '#toolbar',
			defaultToolbar: ['filter', 'exports', 'print'],
			cellMinWidth: 50,
			cols: [[
				{type: "checkbox", width: 50, fixed: 'left'},
				{field: 'id', width: 70, title: 'ID', sort: true, align: 'center'},
				{field: 'title', title: '标题', edit: 'text'},
				{field: 'cate', width: 120, title: '所属分类', align: 'center'},
				{field: 'tags', title: '标签', edit: 'text'},
				{field: 'author', width: 120, title: '作者', align: 'center'},
				{field: 'views', width: 100, title: '浏览量', sort: true, edit: 'number', align: 'center'},
				{field: 'orderby', width: 100, title: '排序值', sort: true, edit: 'number', align: 'center'},
				{field: 'date', width: 145, title: '发布时间', align: 'center'},
				{title: '操作', width: 140, toolbar: '#currentTableBar', align: "center"}
			]],
			limits: [10, 15, 20, 25, 50, 100],
			limit: 15,
			page: true
		});
		// 监听搜索操作
		form.on('submit(data-search-btn)', function (data) {
			//执行搜索重载
			table.reload('data-table', {
				page: {curr: 1}
				, where: {
					cid: data.field.cid,
					keyword: data.field.keyword
				}
			}, 'data');
			return false;
		});
		/**
		 * toolbar监听事件 table列表 头部的操作
		 */
		table.on('toolbar(data-table-filter)', function (obj) {
			if (obj.event === 'add') {  // 监听添加操作
				miniTab.openNewTabByIframe({
					href:"index.php?drafts-add-mid-"+mid,
					title:"添加"+name+"草稿",
				});
			} else if (obj.event === 'delete') {  // 监听删除操作
				var checkStatus = table.checkStatus('data-table')
						, data = checkStatus.data;
				var len = data.length;
				if(len == 0){
					layer.msg('请选择数据！',{icon:5});
					return false;
				}else{
					layer.confirm('删除不可恢复，确定删除？', function () {
						var id_arr = [];
						for (var i in data) {
							id_arr[i] = data[i]['id'];
						}
						adminAjax.postd("index.php?drafts-batch_del-mid-"+mid+"-ajax-1", {"id_arr": id_arr});
					});
				}
			} else if (obj.event === 'release') {  // 监听发布操作
				var checkStatus = table.checkStatus('data-table')
						, data = checkStatus.data;
				var len = data.length;
				if(len == 0){
					layer.msg('请选择数据！',{icon:5});
					return false;
				}else{
					layer.confirm('确定发布？', function () {
						var id_arr = [];
						for (var i in data) {
							id_arr[i] = data[i]['id'];
						}
						adminAjax.postd("index.php?drafts-batch_release-mid-"+mid+"-ajax-1", {"id_arr": id_arr});
					});
				}
			} else if (obj.event === 'cids') {  // 监听修改分类操作
				var checkStatus = table.checkStatus('data-table')
						, data = checkStatus.data;
				var len = data.length;
				if(len == 0){
					layer.msg('请选择数据！',{icon:5});
					return false;
				}else{
					var s = '<select name="op_cid">'+$(".cids #cid").html()+'</select>';
					layer.confirm(s, function () {
						var op_cid = $("select[name='op_cid']").val();
						if(op_cid == 0){
							layer.msg('请选择分类！',{icon:5});
							return false;
						}else{
							var id_arr = [];
							for (var i in data) {
								id_arr[i] = data[i]['id'];
							}
							adminAjax.postd("index.php?drafts-batch_edit_cid-mid-"+mid+"-ajax-1", {"cid": op_cid,"id_arr": id_arr});
						}
					});
				}
			}
		});


		//监听单元格编辑
		table.on('edit(data-table-filter)', function(obj){
			var value = obj.value //得到修改后的值
					,data = obj.data //得到所在行所有键值
					,field = obj.field; //得到字段
			adminAjax.postd("index.php?drafts-set-ajax-1", {"mid":mid, "id":data.id, "field":field, "value":value});
		});
		//监听每一行的操作
		table.on('tool(data-table-filter)', function (obj) {
			var data = obj.data;

			if (obj.event === 'edit') {
				miniTab.openNewTabByIframe({
					href:'index.php?drafts-edit-mid-'+mid+'-id-'+data.id+'-cid-'+data.cid,
					title:"编辑"+name+"草稿",
				});
			} else if (obj.event === 'release') {
				layer.confirm('确定发布？', function () {
					adminAjax.postd("index.php?drafts-release-ajax-1", {"mid":mid, "id": data.id});
				});
			} else if (obj.event === 'delete') {
				layer.confirm('删除不可恢复，确定删除？', function () {
					adminAjax.postd("index.php?drafts-del-ajax-1", {"mid":mid, "id":data.id});
				});
			}
		});

	});

</script>
</body>
</html>
