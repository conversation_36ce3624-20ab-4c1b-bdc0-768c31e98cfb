{inc:user/header.htm}
<link rel="stylesheet" href="{$cfg[webdir]}static/layui/lib/layui-v2.8.15/css/layui.css" media="all">
<style>
  .panel-post {position: relative;}
  .btn-post {position: absolute;right: 0;bottom: 10px;}
  .img-border {border-radius: 3px;box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);}
  .embed-responsive img {position: absolute;object-fit: cover;width: 100%;height: 100%;border: 0;}
  .comment-content {padding: 15px;background: #efefef;color: #444;}
  .panel-user h4 {font-weight: normal;font-size: 14px;}
  .float-right{float: right;}
  .pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span{border-bottom-right-radius:3px;border-top-right-radius:3px;}
  .pager li:first-child > a, .pager li:last-child > a, .pager li:first-child > span, .pager li:last-child > span{border-bottom-left-radius:3px;border-top-left-radius:3px;}
  body {
    padding-top: 60px;
    font-size: 14px;
    background: #f4f6f8;
    height: 100%;
    line-height: 1.5715;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'liga';
    -webkit-text-size-adjust: 100%;
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Ubuntu,Helvetica Neue,Helvetica,Arial,PingFang SC,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Source Han Sans CN,sans-serif;
    font-weight: 400;
    color: #616161;
  }
</style>
  <main class="content">
    <div id="content-container" class="container">
      <div class="row">
        <div class="col-md-12">
          <div class="panel panel-default">
            <div class="panel-body">
              <div class="panel-post">
                <h2 class="page-header">{$actName}投稿</h2>
                <div style="position:absolute;bottom:12px;right:0;">
                  <div class="form-inline nice-validator n-default">
                    <a href="index.php?my-drafts.html" class="btn btn-primary"><i class="fa fa-book"></i> 返回</a>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-xs-12">
                  <form id="form" class="form-horizontal" method="post" action="index.php?my-drafts-act-{$act}-ajax-1.html">
                    <input type="hidden" name="id" value="{$data[id]}" />
                    <div class="form-group" data-field="cid">
                      <label class="control-label col-xs-12 col-sm-2">分类</label>
                      <div class="col-xs-12 col-sm-4">
                        {$cidhtml}
                      </div>
                    </div>
                    <div class="form-group" data-field="title">
                      <label class="control-label col-xs-12 col-sm-2">标题</label>
                      <div class="col-xs-12 col-sm-10">
                        <input id="c-title" class="form-control" name="title" type="text" required placeholder="请输入标题" value="{$data[title]}" autocomplete="off" maxlength="80">
                      </div>
                    </div>
                    <div class="form-group" data-field="pic">
                      <label class="control-label col-xs-12 col-sm-2">缩略图</label>
                      <div class="col-xs-12 col-sm-8">
                        <div class="input-group">
                          <input id="pic" class="form-control" name="pic" readonly type="text" value="{$data[pic]}">
                          <div class="input-group-addon no-border no-padding">
                            <span><button type="button" id="pic_btn" class="btn btn-danger plupload dz-clickable"><i class="fa fa-upload dz-message"></i> 上传</button></span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="form-group" data-field="intro">
                      <label class="control-label col-xs-12 col-sm-2">摘要</label>
                      <div class="col-xs-12 col-sm-10">
                        <textarea class="form-control" name="intro" rows="2" maxlength="255" autocomplete="off" placeholder="请输入摘要">{$data[intro]}</textarea>
                      </div>
                    </div>
                    <div class="form-group" data-field="content">
                      <label class="control-label col-xs-12 col-sm-2">内容</label>
                      <div class="col-xs-12 col-sm-10">
                        <textarea id="content" name="content" required class="form-control" autocomplete="off" placeholder="请输入内容" style="min-height: 50px;">{$data[content]}</textarea>
                      </div>
                    </div>
                    <div class="form-group" data-field="title">
                      <label class="control-label col-xs-12 col-sm-2">发布时间</label>
                      <div class="col-xs-12 col-sm-10">
                        <input id="c-dateline" class="form-control" name="dateline" type="text" placeholder="请选择发布时间" value="" autocomplete="off">
                      </div>
                    </div>
                    <div class="form-group normal-footer">
                      <label class="control-label col-xs-12 col-sm-2"></label>
                      <div class="col-xs-12 col-sm-10">
                        <button type="submit" class="btn btn-primary btn-embossed" lay-submit lay-filter="form">发布</button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
{inc:user/footer.htm}
<style>
  *, ::after, ::before{box-sizing:border-box;}
  .navbar-fixed-top{z-index: 900;}
  .layui-upload-file {
    display: none!important;
    opacity: .01;
  }
</style>
<script type="text/javascript">
  var mid = "{$mid}";
  layui.use(['form','layer', 'upload', 'laydate'], function () {
    var form = layui.form, layer = layui.layer, $ = layui.$, upload = layui.upload, laydate = layui.laydate;

    $("#my-drafts").addClass("active");

    //缩略图上传
    upload.render({
      elem: '#pic_btn'
      ,url: 'index.php?my-drafts-act-upload_pic-mid-'+mid
      ,field:'upfile'
      ,accept: 'images'
      ,acceptMime:'image/*'
      ,done: function(res){
        if(res.err == 1){
          layer.msg(res.msg, {icon: 5});
        }else{
          $("#pic").val(res.data.path);
          layer.msg('上传成功！', {icon: 1});
        }
      }
      ,error: function(){
        layer.msg('请求异常',{icon: 5});
      }
    });

    laydate.render({
      elem: '#c-dateline',
      type: 'datetime',
      min: '{$min}'
    });

    form.on('submit(form)', function (data) {
      if (window.hasOwnProperty('editor')) {
        window.editor.async();
      }
      layer.load(2, {time: 10*1000});
      $.ajax({
        type	: "POST",
        cache	: false,
        url		: $("#form").attr("action"),
        data	: $("#form").serialize(),
        success	: function(data) {
          layer.closeAll('loading');
          var data = eval("("+data+")");
          var icon = 1;
          if( data.err == 1 ){
            icon = 5;
          }
          layer.msg(data.msg, {icon: icon});
          if(data.err==0) setTimeout(function(){ window.location.href="{$my_drafts_url}"; }, 1000);
        },
        error	: function(html){
          layer.closeAll('loading');
          alert("提交数据失败，代码:"+ html.status +"，请稍候再试");
        }
      });
      return false;
    });
  });
</script>
{hook:user_content_add_after.htm}
</body>
</html>