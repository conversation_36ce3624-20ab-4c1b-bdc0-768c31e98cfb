{inc:header.htm}
<div class="layuimini-container">
	<div class="layuimini-main">
		<form id="form" class="layui-form"  action="index.php?drafts-{$_GET['action']}-mid-{$mid}-ajax-1" method="post">
			<input name="id" type="hidden" value="{$data[id]}" />
			<input name="mid" type="hidden" value="{$mid}" />
			<div class="layui-row layui-col-space10">
				<div class="layui-col-md9">
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label required">分类</label>
							<div class="layui-input-inline">
								{$cidhtml}
							</div>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label required">标题</label>
						<div class="layui-input-block">
							<input type="text" name="title" value="{$data[title]}" maxlength="80" autocomplete="off" placeholder="请输入标题" class="layui-input" lay-verify="required" lay-reqtext="标题不能为空" />
						</div>
					</div>
					{hook:admin_drafts_set_title_after.htm}

					<div class="layui-form-item">
						<label class="layui-form-label">摘要</label>
						<div class="layui-input-block">
							<textarea name="intro" class="layui-textarea" maxlength="255" autocomplete="off" placeholder="请输入摘要" style="min-height: 50px;">{$data[intro]}</textarea>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label required">内容</label>
						<div class="layui-input-block">
							<textarea id="content" name="content" class="layui-textarea" autocomplete="off" placeholder="请输入内容" style="min-height: 50px;">{$data[content]}</textarea>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label"></label>
						<div class="layui-input-block">
							<input name="isremote" type="checkbox" value="1" title="远程图片本地化" lay-skin="primary">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">标签</label>
						<div class="layui-input-block">
							<input name="tags" type="text" value="{$data[tags]}" maxlength="200" class="layui-input" placeholder="多个以英文逗号隔开" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">别名</label>
						<div class="layui-input-inline">
							<input name="alias" type="text" value="{$data[alias]}" maxlength="50" class="layui-input" placeholder="请输入URL别名" />
						</div>
						<div class="layui-form-mid layui-word-aux">数字 字母 横线 下划线</div>
					</div>
					{hook:admin_drafts_set_left_after.htm}
				</div>
				<div class="layui-col-md3">
					<div class="layui-form-item">
						<label class="layui-form-label">缩略图</label>
						<div class="layui-input-block">
							<div class="layui-inline"><input id="pic" name="pic" type="text" value="{$data[pic]}" maxlength="255" class="layui-input" placeholder="图片地址" /></div>
							<div class="layui-inline">
								<button type="button" class="layui-btn" id="pic_btn">
									<i class="layui-icon">&#xe67c;</i>上传图片
								</button>
							</div>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">属性</label>
						<div class="layui-input-block">
							{$flaghtml}
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">作者</label>
						<div class="layui-input-block">
							<input name="author" type="text" value="{$data[author]}" maxlength="20" class="layui-input" placeholder="请输入内容作者" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">来源</label>
						<div class="layui-input-block">
							<input name="source" type="text" value="{$data[source]}" maxlength="100" class="layui-input" placeholder="请输入内容来源" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">浏览量</label>
						<div class="layui-input-block">
							<input name="views" type="number" value="{$data[views]}" class="layui-input" placeholder="请输入浏览量" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">发布时间</label>
						<div class="layui-input-block">
							<input name="dateline" id="dateline" type="text" value="{$data[dateline]}" class="layui-input" placeholder="请选择发布时间" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">SEO标题</label>
						<div class="layui-input-block">
							<input type="text" name="seo_title" value="{$data[seo_title]}" maxlength="100" autocomplete="off" placeholder="请输入SEO标题" class="layui-input" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">SEO关键词</label>
						<div class="layui-input-block">
							<input type="text" name="seo_keywords" value="{$data[seo_keywords]}" maxlength="200" autocomplete="off" placeholder="请输入SEO关键词" class="layui-input" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">SEO描述</label>
						<div class="layui-input-block">
							<textarea name="seo_description" class="layui-textarea" maxlength="255" autocomplete="off" placeholder="请输入SEO描述" style="min-height: 50px;">{$data[seo_description]}</textarea>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">跳转URL</label>
						<div class="layui-input-block">
							<input name="jumpurl" type="url" value="{$data[jumpurl]}" maxlength="255" class="layui-input" placeholder="请输入跳转URL" />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">禁止评论</label>
						<div class="layui-input-block">
							<input type="radio" name="iscomment" value="1" title="是" {if:!empty($data[iscomment])}checked{/if} />
							<input type="radio" name="iscomment" value="0" title="否" {if:empty($data[iscomment])}checked{/if} />
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">排序值</label>
						<div class="layui-input-block">
							<input name="orderby" type="number" value="{$data[orderby]}"  class="layui-input" placeholder="请输入发布排序值" />
						</div>
					</div>
					{hook:admin_drafts_set_right_after.htm}
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-form-item">
					<div class="layui-input-block">
						<button class="layui-btn" lay-submit lay-filter="form">保存</button>
					</div>
				</div>
			</div>
		</form>
	</div>
</div>
<script type="text/javascript">
var mid = "{$mid}";
layui.use(['form','layer', 'upload', 'miniTab', 'laydate'], function () {
	var layer = layui.layer, upload = layui.upload, form = layui.form, miniTab = layui.miniTab, laydate = layui.laydate;

	//缩略图上传
	upload.render({
		elem: '#pic_btn'
		,url: 'index.php?drafts-upload_pic-mid-'+mid
		,field:'upfile'
		,accept: 'images'
		,acceptMime:'image/*'
		,done: function(res){
		    if(res.err == 1){
                layer.msg(res.msg, {icon: 5});
            }else{
                $("#pic").val(res.data.src);
                layer.msg('上传成功！', {icon: 1});
            }
		}
		,error: function(){
			layer.msg('请求异常',{icon: 5});
		}
	});

	laydate.render({
		elem: '#dateline',
		type: 'datetime',
		min: '{$min}'
	});

	//监听提交
	form.on('submit(form)', function () {
		if (window.hasOwnProperty('editor')) {
			window.editor.async();
		}
		adminAjax.postform('#form',function (data) {
			var json = toJson(data);
			if( json.err == 0 ){
				var icon = 1;
			}else{
				var icon = 5;
			}
			layer.msg(json.msg, {icon: icon});
			if(json.err==0) {
				setTimeout(function(){
					miniTab.reloadIframe('index.php?drafts-index-mid-'+mid);
					miniTab.deleteCurrentByIframe();
				}, 1500);
			}
		});
		return false;
	});
});
</script>
{hook:admin_content_add_after.htm}
</body>
</html>
