<?php
defined('ROOT_PATH') || exit;
$tableprefix = $_ENV['_config']['db']['master']['tablepre'];	//表前缀

//草稿
$sql_table = "CREATE TABLE IF NOT EXISTS ".$tableprefix."drafts (
  id int(10) unsigned NOT NULL AUTO_INCREMENT,
  mid tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '模型ID',
  cid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
  title varchar(80) NOT NULL DEFAULT '' COMMENT '标题',
  alias varchar(50) NOT NULL DEFAULT '' COMMENT '英文别名',
  tags varchar(255) NOT NULL DEFAULT '' COMMENT '标签 (,隔开)',
  intro varchar(255) NOT NULL DEFAULT '' COMMENT '内容介绍',
  pic varchar(255) NOT NULL DEFAULT '' COMMENT '缩略图地址',
  uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  author varchar(20) NOT NULL DEFAULT '' COMMENT '作者',
  source varchar(100) NOT NULL DEFAULT '' COMMENT '来源',
  dateline int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发表时间',
  lasttime int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  ip int(10) NOT NULL DEFAULT '0' COMMENT 'IP',
  views int(10) unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
  iscomment tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '禁止评论',
  flags varchar(20) NOT NULL DEFAULT '' COMMENT '所有属性 ,分割',
  seo_title varchar(100) NOT NULL DEFAULT '' COMMENT 'SEO标题',
  seo_keywords varchar(200) NOT NULL DEFAULT '' COMMENT 'SEO关键词',
  seo_description varchar(255) NOT NULL DEFAULT '' COMMENT 'SEO描述',
  jumpurl varchar(255) NOT NULL DEFAULT '' COMMENT '跳转URL',
  content mediumtext NOT NULL COMMENT '内容',
  orderby int(10) unsigned NOT NULL DEFAULT '0' COMMENT '随机值,随机发布时用',
  PRIMARY KEY  (id),
  KEY mid (mid),
  KEY orderby (orderby),
  KEY cid_id (cid,id)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;";
$this->db->query($sql_table);

$arr = array(
    'locoy_pwd' => 'lecms',//火车头免登录发布密码
    'locoy_uid'=>1, //火车头发布用户UID
    'corn_limit'=>10,   //定时发布 每次发布数量
    'orderway'=>0,  //0 ID升序 1 ID降序 2 随机
    'isremote'=>0,  //草稿内容图片本地化？
    'in_hour'=>'8,9,10',  //指定小时内发布 0~23
    'check_title'=>0,   //发布时是否检查重复标题
    'high_release'=>'', //高级发布设置
    'open_tougao'=>0,   //开启投稿
    'tougao_limit'=>0,  //每日条数
    'disable_uid'=>'',  //禁止的用户UID
);
$this->kv->set('le_drafts_setting', $arr);