<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-12-21
 * Time: 11:07
 * Description:核心框架英文语言包
 */
return array(
    'tips'=>'Tips',
    'successfully'=>'Successfully',
    'failed'=>'Failed',
    'error'=>'Error',
    'error_info'=>'Error Info',
    'error_line'=>'Error Line',
    'jump_now'=>'Jump Now',
    'auto_jump_tips'=>' seconds jump automatically',
    'close_trace'=>'X',
    'max_screen_trace'=>'Full',
    'min_screen_trace'=>'Min',
    'basic_trace'=>'Basic',
    'include_file'=>'Include File',
    'autoload_file'=>'Auto Load',
    'model_trace'=>'Model',
    'view_trace'=>'View',
    'control_trace'=>'Control',
    'logs_trace'=>'Logs',
    'script_filename_trace'=>'Filename',
    'time_trace'=>'Time',
    'ip_trace'=>'IP',
    'request_uri_trace'=>'URI',
    'runtime_trace'=>'Runtime',
    'runmen_trace'=>'Memory',
    'other_trace'=>'Other',
    'program_flow'=>'Program',
    'exception_message'=>'Message',
    'exception_file'=>'File',
    'exception_line'=>'Line',
    'exception_lines'=>'{line}',
    'visitor'=>'Visitor',
    'page'=>'Page',
    'page_current'=>'page {page}',
    'url_suffix'=>'URL Suffix',

    'yes'=>'Yes',
    'no'=>'No',
    'none'=>'None',
    'all'=>'All',
    'unknown'=>'Unknown',
    'enable'=>'Enable',
    'disable'=>'Disable',
    'upload'=>'Upload',
    'download'=>'Download',
    'install'=>'Install',
    'unstall'=>'Unstall',
    'update'=>'Update',
    'checkall'=>'Check All',
    'back'=>'Back',
    'return'=>'Return',

    'submit'=>'Submit',
    'reset'=>'Reset',
    'submiting'=>'Submiting',
    'create'=>'Create',
    'update'=>'Update',
    'edit'=>'Edit',
    'add'=>'Add',
    'modify'=>'Modify',
    'delete'=>'Delete',
    'confirm'=>'Confirm',
    'cancel'=>'Cancel',
    'save'=>'Save',
    'close'=>'Close',
    'open'=>'Open',
    'modify'=>'Modify',
    'count'=>' Count',
    'login'=>'Login',
    'logout'=>'Logout',
    'move'=>'Move',

    'zh-cn'=>'Simplified Chinese',
    'en'=>'English',

    'no_data'=>'No data',
    'data_error'=>'Data error',
    'data_no_exists'=>'Data does not exist',
    'delete_failed'=>'Delete failed',
    'delete_successfully'=>'Delete successfully',
    'delete_confirm'=>'Confirm Delete?',
    'edit_successfully'=>'Edit successfully',
    'edit_failed'=>'Edit failed',
    'add_successfully'=>'Add successfully',
    'add_failed'=>'Add failed',
    'opt_successfully'=>'successfully',
    'opt_failed'=>'Failed',
    'upload_failed'=>'Upload failed',
    'upload_successfully'=>'Upload successfully',
    'request_exception'=>'Request exception',

    'vcode'=>'Code',
    'vcode_no_empty'=>'Verification code cannot be empty',
    'vcode_error'=>'Verification code error',
    'email_format_error'=>'Email format error',

    'tpl_file_not_exists'=>'Template file {tplfile} does not exist',
    'write_tpl_file_failed'=>'Failed to write template compilation file {tplfile}',

    'edit_field_successfully'=>'Edit {field} successfully',
    'edit_field_failed'=>'Edit {field} failed',
    'add_field_successfully'=>'Add {field} successfully',
    'add_field_failed'=>'Add {field} failed',
    'filed_not_empty'=>'{field} cannot be empty',

    //前后台共用（主要是发布内容，不只后台、前台也用得着）
    'user_not_exists'=>'User information does not exist',
    'category_not_exists'=>'Category does not exist',
    'alias_error_number_and_number'=>'Alias cannot be in the format of "number_number"',
    'please_select_category'=>'Please select category',
    'please_fill_title'=>'Please fill in the title',
    'please_fill_content_over_5'=>'Your content is too few words. It cannot be less than 5 characters',
    'id_not_exists'=>'ID does not exist',
    'alias_error_1'=>'Alias can only be number letter _',
    'alias_error_2'=>'Alias can only be number letter _ -',
    'alias_error_3'=>'Alias already used by tag URL',
    'alias_error_4'=>'Alias already used by comment URL',
    'alias_error_5'=>'Alias already used by Personal URL',
    'alias_error_6'=>'Reserved keywords are not allowed for alias',
    'alias_error_7'=>'Alias is already used by category',
    'alias_error_8'=>'Alias is already used by content',
    'alias_error_9'=>'Alias is already used by model tablename',
    'cid_error'=>'Category ID error',
    'write_tag_table_failed'=>'Failed to write database table',
    'write_content_table_failed'=>'Failed to write database table',
    'write_content_data_table_failed'=>'Failed to write database table',
    'write_content_views_table_failed'=>'Failed to write database table',
    'fabu_successfully'=>'Add successfully',
    'isremote_failed_tip_1'=>',Remote capture of picture failed:',
    'isremote_failed_tip_2'=>'',

    // hook xiunophp_lang_en.php
);