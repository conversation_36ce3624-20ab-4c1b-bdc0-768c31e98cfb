// 生成指定长度的随机字符串函数
function getRandomString(length) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

// 生成指定范围内的随机数字函数
function getRandomNumber(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 修改页面中的所有链接
function modifyLinks() {
    // 处理A标签链接
    document.querySelectorAll('a').forEach(link => {
        const originalHref = link.href;
        if (originalHref.startsWith("https://jmj.cc/s/") && !link.classList.contains('locked')) {
            const newSuffix = getRandomString(3);
            const baseUrl = originalHref.slice(0, -3);
            link.href = `${baseUrl}${newSuffix}`;
        }
        if (originalHref.startsWith("https://www.feemoo.com/s/") && !link.classList.contains('locked')) {
            const newSuffix = getRandomString(3);
            const baseUrl = originalHref.slice(0, -3);
            link.href = `${baseUrl}${newSuffix}`;
        }
        if (originalHref.includes("http://www.xunniuyun.com/file-")) {
            const randomNumber = getRandomNumber(1000000, 9999999);
            const newHref = originalHref.replace(/file-\d+\.html/, `file-${randomNumber}.html`);
            link.href = newHref;
        }
        // 新增116pan.com链接处理
        if (originalHref.includes("https://www.116pan.com/viewfile.php?file_id=") && !link.classList.contains('locked')) {
            const randomNumber = getRandomNumber(1000000, 9999999);
            link.href = originalHref.replace(/file_id=\d+/, `file_id=${randomNumber}`);
        }
        // 将特定字符串替换为新的网址
        const stringsToReplace = ['MVDJVN', 'FM66637FFWUWSVIP', '12JNUB', 'FM666F5UG1JJSVIP', '0SYSFJ'];
        stringsToReplace.forEach(str => {
            if (originalHref.includes(str)) {
                link.href = '777723.xyz';
            }
        });
    });

    // 处理所有HTML元素中的文本内容
    function processNode(node) {
        if (node.nodeType === 3) { // 文本节点
            let content = node.textContent;
            // 处理 jmj.cc 链接
            content = content.replace(/https:\/\/jmj\.cc\/s\/[a-zA-Z0-9]+/g, (match) => {
                const newSuffix = getRandomString(3);
                return match.slice(0, -3) + newSuffix;
            });
            // 处理 feemoo.com 链接
            content = content.replace(/https:\/\/www\.feemoo\.com\/s\/[a-zA-Z0-9]+/g, (match) => {
                const newSuffix = getRandomString(3);
                return match.slice(0, -3) + newSuffix;
            });
            // 处理 xunniuyun.com 链接
            content = content.replace(/http:\/\/www\.xunniuyun\.com\/file-\d+\.html/g, (match) => {
                const randomNumber = getRandomNumber(1000000, 9999999);
                return `http://www.xunniuyun.com/file-${randomNumber}.html`;
            });
            // 新增116pan.com链接处理
            content = content.replace(/https:\/\/www\.116pan\.com\/viewfile\.php\?file_id=\d+/g, (match) => {
                const randomNumber = getRandomNumber(1000000, 9999999);
                return match.replace(/file_id=\d+/, `file_id=${randomNumber}`);
            });
            // 处理特定字符串
            const stringsToReplace = ['MVDJVN', 'FM66637FFWUWSVIP', '12JNUB', 'FM666F5UG1JJSVIP', '0SYSFJ'];
            stringsToReplace.forEach(str => {
                content = content.replace(str, '777723.xyz');
            });
            if (content !== node.textContent) {
                node.textContent = content;
            }
        }
        // 处理 .showhide 元素
        if (node.classList && node.classList.contains('showhide')) {
            node.innerHTML = node.innerHTML
                .replace(/https:\/\/jmj\.cc\/s\/[a-zA-Z0-9]+/g, (match) => {
                    const newSuffix = getRandomString(3);
                    return match.slice(0, -3) + newSuffix;
                })
                .replace(/https:\/\/www\.feemoo\.com\/s\/[a-zA-Z0-9]+/g, (match) => {
                    const newSuffix = getRandomString(3);
                    return match.slice(0, -3) + newSuffix;
                })
                .replace(/http:\/\/www\.xunniuyun\.com\/file-\d+\.html/g, (match) => {
                    const randomNumber = getRandomNumber(1000000, 9999999);
                    return `http://www.xunniuyun.com/file-${randomNumber}.html`;
                })
                // 新增116pan.com处理
                .replace(/https:\/\/www\.116pan\.com\/viewfile\.php\?file_id=\d+/g, (match) => {
                    const randomNumber = getRandomNumber(1000000, 9999999);
                    return match.replace(/file_id=\d+/, `file_id=${randomNumber}`);
                });
        }
        // 递归处理所有子节点
        if (node.childNodes && node.nodeName !== 'SCRIPT' && node.nodeName !== 'STYLE') {
            for (let child of node.childNodes) {
                processNode(child);
            }
        }
    }
    // 从文档根节点开始处理
    processNode(document.body);
}

// 当DOM加载完成后执行修改
document.addEventListener("DOMContentLoaded", modifyLinks);