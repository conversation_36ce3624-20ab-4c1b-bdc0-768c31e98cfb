<?php
/**
 * Author: dadadezhou <<EMAIL>>
 * Date: 2022-10-09
 * Time: 15:21
 * Description: 后台父类控制器
 */

defined('ROOT_PATH') or exit;

class admin_control extends control {
    public $_uid = 0;   //用户ID
    public $_user = array();	// 用户
    public $_group = array();	// 用户组

    function __construct() {
        $_ENV['_config']['FORM_HASH'] = form_hash();
        $this->assign('C', $_ENV['_config']);
        $this->assign_value('core', F_APP_NAME);

        $admauth = R($_ENV['_config']['cookie_pre'].'admauth', 'R');

        $err = 0;
        if(empty($admauth)) {
            $err = 1;
        }else{
            $admauth = str_auth($admauth);
            if(empty($admauth)) {
                $err = 1;
            }else{
                $arr = explode("\t", $admauth);
                // hook admin_admin_control_admauth_after.php
                if(count($arr) < 4) {
                    $err = 1;
                }else{
                    $uid      = $arr[0];
                    $username = $arr[1];
                    $password = $arr[2];
                    $groupid  = $arr[3];

                    $user = &$this->user;
                    $user_group = &$this->user_group;

                    $this->_user = $user->get($uid);
                    $this->_group = $user_group->get($groupid);

                    if(empty($this->_group) || empty($this->_user)) {
                        $err = 1;
                    }elseif($this->_user['password'] != $password || $this->_user['username'] != $username || $this->_user['groupid'] != $groupid) {
                        $err = 1;
                    }else{
                        // 检查用户组权限 (如果非管理员将重新定义导航数组)
                        $this->check_user_group();

                        $this->assign('_uid', $this->_user['uid']);
                        $this->assign('_user', $this->_user);
                        $this->assign('_group', $this->_group);
                        // hook admin_admin_control_admauth_success.php
                    }
                }
            }
        }

        if(R('control') == 'index' && R('action') == 'login') {
            if(!$err) {
                exit('<html><body><script>top.location="./"</script></body></html>');
            }
        }elseif(R('control') == 'admin' && R('action') == 'init_navigation' && $err) {
            E(1, lang('please_login_again'));
        }elseif($err) {
            if(R('ajax')) {
                $this->message(0, lang('illegal_access'), 'index.php?index-login');
            }
            exit('<html><body><script>top.location="index.php?index-login"</script></body></html>');
        }

        // hook admin_admin_control_construct_after.php
    }

    // 检查是不是管理员
    protected function check_isadmin() {
        // hook admin_admin_control_check_isadmin_before.php
        if($this->_group['groupid'] != 1) {
            $this->message(0, lang('access_dis1'), -1);
        }
    }

    // 检查用户组权限
    protected function check_user_group() {
        // hook admin_admin_control_check_user_group_before.php

        if($this->_group['groupid'] == 1) return;
        if($this->_group['groupid'] > 5) {
            _setcookie('admauth', '', 1);
            log::write(lang('access_dis2_log'), 'login_log.php');
            $this->message(0, lang('access_dis2'), -1);
        }else{
            // hook admin_admin_control_check_user_group_purviews_before.php

        }
    }

    // 清除缓存
    public function clear_cache() {
        // hook admin_admin_control_clear_cache_before.php

        $this->runtime->truncate();

        try{ unlink(RUNTIME_PATH.'_runtime.php'); }catch(Exception $e) {}
        $tpmdir = array('_control', '_model', '_view');
        foreach($tpmdir as $dir) _rmdir(RUNTIME_PATH.APP_NAME.$dir);
        foreach($tpmdir as $dir) _rmdir(RUNTIME_PATH.F_APP_NAME.$dir);
        return TRUE;
    }

    // 初始化导航数组
    public function init_navigation() {
        //首个tab页和左上角链接
        $menu = array(
            'homeInfo' => array('title' => lang('home'), 'href' => 'index.php?my-index'),
            'logoInfo' => array('title' => lang('admin_manage'), 'href' => '', 'image' => $_ENV['_config']['admin_static'].'admin/images/logo.png'),
        );

        //操作类菜单
        $menu['menuInfo'] = array();

        // hook admin_admin_control_init_nav_before.php

        $menu['menuInfo']['setting'] = array(
            'title' => lang('setting'),
            'icon' => 'fa fa-cogs',
            'href' => '',
            'target' => '_self',
            'child' =>array(
                array('title' => lang('basic_setting'), 'href' => 'index.php?setting-index', 'icon' => 'fa fa-cog', 'target' => '_self'),
                array('title' => lang('seo_setting'), 'href' => 'index.php?setting-seo', 'icon' => 'fa fa-life-ring', 'target' => '_self'),
                array('title' => lang('link_setting'), 'href' => 'index.php?setting-link', 'icon' => 'fa fa-link', 'target' => '_self'),
                array('title' => lang('user_setting'), 'href' => 'index.php?setting-user', 'icon' => 'fa fa-user-circle', 'target' => '_self'),
                array('title' => lang('attach_setting'), 'href' => 'index.php?setting-attach', 'icon' => 'fa fa-paperclip', 'target' => '_self'),
                array('title' => lang('image_setting'), 'href' => 'index.php?setting-image', 'icon' => 'fa fa-file-image-o', 'target' => '_self'),
                array('title' => lang('comment_setting'), 'href' => 'index.php?setting-comment', 'icon' => 'fa fa-comments-o', 'target' => '_self'),
                array('title' => lang('email_setting'), 'href' => 'index.php?setting-email', 'icon' => 'fa fa-envelope', 'target' => '_self'),
                array('title' => lang('other_setting'), 'href' => 'index.php?setting-other', 'icon' => 'fa fa-info', 'target' => '_self'),
            ),
        );

        // hook admin_admin_control_init_nav_setting_after.php

        $menu['menuInfo']['category'] = array(
            'title' => lang('category_manage'),
            'icon' => 'fa fa-bars',
            'href' => '',
            'target' => '_self',
            'child' =>array(
                array('title' => lang('cate_manage'), 'href' => 'index.php?category-index', 'icon' => 'fa fa-window-restore', 'target' => '_self'),
                array('title' => lang('navigate_manage'), 'href' => 'index.php?navigate-index', 'icon' => 'fa fa-list', 'target' => '_self'),
                array('title' => lang('mobil_navigate_manage'), 'href' => 'index.php?navigate-index-mobile-1', 'icon' => 'fa fa-list', 'target' => '_self'),
            ),
        );

        // hook admin_admin_control_init_nav_category_after.php

        $menu['menuInfo']['content'] = array(
            'title' => lang('content_manage'),
            'icon' => 'fa fa-folder-open',
            'href' => '',
            'target' => '_self',
            'child' =>array(),
        );
        $models_arr = $this->models->get_models();
        unset($models_arr['models-mid-1']); //去掉单页模型
        foreach ($models_arr as $model){
            if( $_ENV['_config']['admin_lang'] == 'zh-cn' ){
                $modelname = $model['name'];
            }else{
                $modelname = ucfirst($model['tablename']);
            }
            $menu['menuInfo']['content']['child'][] = array('title' => $modelname.lang('manage'), 'href' => 'index.php?content-index-mid-'.$model['mid'], 'icon' => isset($model['icon']) ? $model['icon'] : 'fa fa-bars', 'target' => '_self');
        }

        // hook admin_admin_control_init_nav_content_center.php

        $menu['menuInfo']['content']['child'][] = array('title' => lang('tags_manage'), 'href' => 'index.php?tag-index', 'icon' => 'fa fa-tags', 'target' => '_self');
        $menu['menuInfo']['content']['child'][] = array('title' => lang('comment_manage'), 'href' => 'index.php?comment-index', 'icon' => 'fa fa-comment-o', 'target' => '_self');
        $menu['menuInfo']['content']['child'][] = array('title' => lang('attach_manage'), 'href' => 'index.php?attach_manage-index', 'icon' => 'fa fa-file', 'target' => '_self');
        $menu['menuInfo']['content']['child'][] = array('title' => lang('model_manage'), 'href' => 'index.php?models-index', 'icon' => 'fa fa-cube', 'target' => '_self');

        // hook admin_admin_control_init_nav_content_after.php

        $menu['menuInfo']['user'] = array(
            'title' => lang('user_manage'),
            'icon' => 'fa fa-user-circle-o',
            'href' => '',
            'target' => '_self',
            'child' =>array(
                array('title' => lang('user_manage'), 'href' => 'index.php?user-index', 'icon' => 'fa fa-user', 'target' => '_self'),
                array('title' => lang('user_group_manage'), 'href' => 'index.php?user_group-index', 'icon' => 'fa fa-users', 'target' => '_self'),
            ),
        );

        // hook admin_admin_control_init_nav_user_after.php

        $menu['menuInfo']['plugin'] = array(
            'title' => lang('plugin_manage'),
            'icon' => 'fa fa-plug',
            'href' => '',
            'target' => '_self',
            'child' =>array(
                array('title' => lang('plugin_manage'), 'href' => 'index.php?plugin-index', 'icon' => 'fa fa-plug', 'target' => '_self'),
                array('title' => lang('theme_manage'), 'href' => 'index.php?theme-index', 'icon' => 'fa fa-tachometer', 'target' => '_self'),
            ),
        );

        // hook admin_admin_control_init_nav_plugin_after.php

        $menu['menuInfo']['tools'] = array(
            'title' => lang('tool_manage'),
            'icon' => 'fa fa-wrench',
            'href' => '',
            'target' => '_self',
            'child' =>array(
                array('title' => lang('clear_cache'), 'href' => 'index.php?tool-index', 'icon' => 'fa fa-trash-o', 'target' => '_self'),
                array('title' => lang('rebuild_statistics'), 'href' => 'index.php?tool-rebuild', 'icon' => 'fa fa-wrench', 'target' => '_self'),
            ),
        );

        // hook admin_admin_control_init_nav_tools_after.php

        // hook admin_admin_control_init_nav_after.php

        if( isset($this->_group['purviews']) && !empty($this->_group['purviews']) ){
            $menu['menuInfo'] = _json_decode($this->_group['purviews']);
        }

        $menu['menuInfo'] = array_values($menu['menuInfo']);//重置数组的键为 0 1 2....

        echo json_encode($menu);
        exit();
    }

    // hook admin_admin_control_after.php
}
