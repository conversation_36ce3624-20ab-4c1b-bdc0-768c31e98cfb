<?php
defined('ROOT_PATH') or exit;

class comment_control extends admin_control {
    public $_mid = 2;
    public $_table = 'article';
    public $_name = '文章';

    function __construct(){
        parent::__construct();

        $this->_mid = max(2, (int)R('mid','R'));
        $models = $this->models->get($this->_mid);
        empty($models) && $this->message(1, lang('data_no_exists'));

        $this->_table = $models['tablename'];
        $this->_name = $models['name'];

        $this->assign('mid',$this->_mid);
        $this->assign('table',$this->_table);
        $this->assign('name',$this->_name);
    }

	// 内容管理
	public function index() {
		// hook admin_comment_control_index_before.php

        $midhtml = $this->cms_content_comment->get_commenthtml_mid($this->_mid, 'lay-filter="mid"');
        $this->assign('midhtml',$midhtml);

		$this->display();
	}

    //ajax获取列表
    public function get_list(){
        //分页
        $page = isset( $_REQUEST['page'] ) ? intval($_REQUEST['page']) : 1;
        $pagenum = isset( $_REQUEST['limit'] ) ? intval($_REQUEST['limit']) : 15;

        //获取查询条件
        $keyword = isset( $_REQUEST['keyword'] ) ? trim($_REQUEST['keyword']) : '';
        if($keyword) {
            $keyword = urldecode($keyword);
            $keyword = safe_str($keyword);
        }
        $uid = isset( $_REQUEST['uid'] ) ? trim($_REQUEST['uid']) : 0;
        $id = isset( $_REQUEST['id'] ) ? trim($_REQUEST['id']) : 0;

        //组合查询条件
        $where['mid'] = $this->_mid;
        if( $id ){
            $where['id'] = $id;
        }
        if( $uid ){
            $where['uid'] = $uid;
        }
        if( $keyword ){
            $where['name'] = array('LIKE'=>$keyword);
        }
        // hook admin_comment_control_get_list_before.php

        $total = $this->cms_content_comment->find_count($where);

        //页数
        $maxpage = max(1, ceil($total/$pagenum));
        $page = min($maxpage, max(1, $page));

        // 获取列表
        $data_arr = array();
        $cms_arr = $this->cms_content_comment->list_arr($where, -1, ($page-1)*$pagenum, $pagenum, $total);

        $keys = array();
        foreach($cms_arr as $v) {
            $keys[] = $v['id'];
        }
        $this->cms_content->table = 'cms_'.$this->_table;
        $list_arr = $this->cms_content->mget($keys);

        foreach($cms_arr as &$v) {
            $this->cms_content_comment->format($v, 'Y-m-d H:i', false);

            $key = 'cms_'.$this->_table.'-id-'.$v['id'];
            if(isset($list_arr[$key])){
                $v['title'] = $list_arr[$key]['title'];
                $v['url'] = $this->cms_content->comment_url($list_arr[$key]['cid'], $list_arr[$key]['id']);
            }else{
                $v['title'] = lang('unknown');
                $v['url'] = '';
            }

            $data_arr[] = $v;   //排序需要索引从0开始
        }
        // hook admin_comment_control_get_list_after.php
        unset($cms_arr);
        //组合数据 输出到页面
        $arr = array(
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data_arr,
        );
        exit( json_encode($arr) );
    }

    //编辑表格字段
    public function set(){
        // hook admin_comment_control_set_before.php
        if( !empty($_POST) ){
            $field = trim( R('field','P') );
            $commentid = intval( R('commentid','P') );
            $value = trim( R('value','P') );
            $data = array(
                'commentid' => $commentid,
                $field => $value,
            );

            if(!$this->cms_content_comment->update($data)) {
                E(1, lang('edit_failed'));
            }
            // hook admin_comment_control_set_after.php
            E(0, lang('edit').' '.$field.' '.lang('successfully'));
        }
    }

	// 删除
	public function del() {
		// hook admin_comment_control_del_before.php

        $commentid = (int) R('commentid', 'P');
		empty($commentid) && E(1, lang('data_error'));

		$err = $this->cms_content_comment->xdelete($this->_table, $commentid);
		if($err) {
			E(1, $err);
		}else{
            // hook admin_comment_control_del_success.php
			E(0, lang('delete_successfully'));
		}
	}

	// 批量删除
	public function batch_del() {
		// hook admin_comment_control_batch_del_before.php
		$id_arr = R('id_arr', 'P');

		if(!empty($id_arr) && is_array($id_arr)) {

			$err_num = 0;
			foreach($id_arr as $commentid) {
				$err = $this->cms_content_comment->xdelete($this->_table, $commentid);
				if($err) $err_num++;
			}

			if($err_num) {
                E(1, $err_num.lang('num_del_failed'));
			}else{
				E(0, lang('delete_successfully'));
			}
		}else{
            E(1, lang('data_error'));
		}
	}

	// hook admin_comment_control_after.php
}
