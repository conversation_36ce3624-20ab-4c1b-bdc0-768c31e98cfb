<?php
defined('ROOT_PATH') or exit;
class tool_control extends admin_control{

    // 清除缓存
    public function index() {
        // hook admin_tool_control_index_before.php
        if(!empty($_POST)) {
            !empty($_POST['dbcache']) && $this->runtime->truncate();
            !empty($_POST['filecache']) && $this->un_filecache();
            E(0, lang('clear_success'));
        }
        $this->display();
    }

    // 重新统计
    public function rebuild() {
        // hook admin_tool_control_rebuild_before.php
        if(!empty($_POST)) {
            // hook admin_tool_control_rebuild_post_before.php
            // 重新统计分类的内容数量
            if(!empty($_POST['re_cate'])) {
                $tables = $this->models->get_table_arr();
                $cids = $this->category->get_category_db();

                foreach($cids as $row) {
                    if($row['mid'] == 1) continue;

                    $this->cms_content->table = 'cms_'.(isset($tables[$row['mid']]) ? $tables[$row['mid']] : 'article');
                    $count = $this->cms_content->find_count(array('cid'=>$row['cid']));

                    $this->category->update(array('cid'=>$row['cid'], 'count'=>$count));
                }
            }

            // 清空数据表的 count max 值，让其重新统计
            if(!empty($_POST['re_table'])) {
                $this->db->truncate('framework_count');
                $this->db->truncate('framework_maxid');
            }

            E(0, lang('rebuild_success'));
        }

        $this->display();
    }

    // 删除文件缓存
    private function un_filecache() {
        // hook admin_tool_control_un_filecache_before.php
        try{ unlink(RUNTIME_PATH.'_runtime.php'); }catch(Exception $e) {}
        $tpmdir = array('_control', '_model', '_view');
        foreach($tpmdir as $dir) {
            _rmdir(RUNTIME_PATH.APP_NAME.$dir);
            _rmdir(RUNTIME_PATH.F_APP_NAME.$dir);
            _rmdir(RUNTIME_PATH.'user'.$dir);
        }
        //清除语言包缓存 by zhou 2023/02/07
        _rmdir(RUNTIME_PATH.'core_lang');
        _rmdir(RUNTIME_PATH.'lang');

        // hook admin_tool_control_un_filecache_after.php
        return TRUE;
    }
    // hook admin_tool_control_after.php
}