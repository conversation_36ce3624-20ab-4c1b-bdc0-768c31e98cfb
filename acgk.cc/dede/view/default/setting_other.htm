{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">{lang:other_setting}</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?setting-other-ajax-1" method="post">
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:debug_mod}</label>
				<div class="layui-input-inline">
					{$input[debug]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:debug_mod_admin}</label>
				<div class="layui-input-inline">
					{$input[debug_admin]}
				</div>
			</div>
			<hr/>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:lang_mod}</label>
				<div class="layui-input-inline">
					{$input[lang]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:lang_mod_admin}</label>
				<div class="layui-input-inline">
					{$input[lang_admin]}
				</div>
			</div>
			{hook:admin_setting_other_after.htm}
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
layui.use(['form','layer'], function(){
	var layer = layui.layer, form = layui.form;
	//监听提交
	form.on('submit(form)', function(){
		adminAjax.submit("#form", function (data) {
			var json = toJson(data);
			if( json.err == 0 ){
				var icon = 1;
			}else{
				var icon = 5;
			}
			layer.msg(json.msg, {icon: icon});
			if(json.err==0) {
				setTimeout(function(){
					top.location.reload();
				}, 1500);
			}
		});
	});
});
</script>
</body>
</html>
