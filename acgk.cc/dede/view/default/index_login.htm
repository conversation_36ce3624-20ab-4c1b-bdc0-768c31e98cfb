<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>{lang:admin_login}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta http-equiv="Access-Control-Allow-Origin" content="*">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="format-detection" content="telephone=no">
	<link rel="stylesheet" href="{$C[admin_static]}layui/lib/layui-v2.6.3/css/layui.css" media="all">
	<!--[if lt IE 9]>
	<script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
	<script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
	<![endif]-->
	<style>
		html, body {width: 100%;height: 100%;overflow: hidden}
		body {background: #1E9FFF;}
		body:after {content:'';background-repeat:no-repeat;background-size:cover;-webkit-filter:blur(3px);-moz-filter:blur(3px);-o-filter:blur(3px);-ms-filter:blur(3px);filter:blur(3px);position:absolute;top:0;left:0;right:0;bottom:0;z-index:-1;}
		.layui-container {width: 100%;height: 100%;overflow: hidden}
		.admin-login-background {width:360px;height:300px;position:absolute;left:50%;top:40%;margin-left:-180px;margin-top:-100px;}
		.logo-title {text-align:center;letter-spacing:2px;padding:5px 0;}
		.logo-title h1 {color:#1E9FFF;font-size:25px;font-weight:bold;}
		.login-form {background-color:#fff;border:1px solid #fff;border-radius:3px;padding:14px 20px;box-shadow:0 0 8px #eeeeee;}
		.login-form .layui-form-item {position:relative;}
		.login-form .layui-form-item label {position:absolute;left:1px;top:1px;width:38px;line-height:36px;text-align:center;color:#d2d2d2;}
		.login-form .layui-form-item input {padding-left:36px;}
		.captcha-img img {height:34px;border:1px solid #e6e6e6;height:36px;width:100%;}
	</style>
</head>
<body>
<div class="layui-container">
	<div class="admin-login-background">
		<div class="layui-form login-form">
			<form class="layui-form" action="index.php?index-login" method="post">
				<input type="hidden" name="FORM_HASH" value="{$C[FORM_HASH]}" />
				<div class="layui-form-item logo-title">
					<img src="{$C[admin_static]}admin/images/logo-login.png" alt="" />
				</div>
				<div class="layui-form-item">
					<label class="layui-icon layui-icon-username" for="username"></label>
					<input type="text" name="username" required placeholder="{lang:please_input_username}" autocomplete="off" class="layui-input">
				</div>
				<div class="layui-form-item">
					<label class="layui-icon layui-icon-password" for="password"></label>
					<input type="password" name="password" required placeholder="{lang:please_input_password}" autocomplete="off" class="layui-input">
				</div>
				<div class="layui-form-item">
					<button class="layui-btn layui-btn layui-btn-normal layui-btn-fluid" lay-submit="" lay-filter="login">{lang:login1}</button>
				</div>
			</form>
		</div>
	</div>
</div>
<script src="{$C[admin_static]}layui/lib/jquery-3.4.1/jquery-3.4.1.min.js" charset="utf-8"></script>
<script src="{$C[admin_static]}layui/lib/layui-v2.6.3/layui.js" charset="utf-8"></script>
<script src="{$C[admin_static]}layui/lib/jq-module/jquery.particleground.min.js" charset="utf-8"></script>
<script type="text/javascript">
	layui.use(['form'], function () {
		var form = layui.form,layer = layui.layer;

		if (top.location != self.location) top.location = self.location;

		$(document).ready(function(){
			$('.layui-container').particleground({
				dotColor:'#7ec7fd',
				lineColor:'#7ec7fd'
			});
		});

		form.on('submit(login)', function (data) {
			data = data.field;
			if (data.username == '') {
				layer.msg('{lang:please_input_username}', {icon: 5});
				return false;
			}else if (data.password == '') {
				layer.msg('{lang:please_input_password}', {icon: 5});
				return false;
			}
			$.post("index.php?index-login-ajax-1",data,function(res){
				if( res.err == 1){
					var icon = 5;
				}else{
					var icon = 1;
				}
				layer.msg(res.msg, {icon: icon});
				if(res.err==0) setTimeout(function(){ location.href="./"; }, 1000);
				return false;
			},'json');
			return false;
		});
	});
</script>
</body>
</html>
