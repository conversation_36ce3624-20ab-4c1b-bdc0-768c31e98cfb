{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">{lang:theme_manage}</div>
	<div class="layui-card-body">
		<div class="layui-tab-item layui-show cf">
			{if:!$themes}
            <div class="layui-btn layui-btn-disabled layui-btn-fluid">{lang:no_data}</div>
			{else}
			{loop:$themes $v $k}
			{php} $enable = ($k == $theme); {/php}
			<div class="thm{if:$enable} thmon{/if}">
				<div class="to">
					<p class="tu" style="background:url(../view/{$k}/show.jpg)"></p>
					<p class="full"><b>{lang:version}：{$v[version]}</b><b>{lang:update}：{$v[update]}</b>{$v[brief]}</p>
				</div>
				<h1>{$v[name]}({$k})</h1>
				<p class="author">{lang:author}: <a target="_blank" rel="nofollow" href="{$v[authorurl]}">{$v[author]}</a></p>
				<p class="gsm" theme="{$k}">
					{if:$enable}<b>{lang:being_used}</b>{else}<a class="enable layui-btn layui-btn-sm">{lang:enable}</a><a class="del layui-btn layui-btn-sm layui-btn-danger">{lang:delete}</a>{/if}
				</p>
			</div>
			{/loop}
			{/if}
		</div>
	</div>
</div>
<script type="text/javascript">
layui.use(['form','layer'], function () {
    var layer = layui.layer, form = layui.form;

	//启用
	$(".enable").off("click").click(function(){ enable($(this).parent().attr("theme")); });
	//删除
	$(".del").off("click").click(function(){
		var theme = $(this).parent().attr("theme");
		adminAjax.confirm("{lang:delete_confirm} “<font color='red'>"+$(this).parent().parent().children("h1").html()+"</font>”", function(){
			adminAjax.postd("index.php?theme-delete-ajax-1", {"theme" : theme});
		});
	});
});
// 启用
function enable(theme) {
	adminAjax.postd("index.php?theme-enable-ajax-1", {"theme" : theme});
}
function load() {
	$(".gsm>a").attr("href","javascript:;");

	$(".thm").off("hover").hover(function(){
		$(this).addClass("thm_x");
	}, function(){
		$(this).removeClass("thm_x");
	});

	$(".to").off("hover").hover(function(){
		$(this).children(".tu").hide();
		$(this).children(".full").show();
	},function(){
		$(this).children(".tu").show();
		$(this).children(".full").hide();
	});
}

load();
</script>
</body>
</html>
