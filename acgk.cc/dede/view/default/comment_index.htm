{inc:header.htm}
<div class="layuimini-container">
	<div class="layuimini-main">
		<fieldset class="table-search-fieldset">
			<legend>{lang:search}</legend>
			<div>
				<form class="layui-form layui-form-pane">
					<div class="layui-form-item">
						<div class="layui-inline">
							<div class="layui-input-inline">
								{$midhtml}
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">{lang:content}</label>
							<div class="layui-input-inline">
								<input placeholder="{lang:content}" autocomplete="off" type="text" class="layui-input" name="keyword" />
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">{lang:uid}</label>
							<div class="layui-input-inline">
								<input placeholder="{lang:uid}" autocomplete="off" type="number" class="layui-input" name="uid" />
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">{lang:cmsid}</label>
							<div class="layui-input-inline">
								<input placeholder="{lang:cmsid}" autocomplete="off" type="number" class="layui-input" name="cmsid" />
							</div>
						</div>
						<div class="layui-inline">
							<button type="submit" class="layui-btn layui-btn-primary"  lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> {lang:search}</button>
						</div>
					</div>
				</form>
			</div>
		</fieldset>
		<script type="text/html" id="toolbar">
			<div class="layui-btn-container">
				<button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete">{lang:batch_delete}</button>
			</div>
		</script>
		<table class="layui-hide" id="data-table" lay-filter="data-table-filter"></table>

		<script type="text/html" id="currentTableBar">
			<div class="layui-btn-group">
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">{lang:view}</a>
				<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">{lang:delete}</a>
			</div>
		</script>
	</div>
</div>

<script type="text/javascript">
	var mid = "{$mid}";
	var table = "{$table}";
	var name = "{$name}";
	var selectmid = $("#mid").val();
	if(mid != selectmid){
		mid = selectmid;
	}

	layui.use(['form','layer', 'table'], function () {
		var layer = layui.layer, form = layui.form, table = layui.table;

		table.render({
			elem: '#data-table',
			url: 'index.php?comment-get_list-mid-'+mid+'-',
			height: 'full-145',
			toolbar: '#toolbar',
			defaultToolbar: ['filter', 'exports', 'print'],
			cellMinWidth: 50,
			cols: [[
				{type: "checkbox", width: 50, fixed: 'left'},
				{field: 'title', title: '{lang:title}'},
				{field: 'author', width: 100, title: '{lang:author}', align: 'center', edit: 'text'},
				{field: 'date', width: 145, title: '{lang:date}', align: 'center'},
				{field: 'fullip', width: 145, title: '{lang:ip}', align: 'center'},
				{field: 'content', minwidth: 150, title: '{lang:content}', edit: 'text'},
				{title: '{lang:opt}', width: 100, toolbar: '#currentTableBar', align: "center"}
			]],
			limits: [10, 15, 20, 25, 50, 100],
			limit: 15,
			page: true
		});
		// 监听搜索操作
		form.on('submit(data-search-btn)', function (data) {
			mid = data.field.mid;
			//执行搜索重载
			table.reload('data-table', {
				url: 'index.php?comment-get_list-mid-'+mid+'-',
				page: {
					curr: 1
				}
				, where: {
					keyword: data.field.keyword,
					uid: data.field.uid,
					id: data.field.cmsid
				}
			}, 'data');

			return false;
		});
		// 监听下拉框操作
		form.on('select(mid)', function(data){
			//执行搜索重载
			table.reload('data-table', {
				url: 'index.php?comment-get_list-mid-'+data.value+'-',
				page: {
					curr: 1
				}
			}, 'data');
		});
		/**
		 * toolbar监听事件 table列表 头部的操作
		 */
		table.on('toolbar(data-table-filter)', function (obj) {
			if (obj.event === 'delete') {  // 监听删除操作
				var checkStatus = table.checkStatus('data-table')
						, data = checkStatus.data;
				var len = data.length;
				if(len == 0){
					layer.msg('{lang:select_data}',{icon:5});
					return false;
				}else{
					adminAjax.confirm('{lang:delete_confirm}', function () {
						var id_arr = [];
						for (var i in data) {
							id_arr[i] = data[i]['commentid'];
						}
						adminAjax.postd("index.php?comment-batch_del-ajax-1", {"mid":mid, "id_arr": id_arr});
					});
				}
			}
		});

		//监听单元格编辑
		table.on('edit(data-table-filter)', function(obj){
			var value = obj.value //得到修改后的值
					,data = obj.data //得到所在行所有键值
					,field = obj.field; //得到字段
			adminAjax.postd("index.php?comment-set-ajax-1", {"mid":mid, "commentid":data.commentid, "field":field, "value":value});
		});
		//监听每一行的操作
		table.on('tool(data-table-filter)', function (obj) {
			var data = obj.data;

			if (obj.event === 'view') {
				window.open(data.url);
			} else if (obj.event === 'delete') {
				adminAjax.confirm('{lang:delete_confirm}', function () {
					adminAjax.postd("index.php?comment-del-ajax-1", {"mid":mid, "commentid":data.commentid});
				});
			}
		});
	});
</script>
</body>
</html>
