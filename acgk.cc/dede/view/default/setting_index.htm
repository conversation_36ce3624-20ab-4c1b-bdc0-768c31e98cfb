{inc:header.htm}
<div class="layui-card">
	<div class="layui-card-header">{lang:basic_setting}</div>
	<div class="layui-card-body">
		<form id="form" class="layui-form" action="index.php?setting-index-ajax-1" method="post">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label required">{lang:webname}</label>
					<div class="layui-input-inline">
						{$input[webname]}
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label required">{lang:webdomain}</label>
					<div class="layui-input-inline">
						{$input[webdomain]}
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label required">{lang:webdir}</label>
					<div class="layui-input-inline">
						{$input[webdir]}
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">{lang:beian}</label>
					<div class="layui-input-inline">
						{$input[beian]}
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">{lang:webmail}</label>
					<div class="layui-input-inline">
						{$input[webmail]}
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">{lang:webqq}</label>
					<div class="layui-input-inline">
						{$input[webqq]}
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">{lang:webweixin}</label>
					<div class="layui-input-inline">
						{$input[webweixin]}
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">{lang:webtel}</label>
					<div class="layui-input-inline">
						{$input[webtel]}
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:tongji}</label>
				<div class="layui-input-block">
					{$input[tongji]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:copyright}</label>
				<div class="layui-input-block">
					{$input[copyright]}
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:link_tag_type}</label>
				<div class="layui-input-block">
					{$input[link_tag_type]}
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">{lang:mobile_view}</label>
					<div class="layui-input-inline">
						<input type="radio" name="open_mobile_view" value="1" title="{lang:open}" {if:!empty($input['open_mobile_view'])} checked="checked"{/if}>
						<input type="radio" name="open_mobile_view" value="0" title="{lang:close}"  {if:empty($input['open_mobile_view'])} checked="checked"{/if}>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">{lang:mobile_view_dir}</label>
					<div class="layui-input-inline">
						{$input[mobile_view]}
					</div>
					<div class="layui-form-mid layui-word-aux">{lang:default} “mobile”</div>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">{lang:admin_layout}</label>
				<div class="layui-input-block">
					{$input[admin_layout]}
				</div>
			</div>
			{hook:admin_setting_index_after.htm}
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
				</div>
			</div>
		</form>
	</div>
</div>

<script type="text/javascript">
layui.use(['form','layer'], function(){
	var layer = layui.layer, form = layui.form;
	//监听提交
	form.on('submit(form)', function(){
		adminAjax.submit("#form");
	});
});
</script>
</body>
</html>
