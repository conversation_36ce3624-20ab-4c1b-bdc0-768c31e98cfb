<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>{lang:admin_manage}</title>
	<meta name="keywords" content="{lang:admin_manage} Power <NAME_EMAIL>">
	<meta name="description" content="{lang:admin_manage}">
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta http-equiv="Access-Control-Allow-Origin" content="*">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="format-detection" content="telephone=no">
	<link rel="stylesheet" href="{$C[admin_static]}layui/lib/layui-v2.6.3/css/layui.css" media="all">
	<link rel="stylesheet" href="{$C[admin_static]}layui/css/layuimini.css?v=*******" media="all">
	<link rel="stylesheet" href="{$C[admin_static]}layui/css/themes/default.css" media="all">
	<link rel="stylesheet" href="//cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css" media="all">
	<!--[if lt IE 9]>
	<script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
	<script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
	<![endif]-->
	<style id="layuimini-bg-color"></style>
</head>
<body class="layui-layout-body layuimini-all">

<div class="layui-layout layui-layout-admin">

	<div class="layui-header header">
		<div class="layui-logo layuimini-logo"></div>

		<div class="layuimini-header-content">
			<a>
				<div class="layuimini-tool"><i title="展开" class="fa fa-outdent" data-side-fold="1"></i></div>
			</a>

			<!--电脑端头部菜单-->
			<ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-menu-header-pc layuimini-pc-show">
			</ul>

			<!--手机端头部菜单-->
			<ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-mobile-show">
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-list-ul"></i> {lang:select_module}</a>
					<dl class="layui-nav-child layuimini-menu-header-mobile">
					</dl>
				</li>
			</ul>

			<ul class="layui-nav layui-layout-right">
				<li class="layui-nav-item" lay-unselect>
					<a href="../" target="_blank" title="{lang:website}"><i class="fa fa-home"></i></a>
				</li>
				<li class="layui-nav-item" lay-unselect>
					<a href="javascript:;" data-refresh="刷新" data_title="{lang:refresh_success}"><i class="fa fa-refresh"></i></a>
				</li>
				<li class="layui-nav-item mobile layui-hide-xs" lay-unselect>
					<a href="javascript:;" data-check-screen="full"><i class="fa fa-arrows-alt"></i></a>
				</li>
				<li class="layui-nav-item layuimini-setting">
					<a href="javascript:;">{$_user[username]}</a>
					<dl class="layui-nav-child">
						<dd>
							<a href="javascript:;" layuimini-content-href="index.php?my-info" data-title="{lang:edit_information}" data-icon="fa fa-gears">{lang:edit_information}</a>
						</dd>
						<dd>
							<a href="javascript:;" layuimini-content-href="index.php?my-password" data-title="{lang:edit_password}" data-icon="fa fa-gears">{lang:edit_password}</a>
						</dd>
						<dd>
							<hr>
						</dd>
						<dd>
							<a href="javascript:;" class="login-out">{lang:logout}</a>
						</dd>
					</dl>
				</li>
				<li class="layui-nav-item layuimini-select-bgcolor" lay-unselect>
					<a href="javascript:;" data-bgcolor="{lang:change_color}" data-title="{lang:change_color}"><i class="fa fa-ellipsis-v"></i></a>
				</li>
			</ul>
		</div>
	</div>

	<!--无限极左侧菜单-->
	<div class="layui-side layui-bg-black layuimini-menu-left">
	</div>

	<!--初始化加载层-->
	<div class="layuimini-loader">
		<div class="layuimini-loader-inner"></div>
	</div>

	<!--手机端遮罩层-->
	<div class="layuimini-make"></div>

	<!-- 移动导航 -->
	<div class="layuimini-site-mobile"><i class="layui-icon"></i></div>

	<div class="layui-body">

		<div class="layuimini-tab layui-tab-rollTool layui-tab" lay-filter="layuiminiTab" lay-allowclose="true">
			<ul class="layui-tab-title">
				<li class="layui-this" id="layuiminiHomeTabId" lay-id=""></li>
			</ul>
			<div class="layui-tab-control">
				<li class="layuimini-tab-roll-left layui-icon layui-icon-left"></li>
				<li class="layuimini-tab-roll-right layui-icon layui-icon-right"></li>
				<li class="layui-tab-tool layui-icon layui-icon-down">
					<ul class="layui-nav close-box">
						<li class="layui-nav-item">
							<a href="javascript:;"><span class="layui-nav-more"></span></a>
							<dl class="layui-nav-child">
								<dd><a href="javascript:;" layuimini-tab-close="current">{lang:close_current}</a></dd>
								<dd><a href="javascript:;" layuimini-tab-close="other">{lang:close_other}</a></dd>
								<dd><a href="javascript:;" layuimini-tab-close="all">{lang:close_all}</a></dd>
							</dl>
						</li>
					</ul>
				</li>
			</div>
			<div class="layui-tab-content">
				<div id="layuiminiHomeTabIframe" class="layui-tab-item layui-show"></div>
			</div>
		</div>

	</div>
    <div class="layui-footer footer">
        {php}echo base64_decode('wqkgTGVjbXMu');{/php}
    </div>
</div>

<script src="{$C[admin_static]}layui/lib/layui-v2.6.3/layui.js" charset="utf-8"></script>
<script src="{$C[admin_static]}layui/js/lay-config.js?v=2.0.0" charset="utf-8"></script>
<script type="text/javascript">
	layui.use(['jquery', 'layer', 'miniAdmin'], function () {
		var $ = layui.jquery,layer = layui.layer,miniAdmin = layui.miniAdmin;
		// 初始化接口
		// 是否打开hash定位
		// 主题默认配置
		// 是否开启多模块	开启后顶级菜单横向排列
		// 是否默认展开菜单	只有 multiModule设置为false 才有效
		// 初始化加载时间
		// iframe窗口动画
		// 最大的tab打开数量
		//备注说明 不要放在下面参数的后面，否则开启html压缩将出错
		var admin_layout = "{$cfg[admin_layout]}";
		if(admin_layout == 1){
			var multiModule = true;
		}else{
			var multiModule = false;
		}
		var options = {
			iniUrl: "index.php?admin-init_navigation",
			urlHashLocation: false,
			bgColorDefault: false,
			multiModule: multiModule,
			menuChildOpen: false,
			loadingTime: 0,
			pageAnim: false,
			maxTabNum: 20
		};
		miniAdmin.render(options);

		$('.login-out').on("click", function () {
            $.post("index.php?index-logout-ajax-1",{"do": 1});
			layer.msg('{lang:logout_successfully}',{icon: 1}, function () {
				window.location = 'index.php?index-login';
			});
		});
	});
</script>
</body>
</html>
