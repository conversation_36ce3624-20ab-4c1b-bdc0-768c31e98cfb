{inc:header.htm}
<style>
    .layui-elem-field legend{font-size: 14px;}
</style>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-card">
            <div class="layui-card-body">
            <form class="layui-form layuimini-form" id="form" action="index.php?category-{$_GET['action']}-ajax-1" method="post">
                <input name="cid" type="hidden" value="{$data[cid]}" />
                <fieldset class="layui-elem-field">
                    <legend>{lang:basic_info}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label required">{lang:model}</label>
                            <div class="layui-input-block">
                                {loop:$mod_name $v $k}<input lay-filter="mid" name="mid" type="radio" value="{$k}" title="{$v}" {if:$data[mid] == $k}checked{/if}>{/loop}
                            </div>
                        </div>

                        <div class="layui-form-item" id="i_type">
                            <label class="layui-form-label required">{lang:type}</label>
                            <div class="layui-input-block">
                                <input lay-filter="type" name="type" type="radio" value="0" title="{lang:cate_type_0}" {if:$data[type] == 0}checked{/if}>
                                <input lay-filter="type" name="type" type="radio" value="1" title="{lang:cate_type_1}" {if:$data[type] == 1}checked{/if}>
                            </div>
                        </div>
                        <div class="layui-form-item" id="i_upid">
                            <label class="layui-form-label required">{lang:select_upid}</label>
                            <div class="layui-input-inline">
                                <select name="upid" id="upid"><option value="0">{lang:none}</option></select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label required">{lang:categoey_name}</label>
                                <div class="layui-input-inline">
                                    <input name="name" value="{$data[name]}" placeholder="{lang:categoey_name}" type="text" class="layui-input" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label required">{lang:alias}</label>
                                <div class="layui-input-inline">
                                    <input name="alias" value="{$data[alias]}" placeholder="{lang:alias}" type="text" class="layui-input" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:intro}</label>
                            <div class="layui-input-block">
                                <input name="intro" value="{$data[intro]}" placeholder="{lang:intro}" type="text" class="layui-input" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:thumb}</label>
                            <div class="layui-input-block">
                                <div class="layui-input-inline"><input name="pic" value="{$data[pic]}" id="pic_val" placeholder="{lang:thumb}" type="text" class="layui-input" /></div>
                                <div class="layui-input-inline">
                                    <button type="button" class="layui-btn" id="pic">
                                        <i class="layui-icon">&#xe67c;</i>{lang:upload_pic}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">{lang:orderby}</label>
                                <div class="layui-input-inline">
                                    <input name="orderby" value="{$data[orderby]}" type="number" class="layui-input" />
                                </div>
                            </div>
                            <div class="layui-inline" id="i_contribute">
                                <label class="layui-form-label">{lang:contribute}</label>
                                <div class="layui-input-inline">
                                    <input title="{lang:disable}" name="contribute" type="radio" value="0" {if:$data[contribute] == 0}checked{/if}>
                                    <input title="{lang:enable}" name="contribute" type="radio" value="1" {if:$data[contribute] == 1}checked{/if}>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" id="i_page_content" style="display:none">
                            <label class="layui-form-label">{lang:page_content}</label>
                            <div class="layui-input-block">
                                <textarea id="page_content" name="page_content" class="layui-textarea">{$data[page_content]}</textarea>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <fieldset class="layui-elem-field">
                    <legend>{lang:seo}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:seo_title}</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="{lang:seo_title}" type="text" name="seo_title" value="{$data[seo_title]}" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:seo_keywords}</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="{lang:seo_keywords}" type="text" name="seo_keywords" value="{$data[seo_keywords]}" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">{lang:seo_description}</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="{lang:seo_description}" type="text" name="seo_description" value="{$data[seo_description]}" />
                            </div>
                        </div>
                    </div>
                </fieldset>

                <fieldset class="layui-elem-field">
                    <legend>{lang:tpl_info}</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline" id="i_cate_tpl">
                                <label class="layui-form-label required">{lang:cate_tpl}</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" type="text" name="cate_tpl" id="cate_tpl" value="{$data[cate_tpl]}" />
                                </div>
                            </div>
                            <div class="layui-inline" id="i_show_tpl">
                                <label class="layui-form-label required">{lang:show_tpl}</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" type="text" name="show_tpl" id="show_tpl" value="{$data[show_tpl]}" />
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                {hook:admin_category_content_set_after.htm}
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="form">{lang:submit}</button>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 默认无编辑器
    window.editor_init = function(){
        // 编辑器API
        window.editor_api = {
            // 单页内容
            page_content : {
                obj : $('#page_content'),
                get : function() {
                    return this.obj.val();
                },
                set : function(s) {
                    return this.obj.val(s);
                },
                focus : function() {
                    return this.obj.focus();
                }
            }
        }
    }
    var models = {$models};

    // 设置表单内的值
    function setFormVal(mid, type) {

        if(mid == 1) {
            $("#i_type").hide();
            $("#i_page_content").show();
            $("#i_show_tpl").hide();
            $("#i_upid>.layui-form-label").html("{lang:select_upid}");

            // 单页时把类型设置为 0，禁止投稿
            $("input[name='type']").val(0);
            $("#i_contribute").hide();
            $("input[name='contribute']").val(0);

            $("#i_cate_tpl>.layui-form-label").html("{lang:page_tpl}");
            editor_init();
        }else{
            $("#i_type").show();
            $("#i_page_content").hide();
            $("#i_show_tpl").show();
            $("#i_upid>.layui-form-label").html("{lang:select_upid}");
            $("#i_cate_tpl>.layui-form-label").html(type == 1 ? "{lang:channel_tpl}" : "{lang:cate_tpl}");
            $("#i_contribute").show();
        }

        // 默认模板设置
        var edit_mid = "{$data[mid]}";
        var edit_type = "{$data[type]}";
        if(!!edit_mid && edit_mid == mid && edit_type == type) {
            $("#cate_tpl").val("{$data[cate_tpl]}");
            $("#show_tpl").val("{$data[show_tpl]}");
        }else{
            var k = "models-mid-"+mid;

            if(mid == 1){
                $("#cate_tpl").val(models[k]["cate_tpl"]);
            }else{
                $("#cate_tpl").val(type == 1 ? models[k]["index_tpl"] : models[k]["cate_tpl"]);
            }
            $("#show_tpl").val(models[k]["show_tpl"]);
        }
    }

    // 加载所属频道
    function loadCategoryUpid(mid, upid, noid) {
        adminAjax.get("index.php?category-get_category_upid-ajax-1-mid-"+mid+"-upid-"+Math.max(0, upid)+"-noid-"+Math.max(0, noid)+"&r="+time(), function(data){
            data = toJson(data);
            $("#i_upid>.layui-input-inline").html(data.upid);
            layui.use('form', function(){  //此段代码必不可少，不然select不可见
                var form = layui.form;
                form.render();
            });
        });
    }

    layui.use(['form','layer', 'upload', 'miniTab'], function () {
        var layer = layui.layer, upload = layui.upload, form = layui.form, miniTab = layui.miniTab;
        var mid = "{$data[mid]}";

        loadCategoryUpid(mid,"{$data[upid]}","{$data[cid]}");

        var def_type = $('input[name="type"]:checked').val();
        var type = "{$data[type]}";
        if(def_type != type){
            type = def_type;
        }
        var def_mid = $('input[name="mid"]:checked').val();
        if(def_mid != mid){
            mid = def_mid;
        }
        setFormVal(mid,type);

        if(mid == 1){
            editor_init();
        }

        //缩略图上传
        upload.render({
        	elem: '#pic' //绑定元素
        	,url: 'index.php?attach-upload_pic-table-category' //上传接口
        	,field:'upfile'
        	,accept: 'images'
        	,acceptMime:'image/*'
        	,done: function(res){
        		if(res.err == 1){
        			layer.msg(res.msg, {icon: 5});
        		}else{
        			$("#pic_val").val(res.data.src);
        			layer.msg('{lang:upload_sucessfully}', {icon: 1});
        		}
        	}
        	,error: function(){
        		//请求异常回调
        		layer.msg('{lang:request_exception}',{icon: 5});
        	}
        });

        // 如果分类已发布了内容或有下级分类或者是单页就不允许改变模型和类型
        var count = "{$data[count]}";
        var son_cate = "{$data[son_cate]}";

        if(count > 0 || son_cate > 0 || mid == 1) {
            $("input[name='mid']").each(function() {
                if($(this).val() != "{$data[mid]}") {
                    $(this).attr("disabled", "disabled");
                }
            });
            $("input[name='type']").each(function() {
                if($(this).val() != "{$data[type]}") {
                    $(this).attr("disabled", "disabled");
                }
            });
        }else{
            form.on('radio(mid)', function(data) {
                var type = data.value == 1 ? 0 : $('input[name="type"]:checked').val();
                //设置表单的值
                setFormVal(data.value, type);
                // 加载所属频道
                loadCategoryUpid(data.value, "{$data[upid]}", "{$data[cid]}");
            });

            form.on('radio(type)', function(data) {
                setFormVal($('input[name="mid"]:checked').val(), data.value);
            });
        }

        //监听提交
        form.on('submit(form)', function () {
            if (window.hasOwnProperty('editor')) {
                window.editor.async();
            }
            adminAjax.postform("#form",function (data) {
                var json = toJson(data);
                if( json.err == 0 ){
                    var icon = 1;
                }else{
                    var icon = 5;
                }
                layer.msg(json.msg, {icon: icon});
                if(json.err==0) {
                    setTimeout(function(){
                        miniTab.reloadIframe('index.php?category-index');
                        miniTab.deleteCurrentByIframe();
                    }, 1500);
                }
            });
            return false;
        });
    });
</script>
{hook:admin_category_set_after.htm}
</body>
</html>
